-- Add translation strings for Auto Title Generation feature
-- Insert these into the language_translations table

INSERT INTO `language_translations` (`lang_id`, `label`, `translation`) VALUES
(1, 'auto_generate', 'Auto Generate'),
(1, 'auto_generate_title', 'Auto Generate Title'),
(1, 'auto_generate_title_help', 'Click to automatically generate title from custom fields'),
(1, 'generating', 'Generating'),
(1, 'title_generated_successfully', 'Title generated successfully'),
(1, 'error_generating_title', 'Error generating title'),
(1, 'no_custom_fields_for_title', 'No custom fields available for title generation'),
(1, 'error_product_not_found', 'Product not found'),
(1, 'error_permission_denied', 'Permission denied'),
(1, 'title_auto_generated', 'Title auto-generated'),
(1, 'custom_fields', 'Custom Fields'),
(1, 'fill_fields_auto_title', 'Fill fields to automatically generate title'),
(1, 'select_category_to_load_fields', 'Select a category to load custom fields'),
(1, 'loading_custom_fields', 'Loading custom fields'),
(1, 'no_custom_fields_for_category', 'No custom fields available for this category'),
(1, 'error_loading_custom_fields', 'Error loading custom fields'),
(1, 'custom_fields_loaded_successfully', 'Custom fields loaded successfully'),
(1, 'select_option', 'Select option');

-- Note: These translations are for English (lang_id = 1)
-- Add similar translations for other languages if needed
