{"url": "http://web.test/Category/loadCategories", "method": "POST", "isAJAX": true, "startTime": **********.068555, "totalTime": 71.4, "totalMemory": "9.758", "segmentDuration": 15, "segmentCount": 5, "CI_VERSION": "4.5.4", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.070071, "duration": 0.010760068893432617}, {"name": "Required Before Filters", "component": "Timer", "start": **********.080832, "duration": 0.0012629032135009766}, {"name": "Routing", "component": "Timer", "start": **********.082098, "duration": 0.041517019271850586}, {"name": "Before Filters", "component": "Timer", "start": **********.123621, "duration": 0.0009799003601074219}, {"name": "Controller", "component": "Timer", "start": **********.124605, "duration": 0.015162944793701172}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.124607, "duration": 0.013737916946411133}, {"name": "After Filters", "component": "Timer", "start": **********.139778, "duration": 4.0531158447265625e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.139798, "duration": 0.00023317337036132812}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(15 total Queries, 15 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.41 ms", "sql": "<strong>SELECT</strong> GET_LOCK(&#039;1c7803e751dae243e580addc4059ca7a&#039;, 300) <strong>AS</strong> ci_session_lock", "trace": [{"file": "SYSTEMPATH\\Session\\Handlers\\Database\\MySQLiHandler.php:31", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Session\\Handlers\\DatabaseHandler.php:117", "function": "        CodeIgniter\\Session\\Handlers\\Database\\MySQLiHandler->lockSession()", "index": "  2    "}, {"function": "        CodeIgniter\\Session\\Handlers\\DatabaseHandler->read()", "file": "[internal function]", "index": "  3    "}, {"file": "SYSTEMPATH\\Session\\Session.php:920", "function": "        session_start()", "index": "  4    "}, {"file": "SYSTEMPATH\\Session\\Session.php:242", "function": "        CodeIgniter\\Session\\Session->startSession()", "index": "  5    "}, {"file": "SYSTEMPATH\\Config\\Services.php:710", "function": "        CodeIgniter\\Session\\Session->start()", "index": "  6    "}, {"file": "SYSTEMPATH\\Config\\BaseService.php:312", "function": "        CodeIgniter\\Config\\Services::session()", "index": "  7    "}, {"file": "SYSTEMPATH\\Config\\BaseService.php:251", "function": "        CodeIgniter\\Config\\BaseService::__callStatic()", "index": "  8    "}, {"file": "SYSTEMPATH\\Config\\Services.php:668", "function": "        CodeIgniter\\Config\\BaseService::getSharedInstance()", "index": "  9    "}, {"file": "SYSTEMPATH\\Config\\BaseService.php:321", "function": "        CodeIgniter\\Config\\Services::session()", "index": " 10    "}, {"file": "APPPATH\\Config\\Globals.php:32", "function": "        CodeIgniter\\Config\\BaseService::__callStatic()", "index": " 11    "}, {"file": "APPPATH\\Config\\Globals.php:245", "function": "        Config\\Globals::setGlobals()", "index": " 12    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:317", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Globals.php"], "function": "        include_once()", "index": " 13    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:296", "function": "        CodeIgniter\\Autoloader\\Autoloader->includeFile()", "index": " 14    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:272", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadInNamespace()", "index": " 15    "}, {"file": "APPPATH\\Config\\Routes.php:6", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadClass()", "index": " 16    "}, {"file": "SYSTEMPATH\\Router\\RouteCollection.php:340", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Routes.php"], "function": "        require()", "index": " 17    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:823", "function": "        CodeIgniter\\Router\\RouteCollection->loadRoutes()", "index": " 18    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:457", "function": "        CodeIgniter\\CodeIgniter->tryToRouteIt()", "index": " 19    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:355", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 20    "}, {"file": "SYSTEMPATH\\Boot.php:325", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 21    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 22    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 23    "}], "trace-file": "[internal function]", "qid": "252f8d8d904c203b787f958d094a106f"}, {"hover": "", "class": "", "duration": "1.48 ms", "sql": "<strong>SELECT</strong> `data`\n<strong>FROM</strong> `ci_sessions`\n<strong>WHERE</strong> `id` = &#039;mds_session:6vcro1fguv6ntunps7hsp0motgmkbgcg&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1644", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Session\\Handlers\\DatabaseHandler.php:135", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"function": "        CodeIgniter\\Session\\Handlers\\DatabaseHandler->read()", "file": "[internal function]", "index": "  3    "}, {"file": "SYSTEMPATH\\Session\\Session.php:920", "function": "        session_start()", "index": "  4    "}, {"file": "SYSTEMPATH\\Session\\Session.php:242", "function": "        CodeIgniter\\Session\\Session->startSession()", "index": "  5    "}, {"file": "SYSTEMPATH\\Config\\Services.php:710", "function": "        CodeIgniter\\Session\\Session->start()", "index": "  6    "}, {"file": "SYSTEMPATH\\Config\\BaseService.php:312", "function": "        CodeIgniter\\Config\\Services::session()", "index": "  7    "}, {"file": "SYSTEMPATH\\Config\\BaseService.php:251", "function": "        CodeIgniter\\Config\\BaseService::__callStatic()", "index": "  8    "}, {"file": "SYSTEMPATH\\Config\\Services.php:668", "function": "        CodeIgniter\\Config\\BaseService::getSharedInstance()", "index": "  9    "}, {"file": "SYSTEMPATH\\Config\\BaseService.php:321", "function": "        CodeIgniter\\Config\\Services::session()", "index": " 10    "}, {"file": "APPPATH\\Config\\Globals.php:32", "function": "        CodeIgniter\\Config\\BaseService::__callStatic()", "index": " 11    "}, {"file": "APPPATH\\Config\\Globals.php:245", "function": "        Config\\Globals::setGlobals()", "index": " 12    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:317", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Globals.php"], "function": "        include_once()", "index": " 13    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:296", "function": "        CodeIgniter\\Autoloader\\Autoloader->includeFile()", "index": " 14    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:272", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadInNamespace()", "index": " 15    "}, {"file": "APPPATH\\Config\\Routes.php:6", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadClass()", "index": " 16    "}, {"file": "SYSTEMPATH\\Router\\RouteCollection.php:340", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Routes.php"], "function": "        require()", "index": " 17    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:823", "function": "        CodeIgniter\\Router\\RouteCollection->loadRoutes()", "index": " 18    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:457", "function": "        CodeIgniter\\CodeIgniter->tryToRouteIt()", "index": " 19    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:355", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 20    "}, {"file": "SYSTEMPATH\\Boot.php:325", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 21    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 22    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 23    "}], "trace-file": "[internal function]", "qid": "5be40d4ab59676bc9ac01fade3cf71a7"}, {"hover": "", "class": "", "duration": "0.38 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `general_settings`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1644", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Config\\Globals.php:34", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH\\Config\\Globals.php:245", "function": "        Config\\Globals::setGlobals()", "index": "  3    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:317", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Globals.php"], "function": "        include_once()", "index": "  4    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:296", "function": "        CodeIgniter\\Autoloader\\Autoloader->includeFile()", "index": "  5    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:272", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadInNamespace()", "index": "  6    "}, {"file": "APPPATH\\Config\\Routes.php:6", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadClass()", "index": "  7    "}, {"file": "SYSTEMPATH\\Router\\RouteCollection.php:340", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Routes.php"], "function": "        require()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:823", "function": "        CodeIgniter\\Router\\RouteCollection->loadRoutes()", "index": "  9    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:457", "function": "        CodeIgniter\\CodeIgniter->tryToRouteIt()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:355", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 11    "}, {"file": "SYSTEMPATH\\Boot.php:325", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 12    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 13    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 14    "}], "trace-file": "APPPATH\\Config\\Globals.php:34", "qid": "2f95e484ee7df1f89586edda5be26e2d"}, {"hover": "", "class": "", "duration": "0.17 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payment_settings`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1644", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Config\\Globals.php:160", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH\\Config\\Globals.php:36", "function": "        Config\\Globals::getSettingsCache()", "index": "  3    "}, {"file": "APPPATH\\Config\\Globals.php:245", "function": "        Config\\Globals::setGlobals()", "index": "  4    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:317", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Globals.php"], "function": "        include_once()", "index": "  5    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:296", "function": "        CodeIgniter\\Autoloader\\Autoloader->includeFile()", "index": "  6    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:272", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadInNamespace()", "index": "  7    "}, {"file": "APPPATH\\Config\\Routes.php:6", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadClass()", "index": "  8    "}, {"file": "SYSTEMPATH\\Router\\RouteCollection.php:340", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Routes.php"], "function": "        require()", "index": "  9    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:823", "function": "        CodeIgniter\\Router\\RouteCollection->loadRoutes()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:457", "function": "        CodeIgniter\\CodeIgniter->tryToRouteIt()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:355", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 12    "}, {"file": "SYSTEMPATH\\Boot.php:325", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 14    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 15    "}], "trace-file": "APPPATH\\Config\\Globals.php:160", "qid": "82b471c3a9840e51667f88d0375e4bd3"}, {"hover": "", "class": "", "duration": "0.14 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `product_settings`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1644", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Config\\Globals.php:160", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH\\Config\\Globals.php:38", "function": "        Config\\Globals::getSettingsCache()", "index": "  3    "}, {"file": "APPPATH\\Config\\Globals.php:245", "function": "        Config\\Globals::setGlobals()", "index": "  4    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:317", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Globals.php"], "function": "        include_once()", "index": "  5    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:296", "function": "        CodeIgniter\\Autoloader\\Autoloader->includeFile()", "index": "  6    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:272", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadInNamespace()", "index": "  7    "}, {"file": "APPPATH\\Config\\Routes.php:6", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadClass()", "index": "  8    "}, {"file": "SYSTEMPATH\\Router\\RouteCollection.php:340", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Routes.php"], "function": "        require()", "index": "  9    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:823", "function": "        CodeIgniter\\Router\\RouteCollection->loadRoutes()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:457", "function": "        CodeIgniter\\CodeIgniter->tryToRouteIt()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:355", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 12    "}, {"file": "SYSTEMPATH\\Boot.php:325", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 14    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 15    "}], "trace-file": "APPPATH\\Config\\Globals.php:160", "qid": "6e91cb63fcfac8bcbdd513abd0447ad4"}, {"hover": "", "class": "", "duration": "0.11 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `storage_settings`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1644", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Config\\Globals.php:160", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH\\Config\\Globals.php:40", "function": "        Config\\Globals::getSettingsCache()", "index": "  3    "}, {"file": "APPPATH\\Config\\Globals.php:245", "function": "        Config\\Globals::setGlobals()", "index": "  4    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:317", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Globals.php"], "function": "        include_once()", "index": "  5    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:296", "function": "        CodeIgniter\\Autoloader\\Autoloader->includeFile()", "index": "  6    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:272", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadInNamespace()", "index": "  7    "}, {"file": "APPPATH\\Config\\Routes.php:6", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadClass()", "index": "  8    "}, {"file": "SYSTEMPATH\\Router\\RouteCollection.php:340", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Routes.php"], "function": "        require()", "index": "  9    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:823", "function": "        CodeIgniter\\Router\\RouteCollection->loadRoutes()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:457", "function": "        CodeIgniter\\CodeIgniter->tryToRouteIt()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:355", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 12    "}, {"file": "SYSTEMPATH\\Boot.php:325", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 14    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 15    "}], "trace-file": "APPPATH\\Config\\Globals.php:160", "qid": "08c8dbbe6a9bb5a656189c1c0fdd51b9"}, {"hover": "", "class": "", "duration": "0.16 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `routes`", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1644", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Config\\Globals.php:186", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH\\Config\\Globals.php:42", "function": "        Config\\Globals::getRoutes()", "index": "  3    "}, {"file": "APPPATH\\Config\\Globals.php:245", "function": "        Config\\Globals::setGlobals()", "index": "  4    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:317", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Globals.php"], "function": "        include_once()", "index": "  5    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:296", "function": "        CodeIgniter\\Autoloader\\Autoloader->includeFile()", "index": "  6    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:272", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadInNamespace()", "index": "  7    "}, {"file": "APPPATH\\Config\\Routes.php:6", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadClass()", "index": "  8    "}, {"file": "SYSTEMPATH\\Router\\RouteCollection.php:340", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Routes.php"], "function": "        require()", "index": "  9    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:823", "function": "        CodeIgniter\\Router\\RouteCollection->loadRoutes()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:457", "function": "        CodeIgniter\\CodeIgniter->tryToRouteIt()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:355", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 12    "}, {"file": "SYSTEMPATH\\Boot.php:325", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 14    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 15    "}], "trace-file": "APPPATH\\Config\\Globals.php:186", "qid": "72e60e0f54a064a2750ce3e6d599636e"}, {"hover": "", "class": "", "duration": "0.14 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `languages`\n<strong>WHERE</strong> `status` = 1\n<strong>ORDER</strong> <strong>BY</strong> `language_order`", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1644", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Config\\Globals.php:199", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH\\Config\\Globals.php:51", "function": "        Config\\Globals::getLanguages()", "index": "  3    "}, {"file": "APPPATH\\Config\\Globals.php:245", "function": "        Config\\Globals::setGlobals()", "index": "  4    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:317", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Globals.php"], "function": "        include_once()", "index": "  5    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:296", "function": "        CodeIgniter\\Autoloader\\Autoloader->includeFile()", "index": "  6    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:272", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadInNamespace()", "index": "  7    "}, {"file": "APPPATH\\Config\\Routes.php:6", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadClass()", "index": "  8    "}, {"file": "SYSTEMPATH\\Router\\RouteCollection.php:340", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Routes.php"], "function": "        require()", "index": "  9    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:823", "function": "        CodeIgniter\\Router\\RouteCollection->loadRoutes()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:457", "function": "        CodeIgniter\\CodeIgniter->tryToRouteIt()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:355", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 12    "}, {"file": "SYSTEMPATH\\Boot.php:325", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 14    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 15    "}], "trace-file": "APPPATH\\Config\\Globals.php:199", "qid": "73b28784f6f204403c25a46ba2a14ab4"}, {"hover": "", "class": "", "duration": "0.1 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `roles_permissions`", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1644", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Config\\Globals.php:238", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH\\Config\\Globals.php:53", "function": "        Config\\Globals::getRolesPermissions()", "index": "  3    "}, {"file": "APPPATH\\Config\\Globals.php:245", "function": "        Config\\Globals::setGlobals()", "index": "  4    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:317", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Globals.php"], "function": "        include_once()", "index": "  5    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:296", "function": "        CodeIgniter\\Autoloader\\Autoloader->includeFile()", "index": "  6    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:272", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadInNamespace()", "index": "  7    "}, {"file": "APPPATH\\Config\\Routes.php:6", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadClass()", "index": "  8    "}, {"file": "SYSTEMPATH\\Router\\RouteCollection.php:340", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Routes.php"], "function": "        require()", "index": "  9    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:823", "function": "        CodeIgniter\\Router\\RouteCollection->loadRoutes()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:457", "function": "        CodeIgniter\\CodeIgniter->tryToRouteIt()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:355", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 12    "}, {"file": "SYSTEMPATH\\Boot.php:325", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 14    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 15    "}], "trace-file": "APPPATH\\Config\\Globals.php:238", "qid": "5b0f2a7b055a018192d722530c5955d2"}, {"hover": "", "class": "", "duration": "0.06 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `languages`\n<strong>WHERE</strong> `id` = &#039;1&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1644", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Config\\Globals.php:212", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH\\Config\\Globals.php:59", "function": "        Config\\Globals::getLanguage()", "index": "  3    "}, {"file": "APPPATH\\Config\\Globals.php:245", "function": "        Config\\Globals::setGlobals()", "index": "  4    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:317", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Globals.php"], "function": "        include_once()", "index": "  5    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:296", "function": "        CodeIgniter\\Autoloader\\Autoloader->includeFile()", "index": "  6    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:272", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadInNamespace()", "index": "  7    "}, {"file": "APPPATH\\Config\\Routes.php:6", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadClass()", "index": "  8    "}, {"file": "SYSTEMPATH\\Router\\RouteCollection.php:340", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Routes.php"], "function": "        require()", "index": "  9    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:823", "function": "        CodeIgniter\\Router\\RouteCollection->loadRoutes()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:457", "function": "        CodeIgniter\\CodeIgniter->tryToRouteIt()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:355", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 12    "}, {"file": "SYSTEMPATH\\Boot.php:325", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 14    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 15    "}], "trace-file": "APPPATH\\Config\\Globals.php:212", "qid": "4f124e60cb9a34f26af83db533f6385b"}, {"hover": "", "class": "", "duration": "0.71 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `language_translations`\n<strong>WHERE</strong> `lang_id` = &#039;1&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1644", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Config\\Globals.php:225", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH\\Config\\Globals.php:133", "function": "        Config\\Globals::getLanguageTranslations()", "index": "  3    "}, {"file": "APPPATH\\Config\\Globals.php:78", "function": "        Config\\Globals::setActiveLanguage()", "index": "  4    "}, {"file": "APPPATH\\Config\\Globals.php:245", "function": "        Config\\Globals::setGlobals()", "index": "  5    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:317", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Globals.php"], "function": "        include_once()", "index": "  6    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:296", "function": "        CodeIgniter\\Autoloader\\Autoloader->includeFile()", "index": "  7    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:272", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadInNamespace()", "index": "  8    "}, {"file": "APPPATH\\Config\\Routes.php:6", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadClass()", "index": "  9    "}, {"file": "SYSTEMPATH\\Router\\RouteCollection.php:340", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Routes.php"], "function": "        require()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:823", "function": "        CodeIgniter\\Router\\RouteCollection->loadRoutes()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:457", "function": "        CodeIgniter\\CodeIgniter->tryToRouteIt()", "index": " 12    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:355", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:325", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 15    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 16    "}], "trace-file": "APPPATH\\Config\\Globals.php:225", "qid": "b39d78899c78cb4acc0479e33200dbb1"}, {"hover": "", "class": "", "duration": "0.18 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `lang_id` = &#039;1&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1644", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Config\\Globals.php:173", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH\\Config\\Globals.php:88", "function": "        Config\\Globals::getSettings()", "index": "  3    "}, {"file": "APPPATH\\Config\\Globals.php:245", "function": "        Config\\Globals::setGlobals()", "index": "  4    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:317", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Globals.php"], "function": "        include_once()", "index": "  5    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:296", "function": "        CodeIgniter\\Autoloader\\Autoloader->includeFile()", "index": "  6    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:272", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadInNamespace()", "index": "  7    "}, {"file": "APPPATH\\Config\\Routes.php:6", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadClass()", "index": "  8    "}, {"file": "SYSTEMPATH\\Router\\RouteCollection.php:340", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Routes.php"], "function": "        require()", "index": "  9    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:823", "function": "        CodeIgniter\\Router\\RouteCollection->loadRoutes()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:457", "function": "        CodeIgniter\\CodeIgniter->tryToRouteIt()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:355", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 12    "}, {"file": "SYSTEMPATH\\Boot.php:325", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 14    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 15    "}], "trace-file": "APPPATH\\Config\\Globals.php:173", "qid": "745e4e8ec271511ae74f1c5f9e8a737b"}, {"hover": "", "class": "", "duration": "0.16 ms", "sql": "<strong>SELECT</strong> `users`.*, `role_name`, `permissions`, `is_super_admin`, `is_admin`, `is_vendor`, `is_member`\n<strong>FROM</strong> `users`\n<strong>JOIN</strong> `roles_permissions` <strong>ON</strong> `roles_permissions`.`id` = `users`.`role_id`\n<strong>WHERE</strong> `users`.`id` = 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1644", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Config\\Globals.php:92", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH\\Config\\Globals.php:245", "function": "        Config\\Globals::setGlobals()", "index": "  3    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:317", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Globals.php"], "function": "        include_once()", "index": "  4    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:296", "function": "        CodeIgniter\\Autoloader\\Autoloader->includeFile()", "index": "  5    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:272", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadInNamespace()", "index": "  6    "}, {"file": "APPPATH\\Config\\Routes.php:6", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadClass()", "index": "  7    "}, {"file": "SYSTEMPATH\\Router\\RouteCollection.php:340", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Routes.php"], "function": "        require()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:823", "function": "        CodeIgniter\\Router\\RouteCollection->loadRoutes()", "index": "  9    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:457", "function": "        CodeIgniter\\CodeIgniter->tryToRouteIt()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:355", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 11    "}, {"file": "SYSTEMPATH\\Boot.php:325", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 12    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 13    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 14    "}], "trace-file": "APPPATH\\Config\\Globals.php:92", "qid": "e5fa8a3177354fdaea9df96e2d331a6f"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `currencies`\n<strong>ORDER</strong> <strong>BY</strong> `status` <strong>DESC</strong>, `id`", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1644", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Config\\Globals.php:99", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH\\Config\\Globals.php:245", "function": "        Config\\Globals::setGlobals()", "index": "  3    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:317", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Globals.php"], "function": "        include_once()", "index": "  4    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:296", "function": "        CodeIgniter\\Autoloader\\Autoloader->includeFile()", "index": "  5    "}, {"file": "SYSTEMPATH\\Autoloader\\Autoloader.php:272", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadInNamespace()", "index": "  6    "}, {"file": "APPPATH\\Config\\Routes.php:6", "function": "        CodeIgniter\\Autoloader\\Autoloader->loadClass()", "index": "  7    "}, {"file": "SYSTEMPATH\\Router\\RouteCollection.php:340", "args": ["D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Routes.php"], "function": "        require()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:823", "function": "        CodeIgniter\\Router\\RouteCollection->loadRoutes()", "index": "  9    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:457", "function": "        CodeIgniter\\CodeIgniter->tryToRouteIt()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:355", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 11    "}, {"file": "SYSTEMPATH\\Boot.php:325", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 12    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 13    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 14    "}], "trace-file": "APPPATH\\Config\\Globals.php:99", "qid": "86146d5893a13f790ffb1226f733c43a"}, {"hover": "", "class": "", "duration": "0.77 ms", "sql": "<strong>SELECT</strong> `categories`.*, `categories`.`parent_id` <strong>AS</strong> `join_parent_id`, (<strong>SELECT</strong> GROUP_CONCAT(lang_id, &#039;:::&#039;, name SEPARATOR &#039;|||&#039;) <strong>FROM</strong> categories_lang <strong>WHERE</strong> categories_lang.category_id = categories.id) <strong>AS</strong> name, (<strong>SELECT</strong> slug <strong>FROM</strong> categories <strong>WHERE</strong> id = join_parent_id) <strong>AS</strong> parent_slug, (<strong>SELECT</strong> id <strong>FROM</strong> categories <strong>AS</strong> sub_categories <strong>WHERE</strong> sub_categories.parent_id = categories.id <strong>LIMIT</strong> 1) <strong>AS</strong> has_subcategory\n<strong>FROM</strong> `categories`\n<strong>WHERE</strong> `categories`.`parent_id` = 1\n<strong>OR<PERSON><PERSON></strong> <strong>BY</strong> `categories`.`created_at`", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1644", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Models\\CategoryModel.php:147", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\CategoryController.php:248", "function": "        App\\Models\\CategoryModel->getSubCategoriesByParentId()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\CategoryController->loadCategories()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:509", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:355", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:325", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\CategoryModel.php:147", "qid": "9c92521b479ea710607510a9c8c5f326"}]}, "badgeValue": 15, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.086878, "duration": "0.020892"}, {"name": "Query", "component": "Database", "start": **********.109493, "duration": "0.000414", "query": "<strong>SELECT</strong> GET_LOCK(&#039;1c7803e751dae243e580addc4059ca7a&#039;, 300) <strong>AS</strong> ci_session_lock"}, {"name": "Query", "component": "Database", "start": **********.114466, "duration": "0.001480", "query": "<strong>SELECT</strong> `data`\n<strong>FROM</strong> `ci_sessions`\n<strong>WHERE</strong> `id` = &#039;mds_session:6vcro1fguv6ntunps7hsp0motgmkbgcg&#039;"}, {"name": "Query", "component": "Database", "start": **********.116579, "duration": "0.000380", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `general_settings`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": **********.117031, "duration": "0.000171", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payment_settings`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": **********.117251, "duration": "0.000136", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `product_settings`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": **********.117424, "duration": "0.000115", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `storage_settings`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": **********.117565, "duration": "0.000159", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `routes`"}, {"name": "Query", "component": "Database", "start": **********.117824, "duration": "0.000143", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `languages`\n<strong>WHERE</strong> `status` = 1\n<strong>ORDER</strong> <strong>BY</strong> `language_order`"}, {"name": "Query", "component": "Database", "start": **********.117995, "duration": "0.000101", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `roles_permissions`"}, {"name": "Query", "component": "Database", "start": **********.118134, "duration": "0.000063", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `languages`\n<strong>WHERE</strong> `id` = &#039;1&#039;"}, {"name": "Query", "component": "Database", "start": **********.118236, "duration": "0.000711", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `language_translations`\n<strong>WHERE</strong> `lang_id` = &#039;1&#039;"}, {"name": "Query", "component": "Database", "start": **********.12004, "duration": "0.000177", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `lang_id` = &#039;1&#039;"}, {"name": "Query", "component": "Database", "start": **********.120313, "duration": "0.000160", "query": "<strong>SELECT</strong> `users`.*, `role_name`, `permissions`, `is_super_admin`, `is_admin`, `is_vendor`, `is_member`\n<strong>FROM</strong> `users`\n<strong>JOIN</strong> `roles_permissions` <strong>ON</strong> `roles_permissions`.`id` = `users`.`role_id`\n<strong>WHERE</strong> `users`.`id` = 1"}, {"name": "Query", "component": "Database", "start": **********.12052, "duration": "0.000325", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `currencies`\n<strong>ORDER</strong> <strong>BY</strong> `status` <strong>DESC</strong>, `id`"}, {"name": "Query", "component": "Database", "start": **********.138476, "duration": "0.000774", "query": "<strong>SELECT</strong> `categories`.*, `categories`.`parent_id` <strong>AS</strong> `join_parent_id`, (<strong>SELECT</strong> GROUP_CONCAT(lang_id, &#039;:::&#039;, name SEPARATOR &#039;|||&#039;) <strong>FROM</strong> categories_lang <strong>WHERE</strong> categories_lang.category_id = categories.id) <strong>AS</strong> name, (<strong>SELECT</strong> slug <strong>FROM</strong> categories <strong>WHERE</strong> id = join_parent_id) <strong>AS</strong> parent_slug, (<strong>SELECT</strong> id <strong>FROM</strong> categories <strong>AS</strong> sub_categories <strong>WHERE</strong> sub_categories.parent_id = categories.id <strong>LIMIT</strong> 1) <strong>AS</strong> has_subcategory\n<strong>FROM</strong> `categories`\n<strong>WHERE</strong> `categories`.`parent_id` = 1\n<strong>OR<PERSON><PERSON></strong> <strong>BY</strong> `categories`.`created_at`"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "info", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\DatabaseHandler' driver."}, {"level": "info", "msg": "CSRF token verified."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 1, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: admin/category/_categories_print.php", "component": "Views", "start": **********.13932, "duration": 0.0003521442413330078}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 190 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\CSRF.php", "name": "CSRF.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\Helpers\\Array\\ArrayHelper.php", "name": "ArrayHelper.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\security_helper.php", "name": "security_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\text_helper.php", "name": "text_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Security\\Security.php", "name": "Security.php"}, {"path": "SYSTEMPATH\\Security\\SecurityInterface.php", "name": "SecurityInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\DatabaseHandler.php", "name": "DatabaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\Database\\MySQLiHandler.php", "name": "MySQLiHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Escaper\\Escaper.php", "name": "Escaper.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LogLevel.php", "name": "LogLevel.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Globals.php", "name": "Globals.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\RoutesStatic.php", "name": "RoutesStatic.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Security.php", "name": "Security.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\BaseAdminController.php", "name": "BaseAdminController.php"}, {"path": "APPPATH\\Controllers\\CategoryController.php", "name": "CategoryController.php"}, {"path": "APPPATH\\Helpers\\app_helper.php", "name": "app_helper.php"}, {"path": "APPPATH\\Helpers\\product_helper.php", "name": "product_helper.php"}, {"path": "APPPATH\\Models\\AuthModel.php", "name": "AuthModel.php"}, {"path": "APPPATH\\Models\\BaseModel.php", "name": "BaseModel.php"}, {"path": "APPPATH\\Models\\CategoryModel.php", "name": "CategoryModel.php"}, {"path": "APPPATH\\Models\\CommonModel.php", "name": "CommonModel.php"}, {"path": "APPPATH\\Models\\FieldModel.php", "name": "FieldModel.php"}, {"path": "APPPATH\\Models\\FileModel.php", "name": "FileModel.php"}, {"path": "APPPATH\\Models\\SettingsModel.php", "name": "SettingsModel.php"}, {"path": "APPPATH\\Models\\UploadModel.php", "name": "UploadModel.php"}, {"path": "APPPATH\\ThirdParty\\domain-parser\\autoload.php", "name": "autoload.php"}, {"path": "APPPATH\\ThirdParty\\domain-parser\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "APPPATH\\ThirdParty\\domain-parser\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "APPPATH\\ThirdParty\\domain-parser\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "APPPATH\\ThirdParty\\domain-parser\\layershifter\\tld-database\\resources\\database.php", "name": "database.php"}, {"path": "APPPATH\\ThirdParty\\domain-parser\\layershifter\\tld-database\\src\\Store.php", "name": "Store.php"}, {"path": "APPPATH\\ThirdParty\\domain-parser\\layershifter\\tld-extract\\src\\Extract.php", "name": "Extract.php"}, {"path": "APPPATH\\ThirdParty\\domain-parser\\layershifter\\tld-extract\\src\\IDN.php", "name": "IDN.php"}, {"path": "APPPATH\\ThirdParty\\domain-parser\\layershifter\\tld-extract\\src\\Result.php", "name": "Result.php"}, {"path": "APPPATH\\ThirdParty\\domain-parser\\layershifter\\tld-extract\\src\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "APPPATH\\ThirdParty\\domain-parser\\layershifter\\tld-extract\\src\\static.php", "name": "static.php"}, {"path": "APPPATH\\ThirdParty\\domain-parser\\layershifter\\tld-support\\src\\Helpers\\Arr.php", "name": "Arr.php"}, {"path": "APPPATH\\ThirdParty\\domain-parser\\layershifter\\tld-support\\src\\Helpers\\IP.php", "name": "IP.php"}, {"path": "APPPATH\\ThirdParty\\domain-parser\\layershifter\\tld-support\\src\\Helpers\\Str.php", "name": "Str.php"}, {"path": "APPPATH\\ThirdParty\\domain-parser\\symfony\\polyfill-mbstring\\bootstrap.php", "name": "bootstrap.php"}, {"path": "APPPATH\\ThirdParty\\intervention-image\\vendor\\autoload.php", "name": "autoload.php"}, {"path": "APPPATH\\ThirdParty\\intervention-image\\vendor\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "APPPATH\\ThirdParty\\intervention-image\\vendor\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "APPPATH\\ThirdParty\\intervention-image\\vendor\\guzzlehttp\\psr7\\src\\functions.php", "name": "functions.php"}, {"path": "APPPATH\\ThirdParty\\intervention-image\\vendor\\guzzlehttp\\psr7\\src\\functions_include.php", "name": "functions_include.php"}, {"path": "APPPATH\\ThirdParty\\intervention-image\\vendor\\ralouphie\\getallheaders\\src\\getallheaders.php", "name": "getallheaders.php"}, {"path": "APPPATH\\ThirdParty\\webp-convert\\vendor\\autoload.php", "name": "autoload.php"}, {"path": "APPPATH\\ThirdParty\\webp-convert\\vendor\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "APPPATH\\ThirdParty\\webp-convert\\vendor\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "APPPATH\\ThirdParty\\webp-convert\\vendor\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "APPPATH\\Views\\admin\\category\\_categories_print.php", "name": "_categories_print.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}]}, "badgeValue": 190, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\CategoryController", "method": "loadCategories", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\HomeController::index"}, {"method": "GET", "route": "affiliate/(.*)", "handler": "\\App\\Controllers\\HomeController::affiliate/$1"}, {"method": "GET", "route": "mds/load-products", "handler": "\\App\\Controllers\\AjaxController::loadProducts"}, {"method": "GET", "route": "cron/update-sitemap", "handler": "\\App\\Controllers\\HomeController::cronUpdateSitemap"}, {"method": "GET", "route": "unsubscribe", "handler": "\\App\\Controllers\\HomeController::unSubscribe"}, {"method": "GET", "route": "connect-with-facebook", "handler": "\\App\\Controllers\\AuthController::connectWithFacebook"}, {"method": "GET", "route": "facebook-callback", "handler": "\\App\\Controllers\\AuthController::facebookCallback"}, {"method": "GET", "route": "connect-with-google", "handler": "\\App\\Controllers\\AuthController::connectWithGoogle"}, {"method": "GET", "route": "connect-with-vk", "handler": "\\App\\Controllers\\AuthController::connectWithVk"}, {"method": "GET", "route": "flutterwave-payment-post", "handler": "\\App\\Controllers\\CartController::flutterwavePaymentPost"}, {"method": "GET", "route": "iyzico-payment-post", "handler": "\\App\\Controllers\\CartController::iyzicoPaymentPost"}, {"method": "GET", "route": "cart/paytabs-payment-post", "handler": "\\App\\Controllers\\CartController::paytabsPaymentPost"}, {"method": "GET", "route": "mercado-pago-payment-post", "handler": "\\App\\Controllers\\CartController::mercadoPagoPaymentPost"}, {"method": "GET", "route": "Ajax/updateChatGet", "handler": "\\App\\Controllers\\AjaxController::updateChatGet"}, {"method": "GET", "route": "admin", "handler": "\\App\\Controllers\\AdminController::index"}, {"method": "GET", "route": "admin/login", "handler": "\\App\\Controllers\\CommonController::adminLogin"}, {"method": "GET", "route": "confirm-account", "handler": "\\App\\Controllers\\AuthController::confirmAccount"}, {"method": "GET", "route": "admin/theme", "handler": "\\App\\Controllers\\AdminController::theme"}, {"method": "GET", "route": "admin/homepage-manager", "handler": "\\App\\Controllers\\AdminController::homepageManager"}, {"method": "GET", "route": "admin/edit-banner/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::editIndexBanner/$1"}, {"method": "GET", "route": "admin/slider", "handler": "\\App\\Controllers\\AdminController::slider"}, {"method": "GET", "route": "admin/edit-slider-item/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::editSliderItem/$1"}, {"method": "GET", "route": "admin/add-page", "handler": "\\App\\Controllers\\AdminController::addPage"}, {"method": "GET", "route": "admin/edit-page/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::editPage/$1"}, {"method": "GET", "route": "admin/pages", "handler": "\\App\\Controllers\\AdminController::pages"}, {"method": "GET", "route": "admin/orders", "handler": "\\App\\Controllers\\OrderAdminController::orders"}, {"method": "GET", "route": "admin/order-details/([0-9]+)", "handler": "\\App\\Controllers\\OrderAdminController::orderDetails/$1"}, {"method": "GET", "route": "admin/transactions", "handler": "\\App\\Controllers\\OrderAdminController::transactions"}, {"method": "GET", "route": "admin/digital-sales", "handler": "\\App\\Controllers\\OrderAdminController::digitalSales"}, {"method": "GET", "route": "admin/products", "handler": "\\App\\Controllers\\ProductController::products"}, {"method": "GET", "route": "admin/product-details/([0-9]+)", "handler": "\\App\\Controllers\\ProductController::productDetails/$1"}, {"method": "GET", "route": "admin/featured-products", "handler": "\\App\\Controllers\\ProductController::featuredProducts"}, {"method": "GET", "route": "admin/featured-products-pricing", "handler": "\\App\\Controllers\\ProductController::featuredProductsPricing"}, {"method": "GET", "route": "admin/membership-payments", "handler": "\\App\\Controllers\\AdminController::membershipPayments"}, {"method": "GET", "route": "admin/promotion-payments", "handler": "\\App\\Controllers\\AdminController::promotionPayments"}, {"method": "GET", "route": "admin/wallet-deposits", "handler": "\\App\\Controllers\\AdminController::walletDeposits"}, {"method": "GET", "route": "admin/bank-transfer-reports", "handler": "\\App\\Controllers\\AdminController::bankTransferReports"}, {"method": "GET", "route": "admin/quote-requests", "handler": "\\App\\Controllers\\ProductController::quoteRequests"}, {"method": "GET", "route": "admin/add-category", "handler": "\\App\\Controllers\\CategoryController::addCategory"}, {"method": "GET", "route": "admin/categories", "handler": "\\App\\Controllers\\CategoryController::categories"}, {"method": "GET", "route": "admin/edit-category/([0-9]+)", "handler": "\\App\\Controllers\\CategoryController::editCategory/$1"}, {"method": "GET", "route": "admin/bulk-category-upload", "handler": "\\App\\Controllers\\CategoryController::bulkCategoryUpload"}, {"method": "GET", "route": "admin/brands", "handler": "\\App\\Controllers\\CategoryController::brands"}, {"method": "GET", "route": "admin/add-brand", "handler": "\\App\\Controllers\\CategoryController::AddBrand"}, {"method": "GET", "route": "admin/edit-brand/([0-9]+)", "handler": "\\App\\Controllers\\CategoryController::editBrand/$1"}, {"method": "GET", "route": "admin/add-custom-field", "handler": "\\App\\Controllers\\CategoryController::addCustomField"}, {"method": "GET", "route": "admin/custom-fields", "handler": "\\App\\Controllers\\CategoryController::customFields"}, {"method": "GET", "route": "admin/edit-custom-field/([0-9]+)", "handler": "\\App\\Controllers\\CategoryController::editCustomField/$1"}, {"method": "GET", "route": "admin/custom-field-options/([0-9]+)", "handler": "\\App\\Controllers\\CategoryController::customFieldOptions/$1"}, {"method": "GET", "route": "admin/bulk-custom-field-upload", "handler": "\\App\\Controllers\\CategoryController::bulkCustomFieldUpload"}, {"method": "GET", "route": "admin/earnings", "handler": "\\App\\Controllers\\EarningsController::earnings"}, {"method": "GET", "route": "admin/payout-requests", "handler": "\\App\\Controllers\\EarningsController::payoutRequests"}, {"method": "GET", "route": "admin/payout-settings", "handler": "\\App\\Controllers\\EarningsController::payoutSettings"}, {"method": "GET", "route": "admin/add-payout", "handler": "\\App\\Controllers\\EarningsController::addPayout"}, {"method": "GET", "route": "admin/seller-balances", "handler": "\\App\\Controllers\\EarningsController::sellerBalances"}, {"method": "GET", "route": "admin/blog-add-post", "handler": "\\App\\Controllers\\BlogController::addPost"}, {"method": "GET", "route": "admin/blog-posts", "handler": "\\App\\Controllers\\BlogController::posts"}, {"method": "GET", "route": "admin/edit-blog-post/([0-9]+)", "handler": "\\App\\Controllers\\BlogController::editPost/$1"}, {"method": "GET", "route": "admin/blog-categories", "handler": "\\App\\Controllers\\BlogController::categories"}, {"method": "GET", "route": "admin/edit-blog-category/([0-9]+)", "handler": "\\App\\Controllers\\BlogController::editCategory/$1"}, {"method": "GET", "route": "admin/pending-product-comments", "handler": "\\App\\Controllers\\ProductController::pendingComments"}, {"method": "GET", "route": "admin/product-comments", "handler": "\\App\\Controllers\\ProductController::comments"}, {"method": "GET", "route": "admin/pending-blog-comments", "handler": "\\App\\Controllers\\BlogController::pendingComments"}, {"method": "GET", "route": "admin/blog-comments", "handler": "\\App\\Controllers\\BlogController::comments"}, {"method": "GET", "route": "admin/reviews", "handler": "\\App\\Controllers\\ProductController::reviews"}, {"method": "GET", "route": "admin/contact-messages", "handler": "\\App\\Controllers\\AdminController::contactMessages"}, {"method": "GET", "route": "admin/chat-messages", "handler": "\\App\\Controllers\\AdminController::chatMessages"}, {"method": "GET", "route": "admin/abuse-reports", "handler": "\\App\\Controllers\\AdminController::abuseReports"}, {"method": "GET", "route": "admin/ad-spaces", "handler": "\\App\\Controllers\\AdminController::adSpaces"}, {"method": "GET", "route": "admin/seo-tools", "handler": "\\App\\Controllers\\AdminController::seoTools"}, {"method": "GET", "route": "admin/location-settings", "handler": "\\App\\Controllers\\AdminController::locationSettings"}, {"method": "GET", "route": "admin/countries", "handler": "\\App\\Controllers\\AdminController::countries"}, {"method": "GET", "route": "admin/states", "handler": "\\App\\Controllers\\AdminController::states"}, {"method": "GET", "route": "admin/add-country", "handler": "\\App\\Controllers\\AdminController::addCountry"}, {"method": "GET", "route": "admin/edit-country/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::editCountry/$1"}, {"method": "GET", "route": "admin/add-state", "handler": "\\App\\Controllers\\AdminController::addState"}, {"method": "GET", "route": "admin/edit-state/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::editState/$1"}, {"method": "GET", "route": "admin/cities", "handler": "\\App\\Controllers\\AdminController::cities"}, {"method": "GET", "route": "admin/add-city", "handler": "\\App\\Controllers\\AdminController::addCity"}, {"method": "GET", "route": "admin/edit-city/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::editCity/$1"}, {"method": "GET", "route": "admin/users", "handler": "\\App\\Controllers\\MembershipController::users"}, {"method": "GET", "route": "admin/user-login-activities", "handler": "\\App\\Controllers\\MembershipController::userLoginActivities"}, {"method": "GET", "route": "admin/account-deletion-requests", "handler": "\\App\\Controllers\\MembershipController::accountDeletionRequests"}, {"method": "GET", "route": "admin/shop-opening-requests", "handler": "\\App\\Controllers\\MembershipController::shopOpeningRequests"}, {"method": "GET", "route": "admin/add-user", "handler": "\\App\\Controllers\\MembershipController::addUser"}, {"method": "GET", "route": "admin/edit-user/([0-9]+)", "handler": "\\App\\Controllers\\MembershipController::editUser/$1"}, {"method": "GET", "route": "admin/user-details/([0-9]+)", "handler": "\\App\\Controllers\\MembershipController::userDetails/$1"}, {"method": "GET", "route": "admin/membership-plans", "handler": "\\App\\Controllers\\MembershipController::membershipPlans"}, {"method": "GET", "route": "admin/edit-plan/([0-9]+)", "handler": "\\App\\Controllers\\MembershipController::editPlan/$1"}, {"method": "GET", "route": "admin/roles-permissions", "handler": "\\App\\Controllers\\MembershipController::rolesPermissions"}, {"method": "GET", "route": "admin/add-role", "handler": "\\App\\Controllers\\MembershipController::addRole"}, {"method": "GET", "route": "admin/edit-role/([0-9]+)", "handler": "\\App\\Controllers\\MembershipController::editRole/$1"}, {"method": "GET", "route": "admin/knowledge-base", "handler": "\\App\\Controllers\\SupportAdminController::knowledgeBase"}, {"method": "GET", "route": "admin/knowledge-base/add-content", "handler": "\\App\\Controllers\\SupportAdminController::addContent"}, {"method": "GET", "route": "admin/knowledge-base/edit-content/([0-9]+)", "handler": "\\App\\Controllers\\SupportAdminController::editContent/$1"}, {"method": "GET", "route": "admin/knowledge-base-categories", "handler": "\\App\\Controllers\\SupportAdminController::categories"}, {"method": "GET", "route": "admin/knowledge-base/add-category", "handler": "\\App\\Controllers\\SupportAdminController::addCategory"}, {"method": "GET", "route": "admin/knowledge-base/edit-category/([0-9]+)", "handler": "\\App\\Controllers\\SupportAdminController::editCategory/$1"}, {"method": "GET", "route": "admin/support-tickets", "handler": "\\App\\Controllers\\SupportAdminController::supportTickets"}, {"method": "GET", "route": "admin/support-ticket/([0-9]+)", "handler": "\\App\\Controllers\\SupportAdminController::supportTicket/$1"}, {"method": "GET", "route": "admin/refund-requests", "handler": "\\App\\Controllers\\OrderAdminController::refundRequests"}, {"method": "GET", "route": "admin/refund-requests/([0-9]+)", "handler": "\\App\\Controllers\\OrderAdminController::refund/$1"}, {"method": "GET", "route": "admin/language-settings", "handler": "\\App\\Controllers\\LanguageController::languageSettings"}, {"method": "GET", "route": "admin/edit-language/([0-9]+)", "handler": "\\App\\Controllers\\LanguageController::editLanguage/$1"}, {"method": "GET", "route": "admin/edit-translations/([0-9]+)", "handler": "\\App\\Controllers\\LanguageController::editTranslations/$1"}, {"method": "GET", "route": "admin/search-phrases", "handler": "\\App\\Controllers\\LanguageController::searchPhrases"}, {"method": "GET", "route": "admin/newsletter", "handler": "\\App\\Controllers\\AdminController::newsletter"}, {"method": "GET", "route": "admin/affiliate-program", "handler": "\\App\\Controllers\\AdminController::affiliateProgram"}, {"method": "GET", "route": "admin/currency-settings", "handler": "\\App\\Controllers\\AdminController::currencySettings"}, {"method": "GET", "route": "admin/add-currency", "handler": "\\App\\Controllers\\AdminController::addCurrency"}, {"method": "GET", "route": "admin/edit-currency/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::editCurrency/$1"}, {"method": "GET", "route": "admin/general-settings", "handler": "\\App\\Controllers\\AdminController::generalSettings"}, {"method": "GET", "route": "admin/email-settings", "handler": "\\App\\Controllers\\AdminController::emailSettings"}, {"method": "GET", "route": "admin/social-login", "handler": "\\App\\Controllers\\AdminController::socialLoginSettings"}, {"method": "GET", "route": "admin/visual-settings", "handler": "\\App\\Controllers\\AdminController::visualSettings"}, {"method": "GET", "route": "admin/preferences", "handler": "\\App\\Controllers\\AdminController::preferences"}, {"method": "GET", "route": "admin/product-settings", "handler": "\\App\\Controllers\\AdminController::productSettings"}, {"method": "GET", "route": "admin/font-settings", "handler": "\\App\\Controllers\\AdminController::fontSettings"}, {"method": "GET", "route": "admin/edit-font/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::editFont/$1"}, {"method": "GET", "route": "admin/route-settings", "handler": "\\App\\Controllers\\AdminController::routeSettings"}, {"method": "GET", "route": "admin/cache-system", "handler": "\\App\\Controllers\\AdminController::cacheSystem"}, {"method": "GET", "route": "admin/storage", "handler": "\\App\\Controllers\\AdminController::storage"}, {"method": "GET", "route": "admin/payment-settings", "handler": "\\App\\Controllers\\AdminController::paymentSettings"}, {"method": "GET", "route": "admin/add-tax", "handler": "\\App\\Controllers\\AdminController::addTax"}, {"method": "GET", "route": "admin/edit-tax/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::editTax/$1"}, {"method": "GET", "route": "register", "handler": "\\App\\Controllers\\AuthController::register"}, {"method": "GET", "route": "register-success", "handler": "\\App\\Controllers\\AuthController::registerSuccess"}, {"method": "GET", "route": "forgot-password", "handler": "\\App\\Controllers\\AuthController::forgotPassword"}, {"method": "GET", "route": "reset-password", "handler": "\\App\\Controllers\\AuthController::resetPassword"}, {"method": "GET", "route": "s/(.*)", "handler": "\\App\\Controllers\\ProfileController::profile/$1"}, {"method": "GET", "route": "wishlist", "handler": "\\App\\Controllers\\HomeController::wishlist"}, {"method": "GET", "route": "followers/(.*)", "handler": "\\App\\Controllers\\ProfileController::followers/$1"}, {"method": "GET", "route": "following/(.*)", "handler": "\\App\\Controllers\\ProfileController::following/$1"}, {"method": "GET", "route": "reviews/(.*)", "handler": "\\App\\Controllers\\ProfileController::reviews/$1"}, {"method": "GET", "route": "my-reviews/(.*)", "handler": "\\App\\Controllers\\ProfileController::myReviews/$1"}, {"method": "GET", "route": "shop-policies/(.*)", "handler": "\\App\\Controllers\\ProfileController::shopPolicies/$1"}, {"method": "GET", "route": "my-coupons", "handler": "\\App\\Controllers\\ProfileController::myCoupons"}, {"method": "GET", "route": "settings", "handler": "\\App\\Controllers\\ProfileController::editProfile"}, {"method": "GET", "route": "settings/edit-profile", "handler": "\\App\\Controllers\\ProfileController::editProfile"}, {"method": "GET", "route": "settings/location", "handler": "\\App\\Controllers\\ProfileController::location"}, {"method": "GET", "route": "settings/shipping-address", "handler": "\\App\\Controllers\\ProfileController::shippingAddress"}, {"method": "GET", "route": "settings/affiliate-links", "handler": "\\App\\Controllers\\ProfileController::affiliateLinks"}, {"method": "GET", "route": "settings/social-media", "handler": "\\App\\Controllers\\ProfileController::socialMedia"}, {"method": "GET", "route": "settings/change-password", "handler": "\\App\\Controllers\\ProfileController::changePassword"}, {"method": "GET", "route": "settings/delete-account", "handler": "\\App\\Controllers\\ProfileController::deleteAccount"}, {"method": "GET", "route": "wallet", "handler": "\\App\\Controllers\\ProfileController::wallet"}, {"method": "GET", "route": "affiliate-program", "handler": "\\App\\Controllers\\HomeController::affiliateProgram"}, {"method": "GET", "route": "select-membership-plan", "handler": "\\App\\Controllers\\HomeController::selectMembershipPlan"}, {"method": "GET", "route": "start-selling", "handler": "\\App\\Controllers\\HomeController::startSelling"}, {"method": "GET", "route": "search", "handler": "\\App\\Controllers\\HomeController::search"}, {"method": "GET", "route": "products", "handler": "\\App\\Controllers\\HomeController::products"}, {"method": "GET", "route": "downloads", "handler": "\\App\\Controllers\\OrderController::downloads"}, {"method": "GET", "route": "blog", "handler": "\\App\\Controllers\\HomeController::blog"}, {"method": "GET", "route": "blog/tag/(.*)", "handler": "\\App\\Controllers\\HomeController::tag/$1"}, {"method": "GET", "route": "blog/(.*)/(.*)", "handler": "\\App\\Controllers\\HomeController::post/$1/$2"}, {"method": "GET", "route": "blog/(.*)", "handler": "\\App\\Controllers\\HomeController::blogCategory/$1"}, {"method": "GET", "route": "shops", "handler": "\\App\\Controllers\\HomeController::shops"}, {"method": "GET", "route": "contact", "handler": "\\App\\Controllers\\HomeController::contact"}, {"method": "GET", "route": "messages", "handler": "\\App\\Controllers\\HomeController::chat"}, {"method": "GET", "route": "rss-feeds", "handler": "\\App\\Controllers\\RssController::rssFeeds"}, {"method": "GET", "route": "rss/latest-products", "handler": "\\App\\Controllers\\RssController::latestProducts"}, {"method": "GET", "route": "rss/featured-products", "handler": "\\App\\Controllers\\RssController::featuredProducts"}, {"method": "GET", "route": "rss/category/(.*)", "handler": "\\App\\Controllers\\RssController::rssByCategory/$1"}, {"method": "GET", "route": "rss/seller/(.*)", "handler": "\\App\\Controllers\\RssController::rssBySeller/$1"}, {"method": "GET", "route": "cart", "handler": "\\App\\Controllers\\CartController::cart"}, {"method": "GET", "route": "cart/shipping", "handler": "\\App\\Controllers\\CartController::shipping"}, {"method": "GET", "route": "cart/payment-method", "handler": "\\App\\Controllers\\CartController::paymentMethod"}, {"method": "GET", "route": "cart/payment", "handler": "\\App\\Controllers\\CartController::payment"}, {"method": "GET", "route": "orders", "handler": "\\App\\Controllers\\OrderController::orders"}, {"method": "GET", "route": "order-details/([0-9]+)", "handler": "\\App\\Controllers\\OrderController::order/$1"}, {"method": "GET", "route": "order-completed/([0-9]+)", "handler": "\\App\\Controllers\\CartController::orderCompleted/$1"}, {"method": "GET", "route": "service-payment-completed", "handler": "\\App\\Controllers\\CartController::servicePaymentCompleted"}, {"method": "GET", "route": "invoice/([0-9]+)", "handler": "\\App\\Controllers\\HomeController::invoice/$1"}, {"method": "GET", "route": "invoice-promotion/([0-9]+)", "handler": "\\App\\Controllers\\HomeController::invoicePromotion/$1"}, {"method": "GET", "route": "invoice-membership/([0-9]+)", "handler": "\\App\\Controllers\\HomeController::invoiceMembership/$1"}, {"method": "GET", "route": "invoice-wallet-deposit/([0-9]+)", "handler": "\\App\\Controllers\\HomeController::invoiceWalletDeposit/$1"}, {"method": "GET", "route": "invoice-expense/([0-9]+)", "handler": "\\App\\Controllers\\HomeController::invoiceExpense/$1"}, {"method": "GET", "route": "refund-requests", "handler": "\\App\\Controllers\\OrderController::refundRequests"}, {"method": "GET", "route": "refund-requests/([0-9]+)", "handler": "\\App\\Controllers\\OrderController::refund/$1"}, {"method": "GET", "route": "quote-requests", "handler": "\\App\\Controllers\\OrderController::quoteRequests"}, {"method": "GET", "route": "terms-conditions", "handler": "\\App\\Controllers\\HomeController::termsConditions"}, {"method": "GET", "route": "dashboard", "handler": "\\App\\Controllers\\DashboardController::index"}, {"method": "GET", "route": "dashboard/add-product", "handler": "\\App\\Controllers\\DashboardController::addProduct"}, {"method": "GET", "route": "dashboard/product/product-details/([0-9]+)", "handler": "\\App\\Controllers\\DashboardController::editProductDetails/$1"}, {"method": "GET", "route": "dashboard/edit-product/([0-9]+)", "handler": "\\App\\Controllers\\DashboardController::editProduct/$1"}, {"method": "GET", "route": "dashboard/products", "handler": "\\App\\Controllers\\DashboardController::products"}, {"method": "GET", "route": "dashboard/bulk-product-upload", "handler": "\\App\\Controllers\\DashboardController::bulkProductUpload"}, {"method": "GET", "route": "dashboard/sales", "handler": "\\App\\Controllers\\DashboardController::sales"}, {"method": "GET", "route": "dashboard/sale/([0-9]+)", "handler": "\\App\\Controllers\\DashboardController::sale/$1"}, {"method": "GET", "route": "dashboard/quote-requests", "handler": "\\App\\Controllers\\DashboardController::quoteRequests"}, {"method": "GET", "route": "dashboard/cash-on-delivery", "handler": "\\App\\Controllers\\DashboardController::cashOnDelivery"}, {"method": "GET", "route": "dashboard/payments", "handler": "\\App\\Controllers\\DashboardController::payments"}, {"method": "GET", "route": "dashboard/comments", "handler": "\\App\\Controllers\\DashboardController::comments"}, {"method": "GET", "route": "dashboard/reviews", "handler": "\\App\\Controllers\\DashboardController::reviews"}, {"method": "GET", "route": "dashboard/shop-settings", "handler": "\\App\\Controllers\\DashboardController::shopSettings"}, {"method": "GET", "route": "dashboard/shop-policies", "handler": "\\App\\Controllers\\DashboardController::shopPolicies"}, {"method": "GET", "route": "dashboard/shipping-settings", "handler": "\\App\\Controllers\\DashboardController::shippingSettings"}, {"method": "GET", "route": "dashboard/add-shipping-zone", "handler": "\\App\\Controllers\\DashboardController::addShippingZone"}, {"method": "GET", "route": "dashboard/edit-shipping-zone/([0-9]+)", "handler": "\\App\\Controllers\\DashboardController::editShippingZone/$1"}, {"method": "GET", "route": "dashboard/coupons", "handler": "\\App\\Controllers\\DashboardController::coupons"}, {"method": "GET", "route": "dashboard/coupon-products/([0-9]+)", "handler": "\\App\\Controllers\\DashboardController::couponProducts/$1"}, {"method": "GET", "route": "dashboard/add-coupon", "handler": "\\App\\Controllers\\DashboardController::addCoupon"}, {"method": "GET", "route": "dashboard/edit-coupon/([0-9]+)", "handler": "\\App\\Controllers\\DashboardController::editCoupon/$1"}, {"method": "GET", "route": "dashboard/refund-requests", "handler": "\\App\\Controllers\\DashboardController::refundRequests"}, {"method": "GET", "route": "dashboard/refund-requests/([0-9]+)", "handler": "\\App\\Controllers\\DashboardController::refund/$1"}, {"method": "GET", "route": "dashboard/affiliate-program", "handler": "\\App\\Controllers\\DashboardController::affiliateProgram"}, {"method": "GET", "route": "help-center", "handler": "\\App\\Controllers\\SupportController::helpCenter"}, {"method": "GET", "route": "help-center/tickets", "handler": "\\App\\Controllers\\SupportController::tickets"}, {"method": "GET", "route": "help-center/submit-request", "handler": "\\App\\Controllers\\SupportController::submitRequest"}, {"method": "GET", "route": "help-center/ticket/([0-9]+)", "handler": "\\App\\Controllers\\SupportController::ticket/$1"}, {"method": "GET", "route": "help-center/search", "handler": "\\App\\Controllers\\SupportController::search"}, {"method": "GET", "route": "help-center/(.*)/(.*)", "handler": "\\App\\Controllers\\SupportController::article/$1/$2"}, {"method": "GET", "route": "help-center/(.*)", "handler": "\\App\\Controllers\\SupportController::category/$1"}, {"method": "GET", "route": "(.*)/(.*)", "handler": "\\App\\Controllers\\HomeController::subCategory/$1/$2"}, {"method": "GET", "route": "(.*)", "handler": "\\App\\Controllers\\HomeController::any/$1"}, {"method": "POST", "route": "login-post", "handler": "\\App\\Controllers\\AuthController::loginPost"}, {"method": "POST", "route": "logout", "handler": "\\App\\Controllers\\CommonController::logout"}, {"method": "POST", "route": "contact-post", "handler": "\\App\\Controllers\\HomeController::contactPost"}, {"method": "POST", "route": "set-selected-currency-post", "handler": "\\App\\Controllers\\HomeController::setSelectedCurrency"}, {"method": "POST", "route": "add-review-post", "handler": "\\App\\Controllers\\HomeController::addReviewPost"}, {"method": "POST", "route": "submit-request-post", "handler": "\\App\\Controllers\\SupportController::submitRequestPost"}, {"method": "POST", "route": "reply-ticket-post", "handler": "\\App\\Controllers\\SupportController::replyTicketPost"}, {"method": "POST", "route": "close-ticket-post", "handler": "\\App\\Controllers\\SupportController::closeTicketPost"}, {"method": "POST", "route": "download-attachment-post", "handler": "\\App\\Controllers\\SupportController::downloadAttachmentPost"}, {"method": "POST", "route": "forgot-password-post", "handler": "\\App\\Controllers\\AuthController::forgotPasswordPost"}, {"method": "POST", "route": "reset-password-post", "handler": "\\App\\Controllers\\AuthController::resetPasswordPost"}, {"method": "POST", "route": "register-post", "handler": "\\App\\Controllers\\AuthController::registerPost"}, {"method": "POST", "route": "submit-quote-post", "handler": "\\App\\Controllers\\DashboardController::submitQuotePost"}, {"method": "POST", "route": "request-quote-post", "handler": "\\App\\Controllers\\OrderController::requestQuotePost"}, {"method": "POST", "route": "accept-quote-post", "handler": "\\App\\Controllers\\OrderController::acceptQuote"}, {"method": "POST", "route": "reject-quote-post", "handler": "\\App\\Controllers\\OrderController::rejectQuote"}, {"method": "POST", "route": "cart/add-to-cart", "handler": "\\App\\Controllers\\CartController::addToCart"}, {"method": "POST", "route": "add-to-cart-quote", "handler": "\\App\\Controllers\\CartController::addToCartQuote"}, {"method": "POST", "route": "update-cart-product-quantity", "handler": "\\App\\Controllers\\CartController::updateCartProductQuantity"}, {"method": "POST", "route": "payment-method-post", "handler": "\\App\\Controllers\\CartController::paymentMethodPost"}, {"method": "POST", "route": "shipping-post", "handler": "\\App\\Controllers\\CartController::shippingPost"}, {"method": "POST", "route": "bank-transfer-payment-post", "handler": "\\App\\Controllers\\CartController::bankTransferPaymentPost"}, {"method": "POST", "route": "cash-on-delivery-payment-post", "handler": "\\App\\Controllers\\CartController::cashOnDeliveryPaymentPost"}, {"method": "POST", "route": "paypal-payment-post", "handler": "\\App\\Controllers\\CartController::paypalPaymentPost"}, {"method": "POST", "route": "paystack-payment-post", "handler": "\\App\\Controllers\\CartController::paystackPaymentPost"}, {"method": "POST", "route": "razorpay-payment-post", "handler": "\\App\\Controllers\\CartController::razorpayPaymentPost"}, {"method": "POST", "route": "stripe-payment-post", "handler": "\\App\\Controllers\\CartController::stripePaymentPost"}, {"method": "POST", "route": "midtrans-payment-post", "handler": "\\App\\Controllers\\CartController::midtransPaymentPost"}, {"method": "POST", "route": "cart/coupon-code-post", "handler": "\\App\\Controllers\\CartController::couponCodePost"}, {"method": "POST", "route": "submit-refund-request", "handler": "\\App\\Controllers\\OrderController::submitRefundRequest"}, {"method": "POST", "route": "add-refund-message", "handler": "\\App\\Controllers\\OrderController::addRefundMessage"}, {"method": "POST", "route": "wallet/new-payout-request-post", "handler": "\\App\\Controllers\\ProfileController::newPayoutRequestPost"}, {"method": "POST", "route": "wallet/set-payout-account-post", "handler": "\\App\\Controllers\\ProfileController::setPayoutAccountPost"}, {"method": "POST", "route": "send-message-post", "handler": "\\App\\Controllers\\HomeController::sendMessagePost"}, {"method": "POST", "route": "upload-audio-post", "handler": "\\App\\Controllers\\FileController::uploadAudio"}, {"method": "POST", "route": "load-audio-preview-post", "handler": "\\App\\Controllers\\FileController::loadAudioPreview"}, {"method": "POST", "route": "upload-digital-file-post", "handler": "\\App\\Controllers\\FileController::uploadDigitalFile"}, {"method": "POST", "route": "download-digital-file-post", "handler": "\\App\\Controllers\\FileController::downloadDigitalFile"}, {"method": "POST", "route": "upload-file-manager-images-post", "handler": "\\App\\Controllers\\FileController::uploadFileManagerImagePost"}, {"method": "POST", "route": "upload-image-post", "handler": "\\App\\Controllers\\FileController::uploadImage"}, {"method": "POST", "route": "get-uploaded-image-post", "handler": "\\App\\Controllers\\FileController::getUploadedImage"}, {"method": "POST", "route": "upload-image-session-post", "handler": "\\App\\Controllers\\FileController::uploadImageSession"}, {"method": "POST", "route": "get-sess-uploaded-image-post", "handler": "\\App\\Controllers\\FileController::getSessUploadedImage"}, {"method": "POST", "route": "upload-video-post", "handler": "\\App\\Controllers\\FileController::uploadVideo"}, {"method": "POST", "route": "load-video-preview-post", "handler": "\\App\\Controllers\\FileController::loadVideoPreview"}, {"method": "POST", "route": "download-purchased-digital-file-post", "handler": "\\App\\Controllers\\FileController::downloadPurchasedDigitalFile"}, {"method": "POST", "route": "download-free-digital-file-post", "handler": "\\App\\Controllers\\FileController::downloadFreeDigitalFile"}, {"method": "POST", "route": "add-product-post", "handler": "\\App\\Controllers\\DashboardController::addProductPost"}, {"method": "POST", "route": "edit-product-post", "handler": "\\App\\Controllers\\DashboardController::editProductPost"}, {"method": "POST", "route": "edit-product-details-post", "handler": "\\App\\Controllers\\DashboardController::editProductDetailsPost"}, {"method": "POST", "route": "start-selling-post", "handler": "\\App\\Controllers\\HomeController::startSellingPost"}, {"method": "POST", "route": "add-remove-wishlist-post", "handler": "\\App\\Controllers\\AjaxController::addRemoveWishlist"}, {"method": "POST", "route": "add-variation-post", "handler": "\\App\\Controllers\\VariationController::addVariationPost"}, {"method": "POST", "route": "edit-variation", "handler": "\\App\\Controllers\\VariationController::editVariation"}, {"method": "POST", "route": "edit-variation-post", "handler": "\\App\\Controllers\\VariationController::editVariationPost"}, {"method": "POST", "route": "delete-variation-post", "handler": "\\App\\Controllers\\VariationController::deleteVariationPost"}, {"method": "POST", "route": "add-variation-option", "handler": "\\App\\Controllers\\VariationController::addVariationOption"}, {"method": "POST", "route": "add-variation-option-post", "handler": "\\App\\Controllers\\VariationController::addVariationOptionPost"}, {"method": "POST", "route": "view-variation-options", "handler": "\\App\\Controllers\\VariationController::viewVariationOptions"}, {"method": "POST", "route": "edit-variation-option", "handler": "\\App\\Controllers\\VariationController::editVariationOption"}, {"method": "POST", "route": "edit-variation-option-post", "handler": "\\App\\Controllers\\VariationController::editVariationOptionPost"}, {"method": "POST", "route": "delete-variation-option-post", "handler": "\\App\\Controllers\\VariationController::deleteVariationOptionPost"}, {"method": "POST", "route": "select-variation-post", "handler": "\\App\\Controllers\\VariationController::selectVariationPost"}, {"method": "POST", "route": "upload-variation-image-session", "handler": "\\App\\Controllers\\VariationController::uploadVariationImageSession"}, {"method": "POST", "route": "get-uploaded-variation-image-session", "handler": "\\App\\Controllers\\VariationController::getSessUploadedVariationImage"}, {"method": "POST", "route": "delete-variation-image-session-post", "handler": "\\App\\Controllers\\VariationController::deleteVariationImageSessionPost"}, {"method": "POST", "route": "set-variation-image-main-session", "handler": "\\App\\Controllers\\VariationController::setVariationImageMainSession"}, {"method": "POST", "route": "set-variation-image-main", "handler": "\\App\\Controllers\\VariationController::setVariationImageMain"}, {"method": "POST", "route": "upload-variation-image", "handler": "\\App\\Controllers\\VariationController::uploadVariationImage"}, {"method": "POST", "route": "get-uploaded-variation-image", "handler": "\\App\\Controllers\\VariationController::getUploadedVariationImage"}, {"method": "POST", "route": "delete-variation-image-post", "handler": "\\App\\Controllers\\VariationController::deleteVariationImagePost"}, {"method": "POST", "route": "select-variation-option-post", "handler": "\\App\\Controllers\\AjaxController::selectProductVariationOption"}, {"method": "POST", "route": "get-sub-variation-options", "handler": "\\App\\Controllers\\AjaxController::getSubVariationOptions"}, {"method": "POST", "route": "social-media-post", "handler": "\\App\\Controllers\\ProfileController::socialMediaPost"}, {"method": "POST", "route": "edit-profile-post", "handler": "\\App\\Controllers\\ProfileController::editProfilePost"}, {"method": "POST", "route": "cover-image-post", "handler": "\\App\\Controllers\\ProfileController::coverImagePost"}, {"method": "POST", "route": "follow-unfollow-user-post", "handler": "\\App\\Controllers\\ProfileController::followUnfollowUser"}, {"method": "POST", "route": "change-password-post", "handler": "\\App\\Controllers\\ProfileController::changePasswordPost"}, {"method": "POST", "route": "delete-account-post", "handler": "\\App\\Controllers\\ProfileController::deleteAccountPost"}, {"method": "POST", "route": "add-shipping-address-post", "handler": "\\App\\Controllers\\ProfileController::addShippingAddressPost"}, {"method": "POST", "route": "edit-shipping-address-post", "handler": "\\App\\Controllers\\ProfileController::editShippingAddressPost"}, {"method": "POST", "route": "edit-location-post", "handler": "\\App\\Controllers\\ProfileController::locationPost"}, {"method": "POST", "route": "shop-settings-post", "handler": "\\App\\Controllers\\DashboardController::shopSettingsPost"}, {"method": "POST", "route": "add-shipping-zone-post", "handler": "\\App\\Controllers\\DashboardController::addShippingZonePost"}, {"method": "POST", "route": "edit-shipping-zone-post", "handler": "\\App\\Controllers\\DashboardController::editShippingZonePost"}, {"method": "POST", "route": "add-shipping-class-post", "handler": "\\App\\Controllers\\DashboardController::addShippingClassPost"}, {"method": "POST", "route": "edit-shipping-class-post", "handler": "\\App\\Controllers\\DashboardController::editShippingClassPost"}, {"method": "POST", "route": "add-shipping-delivery-time-post", "handler": "\\App\\Controllers\\DashboardController::addShippingDeliveryTimePost"}, {"method": "POST", "route": "edit-shipping-delivery-time-post", "handler": "\\App\\Controllers\\DashboardController::editShippingDeliveryTimePost"}, {"method": "POST", "route": "update-order-product-status-post", "handler": "\\App\\Controllers\\DashboardController::updateOrderProductStatusPost"}, {"method": "POST", "route": "promote-product-post", "handler": "\\App\\Controllers\\DashboardController::promoteProductPost"}, {"method": "POST", "route": "add-coupon-post", "handler": "\\App\\Controllers\\DashboardController::addCouponPost"}, {"method": "POST", "route": "edit-coupon-post", "handler": "\\App\\Controllers\\DashboardController::editCouponPost"}, {"method": "POST", "route": "Admin/editSliderSettingsPost", "handler": "\\App\\Controllers\\AdminController::editSliderSettingsPost"}, {"method": "POST", "route": "Admin/deleteAbuseReportPost", "handler": "\\App\\Controllers\\AdminController::deleteAbuseReportPost"}, {"method": "POST", "route": "Admin/adSpacesPost", "handler": "\\App\\Controllers\\AdminController::adSpacesPost"}, {"method": "POST", "route": "Admin/googleAdsenseCodePost", "handler": "\\App\\Controllers\\AdminController::googleAdsenseCodePost"}, {"method": "POST", "route": "Admin/cacheSystemPost", "handler": "\\App\\Controllers\\AdminController::cacheSystemPost"}, {"method": "POST", "route": "Admin/deleteContactMessagePost", "handler": "\\App\\Controllers\\AdminController::deleteContactMessagePost"}, {"method": "POST", "route": "Admin/themePost", "handler": "\\App\\Controllers\\AdminController::themePost"}, {"method": "POST", "route": "Admin/generateSitemapPost", "handler": "\\App\\Controllers\\AdminController::generateSitemapPost"}, {"method": "POST", "route": "Admin/downloadSitemapPost", "handler": "\\App\\Controllers\\AdminController::downloadSitemapPost"}, {"method": "POST", "route": "Admin/deleteSitemapPost", "handler": "\\App\\Controllers\\AdminController::deleteSitemapPost"}, {"method": "POST", "route": "Admin/seoToolsPost", "handler": "\\App\\Controllers\\AdminController::seoToolsPost"}, {"method": "POST", "route": "Admin/storagePost", "handler": "\\App\\Controllers\\AdminController::storagePost"}, {"method": "POST", "route": "Admin/awsS3Post", "handler": "\\App\\Controllers\\AdminController::awsS3Post"}, {"method": "POST", "route": "Admin/addCurrencyPost", "handler": "\\App\\Controllers\\AdminController::addCurrencyPost"}, {"method": "POST", "route": "Admin/currencySettingsPost", "handler": "\\App\\Controllers\\AdminController::currencySettingsPost"}, {"method": "POST", "route": "Admin/currencyConverterPost", "handler": "\\App\\Controllers\\AdminController::currencyConverterPost"}, {"method": "POST", "route": "Admin/updateCurrencyRates", "handler": "\\App\\Controllers\\AdminController::updateCurrencyRates"}, {"method": "POST", "route": "Admin/deleteCurrencyPost", "handler": "\\App\\Controllers\\AdminController::deleteCurrencyPost"}, {"method": "POST", "route": "Admin/editCurrencyPost", "handler": "\\App\\Controllers\\AdminController::editCurrencyPost"}, {"method": "POST", "route": "Admin/editFontPost", "handler": "\\App\\Controllers\\AdminController::editFontPost"}, {"method": "POST", "route": "Admin/setSiteFontPost", "handler": "\\App\\Controllers\\AdminController::setSiteFontPost"}, {"method": "POST", "route": "Admin/addFontPost", "handler": "\\App\\Controllers\\AdminController::addFontPost"}, {"method": "POST", "route": "Admin/deleteFontPost", "handler": "\\App\\Controllers\\AdminController::deleteFontPost"}, {"method": "POST", "route": "Admin/editIndexBannerPost", "handler": "\\App\\Controllers\\AdminController::editIndexBannerPost"}, {"method": "POST", "route": "Admin/homepageManagerPost", "handler": "\\App\\Controllers\\AdminController::homepageManagerPost"}, {"method": "POST", "route": "Admin/deleteIndexBannerPost", "handler": "\\App\\Controllers\\AdminController::deleteIndexBannerPost"}, {"method": "POST", "route": "Admin/homepageManagerSettingsPost", "handler": "\\App\\Controllers\\AdminController::homepageManagerSettingsPost"}, {"method": "POST", "route": "Admin/addIndexBannerPost", "handler": "\\App\\Controllers\\AdminController::addIndexBannerPost"}, {"method": "POST", "route": "Admin/setActiveLanguagePost", "handler": "\\App\\Controllers\\AdminController::setActiveLanguagePost"}, {"method": "POST", "route": "Admin/downloadDatabaseBackup", "handler": "\\App\\Controllers\\AdminController::downloadDatabaseBackup"}, {"method": "POST", "route": "Admin/addCityPost", "handler": "\\App\\Controllers\\AdminController::addCityPost"}, {"method": "POST", "route": "Admin/addCountryPost", "handler": "\\App\\Controllers\\AdminController::addCountryPost"}, {"method": "POST", "route": "Admin/addStatePost", "handler": "\\App\\Controllers\\AdminController::addStatePost"}, {"method": "POST", "route": "Admin/deleteCityPost", "handler": "\\App\\Controllers\\AdminController::deleteCityPost"}, {"method": "POST", "route": "Admin/deleteCountryPost", "handler": "\\App\\Controllers\\AdminController::deleteCountryPost"}, {"method": "POST", "route": "Admin/locationSettingsPost", "handler": "\\App\\Controllers\\AdminController::locationSettingsPost"}, {"method": "POST", "route": "Admin/editCityPost", "handler": "\\App\\Controllers\\AdminController::editCityPost"}, {"method": "POST", "route": "Admin/editCountryPost", "handler": "\\App\\Controllers\\AdminController::editCountryPost"}, {"method": "POST", "route": "Admin/editStatePost", "handler": "\\App\\Controllers\\AdminController::editStatePost"}, {"method": "POST", "route": "Admin/deleteStatePost", "handler": "\\App\\Controllers\\AdminController::deleteStatePost"}, {"method": "POST", "route": "Admin/newsletterSendEmail", "handler": "\\App\\Controllers\\AdminController::newsletterSendEmail"}, {"method": "POST", "route": "Admin/deleteNewsletterPost", "handler": "\\App\\Controllers\\AdminController::deleteNewsletterPost"}, {"method": "POST", "route": "Admin/newsletterSettingsPost", "handler": "\\App\\Controllers\\AdminController::newsletterSettingsPost"}, {"method": "POST", "route": "Admin/newsletterSendEmailPost", "handler": "\\App\\Controllers\\AdminController::newsletterSendEmailPost"}, {"method": "POST", "route": "Admin/addPagePost", "handler": "\\App\\Controllers\\AdminController::addPagePost"}, {"method": "POST", "route": "Admin/editPagePost", "handler": "\\App\\Controllers\\AdminController::editPagePost"}, {"method": "POST", "route": "Admin/deletePagePost", "handler": "\\App\\Controllers\\AdminController::deletePagePost"}, {"method": "POST", "route": "Admin/emailSettingsPost", "handler": "\\App\\Controllers\\AdminController::emailSettingsPost"}, {"method": "POST", "route": "Admin/sendTestEmailPost", "handler": "\\App\\Controllers\\AdminController::sendTestEmailPost"}, {"method": "POST", "route": "Admin/emailOptionsPost", "handler": "\\App\\Controllers\\AdminController::emailOptionsPost"}, {"method": "POST", "route": "Admin/generalSettingsPost", "handler": "\\App\\Controllers\\AdminController::generalSettingsPost"}, {"method": "POST", "route": "Admin/recaptchaSettingsPost", "handler": "\\App\\Controllers\\AdminController::recaptchaSettingsPost"}, {"method": "POST", "route": "Admin/maintenanceModePost", "handler": "\\App\\Controllers\\AdminController::maintenanceModePost"}, {"method": "POST", "route": "Admin/paymentGatewaySettingsPost", "handler": "\\App\\Controllers\\AdminController::paymentGatewaySettingsPost"}, {"method": "POST", "route": "Admin/commissionSettingsPost", "handler": "\\App\\Controllers\\AdminController::commissionSettingsPost"}, {"method": "POST", "route": "Admin/deleteTaxPost", "handler": "\\App\\Controllers\\AdminController::deleteTaxPost"}, {"method": "POST", "route": "Admin/editTaxPost", "handler": "\\App\\Controllers\\AdminController::editTaxPost"}, {"method": "POST", "route": "Admin/addTaxPost", "handler": "\\App\\Controllers\\AdminController::addTaxPost"}, {"method": "POST", "route": "Admin/additionalInvoiceInfoPost", "handler": "\\App\\Controllers\\AdminController::additionalInvoiceInfoPost"}, {"method": "POST", "route": "Admin/preferencesPost", "handler": "\\App\\Controllers\\AdminController::preferencesPost"}, {"method": "POST", "route": "Admin/productSettingsPost", "handler": "\\App\\Controllers\\AdminController::productSettingsPost"}, {"method": "POST", "route": "Admin/routeSettingsPost", "handler": "\\App\\Controllers\\AdminController::routeSettingsPost"}, {"method": "POST", "route": "Admin/socialLoginSettingsPost", "handler": "\\App\\Controllers\\AdminController::socialLoginSettingsPost"}, {"method": "POST", "route": "Admin/visualSettingsPost", "handler": "\\App\\Controllers\\AdminController::visualSettingsPost"}, {"method": "POST", "route": "Admin/updateWatermarkSettingsPost", "handler": "\\App\\Controllers\\AdminController::updateWatermarkSettingsPost"}, {"method": "POST", "route": "Admin/editSliderItemPost", "handler": "\\App\\Controllers\\AdminController::editSliderItemPost"}, {"method": "POST", "route": "Admin/addSliderItemPost", "handler": "\\App\\Controllers\\AdminController::addSliderItemPost"}, {"method": "POST", "route": "Admin/deleteSliderItemPost", "handler": "\\App\\Controllers\\AdminController::deleteSliderItemPost"}, {"method": "POST", "route": "Admin/activateInactivateCountries", "handler": "\\App\\Controllers\\AdminController::activateInactivateCountries"}, {"method": "POST", "route": "Admin/updateCurrencyRate", "handler": "\\App\\Controllers\\AdminController::updateCurrencyRate"}, {"method": "POST", "route": "Admin/affiliateProgramPost", "handler": "\\App\\Controllers\\AdminController::affiliateProgramPost"}, {"method": "POST", "route": "Admin/deleteChatPost", "handler": "\\App\\Controllers\\AdminController::deleteChatPost"}, {"method": "POST", "route": "Admin/deleteChatMessagePost", "handler": "\\App\\Controllers\\AdminController::deleteChatMessagePost"}, {"method": "POST", "route": "Admin/approveMembershipPaymentPost", "handler": "\\App\\Controllers\\AdminController::approveMembershipPaymentPost"}, {"method": "POST", "route": "Admin/approvePromotionPaymentPost", "handler": "\\App\\Controllers\\AdminController::approvePromotionPaymentPost"}, {"method": "POST", "route": "Admin/approveWalletDepositPaymentPost", "handler": "\\App\\Controllers\\AdminController::approveWalletDepositPaymentPost"}, {"method": "POST", "route": "Admin/deleteMembershipPaymentPost", "handler": "\\App\\Controllers\\AdminController::deleteMembershipPaymentPost"}, {"method": "POST", "route": "Admin/deletePromotionPaymentsPost", "handler": "\\App\\Controllers\\AdminController::deletePromotionPaymentsPost"}, {"method": "POST", "route": "Admin/bankTransferOptionsPost", "handler": "\\App\\Controllers\\AdminController::bankTransferOptionsPost"}, {"method": "POST", "route": "Admin/deleteWalletDepositPost", "handler": "\\App\\Controllers\\AdminController::deleteWalletDepositPost"}, {"method": "POST", "route": "Admin/deleteBankTransferPost", "handler": "\\App\\Controllers\\AdminController::deleteBankTransferPost"}, {"method": "POST", "route": "Admin/loadCountersPost", "handler": "\\App\\Controllers\\AdminController::loadCountersPost"}, {"method": "POST", "route": "Ajax/getStates", "handler": "\\App\\Controllers\\AjaxController::getStates"}, {"method": "POST", "route": "Ajax/getCities", "handler": "\\App\\Controllers\\AjaxController::getCities"}, {"method": "POST", "route": "Ajax/getSubCategories", "handler": "\\App\\Controllers\\AjaxController::getSubCategories"}, {"method": "POST", "route": "Ajax/searchCategories", "handler": "\\App\\Controllers\\AjaxController::searchCategories"}, {"method": "POST", "route": "Ajax/runEmailQueue", "handler": "\\App\\Controllers\\AjaxController::runEmailQueue"}, {"method": "POST", "route": "Ajax/getBlogCategoriesByLang", "handler": "\\App\\Controllers\\AjaxController::getBlogCategoriesByLang"}, {"method": "POST", "route": "Ajax/getCountriesByContinent", "handler": "\\App\\Controllers\\AjaxController::getCountriesByContinent"}, {"method": "POST", "route": "Ajax/getStatesByCountry", "handler": "\\App\\Controllers\\AjaxController::getStatesByCountry"}, {"method": "POST", "route": "Ajax/addComment", "handler": "\\App\\Controllers\\AjaxController::addComment"}, {"method": "POST", "route": "Ajax/loadMoreComments", "handler": "\\App\\Controllers\\AjaxController::loadMoreComments"}, {"method": "POST", "route": "Ajax/loadMoreReviews", "handler": "\\App\\Controllers\\AjaxController::loadMoreReviews"}, {"method": "POST", "route": "Ajax/deleteComment", "handler": "\\App\\Controllers\\AjaxController::deleteComment"}, {"method": "POST", "route": "Ajax/deleteReview", "handler": "\\App\\Controllers\\AjaxController::deleteReview"}, {"method": "POST", "route": "Ajax/loadSubCommentForm", "handler": "\\App\\Controllers\\AjaxController::loadSubCommentForm"}, {"method": "POST", "route": "Ajax/addBlogComment", "handler": "\\App\\Controllers\\AjaxController::addBlogComment"}, {"method": "POST", "route": "Ajax/loadMoreBlogComments", "handler": "\\App\\Controllers\\AjaxController::loadMoreBlogComments"}, {"method": "POST", "route": "Ajax/deleteBlogComment", "handler": "\\App\\Controllers\\AjaxController::deleteBlogComment"}, {"method": "POST", "route": "Ajax/addChatPost", "handler": "\\App\\Controllers\\AjaxController::addChatPost"}, {"method": "POST", "route": "Ajax/loadChatPost", "handler": "\\App\\Controllers\\AjaxController::loadChatPost"}, {"method": "POST", "route": "Ajax/sendMessagePost", "handler": "\\App\\Controllers\\AjaxController::sendMessagePost"}, {"method": "POST", "route": "Ajax/deleteChatPost", "handler": "\\App\\Controllers\\AjaxController::deleteChatPost"}, {"method": "POST", "route": "Ajax/reportAbusePost", "handler": "\\App\\Controllers\\AjaxController::reportAbusePost"}, {"method": "POST", "route": "Ajax/ajaxSearch", "handler": "\\App\\Controllers\\AjaxController::ajaxSearch"}, {"method": "POST", "route": "Ajax/loadMorePromotedProducts", "handler": "\\App\\Controllers\\AjaxController::loadMorePromotedProducts"}, {"method": "POST", "route": "Ajax/hideCookiesWarning", "handler": "\\App\\Controllers\\AjaxController::hideCookiesWarning"}, {"method": "POST", "route": "Ajax/getProductShippingCost", "handler": "\\App\\Controllers\\AjaxController::getProductShippingCost"}, {"method": "POST", "route": "Ajax/addToNewsletter", "handler": "\\App\\Controllers\\AjaxController::addToNewsletter"}, {"method": "POST", "route": "Ajax/createAffiliateLink", "handler": "\\App\\Controllers\\AjaxController::createAffiliateLink"}, {"method": "POST", "route": "Ajax/selectCouponCategoryPost", "handler": "\\App\\Controllers\\AjaxController::selectCouponCategoryPost"}, {"method": "POST", "route": "Ajax/selectCouponProductPost", "handler": "\\App\\Controllers\\AjaxController::selectCouponProductPost"}, {"method": "POST", "route": "Auth/sendActivationEmailPost", "handler": "\\App\\Controllers\\AuthController::sendActivationEmailPost"}, {"method": "POST", "route": "Auth/joinAffiliateProgramPost", "handler": "\\App\\Controllers\\AuthController::joinAffiliateProgramPost"}, {"method": "POST", "route": "Blog/addPostPost", "handler": "\\App\\Controllers\\BlogController::addPostPost"}, {"method": "POST", "route": "Blog/addCategoryPost", "handler": "\\App\\Controllers\\BlogController::addCategoryPost"}, {"method": "POST", "route": "Blog/deleteCategoryPost", "handler": "\\App\\Controllers\\BlogController::deleteCategoryPost"}, {"method": "POST", "route": "Blog/approveCommentPost", "handler": "\\App\\Controllers\\BlogController::approveCommentPost"}, {"method": "POST", "route": "Blog/deleteComment", "handler": "\\App\\Controllers\\BlogController::deleteComment"}, {"method": "POST", "route": "Blog/editCategoryPost", "handler": "\\App\\Controllers\\BlogController::editCategoryPost"}, {"method": "POST", "route": "Blog/editPostPost", "handler": "\\App\\Controllers\\BlogController::editPostPost"}, {"method": "POST", "route": "Blog/deletePostPost", "handler": "\\App\\Controllers\\BlogController::deletePostPost"}, {"method": "POST", "route": "Blog/approveSelectedComments", "handler": "\\App\\Controllers\\BlogController::approveSelectedComments"}, {"method": "POST", "route": "Blog/deleteSelectedComments", "handler": "\\App\\Controllers\\BlogController::deleteSelectedComments"}, {"method": "POST", "route": "Blog/deletePostImagePost", "handler": "\\App\\Controllers\\BlogController::deletePostImagePost"}, {"method": "POST", "route": "Cart/removeCartDiscountCoupon", "handler": "\\App\\Controllers\\CartController::removeCartDiscountCoupon"}, {"method": "POST", "route": "Cart/removeFromCart", "handler": "\\App\\Controllers\\CartController::removeFromCart"}, {"method": "POST", "route": "Cart/getShippingMethodsByLocation", "handler": "\\App\\Controllers\\CartController::getShippingMethodsByLocation"}, {"method": "POST", "route": "Cart/walletBalancePaymentPost", "handler": "\\App\\Controllers\\CartController::walletBalancePaymentPost"}, {"method": "POST", "route": "Category/deleteBrandPost", "handler": "\\App\\Controllers\\CategoryController::deleteBrandPost"}, {"method": "POST", "route": "Category/editBrandPost", "handler": "\\App\\Controllers\\CategoryController::editBrandPost"}, {"method": "POST", "route": "Category/brandSettingsPost", "handler": "\\App\\Controllers\\CategoryController::brandSettingsPost"}, {"method": "POST", "route": "Category/addBrandPost", "handler": "\\App\\Controllers\\CategoryController::addBrandPost"}, {"method": "POST", "route": "Category/addCategoryPost", "handler": "\\App\\Controllers\\CategoryController::addCategoryPost"}, {"method": "POST", "route": "Category/addCustomFieldPost", "handler": "\\App\\Controllers\\CategoryController::addCustomFieldPost"}, {"method": "POST", "route": "Category/downloadCsvFilesPost", "handler": "\\App\\Controllers\\CategoryController::downloadCsvFilesPost"}, {"method": "POST", "route": "Category/generateCsvObjectPost", "handler": "\\App\\Controllers\\CategoryController::generateCsvObjectPost"}, {"method": "POST", "route": "Category/importCsvItemPost", "handler": "\\App\\Controllers\\CategoryController::importCsvItemPost"}, {"method": "POST", "route": "Category/categorySettingsPost", "handler": "\\App\\Controllers\\CategoryController::categorySettingsPost"}, {"method": "POST", "route": "Category/deleteCategoryPost", "handler": "\\App\\Controllers\\CategoryController::deleteCategoryPost"}, {"method": "POST", "route": "Category/loadCategories", "handler": "\\App\\Controllers\\CategoryController::loadCategories"}, {"method": "POST", "route": "Category/editCustomFieldOptionPost", "handler": "\\App\\Controllers\\CategoryController::editCustomFieldOptionPost"}, {"method": "POST", "route": "Category/addCustomFieldOptionPost", "handler": "\\App\\Controllers\\CategoryController::addCustomFieldOptionPost"}, {"method": "POST", "route": "Category/addCategoryToCustomField", "handler": "\\App\\Controllers\\CategoryController::addCategoryToCustomField"}, {"method": "POST", "route": "Category/customFieldSettingsPost", "handler": "\\App\\Controllers\\CategoryController::customFieldSettingsPost"}, {"method": "POST", "route": "Category/addRemoveCustomFieldFiltersPost", "handler": "\\App\\Controllers\\CategoryController::addRemoveCustomFieldFiltersPost"}, {"method": "POST", "route": "Category/deleteCustomFieldPost", "handler": "\\App\\Controllers\\CategoryController::deleteCustomFieldPost"}, {"method": "POST", "route": "Category/editCategoryPost", "handler": "\\App\\Controllers\\CategoryController::editCategoryPost"}, {"method": "POST", "route": "Category/editCustomFieldPost", "handler": "\\App\\Controllers\\CategoryController::editCustomFieldPost"}, {"method": "POST", "route": "Category/deleteCustomFieldOption", "handler": "\\App\\Controllers\\CategoryController::deleteCustomFieldOption"}, {"method": "POST", "route": "Category/deleteCategoryFromField", "handler": "\\App\\Controllers\\CategoryController::deleteCategoryFromField"}, {"method": "POST", "route": "Category/editFeaturedCategoriesOrderPost", "handler": "\\App\\Controllers\\CategoryController::editFeaturedCategoriesOrderPost"}, {"method": "POST", "route": "Category/editIndexCategoriesOrderPost", "handler": "\\App\\Controllers\\CategoryController::editIndexCategoriesOrderPost"}, {"method": "POST", "route": "Category/deleteCategoryImagePost", "handler": "\\App\\Controllers\\CategoryController::deleteCategoryImagePost"}, {"method": "POST", "route": "Category/generateCsvObjectCustomFieldPost", "handler": "\\App\\Controllers\\CategoryController::generateCsvObjectCustomFieldPost"}, {"method": "POST", "route": "Category/importCsvCustomFieldPost", "handler": "\\App\\Controllers\\CategoryController::importCsvCustomFieldPost"}, {"method": "POST", "route": "Category/downloadCsvFilesCustomFieldPost", "handler": "\\App\\Controllers\\CategoryController::downloadCsvFilesCustomFieldPost"}, {"method": "POST", "route": "Dashboard/deleteCouponPost", "handler": "\\App\\Controllers\\DashboardController::deleteCouponPost"}, {"method": "POST", "route": "Dashboard/downloadCsvFilePost", "handler": "\\App\\Controllers\\DashboardController::downloadCsvFilePost"}, {"method": "POST", "route": "Dashboard/generateCsvObjectPost", "handler": "\\App\\Controllers\\DashboardController::generateCsvObjectPost"}, {"method": "POST", "route": "Dashboard/importCsvItemPost", "handler": "\\App\\Controllers\\DashboardController::importCsvItemPost"}, {"method": "POST", "route": "Dashboard/deleteProduct", "handler": "\\App\\Controllers\\DashboardController::deleteProduct"}, {"method": "POST", "route": "Dashboard/approveDeclineRefund", "handler": "\\App\\Controllers\\DashboardController::approveDeclineRefund"}, {"method": "POST", "route": "Dashboard/deleteShippingLocationPost", "handler": "\\App\\Controllers\\DashboardController::deleteShippingLocationPost"}, {"method": "POST", "route": "Dashboard/selectShippingMethod", "handler": "\\App\\Controllers\\DashboardController::selectShippingMethod"}, {"method": "POST", "route": "Dashboard/deleteShippingMethodPost", "handler": "\\App\\Controllers\\DashboardController::deleteShippingMethodPost"}, {"method": "POST", "route": "Dashboard/deleteShippingZonePost", "handler": "\\App\\Controllers\\DashboardController::deleteShippingZonePost"}, {"method": "POST", "route": "Dashboard/deleteShippingClassPost", "handler": "\\App\\Controllers\\DashboardController::deleteShippingClassPost"}, {"method": "POST", "route": "Dashboard/deleteShippingDeliveryTimePost", "handler": "\\App\\Controllers\\DashboardController::deleteShippingDeliveryTimePost"}, {"method": "POST", "route": "Dashboard/addLicenseKeys", "handler": "\\App\\Controllers\\DashboardController::addLicenseKeys"}, {"method": "POST", "route": "Dashboard/deleteLicenseKey", "handler": "\\App\\Controllers\\DashboardController::deleteLicenseKey"}, {"method": "POST", "route": "Dashboard/loadLicenseKeysList", "handler": "\\App\\Controllers\\DashboardController::loadLicenseKeysList"}, {"method": "POST", "route": "Dashboard/getSubCategories", "handler": "\\App\\Controllers\\DashboardController::getSubCategories"}, {"method": "POST", "route": "Dashboard/affiliateProgramPost", "handler": "\\App\\Controllers\\DashboardController::affiliateProgramPost"}, {"method": "POST", "route": "Dashboard/addRemoveAffiliateProductPost", "handler": "\\App\\Controllers\\DashboardController::addRemoveAffiliateProductPost"}, {"method": "POST", "route": "Dashboard/exportTableDataPost", "handler": "\\App\\Controllers\\DashboardController::exportTableDataPost"}, {"method": "POST", "route": "Dashboard/cashOnDeliverySettingsPost", "handler": "\\App\\Controllers\\DashboardController::cashOnDeliverySettingsPost"}, {"method": "POST", "route": "Dashboard/shopPoliciesPost", "handler": "\\App\\Controllers\\DashboardController::shopPoliciesPost"}, {"method": "POST", "route": "Dashboard/loadIndexData", "handler": "\\App\\Controllers\\DashboardController::loadIndexData"}, {"method": "POST", "route": "Earnings/addPayoutPost", "handler": "\\App\\Controllers\\EarningsController::addPayoutPost"}, {"method": "POST", "route": "Earnings/deleteEarningPost", "handler": "\\App\\Controllers\\EarningsController::deleteEarningPost"}, {"method": "POST", "route": "Earnings/completePayoutRequestPost", "handler": "\\App\\Controllers\\EarningsController::completePayoutRequestPost"}, {"method": "POST", "route": "Earnings/deletePayoutPost", "handler": "\\App\\Controllers\\EarningsController::deletePayoutPost"}, {"method": "POST", "route": "Earnings/payoutSettingsPost", "handler": "\\App\\Controllers\\EarningsController::payoutSettingsPost"}, {"method": "POST", "route": "Earnings/editSellerBalancePost", "handler": "\\App\\Controllers\\EarningsController::editSellerBalancePost"}, {"method": "POST", "route": "File/uploadBlogImage", "handler": "\\App\\Controllers\\FileController::uploadBlogImage"}, {"method": "POST", "route": "File/downloadDigitalFile", "handler": "\\App\\Controllers\\FileController::downloadDigitalFile"}, {"method": "POST", "route": "File/setImageMainSession", "handler": "\\App\\Controllers\\FileController::setImageMainSession"}, {"method": "POST", "route": "File/setImageMain", "handler": "\\App\\Controllers\\FileController::setImageMain"}, {"method": "POST", "route": "File/deleteImageSession", "handler": "\\App\\Controllers\\FileController::deleteImageSession"}, {"method": "POST", "route": "File/deleteImage", "handler": "\\App\\Controllers\\FileController::deleteImage"}, {"method": "POST", "route": "File/deleteVideo", "handler": "\\App\\Controllers\\FileController::deleteVideo"}, {"method": "POST", "route": "File/deleteAudio", "handler": "\\App\\Controllers\\FileController::deleteAudio"}, {"method": "POST", "route": "File/deleteDigitalFile", "handler": "\\App\\Controllers\\FileController::deleteDigitalFile"}, {"method": "POST", "route": "File/getBlogImages", "handler": "\\App\\Controllers\\FileController::getBlogImages"}, {"method": "POST", "route": "File/deleteBlogImage", "handler": "\\App\\Controllers\\FileController::deleteBlogImage"}, {"method": "POST", "route": "File/loadMoreBlogImages", "handler": "\\App\\Controllers\\FileController::loadMoreBlogImages"}, {"method": "POST", "route": "File/getFileManagerImages", "handler": "\\App\\Controllers\\FileController::getFileManagerImages"}, {"method": "POST", "route": "File/deleteFileManagerImage", "handler": "\\App\\Controllers\\FileController::deleteFileManagerImage"}, {"method": "POST", "route": "File/exportTableDataPost", "handler": "\\App\\Controllers\\FileController::exportTableDataPost"}, {"method": "POST", "route": "Home/selectMembershipPlanPost", "handler": "\\App\\Controllers\\HomeController::selectMembershipPlanPost"}, {"method": "POST", "route": "Home/setDefaultLocationPost", "handler": "\\App\\Controllers\\HomeController::setDefaultLocationPost"}, {"method": "POST", "route": "Home/bankTransferPaymentReportPost", "handler": "\\App\\Controllers\\HomeController::bankTransferPaymentReportPost"}, {"method": "POST", "route": "Language/editLanguagePost", "handler": "\\App\\Controllers\\LanguageController::editLanguagePost"}, {"method": "POST", "route": "Language/setDefaultLanguagePost", "handler": "\\App\\Controllers\\LanguageController::setDefaultLanguagePost"}, {"method": "POST", "route": "Language/exportLanguagePost", "handler": "\\App\\Controllers\\LanguageController::exportLanguagePost"}, {"method": "POST", "route": "Language/deleteLanguagePost", "handler": "\\App\\Controllers\\LanguageController::deleteLanguagePost"}, {"method": "POST", "route": "Language/addLanguagePost", "handler": "\\App\\Controllers\\LanguageController::addLanguagePost"}, {"method": "POST", "route": "Language/importLanguagePost", "handler": "\\App\\Controllers\\LanguageController::importLanguagePost"}, {"method": "POST", "route": "Language/editTranslationsPost", "handler": "\\App\\Controllers\\LanguageController::editTranslationsPost"}, {"method": "POST", "route": "Membership/addRolePost", "handler": "\\App\\Controllers\\MembershipController::addRolePost"}, {"method": "POST", "route": "Membership/addUserPost", "handler": "\\App\\Controllers\\MembershipController::addUserPost"}, {"method": "POST", "route": "Membership/editPlanPost", "handler": "\\App\\Controllers\\MembershipController::editPlanPost"}, {"method": "POST", "route": "Membership/editRolePost", "handler": "\\App\\Controllers\\MembershipController::editRolePost"}, {"method": "POST", "route": "Membership/editUserPost", "handler": "\\App\\Controllers\\MembershipController::editUserPost"}, {"method": "POST", "route": "Membership/addPlanPost", "handler": "\\App\\Controllers\\MembershipController::addPlanPost"}, {"method": "POST", "route": "Membership/settingsPost", "handler": "\\App\\Controllers\\MembershipController::settingsPost"}, {"method": "POST", "route": "Membership/deletePlanPost", "handler": "\\App\\Controllers\\MembershipController::deletePlanPost"}, {"method": "POST", "route": "Membership/deleteRolePost", "handler": "\\App\\Controllers\\MembershipController::deleteRolePost"}, {"method": "POST", "route": "Membership/approveShopOpeningRequest", "handler": "\\App\\Controllers\\MembershipController::approveShopOpeningRequest"}, {"method": "POST", "route": "Membership/deleteUserPost", "handler": "\\App\\Controllers\\MembershipController::deleteUserPost"}, {"method": "POST", "route": "Membership/assignMembershipPlanPost", "handler": "\\App\\Controllers\\MembershipController::assignMembershipPlanPost"}, {"method": "POST", "route": "Membership/changeUserRolePost", "handler": "\\App\\Controllers\\MembershipController::changeUserRolePost"}, {"method": "POST", "route": "Membership/confirmUserEmail", "handler": "\\App\\Controllers\\MembershipController::confirmUserEmail"}, {"method": "POST", "route": "Membership/banRemoveBanUser", "handler": "\\App\\Controllers\\MembershipController::banRemoveBanUser"}, {"method": "POST", "route": "Membership/addDeleteUserAffiliateProgram", "handler": "\\App\\Controllers\\MembershipController::addDeleteUserAffiliateProgram"}, {"method": "POST", "route": "Membership/loginToUserAccountPost", "handler": "\\App\\Controllers\\MembershipController::loginToUserAccountPost"}, {"method": "POST", "route": "Membership/rejectShopOpeningRequest", "handler": "\\App\\Controllers\\MembershipController::rejectShopOpeningRequest"}, {"method": "POST", "route": "Membership/cancelAccountDeleteRequestPost", "handler": "\\App\\Controllers\\MembershipController::cancelAccountDeleteRequestPost"}, {"method": "POST", "route": "OrderAdmin/deleteDigitalSalePost", "handler": "\\App\\Controllers\\OrderAdminController::deleteDigitalSalePost"}, {"method": "POST", "route": "OrderAdmin/approveGuestOrderProduct", "handler": "\\App\\Controllers\\OrderAdminController::approveGuestOrderProduct"}, {"method": "POST", "route": "OrderAdmin/deleteOrderProductPost", "handler": "\\App\\Controllers\\OrderAdminController::deleteOrderProductPost"}, {"method": "POST", "route": "OrderAdmin/updateOrderProductStatusPost", "handler": "\\App\\Controllers\\OrderAdminController::updateOrderProductStatusPost"}, {"method": "POST", "route": "OrderAdmin/orderPaymentReceivedPost", "handler": "\\App\\Controllers\\OrderAdminController::orderPaymentReceivedPost"}, {"method": "POST", "route": "OrderAdmin/deleteOrderPost", "handler": "\\App\\Controllers\\OrderAdminController::deleteOrderPost"}, {"method": "POST", "route": "OrderAdmin/deleteTransactionPost", "handler": "\\App\\Controllers\\OrderAdminController::deleteTransactionPost"}, {"method": "POST", "route": "OrderAdmin/approveRefundPost", "handler": "\\App\\Controllers\\OrderAdminController::approveRefundPost"}, {"method": "POST", "route": "Order/deleteQuoteRequest", "handler": "\\App\\Controllers\\OrderController::deleteQuoteRequest"}, {"method": "POST", "route": "Order/addRefundMessage", "handler": "\\App\\Controllers\\OrderController::addRefundMessage"}, {"method": "POST", "route": "Order/cancelOrderPost", "handler": "\\App\\Controllers\\OrderController::cancelOrderPost"}, {"method": "POST", "route": "Order/approveOrderProductPost", "handler": "\\App\\Controllers\\OrderController::approveOrderProductPost"}, {"method": "POST", "route": "Product/deleteReviewPost", "handler": "\\App\\Controllers\\ProductController::deleteReviewPost"}, {"method": "POST", "route": "Product/deleteCommentPost", "handler": "\\App\\Controllers\\ProductController::deleteCommentPost"}, {"method": "POST", "route": "Product/deleteQuoteRequestPost", "handler": "\\App\\Controllers\\ProductController::deleteQuoteRequestPost"}, {"method": "POST", "route": "Product/approveCommentPost", "handler": "\\App\\Controllers\\ProductController::approveCommentPost"}, {"method": "POST", "route": "Product/deleteProductPermanently", "handler": "\\App\\Controllers\\ProductController::deleteProductPermanently"}, {"method": "POST", "route": "Product/deleteProduct", "handler": "\\App\\Controllers\\ProductController::deleteProduct"}, {"method": "POST", "route": "Product/featuredProductsPricingPost", "handler": "\\App\\Controllers\\ProductController::featuredProductsPricingPost"}, {"method": "POST", "route": "Product/addRemoveFeaturedProduct", "handler": "\\App\\Controllers\\ProductController::addRemoveFeaturedProduct"}, {"method": "POST", "route": "Product/rejectProduct", "handler": "\\App\\Controllers\\ProductController::rejectProduct"}, {"method": "POST", "route": "Product/approveProduct", "handler": "\\App\\Controllers\\ProductController::approveProduct"}, {"method": "POST", "route": "Product/deleteSelectedProducts", "handler": "\\App\\Controllers\\ProductController::deleteSelectedProducts"}, {"method": "POST", "route": "Product/deleteSelectedProductsPermanently", "handler": "\\App\\Controllers\\ProductController::deleteSelectedProductsPermanently"}, {"method": "POST", "route": "Product/addRemoveSpecialOffer", "handler": "\\App\\Controllers\\ProductController::addRemoveSpecialOffer"}, {"method": "POST", "route": "Product/restoreProduct", "handler": "\\App\\Controllers\\ProductController::restoreProduct"}, {"method": "POST", "route": "Product/deleteSelectedReviews", "handler": "\\App\\Controllers\\ProductController::deleteSelectedReviews"}, {"method": "POST", "route": "Product/approveSelectedComments", "handler": "\\App\\Controllers\\ProductController::approveSelectedComments"}, {"method": "POST", "route": "Product/deleteSelectedComments", "handler": "\\App\\Controllers\\ProductController::deleteSelectedComments"}, {"method": "POST", "route": "Product/approveSelectedEditedProducts", "handler": "\\App\\Controllers\\ProductController::approveSelectedEditedProducts"}, {"method": "POST", "route": "Profile/deleteCoverImagePost", "handler": "\\App\\Controllers\\ProfileController::deleteCoverImagePost"}, {"method": "POST", "route": "Profile/deleteShippingAddressPost", "handler": "\\App\\Controllers\\ProfileController::deleteShippingAddressPost"}, {"method": "POST", "route": "Profile/addFundsPost", "handler": "\\App\\Controllers\\ProfileController::addFundsPost"}, {"method": "POST", "route": "Profile/deleteAffiliateLinkPost", "handler": "\\App\\Controllers\\ProfileController::deleteAffiliateLinkPost"}, {"method": "POST", "route": "SupportAdmin/addCategoryPost", "handler": "\\App\\Controllers\\SupportAdminController::addCategoryPost"}, {"method": "POST", "route": "SupportAdmin/addContentPost", "handler": "\\App\\Controllers\\SupportAdminController::addContentPost"}, {"method": "POST", "route": "SupportAdmin/editCategoryPost", "handler": "\\App\\Controllers\\SupportAdminController::editCategoryPost"}, {"method": "POST", "route": "SupportAdmin/editContentPost", "handler": "\\App\\Controllers\\SupportAdminController::editContentPost"}, {"method": "POST", "route": "SupportAdmin/deleteContentPost", "handler": "\\App\\Controllers\\SupportAdminController::deleteContentPost"}, {"method": "POST", "route": "SupportAdmin/deleteCategoryPost", "handler": "\\App\\Controllers\\SupportAdminController::deleteCategoryPost"}, {"method": "POST", "route": "SupportAdmin/sendMessagePost", "handler": "\\App\\Controllers\\SupportAdminController::sendMessagePost"}, {"method": "POST", "route": "SupportAdmin/deleteTicketPost", "handler": "\\App\\Controllers\\SupportAdminController::deleteTicketPost"}, {"method": "POST", "route": "SupportAdmin/changeTicketStatusPost", "handler": "\\App\\Controllers\\SupportAdminController::changeTicketStatusPost"}, {"method": "POST", "route": "SupportAdmin/getCategoriesByLang", "handler": "\\App\\Controllers\\SupportAdminController::getCategoriesByLang"}, {"method": "POST", "route": "Support/downloadAttachmentPost", "handler": "\\App\\Controllers\\SupportController::downloadAttachmentPost"}, {"method": "POST", "route": "Support/uploadSupportAttachment", "handler": "\\App\\Controllers\\SupportController::uploadSupportAttachment"}, {"method": "POST", "route": "Support/deleteSupportAttachmentPost", "handler": "\\App\\Controllers\\SupportController::deleteSupportAttachmentPost"}, {"method": "POST", "route": "admin/login-post", "handler": "\\App\\Controllers\\CommonController::adminLoginpost"}]}, "badgeValue": 370, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "4.04", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.22", "count": 30}}}, "badgeValue": 31, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.07679, "duration": 0.0040378570556640625}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.109935, "duration": 5.698204040527344e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.109995, "duration": 2.2172927856445312e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.115948, "duration": 1.4066696166992188e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.115963, "duration": 5.9604644775390625e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.116961, "duration": 9.059906005859375e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.11697, "duration": 5.9604644775390625e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.117203, "duration": 6.9141387939453125e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.11721, "duration": 4.0531158447265625e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.117388, "duration": 4.0531158447265625e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.117392, "duration": 2.86102294921875e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.11754, "duration": 3.0994415283203125e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.117544, "duration": 3.0994415283203125e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.117725, "duration": 4.0531158447265625e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.117729, "duration": 3.0994415283203125e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.117968, "duration": 5.0067901611328125e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.117973, "duration": 3.814697265625e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.118097, "duration": 2.86102294921875e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.1181, "duration": 4.0531158447265625e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.118198, "duration": 3.0994415283203125e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.118202, "duration": 3.0994415283203125e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.118947, "duration": 4.0531158447265625e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.118952, "duration": 2.86102294921875e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.120219, "duration": 5.9604644775390625e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.120225, "duration": 4.0531158447265625e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.120474, "duration": 3.814697265625e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.120479, "duration": 5.0067901611328125e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.120845, "duration": 3.814697265625e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.12085, "duration": 2.86102294921875e-06}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.139252, "duration": 1.3113021850585938e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.139266, "duration": 1.0967254638671875e-05}]}], "vars": {"varData": {"View Data": {"generalSettings": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>stdClass</var>#81 (115)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (115)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>affiliate_commission_rate</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;affiliate_commission_rate</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>affiliate_discount_rate</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;affiliate_discount_rate</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>affiliate_image</dfn> -&gt; <var>null</var><div class=\"access-path\">$value-&gt;affiliate_image</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>affiliate_status</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;affiliate_status</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>affiliate_type</dfn> -&gt; <var>string</var> (12) \"seller_based\"<div class=\"access-path\">$value-&gt;affiliate_type</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>allow_free_plan_multiple_times</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;allow_free_plan_multiple_times</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>application_name</dfn> -&gt; <var>string</var> (5) \"FiiQI\"<div class=\"access-path\">$value-&gt;application_name</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>approve_after_editing</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;approve_after_editing</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>approve_before_publishing</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;approve_before_publishing</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>auto_approve_orders</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;auto_approve_orders</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>auto_approve_orders_days</dfn> -&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value-&gt;auto_approve_orders_days</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>bidding_system</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;bidding_system</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>blog_comments</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;blog_comments</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>cache_refresh_time</dfn> -&gt; <var>string</var> (4) \"1800\"<div class=\"access-path\">$value-&gt;cache_refresh_time</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>cache_static_system</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;cache_static_system</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>cache_system</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;cache_system</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>classified_ads_system</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;classified_ads_system</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>comment_approval_system</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;comment_approval_system</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>custom_footer_codes</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value-&gt;custom_footer_codes</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>custom_header_codes</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value-&gt;custom_header_codes</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>digital_products_system</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;digital_products_system</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>email_options</dfn> -&gt; <var>string</var> (186) \"a:7:{s:11:\"new_product\";i:1;s:9:\"new_order\";i:1;s:13:\"order_shipped\";i:1;s:1...<div class=\"access-path\">$value-&gt;email_options</div></dt><dd><pre>a:7:{s:11:\"new_product\";i:1;s:9:\"new_order\";i:1;s:13:\"order_shipped\";i:1;s:16:\"contact_messages\";i:1;s:20:\"shop_opening_request\";i:1;s:14:\"bidding_system\";i:1;s:14:\"support_system\";i:1;}\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>email_verification</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;email_verification</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>explanation_documents_vendors</dfn> -&gt; <var>string</var> (88) \"Your Government Issued ID card like NIN, International Passport, Driver Lice...<div class=\"access-path\">$value-&gt;explanation_documents_vendors</div></dt><dd><pre>Your Government Issued ID card like NIN, International Passport, Driver License and co..\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>facebook_app_id</dfn> -&gt; <var>string</var> (16) \"1246356327091922\"<div class=\"access-path\">$value-&gt;facebook_app_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>facebook_app_secret</dfn> -&gt; <var>string</var> (32) \"1fa3d3427e8acda39c7137df9254d6e0\"<div class=\"access-path\">$value-&gt;facebook_app_secret</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>facebook_comment</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value-&gt;facebook_comment</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>facebook_comment_status</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;facebook_comment_status</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>favicon</dfn> -&gt; <var>string</var> (45) \"uploads/logo/logo_6847881c49d7d8-47091430.png\"<div class=\"access-path\">$value-&gt;favicon</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (9.8KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.8KB Jun 28 00:53 uploads/logo/logo_6847881c49d7d8-47091430.png\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>featured_categories</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;featured_categories</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>fea_categories_design</dfn> -&gt; <var>string</var> (11) \"round_boxes\"<div class=\"access-path\">$value-&gt;fea_categories_design</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>google_adsense_code</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value-&gt;google_adsense_code</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>google_analytics</dfn> -&gt; <var>string</var> (295) \"&lt;!-- Google tag (gtag.js) --&gt; &lt;script async src=\"https://www.googletagmanage...<div class=\"access-path\">$value-&gt;google_analytics</div></dt><dd><pre>&lt;!-- Google tag (gtag.js) --&gt;\r\n&lt;script async src=\"https://www.googletagmanager.com/gtag/js?id=G-V57YL2MQYV\"&gt;&lt;/script&gt;\r\n&lt;script&gt;\r\n  window.dataLayer = window.dataLayer || [];\r\n  function gtag(){dataLayer.push(arguments);}\r\n  gtag('js', new Date());\r\n\r\n  gtag('config', 'G-V57YL2MQYV');\r\n&lt;/script&gt;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>google_client_id</dfn> -&gt; <var>string</var> (72) \"489565079589-v499eksspa8h4eh98g38i8fqc1medekr.apps.googleusercontent.com\"<div class=\"access-path\">$value-&gt;google_client_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>google_client_secret</dfn> -&gt; <var>string</var> (35) \"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\"<div class=\"access-path\">$value-&gt;google_client_secret</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>guest_checkout</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;guest_checkout</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>index_blog_slider</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;index_blog_slider</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>index_latest_products</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;index_latest_products</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>index_latest_products_count</dfn> -&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value-&gt;index_latest_products_count</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>index_promoted_products</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;index_promoted_products</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>index_promoted_products_count</dfn> -&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value-&gt;index_promoted_products_count</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>last_cron_update</dfn> -&gt; <var>string</var> (19) \"2025-06-28 15:43:25\"<div class=\"access-path\">$value-&gt;last_cron_update</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>location_search_header</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;location_search_header</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>logo</dfn> -&gt; <var>string</var> (45) \"uploads/logo/logo_68478c94d41462-92947160.png\"<div class=\"access-path\">$value-&gt;logo</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (3.3KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.3KB Jun 28 00:53 uploads/logo/logo_68478c94d41462-92947160.png\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>logo_email</dfn> -&gt; <var>string</var> (45) \"uploads/logo/logo_68478c94d44796-54411141.png\"<div class=\"access-path\">$value-&gt;logo_email</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (3.3KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.3KB Jun 28 00:53 uploads/logo/logo_68478c94d44796-54411141.png\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>logo_size</dfn> -&gt; <var>string</var> (6) \"150x50\"<div class=\"access-path\">$value-&gt;logo_size</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>mailjet_api_key</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value-&gt;mailjet_api_key</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>mailjet_email_address</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value-&gt;mailjet_email_address</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>mailjet_secret_key</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value-&gt;mailjet_secret_key</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>mail_encryption</dfn> -&gt; <var>string</var> (3) \"tls\"<div class=\"access-path\">$value-&gt;mail_encryption</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>mail_host</dfn> -&gt; <var>string</var> (18) \"smtp.hostinger.com\"<div class=\"access-path\">$value-&gt;mail_host</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>mail_options_account</dfn> -&gt; <var>string</var> (18) \"<EMAIL>\"<div class=\"access-path\">$value-&gt;mail_options_account</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>mail_password</dfn> -&gt; <var>string</var> (10) \"4ZWEUV@x&amp;d\"<div class=\"access-path\">$value-&gt;mail_password</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>mail_port</dfn> -&gt; <var>string</var> (3) \"587\"<div class=\"access-path\">$value-&gt;mail_port</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>mail_protocol</dfn> -&gt; <var>string</var> (4) \"smtp\"<div class=\"access-path\">$value-&gt;mail_protocol</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>mail_reply_to</dfn> -&gt; <var>string</var> (18) \"<EMAIL>\"<div class=\"access-path\">$value-&gt;mail_reply_to</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>mail_service</dfn> -&gt; <var>string</var> (3) \"php\"<div class=\"access-path\">$value-&gt;mail_service</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>mail_title</dfn> -&gt; <var>string</var> (5) \"FiiQi\"<div class=\"access-path\">$value-&gt;mail_title</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>mail_username</dfn> -&gt; <var>string</var> (18) \"<EMAIL>\"<div class=\"access-path\">$value-&gt;mail_username</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>maintenance_mode_description</dfn> -&gt; <var>string</var> (80) \"Our website is under construction. We'll be here soon with our new awesome s...<div class=\"access-path\">$value-&gt;maintenance_mode_description</div></dt><dd><pre>Our website is under construction. We'll be here soon with our new awesome site.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>maintenance_mode_status</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;maintenance_mode_status</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>maintenance_mode_title</dfn> -&gt; <var>string</var> (11) \"Coming Soon\"<div class=\"access-path\">$value-&gt;maintenance_mode_title</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>marketplace_system</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;marketplace_system</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>membership_plans_system</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;membership_plans_system</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>menu_limit</dfn> -&gt; <var>string</var> (1) \"9\"<div class=\"access-path\">$value-&gt;menu_limit</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>multilingual_system</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;multilingual_system</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>multi_vendor_system</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;multi_vendor_system</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>newsletter_image</dfn> -&gt; <var>string</var> (55) \"uploads/blocks/img_641c88699636a8-14633367-10107482.jpg\"<div class=\"access-path\">$value-&gt;newsletter_image</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (143.2KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.2KB Jun 28 00:53 uploads/blocks/img_641c88699636a8-14633367-10107482.jpg\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>newsletter_popup</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;newsletter_popup</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>newsletter_status</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;newsletter_status</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>physical_products_system</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;physical_products_system</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>product_comments</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;product_comments</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>product_link_structure</dfn> -&gt; <var>string</var> (7) \"slug-id\"<div class=\"access-path\">$value-&gt;product_link_structure</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>profile_number_of_sales</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;profile_number_of_sales</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>promoted_products</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;promoted_products</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>pwa_logo</dfn> -&gt; <var>string</var> (135) \"a:3:{s:2:\"lg\";s:26:\"assets/img/pwa/512x512.png\";s:2:\"md\";s:26:\"assets/img/pw...<div class=\"access-path\">$value-&gt;pwa_logo</div></dt><dd><pre>a:3:{s:2:\"lg\";s:26:\"assets/img/pwa/512x512.png\";s:2:\"md\";s:26:\"assets/img/pwa/192x192.png\";s:2:\"sm\";s:26:\"assets/img/pwa/144x144.png\";}\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>pwa_status</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;pwa_status</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>recaptcha_lang</dfn> -&gt; <var>string</var> (2) \"en\"<div class=\"access-path\">$value-&gt;recaptcha_lang</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>recaptcha_secret_key</dfn> -&gt; <var>string</var> (40) \"6LcvTFwrAAAAALzRElxkstR4DHhawroRy9RN2mdv\"<div class=\"access-path\">$value-&gt;recaptcha_secret_key</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(recaptcha_secret_key)</dfn> <var>binary string</var> (30)<div class=\"access-path\">base64_decode($value-&gt;recaptcha_secret_key)</div></dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>recaptcha_site_key</dfn> -&gt; <var>string</var> (40) \"6LcvTFwrAAAAABMTwBCPHHCWmMiCC5No21siB5ay\"<div class=\"access-path\">$value-&gt;recaptcha_site_key</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(recaptcha_site_key)</dfn> <var>binary string</var> (30)<div class=\"access-path\">base64_decode($value-&gt;recaptcha_site_key)</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>refresh_cache_database_changes</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;refresh_cache_database_changes</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>refund_system</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;refund_system</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>request_documents_vendors</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;request_documents_vendors</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>reviews</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;reviews</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>rss_system</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;rss_system</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>selected_navigation</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;selected_navigation</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>selling_license_keys_system</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;selling_license_keys_system</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_customer_email_seller</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;show_customer_email_seller</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_customer_phone_seller</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;show_customer_phone_seller</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_sold_products</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;show_sold_products</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_vendor_contact_information</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;show_vendor_contact_information</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>single_country_id</dfn> -&gt; <var>null</var><div class=\"access-path\">$value-&gt;single_country_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>single_country_mode</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;single_country_mode</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>site_color</dfn> -&gt; <var>string</var> (7) \"#5A31F4\"<div class=\"kint-color-preview\"><div style=\"background:rgba(90, 49, 244, 1)\"></div></div><div class=\"access-path\">$value-&gt;site_color</div></dt><dd><pre><dfn>#5A31F4</dfn>\n<dfn>rgb(90, 49, 244)</dfn>\n<dfn>hsl(252, 89%, 57%)</dfn>\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>site_lang</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;site_lang</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>slider_effect</dfn> -&gt; <var>string</var> (4) \"fade\"<div class=\"access-path\">$value-&gt;slider_effect</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>slider_status</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;slider_status</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>slider_type</dfn> -&gt; <var>string</var> (10) \"full_width\"<div class=\"access-path\">$value-&gt;slider_type</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>sort_categories</dfn> -&gt; <var>string</var> (4) \"date\"<div class=\"access-path\">$value-&gt;sort_categories</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>sort_parent_categories_by_order</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;sort_parent_categories_by_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>timezone</dfn> -&gt; <var>string</var> (12) \"Africa/Lagos\"<div class=\"access-path\">$value-&gt;timezone</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>vendors_change_shop_name</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;vendors_change_shop_name</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>vendor_bulk_product_upload</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;vendor_bulk_product_upload</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>vendor_verification_system</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;vendor_verification_system</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>version</dfn> -&gt; <var>string</var> (3) \"2.5\"<div class=\"access-path\">$value-&gt;version</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>vk_app_id</dfn> -&gt; <var>null</var><div class=\"access-path\">$value-&gt;vk_app_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>vk_secure_key</dfn> -&gt; <var>null</var><div class=\"access-path\">$value-&gt;vk_secure_key</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>watermark_blog_images</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;watermark_blog_images</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>watermark_font_size</dfn> -&gt; <var>string</var> (2) \"25\"<div class=\"access-path\">$value-&gt;watermark_font_size</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>watermark_hor_alignment</dfn> -&gt; <var>string</var> (6) \"center\"<div class=\"access-path\">$value-&gt;watermark_hor_alignment</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>watermark_product_images</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;watermark_product_images</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>watermark_text</dfn> -&gt; <var>string</var> (19) \"Posted on FiiQi.com\"<div class=\"access-path\">$value-&gt;watermark_text</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>watermark_thumbnail_images</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;watermark_thumbnail_images</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>watermark_vrt_alignment</dfn> -&gt; <var>string</var> (6) \"center\"<div class=\"access-path\">$value-&gt;watermark_vrt_alignment</div></dt></dl></li></ul></dd></dl></div>", "paymentSettings": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>stdClass</var>#83 (27)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (27)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>additional_invoice_info</dfn> -&gt; <var>null</var><div class=\"access-path\">$value-&gt;additional_invoice_info</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>allow_all_currencies_for_classied</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;allow_all_currencies_for_classied</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>auto_update_exchange_rates</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;auto_update_exchange_rates</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>bank_transfer_accounts</dfn> -&gt; <var>string</var> (336) \"&lt;ul&gt; &lt;li&gt;&lt;span style=\"text-decoration: underline;\"&gt;&lt;strong&gt;Account Number: 1...<div class=\"access-path\">$value-&gt;bank_transfer_accounts</div></dt><dd><pre>&lt;ul&gt;\r\n&lt;li&gt;&lt;span style=\"text-decoration: underline;\"&gt;&lt;strong&gt;Account Number: **********&lt;/strong&gt;&lt;/span&gt;&lt;/li&gt;\r\n&lt;li&gt;&lt;span style=\"text-decoration: underline;\"&gt;&lt;strong&gt;Account Name: TechWap Limited&lt;/strong&gt;&lt;/span&gt;&lt;/li&gt;\r\n&lt;li&gt;&lt;span style=\"text-decoration: underline;\"&gt;&lt;strong&gt;Bank Name: United Bank for Africa (UBA)&lt;/strong&gt;&lt;/span&gt;&lt;/li&gt;\r\n&lt;/ul&gt;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>bank_transfer_enabled</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;bank_transfer_enabled</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>cash_on_delivery_debt_limit</dfn> -&gt; <var>string</var> (4) \"1000\"<div class=\"access-path\">$value-&gt;cash_on_delivery_debt_limit</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>cash_on_delivery_enabled</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;cash_on_delivery_enabled</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>commission_rate</dfn> -&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value-&gt;commission_rate</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>currency_converter</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;currency_converter</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>currency_converter_api</dfn> -&gt; <var>string</var> (5) \"fixer\"<div class=\"access-path\">$value-&gt;currency_converter_api</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>currency_converter_api_key</dfn> -&gt; <var>string</var> (32) \"HvsNMx21QWQlrsPknbDmFov5fvHpQz4g\"<div class=\"access-path\">$value-&gt;currency_converter_api_key</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(currency_converter_api_key)</dfn> <var>binary string</var> (24)<div class=\"access-path\">base64_decode($value-&gt;currency_converter_api_key)</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>default_currency</dfn> -&gt; <var>string</var> (3) \"NGN\"<div class=\"access-path\">$value-&gt;default_currency</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>free_product_promotion</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;free_product_promotion</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>min_payout_bitcoin</dfn> -&gt; <var>string</var> (4) \"5000\"<div class=\"access-path\">$value-&gt;min_payout_bitcoin</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>min_payout_iban</dfn> -&gt; <var>string</var> (4) \"5000\"<div class=\"access-path\">$value-&gt;min_payout_iban</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>min_payout_paypal</dfn> -&gt; <var>string</var> (4) \"5000\"<div class=\"access-path\">$value-&gt;min_payout_paypal</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>min_payout_swift</dfn> -&gt; <var>string</var> (4) \"5000\"<div class=\"access-path\">$value-&gt;min_payout_swift</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>payout_bitcoin_enabled</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;payout_bitcoin_enabled</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>payout_iban_enabled</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;payout_iban_enabled</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>payout_paypal_enabled</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;payout_paypal_enabled</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>payout_swift_enabled</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;payout_swift_enabled</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>pay_with_wallet_balance</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;pay_with_wallet_balance</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>price_per_day</dfn> -&gt; <var>string</var> (5) \"20000\"<div class=\"access-path\">$value-&gt;price_per_day</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>price_per_month</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;price_per_month</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>vat_status</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;vat_status</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>wallet_deposit</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;wallet_deposit</div></dt></dl></li></ul></dd></dl></div>", "productSettings": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>stdClass</var>#85 (31)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (31)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>brand_status</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;brand_status</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>brand_where_to_display</dfn> -&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value-&gt;brand_where_to_display</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>classified_external_link</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;classified_external_link</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>classified_price</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;classified_price</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>classified_price_required</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;classified_price_required</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>classified_product_location</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;classified_product_location</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>digital_allowed_file_extensions</dfn> -&gt; <var>string</var> (42) \"\"zip\",\"doc\",\"pdf\",\"mp3\",\"mov\",\"mb4\",\"webp\"\"<div class=\"access-path\">$value-&gt;digital_allowed_file_extensions</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>digital_audio_preview</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;digital_audio_preview</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>digital_demo_url</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;digital_demo_url</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>digital_external_link</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;digital_external_link</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>digital_video_preview</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;digital_video_preview</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>image_file_format</dfn> -&gt; <var>string</var> (8) \"original\"<div class=\"access-path\">$value-&gt;image_file_format</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>is_brand_optional</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;is_brand_optional</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>is_product_image_required</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;is_product_image_required</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>marketplace_product_location</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;marketplace_product_location</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>marketplace_shipping</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;marketplace_shipping</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>marketplace_sku</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;marketplace_sku</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>marketplace_variations</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;marketplace_variations</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>max_file_size_audio</dfn> -&gt; <var>string</var> (9) \"104857600\"<div class=\"access-path\">$value-&gt;max_file_size_audio</div></dt><dd><pre>1973-04-28 15:06:40 UTC</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>max_file_size_image</dfn> -&gt; <var>string</var> (9) \"524288000\"<div class=\"access-path\">$value-&gt;max_file_size_image</div></dt><dd><pre>1986-08-13 03:33:20 UTC</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>max_file_size_video</dfn> -&gt; <var>string</var> (9) \"314572800\"<div class=\"access-path\">$value-&gt;max_file_size_video</div></dt><dd><pre>1979-12-20 21:20:00 UTC</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>pagination_per_page</dfn> -&gt; <var>string</var> (2) \"60\"<div class=\"access-path\">$value-&gt;pagination_per_page</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>physical_audio_preview</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;physical_audio_preview</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>physical_demo_url</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;physical_demo_url</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>physical_video_preview</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;physical_video_preview</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>product_image_limit</dfn> -&gt; <var>string</var> (2) \"20\"<div class=\"access-path\">$value-&gt;product_image_limit</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>sitemap_frequency</dfn> -&gt; <var>string</var> (6) \"hourly\"<div class=\"access-path\">$value-&gt;sitemap_frequency</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>sitemap_last_modification</dfn> -&gt; <var>string</var> (15) \"server_response\"<div class=\"access-path\">$value-&gt;sitemap_last_modification</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>sitemap_priority</dfn> -&gt; <var>string</var> (13) \"automatically\"<div class=\"access-path\">$value-&gt;sitemap_priority</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>sort_by_featured_products</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;sort_by_featured_products</div></dt></dl></li></ul></dd></dl></div>", "baseSettings": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>stdClass</var>#1679 (23)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (23)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>about_footer</dfn> -&gt; <var>UTF-8 string</var> (242) \"&lt;p&gt;&lt;span&gt;This innovative platform offers personalized storefronts, allowing ...<div class=\"access-path\">$value-&gt;about_footer</div></dt><dd><pre>&lt;p&gt;&lt;span&gt;This innovative platform offers personalized storefronts, allowing you to catalog your products and services for customers to purchase through various channels &#8211; phone call, in-web message, add to cart, or request quote.&lt;/span&gt;&lt;/p&gt;\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>affiliate_content</dfn> -&gt; <var>UTF-8 string</var> (1559) \"a:2:{s:5:\"title\";s:38:\"Why Join the tiQbar Affiliate Program?\";s:7:\"content\"...<div class=\"access-path\">$value-&gt;affiliate_content</div></dt><dd><pre>a:2:{s:5:\"title\";s:38:\"Why Join the tiQbar Affiliate Program?\";s:7:\"content\";s:1471:\"&lt;p&gt;Introducing the tiQbar Affiliate Program: Your Opportunity to Earn Big&lt;/p&gt;\r\n&lt;p&gt;tiQbar, a top-tier e-commerce platform renowned for its vast product selection and outstanding customer service, is excited to launch its Affiliate Program. This program is designed to help you earn significant commissions by promoting tiQbar&#8217;s products. Here&#8217;s why the tiQbar Affiliate Program is ideal for you:&lt;/p&gt;\r\n&lt;p&gt;1. Competitive Commission Rates&lt;br&gt;tiQbar offers generous commission rates, ensuring you&#8217;re well-compensated for every referral that leads to a purchase. The more you promote, the more you earn!&lt;/p&gt;\r\n&lt;p&gt;2. Extensive Product Selection&lt;br&gt;With tiQbar&#8217;s broad range of products, you can easily find items that align with your audience&#8217;s interests. Whether it&#8217;s tech, fashion, beauty, or home decor, there&#8217;s something for everyone.&lt;/p&gt;\r\n&lt;p&gt;3. User-Friendly Tools&lt;br&gt;The tiQbar Affiliate Program equips you with easy-to-use tools, including custom referral links and detailed performance reports, making it simple to track your progress and refine your strategies.&lt;/p&gt;\r\n&lt;p&gt;4. Dedicated Support&lt;br&gt;tiQbar values its affiliates and offers dedicated support to help you succeed. Whether you need guidance or tips to maximize your earnings, the support team is always available.&lt;/p&gt;\r\n&lt;p&gt;5. Prompt Payments&lt;br&gt;Your commissions are paid on time, every time. With a hassle-free payout process, you can focus on promoting quality products and earning commissions.&lt;/p&gt;\";}\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>affiliate_description</dfn> -&gt; <var>string</var> (291) \"a:2:{s:5:\"title\";s:53:\"Boost Your Earnings with the tiQbar Affiliate Program...<div class=\"access-path\">$value-&gt;affiliate_description</div></dt><dd><pre>a:2:{s:5:\"title\";s:53:\"Boost Your Earnings with the tiQbar Affiliate Program\";s:11:\"description\";s:184:\"Are you a content creator, blogger, influencer, or someone with a strong online presence? If so, tiQbar offers you an exciting opportunity to transform your influence into real income.\";}\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>affiliate_faq</dfn> -&gt; <var>UTF-8 string</var> (2088) \"a:8:{i:0;a:3:{s:1:\"o\";s:1:\"1\";s:1:\"q\";s:36:\"How do I join the Affiliate prog...<div class=\"access-path\">$value-&gt;affiliate_faq</div></dt><dd><pre>a:8:{i:0;a:3:{s:1:\"o\";s:1:\"1\";s:1:\"q\";s:36:\"How do I join the Affiliate program?\";s:1:\"a\";s:110:\"Simply click the \"Join Now\" button and fill out the registration form. Once approved, you can start promoting!\";}i:1;a:3:{s:1:\"o\";s:1:\"2\";s:1:\"q\";s:45:\"Who can participate in the Affiliate Program?\";s:1:\"a\";s:215:\"Anyone with an online presence, including bloggers, social media influencers, website owners, and content creators, can join the affiliate program. As long as you can promote our products, you&#8217;re welcome to apply!\";}i:2;a:3:{s:1:\"o\";s:1:\"3\";s:1:\"q\";s:40:\"Where can I generate my Affiliate links?\";s:1:\"a\";s:222:\"You can generate your affiliate links directly from any product detail page on our website. Once logged in, visit the product page you want to promote, and you&#8217;ll find an option to create your affiliate link right there.\";}i:3;a:3:{s:1:\"o\";s:1:\"4\";s:1:\"q\";s:28:\"What products can I promote?\";s:1:\"a\";s:162:\"You can promote any product from our store that is included in the affiliate program and earn commission on any qualifying sales made through your affiliate link.\";}i:4;a:3:{s:1:\"o\";s:1:\"5\";s:1:\"q\";s:46:\"How long is the validity of an Affiliate link?\";s:1:\"a\";s:211:\"An affiliate link is valid for 30 days from the moment a person clicks on it and opens the product page. If the product is purchased during this period, the affiliate commission will be applied for that product.\";}i:5;a:3:{s:1:\"o\";s:1:\"6\";s:1:\"q\";s:20:\"How much can I earn?\";s:1:\"a\";s:120:\"There is no limit to how much you can earn. Your earnings depend on the sales you generate through your affiliate links.\";}i:6;a:3:{s:1:\"o\";s:1:\"7\";s:1:\"q\";s:37:\"How do I track my Affiliate earnings?\";s:1:\"a\";s:96:\"You can track your affiliate program earnings in the \"Referral Earnings\" section of your wallet.\";}i:7;a:3:{s:1:\"o\";s:1:\"8\";s:1:\"q\";s:35:\"How do I get my Affiliate earnings?\";s:1:\"a\";s:188:\"Once your earnings exceed the minimum payout limit, you can request a payment from the \"Payouts\" section of your wallet. Simply submit a payout request, and your payment will be processed.\";}}\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>affiliate_works</dfn> -&gt; <var>string</var> (467) \"a:3:{i:0;a:2:{s:5:\"title\";s:23:\"Sign up for the program\";s:11:\"description\";...<div class=\"access-path\">$value-&gt;affiliate_works</div></dt><dd><pre>a:3:{i:0;a:2:{s:5:\"title\";s:23:\"Sign up for the program\";s:11:\"description\";s:77:\"Join the Modesy affiliate program by completing a simple registration process\";}i:1;a:2:{s:5:\"title\";s:34:\"Create and share your referral URL\";s:11:\"description\";s:77:\"Generate a referral URL and share it on your website, email, or social media.\";}i:2;a:2:{s:5:\"title\";s:15:\"Earn commission\";s:11:\"description\";s:64:\"Earn commissions on every sale made through your affiliate links\";}}\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>bulk_upload_documentation</dfn> -&gt; <var>string</var> (4597) \"&lt;p&gt;With the bulk product upload feature, you can upload your products in bul...<div class=\"access-path\">$value-&gt;bulk_upload_documentation</div></dt><dd><pre>&lt;p&gt;With the bulk product upload feature, you can upload your products in bulk with the help of a CSV file.&lt;br&gt;&lt;br&gt;Bulk upload has options to add new products and edit existing products:&lt;br&gt;&lt;br&gt;&lt;strong&gt;Add Products: &lt;/strong&gt;To add new products, download the CSV template, add your products to this CSV file and upload it from this section. You can see detailed explanations of all required or optional columns in the table below. When adding your data, you need to pay attention to the data type of these columns.&lt;br&gt;&lt;br&gt;&lt;strong&gt;Edit Products: &lt;/strong&gt;To edit products, you need to add an \"id\" column to the CSV template. You can see the ID numbers of your products on the \"products\" page. After adding the \"id\" column, you need to add the column names you want to edit. &lt;br&gt;For example, if you want to update the stock and prices of your products, your CSV template should be like this:&lt;br&gt;&lt;span style=\"color: rgb(35, 111, 161);\"&gt;\"id\",\"price\",\"price_discounted\",\"stock\"&lt;/span&gt;&lt;br&gt;&lt;br&gt;Example:&lt;br&gt;&lt;span style=\"color: rgb(35, 111, 161);\"&gt;\"id\",\"price\",\"price_discounted\",\"stock\"&lt;/span&gt;&lt;br&gt;&lt;span style=\"color: rgb(132, 63, 161);\"&gt;\"1\",\"30\",\"20\",\"1000\"&lt;/span&gt;&lt;br&gt;&lt;span style=\"color: rgb(132, 63, 161);\"&gt;\"5\",\"40\",\"40\",\"500\"&lt;/span&gt;&lt;br&gt;&lt;br&gt;&lt;span style=\"color: rgb(186, 55, 42);\"&gt;* To update the product price, you need to add both \"price\" and \"price_discounted\" columns to your CSV file.&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;/span&gt;&lt;/p&gt;\r\n&lt;p&gt;&lt;span style=\"font-size: 12pt;\"&gt;&lt;strong&gt;CSV Columns&lt;/strong&gt;&lt;/span&gt;&lt;/p&gt;\r\n&lt;table style=\"width: 100%;\" class=\"table table-bordered\"&gt;\r\n&lt;tbody&gt;\r\n&lt;tr&gt;\r\n&lt;th&gt;Column&lt;/th&gt;\r\n&lt;th&gt;Description&lt;/th&gt;\r\n&lt;/tr&gt;\r\n&lt;tr&gt;\r\n&lt;td style=\"width: 180px;\"&gt;title&lt;/td&gt;\r\n&lt;td&gt;Data Type: Text &lt;br&gt;&lt;strong&gt;Required&lt;/strong&gt;&lt;br&gt;Example: Modern grey couch and pillows&lt;/td&gt;\r\n&lt;/tr&gt;\r\n&lt;tr&gt;\r\n&lt;td style=\"width: 180px;\"&gt;slug&lt;/td&gt;\r\n&lt;td&gt;Data Type: Text &lt;br&gt;&lt;strong&gt;Optional&lt;/strong&gt; &lt;small&gt;(If you leave it empty, it will be generated automatically.)&lt;/small&gt; &lt;br&gt;Example: modern-grey-couch-and-pillows&lt;/td&gt;\r\n&lt;/tr&gt;\r\n&lt;tr&gt;\r\n&lt;td style=\"width: 180px;\"&gt;sku&lt;/td&gt;\r\n&lt;td&gt;Data Type: Text &lt;br&gt;&lt;strong&gt;Optional&lt;/strong&gt;&lt;br&gt;Example: MD-GR-6898&lt;/td&gt;\r\n&lt;/tr&gt;\r\n&lt;tr&gt;\r\n&lt;td style=\"width: 180px;\"&gt;category_id&lt;/td&gt;\r\n&lt;td&gt;Data Type: Number &lt;br&gt;&lt;strong&gt;Required&lt;/strong&gt;&lt;br&gt;Example: 1&lt;/td&gt;\r\n&lt;/tr&gt;\r\n&lt;tr&gt;\r\n&lt;td style=\"width: 180px;\"&gt;price&lt;/td&gt;\r\n&lt;td&gt;Data Type: Decimal/Number &lt;br&gt;&lt;strong&gt;Required&lt;/strong&gt;&lt;br&gt;Example 1: 50&lt;br&gt;Example 2: 45.90&lt;br&gt;Example 3: 3456.25&lt;/td&gt;\r\n&lt;/tr&gt;\r\n&lt;tr&gt;\r\n&lt;td style=\"width: 180px;\"&gt;price_discounted&lt;/td&gt;\r\n&lt;td&gt;Data Type: Decimal/Number &lt;br&gt;&lt;strong&gt;Optional&lt;/strong&gt;&lt;br&gt;Example 1: 40&lt;br&gt;Example 2: 35.90&lt;br&gt;Example 3: 2456.25&lt;/td&gt;\r\n&lt;/tr&gt;\r\n&lt;tr&gt;\r\n&lt;td style=\"width: 180px;\"&gt;vat_rate&lt;/td&gt;\r\n&lt;td&gt;Data Type: Number &lt;br&gt;&lt;strong&gt;Optional&lt;/strong&gt;&lt;br&gt;Example: 8&lt;/td&gt;\r\n&lt;/tr&gt;\r\n&lt;tr&gt;\r\n&lt;td style=\"width: 180px;\"&gt;stock&lt;/td&gt;\r\n&lt;td&gt;Data Type: Number &lt;br&gt;&lt;strong&gt;Required&lt;/strong&gt;&lt;br&gt;Example: 100&lt;/td&gt;\r\n&lt;/tr&gt;\r\n&lt;tr&gt;\r\n&lt;td style=\"width: 180px;\"&gt;short_description&lt;/td&gt;\r\n&lt;td&gt;Data Type: Text &lt;br&gt;&lt;strong&gt;Optional&lt;/strong&gt;&lt;br&gt;Example: It is a nice and comfortable couch&lt;/td&gt;\r\n&lt;/tr&gt;\r\n&lt;tr&gt;\r\n&lt;td style=\"width: 180px;\"&gt;description&lt;/td&gt;\r\n&lt;td&gt;Data Type: Text &lt;br&gt;&lt;strong&gt;Optional&lt;/strong&gt;&lt;br&gt;Example: It is a nice and comfortable couch...&lt;/td&gt;\r\n&lt;/tr&gt;\r\n&lt;tr&gt;\r\n&lt;td style=\"width: 180px;\"&gt;tags&lt;/td&gt;\r\n&lt;td&gt;Data Type: Text &lt;br&gt;&lt;strong&gt;Optional&lt;/strong&gt;&lt;br&gt;Example: nice, comfortable, couch&lt;/td&gt;\r\n&lt;/tr&gt;\r\n&lt;tr&gt;\r\n&lt;td style=\"width: 180px;\"&gt;image_url&lt;/td&gt;\r\n&lt;td&gt;Data Type: Text &lt;br&gt;&lt;strong&gt;Optional&lt;/strong&gt;&lt;br&gt;Example 1:&lt;br&gt;https://upload.wikimedia.org/wikipedia/commons/7/70/Labrador-sea-paamiut.jpg&lt;br&gt;&lt;br&gt;Example 2:&lt;br&gt;https://upload.wikimedia.org/wikipedia/commons/7/70/Labrador-sea-paamiut.jpg,&lt;br&gt;https://upload.wikimedia.org/wikipedia/commons/thumb/4/42/Shaqi_jrvej.jpg/1600px-Shaqi_jrvej.jpg&lt;br&gt;&lt;br&gt;&lt;span style=\"color: rgb(186, 55, 42);\"&gt;*You can add multiple image links by placing commas between them.&lt;/span&gt;&lt;/td&gt;\r\n&lt;/tr&gt;\r\n&lt;tr&gt;\r\n&lt;td style=\"width: 180px;\"&gt;external_link&lt;/td&gt;\r\n&lt;td&gt;Data Type: Text &lt;br&gt;&lt;strong&gt;Optional&lt;/strong&gt;&lt;br&gt;Example: https://domain.com/product_url&lt;/td&gt;\r\n&lt;/tr&gt;\r\n&lt;tr&gt;\r\n&lt;td style=\"width: 180px;\"&gt;updated_at&lt;/td&gt;\r\n&lt;td&gt;Data Type: Timestamp &lt;br&gt;&lt;strong&gt;Optional&lt;/strong&gt;&lt;br&gt;Example: 2024-06-30 10:27:00 &lt;br&gt;&lt;br&gt;&lt;span style=\"color: rgb(186, 55, 42);\"&gt;*If you leave it blank, the system will not assign an update date.&lt;/span&gt;&lt;/td&gt;\r\n&lt;/tr&gt;\r\n&lt;tr&gt;\r\n&lt;td style=\"width: 180px;\"&gt;created_at&lt;/td&gt;\r\n&lt;td&gt;Data Type: Timestamp &lt;br&gt;&lt;strong&gt;Optional&lt;/strong&gt;&lt;br&gt;Example: 2024-06-30 10:27:00 &lt;br&gt;&lt;br&gt;&lt;span style=\"color: rgb(186, 55, 42);\"&gt;*If you leave it blank, the system will automatically assign the current date.&lt;/span&gt;&lt;/td&gt;\r\n&lt;/tr&gt;\r\n&lt;/tbody&gt;\r\n&lt;/table&gt;\r\n&lt;p&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;/p&gt;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>contact_address</dfn> -&gt; <var>string</var> (14) \"Lagos, Nigeria\"<div class=\"access-path\">$value-&gt;contact_address</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>contact_email</dfn> -&gt; <var>string</var> (18) \"<EMAIL>\"<div class=\"access-path\">$value-&gt;contact_email</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>contact_phone</dfn> -&gt; <var>string</var> (14) \"+2348161617725\"<div class=\"access-path\">$value-&gt;contact_phone</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>contact_text</dfn> -&gt; <var>string</var> (36) \"&lt;p&gt;Feel free to reach out to us.&lt;/p&gt;\"<div class=\"access-path\">$value-&gt;contact_text</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>cookies_warning</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;cookies_warning</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>cookies_warning_text</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value-&gt;cookies_warning_text</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>copyright</dfn> -&gt; <var>UTF-8 string</var> (35) \"&#169; 2023 FiiQi - &#7864;&#768;R&#7864;&#768; AD&#218;R&#192;\"<div class=\"access-path\">$value-&gt;copyright</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>created_at</dfn> -&gt; <var>string</var> (19) \"2021-02-21 05:51:50\"<div class=\"access-path\">$value-&gt;created_at</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>dashboard_font</dfn> -&gt; <var>string</var> (2) \"13\"<div class=\"access-path\">$value-&gt;dashboard_font</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>homepage_title</dfn> -&gt; <var>string</var> (113) \"FiiQi: Open a Storefront free of charge, Connecting Services, Buyers, and Se...<div class=\"access-path\">$value-&gt;homepage_title</div></dt><dd><pre>FiiQi: Open a Storefront free of charge, Connecting Services, Buyers, and Sellers in Nigeria's Thriving Community\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;id</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>keywords</dfn> -&gt; <var>string</var> (399) \"storefronts, community, marketing, platform, Nigerians, Nigeria, social comm...<div class=\"access-path\">$value-&gt;keywords</div></dt><dd><pre>storefronts, community, marketing, platform, Nigerians, Nigeria, social commerce, marketplace, buyers, sellers, connecting, hub, empowerment, nationwide, Naija, first, premier, discover, unite, empower, social, commerce, shoes, bags, clothes, cars, lands, properties, animal, new and used, used, trade, trading, phones, android, used android, used iphones, new phones, new android, new iphone, FiiQi\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>lang_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;lang_id</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>site_description</dfn> -&gt; <var>string</var> (222) \"Revolutionizing E-commerce and Service Provision Across the Globe Are you a ...<div class=\"access-path\">$value-&gt;site_description</div></dt><dd><pre>Revolutionizing E-commerce and Service Provision Across the Globe Are you a service provider, e-commerce vendor, or artisan looking for a platform to showcase and sell your products and services? Look no further than FiiQi\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>site_font</dfn> -&gt; <var>string</var> (2) \"31\"<div class=\"access-path\">$value-&gt;site_font</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>site_title</dfn> -&gt; <var>string</var> (64) \"Naija's Premier Community Marketing Portal: Empowering Nigerians\"<div class=\"access-path\">$value-&gt;site_title</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>social_media_data</dfn> -&gt; <var>string</var> (475) \"a:7:{s:12:\"facebook_url\";s:29:\"https://facebook.com/usefiiqi\";s:11:\"twitter_...<div class=\"access-path\">$value-&gt;social_media_data</div></dt><dd><pre>a:7:{s:12:\"facebook_url\";s:29:\"https://facebook.com/usefiiqi\";s:11:\"twitter_url\";s:28:\"https://twitter.com/usefiiqi\";s:13:\"instagram_url\";s:30:\"https://instagram.com/usefiiqi\";s:12:\"whatsapp_url\";s:48:\"https://chat.whatsapp.com/HWKsZOCPtuQ4dZFcmO8gx9\";s:11:\"youtube_url\";s:30:\"https://www.youtube.com/@fiiqi\";s:12:\"telegram_url\";s:18:\"https://t.me/fiiqi\";s:12:\"linkedin_url\";s:91:\"https://www.linkedin.com/company/tiqbar-community-for-buyers-sellers-and-service-providers/\";}\n</pre></dd></dl></li></ul></dd></dl></div>", "activeLanguages": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>stdClass</var>#168 (9)<div class=\"access-path\">$value[0]</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (9)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>flag_path</dfn> -&gt; <var>string</var> (27) \"uploads/blocks/flag_eng.jpg\"<div class=\"access-path\">$value[0]-&gt;flag_path</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (4.7KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.7KB Jun 28 00:53 uploads/blocks/flag_eng.jpg\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]-&gt;id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>language_code</dfn> -&gt; <var>string</var> (5) \"en-US\"<div class=\"access-path\">$value[0]-&gt;language_code</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>language_order</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]-&gt;language_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>name</dfn> -&gt; <var>string</var> (7) \"English\"<div class=\"access-path\">$value[0]-&gt;name</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>short_form</dfn> -&gt; <var>string</var> (2) \"en\"<div class=\"access-path\">$value[0]-&gt;short_form</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>status</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]-&gt;status</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>text_direction</dfn> -&gt; <var>string</var> (3) \"ltr\"<div class=\"access-path\">$value[0]-&gt;text_direction</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>text_editor_lang</dfn> -&gt; <var>string</var> (2) \"en\"<div class=\"access-path\">$value[0]-&gt;text_editor_lang</div></dt></dl></li></ul></dd></dl></dd></dl></div>", "activeLang": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>stdClass</var>#168 (9)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (9)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>flag_path</dfn> -&gt; <var>string</var> (27) \"uploads/blocks/flag_eng.jpg\"<div class=\"access-path\">$value-&gt;flag_path</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (4.7KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.7KB Jun 28 00:53 uploads/blocks/flag_eng.jpg\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>language_code</dfn> -&gt; <var>string</var> (5) \"en-US\"<div class=\"access-path\">$value-&gt;language_code</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>language_order</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;language_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>name</dfn> -&gt; <var>string</var> (7) \"English\"<div class=\"access-path\">$value-&gt;name</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>short_form</dfn> -&gt; <var>string</var> (2) \"en\"<div class=\"access-path\">$value-&gt;short_form</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>status</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;status</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>text_direction</dfn> -&gt; <var>string</var> (3) \"ltr\"<div class=\"access-path\">$value-&gt;text_direction</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>text_editor_lang</dfn> -&gt; <var>string</var> (2) \"en\"<div class=\"access-path\">$value-&gt;text_editor_lang</div></dt></dl></li></ul></dd></dl></div>", "defaultCurrency": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>stdClass</var>#1674 (9)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (9)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>code</dfn> -&gt; <var>string</var> (3) \"NGN\"<div class=\"access-path\">$value-&gt;code</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>currency_format</dfn> -&gt; <var>string</var> (2) \"us\"<div class=\"access-path\">$value-&gt;currency_format</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>exchange_rate</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;exchange_rate</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>id</dfn> -&gt; <var>string</var> (3) \"102\"<div class=\"access-path\">$value-&gt;id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>name</dfn> -&gt; <var>string</var> (13) \"Nigeria Naira\"<div class=\"access-path\">$value-&gt;name</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>space_money_symbol</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value-&gt;space_money_symbol</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>status</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value-&gt;status</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>symbol</dfn> -&gt; <var>UTF-8 string</var> (3) \"&#8358;\"<div class=\"access-path\">$value-&gt;symbol</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>symbol_direction</dfn> -&gt; <var>string</var> (4) \"left\"<div class=\"access-path\">$value-&gt;symbol_direction</div></dt></dl></li></ul></dd></dl></div>", "baseVars": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>stdClass</var>#128 (3)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>inputInitialPrice</dfn> -&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value-&gt;inputInitialPrice</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>rtl</dfn> -&gt; <var>boolean</var> false<div class=\"access-path\">$value-&gt;rtl</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>thousandsSeparator</dfn> -&gt; <var>string</var> (1) \".\"<div class=\"access-path\">$value-&gt;thousandsSeparator</div></dt></dl></li></ul></dd></dl></div>", "categories": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (7)</dt><dd><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>stdClass</var>#118 (26)<div class=\"access-path\">$value[0]</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (26)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>category_order</dfn> -&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]-&gt;category_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>created_at</dfn> -&gt; <var>string</var> (19) \"2022-12-04 18:53:05\"<div class=\"access-path\">$value[0]-&gt;created_at</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>description</dfn> -&gt; <var>string</var> (53) \"buy clean used and new affordable cars on tradejoints\"<div class=\"access-path\">$value[0]-&gt;description</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>featured_order</dfn> -&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]-&gt;featured_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>has_subcategory</dfn> -&gt; <var>null</var><div class=\"access-path\">$value[0]-&gt;has_subcategory</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>homepage_order</dfn> -&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[0]-&gt;homepage_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>id</dfn> -&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]-&gt;id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>image</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]-&gt;image</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>is_featured</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[0]-&gt;is_featured</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>join_parent_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]-&gt;join_parent_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>keywords</dfn> -&gt; <var>string</var> (31) \"cars,motor,jeep,affordable cars\"<div class=\"access-path\">$value[0]-&gt;keywords</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>level</dfn> -&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]-&gt;level</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>name</dfn> -&gt; <var>string</var> (8) \"1:::Cars\"<div class=\"access-path\">$value[0]-&gt;name</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]-&gt;parent_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_slug</dfn> -&gt; <var>string</var> (12) \"automobile-1\"<div class=\"access-path\">$value[0]-&gt;parent_slug</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_tree</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]-&gt;parent_tree</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_description</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[0]-&gt;show_description</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_image_on_main_menu</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[0]-&gt;show_image_on_main_menu</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_on_main_menu</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]-&gt;show_on_main_menu</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_products_on_index</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[0]-&gt;show_products_on_index</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_subcategory_products</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[0]-&gt;show_subcategory_products</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>slug</dfn> -&gt; <var>string</var> (4) \"cars\"<div class=\"access-path\">$value[0]-&gt;slug</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>storage</dfn> -&gt; <var>string</var> (5) \"local\"<div class=\"access-path\">$value[0]-&gt;storage</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>title_meta_tag</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]-&gt;title_meta_tag</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>tree_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]-&gt;tree_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>visibility</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]-&gt;visibility</div></dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>1</dfn> =&gt; <var>stdClass</var>#117 (26)<div class=\"access-path\">$value[1]</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (26)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>category_order</dfn> -&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[1]-&gt;category_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>created_at</dfn> -&gt; <var>string</var> (19) \"2022-12-04 18:53:08\"<div class=\"access-path\">$value[1]-&gt;created_at</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>description</dfn> -&gt; <var>string</var> (44) \"Buy affordable machines and heavy equipment \"<div class=\"access-path\">$value[1]-&gt;description</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>featured_order</dfn> -&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[1]-&gt;featured_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>has_subcategory</dfn> -&gt; <var>null</var><div class=\"access-path\">$value[1]-&gt;has_subcategory</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>homepage_order</dfn> -&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[1]-&gt;homepage_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>id</dfn> -&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[1]-&gt;id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>image</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]-&gt;image</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>is_featured</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[1]-&gt;is_featured</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>join_parent_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]-&gt;join_parent_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>keywords</dfn> -&gt; <var>string</var> (40) \"equipment,heavy irons,buy equipment here\"<div class=\"access-path\">$value[1]-&gt;keywords</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>level</dfn> -&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]-&gt;level</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>name</dfn> -&gt; <var>string</var> (20) \"1:::Heavy Equipments\"<div class=\"access-path\">$value[1]-&gt;name</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]-&gt;parent_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_slug</dfn> -&gt; <var>string</var> (12) \"automobile-1\"<div class=\"access-path\">$value[1]-&gt;parent_slug</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_tree</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]-&gt;parent_tree</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_description</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[1]-&gt;show_description</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_image_on_main_menu</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[1]-&gt;show_image_on_main_menu</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_on_main_menu</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]-&gt;show_on_main_menu</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_products_on_index</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[1]-&gt;show_products_on_index</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_subcategory_products</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[1]-&gt;show_subcategory_products</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>slug</dfn> -&gt; <var>string</var> (16) \"heavy-equipments\"<div class=\"access-path\">$value[1]-&gt;slug</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>storage</dfn> -&gt; <var>string</var> (5) \"local\"<div class=\"access-path\">$value[1]-&gt;storage</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>title_meta_tag</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]-&gt;title_meta_tag</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>tree_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]-&gt;tree_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>visibility</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]-&gt;visibility</div></dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>2</dfn> =&gt; <var>stdClass</var>#116 (26)<div class=\"access-path\">$value[2]</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (26)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>category_order</dfn> -&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[2]-&gt;category_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>created_at</dfn> -&gt; <var>string</var> (19) \"2022-12-04 18:53:10\"<div class=\"access-path\">$value[2]-&gt;created_at</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>description</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]-&gt;description</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>featured_order</dfn> -&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[2]-&gt;featured_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>has_subcategory</dfn> -&gt; <var>null</var><div class=\"access-path\">$value[2]-&gt;has_subcategory</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>homepage_order</dfn> -&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[2]-&gt;homepage_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>id</dfn> -&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[2]-&gt;id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>image</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]-&gt;image</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>is_featured</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[2]-&gt;is_featured</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>join_parent_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]-&gt;join_parent_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>keywords</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]-&gt;keywords</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>level</dfn> -&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]-&gt;level</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>name</dfn> -&gt; <var>string</var> (26) \"1:::Motorcycles &amp; Scooters\"<div class=\"access-path\">$value[2]-&gt;name</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]-&gt;parent_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_slug</dfn> -&gt; <var>string</var> (12) \"automobile-1\"<div class=\"access-path\">$value[2]-&gt;parent_slug</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_tree</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]-&gt;parent_tree</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_description</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[2]-&gt;show_description</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_image_on_main_menu</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[2]-&gt;show_image_on_main_menu</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_on_main_menu</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]-&gt;show_on_main_menu</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_products_on_index</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[2]-&gt;show_products_on_index</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_subcategory_products</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[2]-&gt;show_subcategory_products</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>slug</dfn> -&gt; <var>string</var> (20) \"motorcycles-scooters\"<div class=\"access-path\">$value[2]-&gt;slug</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>storage</dfn> -&gt; <var>string</var> (5) \"local\"<div class=\"access-path\">$value[2]-&gt;storage</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>title_meta_tag</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]-&gt;title_meta_tag</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>tree_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]-&gt;tree_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>visibility</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]-&gt;visibility</div></dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>3</dfn> =&gt; <var>stdClass</var>#115 (26)<div class=\"access-path\">$value[3]</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (26)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>category_order</dfn> -&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[3]-&gt;category_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>created_at</dfn> -&gt; <var>string</var> (19) \"2022-12-04 18:53:12\"<div class=\"access-path\">$value[3]-&gt;created_at</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>description</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]-&gt;description</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>featured_order</dfn> -&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[3]-&gt;featured_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>has_subcategory</dfn> -&gt; <var>null</var><div class=\"access-path\">$value[3]-&gt;has_subcategory</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>homepage_order</dfn> -&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[3]-&gt;homepage_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>id</dfn> -&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[3]-&gt;id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>image</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]-&gt;image</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>is_featured</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[3]-&gt;is_featured</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>join_parent_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]-&gt;join_parent_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>keywords</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]-&gt;keywords</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>level</dfn> -&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[3]-&gt;level</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>name</dfn> -&gt; <var>string</var> (19) \"1:::Rental Vehicles\"<div class=\"access-path\">$value[3]-&gt;name</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]-&gt;parent_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_slug</dfn> -&gt; <var>string</var> (12) \"automobile-1\"<div class=\"access-path\">$value[3]-&gt;parent_slug</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_tree</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]-&gt;parent_tree</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_description</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[3]-&gt;show_description</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_image_on_main_menu</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[3]-&gt;show_image_on_main_menu</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_on_main_menu</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]-&gt;show_on_main_menu</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_products_on_index</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[3]-&gt;show_products_on_index</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_subcategory_products</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[3]-&gt;show_subcategory_products</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>slug</dfn> -&gt; <var>string</var> (15) \"rental-vehicles\"<div class=\"access-path\">$value[3]-&gt;slug</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>storage</dfn> -&gt; <var>string</var> (5) \"local\"<div class=\"access-path\">$value[3]-&gt;storage</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>title_meta_tag</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]-&gt;title_meta_tag</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>tree_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]-&gt;tree_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>visibility</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]-&gt;visibility</div></dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>4</dfn> =&gt; <var>stdClass</var>#114 (26)<div class=\"access-path\">$value[4]</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (26)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>category_order</dfn> -&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[4]-&gt;category_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>created_at</dfn> -&gt; <var>string</var> (19) \"2022-12-04 18:53:14\"<div class=\"access-path\">$value[4]-&gt;created_at</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>description</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]-&gt;description</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>featured_order</dfn> -&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[4]-&gt;featured_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>has_subcategory</dfn> -&gt; <var>null</var><div class=\"access-path\">$value[4]-&gt;has_subcategory</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>homepage_order</dfn> -&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[4]-&gt;homepage_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>id</dfn> -&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[4]-&gt;id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>image</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]-&gt;image</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>is_featured</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[4]-&gt;is_featured</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>join_parent_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]-&gt;join_parent_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>keywords</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]-&gt;keywords</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>level</dfn> -&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[4]-&gt;level</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>name</dfn> -&gt; <var>string</var> (9) \"1:::Bikes\"<div class=\"access-path\">$value[4]-&gt;name</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]-&gt;parent_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_slug</dfn> -&gt; <var>string</var> (12) \"automobile-1\"<div class=\"access-path\">$value[4]-&gt;parent_slug</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_tree</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]-&gt;parent_tree</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_description</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[4]-&gt;show_description</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_image_on_main_menu</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[4]-&gt;show_image_on_main_menu</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_on_main_menu</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]-&gt;show_on_main_menu</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_products_on_index</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[4]-&gt;show_products_on_index</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_subcategory_products</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[4]-&gt;show_subcategory_products</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>slug</dfn> -&gt; <var>string</var> (5) \"bikes\"<div class=\"access-path\">$value[4]-&gt;slug</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>storage</dfn> -&gt; <var>string</var> (5) \"local\"<div class=\"access-path\">$value[4]-&gt;storage</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>title_meta_tag</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]-&gt;title_meta_tag</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>tree_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]-&gt;tree_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>visibility</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]-&gt;visibility</div></dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>5</dfn> =&gt; <var>stdClass</var>#113 (26)<div class=\"access-path\">$value[5]</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (26)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>category_order</dfn> -&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[5]-&gt;category_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>created_at</dfn> -&gt; <var>string</var> (19) \"2022-12-04 18:53:16\"<div class=\"access-path\">$value[5]-&gt;created_at</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>description</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]-&gt;description</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>featured_order</dfn> -&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[5]-&gt;featured_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>has_subcategory</dfn> -&gt; <var>null</var><div class=\"access-path\">$value[5]-&gt;has_subcategory</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>homepage_order</dfn> -&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[5]-&gt;homepage_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>id</dfn> -&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[5]-&gt;id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>image</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]-&gt;image</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>is_featured</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[5]-&gt;is_featured</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>join_parent_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]-&gt;join_parent_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>keywords</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]-&gt;keywords</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>level</dfn> -&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[5]-&gt;level</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><var>public</var> <dfn>name</dfn> -&gt; <var>string</var> (31) \"1:::Spare parts &amp; Accessories \"<div class=\"access-path\">$value[5]-&gt;name</div></dt><dd><pre>1:::Spare parts &amp; Accessories  \n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]-&gt;parent_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_slug</dfn> -&gt; <var>string</var> (12) \"automobile-1\"<div class=\"access-path\">$value[5]-&gt;parent_slug</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_tree</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]-&gt;parent_tree</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_description</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[5]-&gt;show_description</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_image_on_main_menu</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[5]-&gt;show_image_on_main_menu</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_on_main_menu</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]-&gt;show_on_main_menu</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_products_on_index</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[5]-&gt;show_products_on_index</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_subcategory_products</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[5]-&gt;show_subcategory_products</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>slug</dfn> -&gt; <var>string</var> (23) \"spare-parts-accessories\"<div class=\"access-path\">$value[5]-&gt;slug</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>storage</dfn> -&gt; <var>string</var> (5) \"local\"<div class=\"access-path\">$value[5]-&gt;storage</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>title_meta_tag</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]-&gt;title_meta_tag</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>tree_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]-&gt;tree_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>visibility</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]-&gt;visibility</div></dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>6</dfn> =&gt; <var>stdClass</var>#112 (26)<div class=\"access-path\">$value[6]</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (26)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>category_order</dfn> -&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[6]-&gt;category_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>created_at</dfn> -&gt; <var>string</var> (19) \"2022-12-04 18:53:18\"<div class=\"access-path\">$value[6]-&gt;created_at</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>description</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]-&gt;description</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>featured_order</dfn> -&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[6]-&gt;featured_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>has_subcategory</dfn> -&gt; <var>null</var><div class=\"access-path\">$value[6]-&gt;has_subcategory</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>homepage_order</dfn> -&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[6]-&gt;homepage_order</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>id</dfn> -&gt; <var>string</var> (1) \"9\"<div class=\"access-path\">$value[6]-&gt;id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>image</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]-&gt;image</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>is_featured</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[6]-&gt;is_featured</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>join_parent_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[6]-&gt;join_parent_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>keywords</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]-&gt;keywords</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>level</dfn> -&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[6]-&gt;level</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>name</dfn> -&gt; <var>string</var> (19) \"1:::Boats &amp; Sailing\"<div class=\"access-path\">$value[6]-&gt;name</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[6]-&gt;parent_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_slug</dfn> -&gt; <var>string</var> (12) \"automobile-1\"<div class=\"access-path\">$value[6]-&gt;parent_slug</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>parent_tree</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[6]-&gt;parent_tree</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_description</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[6]-&gt;show_description</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_image_on_main_menu</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[6]-&gt;show_image_on_main_menu</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_on_main_menu</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[6]-&gt;show_on_main_menu</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_products_on_index</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[6]-&gt;show_products_on_index</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>show_subcategory_products</dfn> -&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[6]-&gt;show_subcategory_products</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>slug</dfn> -&gt; <var>string</var> (13) \"boats-sailing\"<div class=\"access-path\">$value[6]-&gt;slug</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>storage</dfn> -&gt; <var>string</var> (5) \"local\"<div class=\"access-path\">$value[6]-&gt;storage</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>title_meta_tag</dfn> -&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]-&gt;title_meta_tag</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>tree_id</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[6]-&gt;tree_id</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><var>public</var> <dfn>visibility</dfn> -&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[6]-&gt;visibility</div></dt></dl></li></ul></dd></dl></dd></dl></div>", "padding": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>boolean</var> true</dt></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>1751121769</pre>", "_ci_previous_url": "http://web.test/admin/categories", "mds_ses_user_id": "1", "mds_ses_role_id": "1", "mds_ses_pass": "2a16380afebc16231bad776123802c79"}, "post": {"sysLangId": "1", "id": "1", "lang_id": "1"}, "headers": {"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8", "Host": "web.test", "Connection": "keep-alive", "X-Requested-With": "XMLHttpRequest", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "*/*", "Origin": "http://web.test", "Referer": "http://web.test/admin/categories", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9,vi;q=0.8", "Cookie": "mds_session=6vcro1fguv6ntunps7hsp0motgmkbgcg; mds_nws_popup=1; mds_csrf_cookie=c74b977b067c29692adbfe1408133c8c"}, "cookies": {"mds_session": "6vcro1fguv6ntunps7hsp0motgmkbgcg", "mds_nws_popup": "1", "mds_csrf_cookie": "c74b977b067c29692adbfe1408133c8c"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8", "Content-Security-Policy": "frame-ancestors &#039;none&#039;;"}}}, "config": {"ciVersion": "4.5.4", "phpVersion": "8.1.10", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://web.test/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}