INFO - 2025-06-28 00:32:50 --> CSRF token verified.
INFO - 2025-06-28 00:00:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 00:05:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 00:07:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 00:15:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 00:30:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 00:31:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 00:45:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 00:45:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 00:45:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 00:45:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 00:46:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 00:46:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 01:46:32 --> CSRF token verified.
INFO - 2025-06-28 00:49:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 01:00:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 01:11:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 01:25:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 01:25:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 01:25:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 02:25:31 --> CSRF token verified.
INFO - 2025-06-28 01:25:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 01:25:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 02:25:40 --> CSRF token verified.
INFO - 2025-06-28 01:30:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 01:42:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 02:00:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 02:01:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 02:30:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 02:56:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 02:56:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:00:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:30:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:31:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:31:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:31:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:31:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:31:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:31:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:31:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:31:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:31:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:31:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:31:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:31:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:31:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:31:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:31:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:31:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:34:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:40:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:40:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 03:57:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 04:00:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 04:24:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 04:24:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 04:24:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 04:24:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 04:24:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 04:30:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 04:53:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 04:53:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 04:54:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 04:54:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 05:00:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 05:09:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 05:16:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 05:30:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 05:53:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 06:00:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 06:13:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 06:30:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 06:38:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 06:38:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 06:39:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 07:00:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 07:07:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 07:26:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 07:26:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 07:30:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 07:32:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 07:36:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 07:37:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 08:00:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 08:01:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 08:05:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 08:06:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 08:10:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 08:19:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 08:30:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 08:43:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 09:00:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 09:30:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 09:42:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 09:42:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 09:42:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 09:56:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 09:59:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 09:59:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 10:59:52 --> CSRF token verified.
INFO - 2025-06-28 10:00:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 10:02:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 10:04:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 10:04:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 10:28:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 10:30:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 10:38:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 10:38:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 10:38:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 10:38:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2025-06-28 11:38:23 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2025-06-28 10:38:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 11:38:24 --> CSRF token verified.
INFO - 2025-06-28 10:38:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2025-06-28 11:38:25 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2025-06-28 10:43:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
ERROR - 2025-06-28 14:42:07 --> Error connecting to the database: mysqli_sql_exception: Access denied for user 'Ayoola_bar'@'localhost' (using password: YES) in D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Database\MySQLi\Connection.php:187
Stack trace:
#0 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Database\MySQLi\Connection.php(187): mysqli->real_connect('localhost', 'Ayoola_bar', 'F4funnysky@4421', 'Ayoola_shop', 3306, '', 0)
#1 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Session\Handlers\DatabaseHandler.php(100): CodeIgniter\Database\BaseConnection->initialize()
#3 [internal function]: CodeIgniter\Session\Handlers\DatabaseHandler->open('ci_sessions', 'mds_session')
#4 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Session\Session.php(920): session_start()
#5 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Session\Session.php(242): CodeIgniter\Session\Session->startSession()
#6 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Config\Services.php(710): CodeIgniter\Session\Session->start()
#7 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Config\BaseService.php(312): CodeIgniter\Config\Services::session(Object(Config\Session), false)
#8 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Config\BaseService.php(251): CodeIgniter\Config\BaseService::__callStatic('session', Array)
#9 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Config\Services.php(668): CodeIgniter\Config\BaseService::getSharedInstance('session', NULL)
#10 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Config\BaseService.php(321): CodeIgniter\Config\Services::session()
#11 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\app\Config\Globals.php(32): CodeIgniter\Config\BaseService::__callStatic('session', Array)
#12 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\app\Config\Globals.php(245): Config\Globals::setGlobals()
#13 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Autoloader\Autoloader.php(317): include_once('D:\\JOBS\\UPWORK\\...')
#14 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Autoloader\Autoloader.php(296): CodeIgniter\Autoloader\Autoloader->includeFile('D:\\JOBS\\UPWORK\\...')
#15 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Autoloader\Autoloader.php(272): CodeIgniter\Autoloader\Autoloader->loadInNamespace('Config\\Globals')
#16 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\app\Config\Routes.php(6): CodeIgniter\Autoloader\Autoloader->loadClass('Config\\Globals')
#17 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Router\RouteCollection.php(340): require('D:\\JOBS\\UPWORK\\...')
#18 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\CodeIgniter.php(823): CodeIgniter\Router\RouteCollection->loadRoutes()
#19 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\CodeIgniter.php(457): CodeIgniter\CodeIgniter->tryToRouteIt(NULL)
#20 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#21 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Boot.php(325): CodeIgniter\CodeIgniter->run()
#22 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#23 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#24 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Access denied for user '****'@'localhost' (using password: YES) in D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Database\MySQLi\Connection.php:231
Stack trace:
#0 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Session\Handlers\DatabaseHandler.php(100): CodeIgniter\Database\BaseConnection->initialize()
#2 [internal function]: CodeIgniter\Session\Handlers\DatabaseHandler->open('ci_sessions', 'mds_session')
#3 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Session\Session.php(920): session_start()
#4 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Session\Session.php(242): CodeIgniter\Session\Session->startSession()
#5 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Config\Services.php(710): CodeIgniter\Session\Session->start()
#6 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Config\BaseService.php(312): CodeIgniter\Config\Services::session(Object(Config\Session), false)
#7 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Config\BaseService.php(251): CodeIgniter\Config\BaseService::__callStatic('session', Array)
#8 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Config\Services.php(668): CodeIgniter\Config\BaseService::getSharedInstance('session', NULL)
#9 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Config\BaseService.php(321): CodeIgniter\Config\Services::session()
#10 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\app\Config\Globals.php(32): CodeIgniter\Config\BaseService::__callStatic('session', Array)
#11 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\app\Config\Globals.php(245): Config\Globals::setGlobals()
#12 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Autoloader\Autoloader.php(317): include_once('D:\\JOBS\\UPWORK\\...')
#13 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Autoloader\Autoloader.php(296): CodeIgniter\Autoloader\Autoloader->includeFile('D:\\JOBS\\UPWORK\\...')
#14 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Autoloader\Autoloader.php(272): CodeIgniter\Autoloader\Autoloader->loadInNamespace('Config\\Globals')
#15 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\app\Config\Routes.php(6): CodeIgniter\Autoloader\Autoloader->loadClass('Config\\Globals')
#16 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Router\RouteCollection.php(340): require('D:\\JOBS\\UPWORK\\...')
#17 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\CodeIgniter.php(823): CodeIgniter\Router\RouteCollection->loadRoutes()
#18 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\CodeIgniter.php(457): CodeIgniter\CodeIgniter->tryToRouteIt(NULL)
#19 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#20 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Boot.php(325): CodeIgniter\CodeIgniter->run()
#21 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#22 D:\JOBS\UPWORK\Ayoola\fiiqi.com\files\web\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#23 {main}
CRITICAL - 2025-06-28 14:42:07 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Access denied for user '****'@'localhost' (using password: YES)
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Session\Handlers\DatabaseHandler.php(100): CodeIgniter\Database\BaseConnection->initialize()
 2 [internal function]: CodeIgniter\Session\Handlers\DatabaseHandler->open('ci_sessions', 'mds_session')
 3 SYSTEMPATH\Session\Session.php(920): session_start()
 4 SYSTEMPATH\Session\Session.php(242): CodeIgniter\Session\Session->startSession()
 5 SYSTEMPATH\Config\Services.php(710): CodeIgniter\Session\Session->start()
 6 SYSTEMPATH\Config\BaseService.php(312): CodeIgniter\Config\Services::session(Object(Config\Session), false)
 7 SYSTEMPATH\Config\BaseService.php(251): CodeIgniter\Config\BaseService::__callStatic('session', [...])
 8 SYSTEMPATH\Config\Services.php(668): CodeIgniter\Config\BaseService::getSharedInstance('session', null)
 9 SYSTEMPATH\Config\BaseService.php(321): CodeIgniter\Config\Services::session()
10 APPPATH\Config\Globals.php(32): CodeIgniter\Config\BaseService::__callStatic('session', [])
11 APPPATH\Config\Globals.php(245): Config\Globals::setGlobals()
12 SYSTEMPATH\Autoloader\Autoloader.php(317): include_once('D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Globals.php')
13 SYSTEMPATH\Autoloader\Autoloader.php(296): CodeIgniter\Autoloader\Autoloader->includeFile('D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Globals.php')
14 SYSTEMPATH\Autoloader\Autoloader.php(272): CodeIgniter\Autoloader\Autoloader->loadInNamespace('Config\\Globals')
15 APPPATH\Config\Routes.php(6): CodeIgniter\Autoloader\Autoloader->loadClass('Config\\Globals')
16 SYSTEMPATH\Router\RouteCollection.php(340): require('D:\\JOBS\\UPWORK\\Ayoola\\fiiqi.com\\files\\web\\app\\Config\\Routes.php')
17 SYSTEMPATH\CodeIgniter.php(823): CodeIgniter\Router\RouteCollection->loadRoutes()
18 SYSTEMPATH\CodeIgniter.php(457): CodeIgniter\CodeIgniter->tryToRouteIt(null)
19 SYSTEMPATH\CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(325): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
INFO - 2025-06-28 14:42:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:43:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:46:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:46:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:46:24 --> CSRF token verified.
INFO - 2025-06-28 14:46:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:46:31 --> CSRF token verified.
INFO - 2025-06-28 14:46:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:46:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:46:32 --> CSRF token verified.
INFO - 2025-06-28 14:46:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:46:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:46:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:46:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:46:58 --> CSRF token verified.
INFO - 2025-06-28 14:47:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:47:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:47:04 --> CSRF token verified.
INFO - 2025-06-28 14:47:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:47:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:47:05 --> CSRF token verified.
INFO - 2025-06-28 14:47:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:47:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:47:10 --> CSRF token verified.
INFO - 2025-06-28 14:47:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:47:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:47:22 --> CSRF token verified.
INFO - 2025-06-28 14:47:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:47:23 --> CSRF token verified.
INFO - 2025-06-28 14:47:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:47:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:47:26 --> CSRF token verified.
INFO - 2025-06-28 14:47:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:47:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:47:44 --> CSRF token verified.
INFO - 2025-06-28 14:47:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:47:47 --> CSRF token verified.
INFO - 2025-06-28 14:47:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:47:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:47:51 --> CSRF token verified.
INFO - 2025-06-28 14:47:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:47:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:47:56 --> CSRF token verified.
INFO - 2025-06-28 14:48:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:48:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:48:04 --> CSRF token verified.
INFO - 2025-06-28 14:48:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:48:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:48:06 --> CSRF token verified.
INFO - 2025-06-28 14:48:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:48:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:48:09 --> CSRF token verified.
INFO - 2025-06-28 14:48:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:48:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:48:14 --> CSRF token verified.
INFO - 2025-06-28 14:48:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:48:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:48:28 --> CSRF token verified.
INFO - 2025-06-28 14:48:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:48:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:48:54 --> CSRF token verified.
INFO - 2025-06-28 14:49:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:49:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:49:05 --> CSRF token verified.
INFO - 2025-06-28 14:49:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:49:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:49:22 --> CSRF token verified.
INFO - 2025-06-28 14:49:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:49:33 --> CSRF token verified.
INFO - 2025-06-28 14:49:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:49:39 --> CSRF token verified.
INFO - 2025-06-28 14:49:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:49:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:49:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:49:40 --> CSRF token verified.
INFO - 2025-06-28 14:49:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:49:44 --> CSRF token verified.
INFO - 2025-06-28 14:49:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:49:45 --> CSRF token verified.
INFO - 2025-06-28 14:49:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:49:48 --> CSRF token verified.
INFO - 2025-06-28 14:49:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:49:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:49:48 --> CSRF token verified.
INFO - 2025-06-28 14:50:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:50:24 --> CSRF token verified.
INFO - 2025-06-28 14:50:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:50:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:50:25 --> CSRF token verified.
INFO - 2025-06-28 14:50:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:50:32 --> CSRF token verified.
INFO - 2025-06-28 14:50:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:50:34 --> CSRF token verified.
INFO - 2025-06-28 14:50:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:50:48 --> CSRF token verified.
INFO - 2025-06-28 14:50:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:50:49 --> CSRF token verified.
INFO - 2025-06-28 14:50:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:50:52 --> CSRF token verified.
INFO - 2025-06-28 14:50:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:50:55 --> CSRF token verified.
INFO - 2025-06-28 14:51:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:51:09 --> CSRF token verified.
INFO - 2025-06-28 14:51:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:51:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:51:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:51:10 --> CSRF token verified.
INFO - 2025-06-28 14:51:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:51:15 --> CSRF token verified.
INFO - 2025-06-28 14:51:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:51:16 --> CSRF token verified.
INFO - 2025-06-28 14:51:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:51:19 --> CSRF token verified.
INFO - 2025-06-28 14:51:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 14:51:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:51:19 --> CSRF token verified.
INFO - 2025-06-28 15:06:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 15:06:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2025-06-28 16:06:29 --> CSRF token verified.
