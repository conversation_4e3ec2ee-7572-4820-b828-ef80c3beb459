CRITICAL - 2024-11-13 04:58:40 --> ErrorException: Undefined property: stdClass::$phone_number
[Method: GET, Route: cosrx-snail-96-mucin-power-essence-611]
in APPPATH/Helpers/product_helper.php on line 1080.
 1 APPPATH/Helpers/product_helper.php(1080): CodeIgniter\Debug\Exceptions->errorHandler()
 2 APPPATH/Views/product/details/_product_details.php(166): getProductFormData()
 3 SYSTEMPATH/View/View.php(234): include('/home/<USER>/web/tiqbar.com/public_html/app/Views/product/details/_product_details.php')
 4 SYSTEMPATH/View/View.php(237): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 5 SYSTEMPATH/Common.php(1195): CodeIgniter\View\View->render()
 6 APPPATH/Views/product/details/product.php(27): view()
 7 SYSTEMPATH/View/View.php(234): include('/home/<USER>/web/tiqbar.com/public_html/app/Views/product/details/product.php')
 8 SYSTEMPATH/View/View.php(237): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 9 SYSTEMPATH/Common.php(1195): CodeIgniter\View\View->render()
10 APPPATH/Controllers/HomeController.php(307): view()
11 APPPATH/Controllers/HomeController.php(81): App\Controllers\HomeController->product()
12 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\HomeController->any()
13 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
14 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
15 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
16 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
17 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
CRITICAL - 2024-11-13 04:58:48 --> Error: Object of class stdClass could not be converted to string
[Method: GET, Route: cosrx-snail-96-mucin-power-essence-611]
in APPPATH/Helpers/product_helper.php on line 1057.
 1 APPPATH/Views/product/details/_product_details.php(166): getProductFormData()
 2 SYSTEMPATH/View/View.php(234): include('/home/<USER>/web/tiqbar.com/public_html/app/Views/product/details/_product_details.php')
 3 SYSTEMPATH/View/View.php(237): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH/Common.php(1195): CodeIgniter\View\View->render()
 5 APPPATH/Views/product/details/product.php(27): view()
 6 SYSTEMPATH/View/View.php(234): include('/home/<USER>/web/tiqbar.com/public_html/app/Views/product/details/product.php')
 7 SYSTEMPATH/View/View.php(237): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH/Common.php(1195): CodeIgniter\View\View->render()
 9 APPPATH/Controllers/HomeController.php(307): view()
10 APPPATH/Controllers/HomeController.php(81): App\Controllers\HomeController->product()
11 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\HomeController->any()
12 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
13 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
14 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
15 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
16 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 03:59:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 04:59:27 --> Error: Object of class stdClass could not be converted to string
[Method: GET, Route: cosrx-snail-96-mucin-power-essence-611]
in APPPATH/Helpers/product_helper.php on line 1057.
 1 APPPATH/Views/product/details/_product_details.php(166): getProductFormData()
 2 SYSTEMPATH/View/View.php(234): include('/home/<USER>/web/tiqbar.com/public_html/app/Views/product/details/_product_details.php')
 3 SYSTEMPATH/View/View.php(237): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH/Common.php(1195): CodeIgniter\View\View->render()
 5 APPPATH/Views/product/details/product.php(27): view()
 6 SYSTEMPATH/View/View.php(234): include('/home/<USER>/web/tiqbar.com/public_html/app/Views/product/details/product.php')
 7 SYSTEMPATH/View/View.php(237): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH/Common.php(1195): CodeIgniter\View\View->render()
 9 APPPATH/Controllers/HomeController.php(307): view()
10 APPPATH/Controllers/HomeController.php(81): App\Controllers\HomeController->product()
11 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\HomeController->any()
12 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
13 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
14 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
15 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
16 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 03:59:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 03:59:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 04:59:43 --> TypeError: json_decode(): Argument #1 ($json) must be of type string, stdClass given
[Method: GET, Route: cosrx-snail-96-mucin-power-essence-611]
in APPPATH/Helpers/product_helper.php on line 1057.
 1 APPPATH/Helpers/product_helper.php(1057): json_decode()
 2 APPPATH/Views/product/details/_product_details.php(166): getProductFormData()
 3 SYSTEMPATH/View/View.php(234): include('/home/<USER>/web/tiqbar.com/public_html/app/Views/product/details/_product_details.php')
 4 SYSTEMPATH/View/View.php(237): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 5 SYSTEMPATH/Common.php(1195): CodeIgniter\View\View->render()
 6 APPPATH/Views/product/details/product.php(27): view()
 7 SYSTEMPATH/View/View.php(234): include('/home/<USER>/web/tiqbar.com/public_html/app/Views/product/details/product.php')
 8 SYSTEMPATH/View/View.php(237): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 9 SYSTEMPATH/Common.php(1195): CodeIgniter\View\View->render()
10 APPPATH/Controllers/HomeController.php(307): view()
11 APPPATH/Controllers/HomeController.php(81): App\Controllers\HomeController->product()
12 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\HomeController->any()
13 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
14 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
15 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
16 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
17 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 03:59:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:00:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:01:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:01:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:01:27 --> CSRF token verified.
INFO - 2024-11-13 04:02:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:02:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:02:10 --> CSRF token verified.
INFO - 2024-11-13 04:02:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:02:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:02:42 --> CSRF token verified.
INFO - 2024-11-13 04:03:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:03:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:03:07 --> CSRF token verified.
INFO - 2024-11-13 04:04:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:04:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:04:19 --> CSRF token verified.
INFO - 2024-11-13 04:05:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:05:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:05:45 --> CSRF token verified.
INFO - 2024-11-13 04:06:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:06:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:06:10 --> CSRF token verified.
INFO - 2024-11-13 04:08:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:08:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:08:14 --> CSRF token verified.
INFO - 2024-11-13 04:08:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:08:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:08:56 --> CSRF token verified.
INFO - 2024-11-13 04:10:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:10:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:11:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:11:01 --> CSRF token verified.
INFO - 2024-11-13 04:11:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:11:04 --> CSRF token verified.
INFO - 2024-11-13 04:12:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:12:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:12:08 --> CSRF token verified.
INFO - 2024-11-13 04:12:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:12:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:12:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:12:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:12:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:12:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:12:54 --> CSRF token verified.
INFO - 2024-11-13 04:12:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:14:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:15:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:15:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:15:30 --> CSRF token verified.
INFO - 2024-11-13 04:15:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:15:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:15:34 --> CSRF token verified.
INFO - 2024-11-13 04:15:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:15:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:15:47 --> CSRF token verified.
INFO - 2024-11-13 04:15:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:15:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:15:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:15:53 --> CSRF token verified.
INFO - 2024-11-13 04:15:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:17:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:21:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:21:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:21:28 --> CSRF token verified.
INFO - 2024-11-13 04:22:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:32:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:32:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:32:23 --> CSRF token verified.
INFO - 2024-11-13 04:52:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:52:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:52:28 --> CSRF token verified.
INFO - 2024-11-13 04:52:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:52:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:52:41 --> CSRF token verified.
INFO - 2024-11-13 04:52:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:52:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:52:48 --> CSRF token verified.
INFO - 2024-11-13 04:52:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:52:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:52:53 --> CSRF token verified.
INFO - 2024-11-13 04:52:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:52:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:52:57 --> CSRF token verified.
INFO - 2024-11-13 04:53:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:53:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:53:31 --> CSRF token verified.
INFO - 2024-11-13 04:53:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:53:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:53:49 --> CSRF token verified.
INFO - 2024-11-13 04:53:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:53:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:53:52 --> CSRF token verified.
INFO - 2024-11-13 04:53:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:53:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:53:59 --> CSRF token verified.
INFO - 2024-11-13 04:54:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:54:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:54:14 --> CSRF token verified.
INFO - 2024-11-13 04:54:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:54:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:54:35 --> CSRF token verified.
INFO - 2024-11-13 04:56:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:56:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:56:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 04:56:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:56:43 --> CSRF token verified.
INFO - 2024-11-13 04:59:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:02:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:04:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:15:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:15:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:27:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:27:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:27:37 --> CSRF token verified.
INFO - 2024-11-13 05:30:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:33:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:38:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:38:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:38:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:38:38 --> CSRF token verified.
INFO - 2024-11-13 05:42:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:43:09 --> CSRF token verified.
INFO - 2024-11-13 05:43:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:43:47 --> CSRF token verified.
INFO - 2024-11-13 05:43:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:43:49 --> CSRF token verified.
INFO - 2024-11-13 05:43:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:43:50 --> CSRF token verified.
INFO - 2024-11-13 05:43:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:43:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:43:58 --> CSRF token verified.
INFO - 2024-11-13 05:44:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:03 --> CSRF token verified.
INFO - 2024-11-13 05:44:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:04 --> CSRF token verified.
INFO - 2024-11-13 05:44:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:06 --> CSRF token verified.
INFO - 2024-11-13 05:44:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:09 --> CSRF token verified.
INFO - 2024-11-13 05:44:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:10 --> CSRF token verified.
INFO - 2024-11-13 05:44:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:10 --> CSRF token verified.
INFO - 2024-11-13 05:44:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:12 --> CSRF token verified.
INFO - 2024-11-13 05:44:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:12 --> CSRF token verified.
INFO - 2024-11-13 05:44:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 05:44:14 --> ErrorException: unlink(/home/<USER>/web/tiqbar.com/public_html/writable/debugbar/debugbar_1731476633.584771.json): No such file or directory
[Method: GET, Route: /]
in SYSTEMPATH/Debug/Toolbar/Collectors/History.php on line 82.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Debug/Toolbar/Collectors/History.php(82): unlink()
 3 SYSTEMPATH/Debug/Toolbar.php(520): CodeIgniter\Debug\Toolbar\Collectors\History->setFiles()
 4 SYSTEMPATH/Debug/Toolbar.php(497): CodeIgniter\Debug\Toolbar->format()
 5 APPPATH/Config/Events.php(48): CodeIgniter\Debug\Toolbar->respond()
 6 SYSTEMPATH/Events/Events.php(155): CodeIgniter\Events\Events::Config\{closure}()
 7 SYSTEMPATH/CodeIgniter.php(340): CodeIgniter\Events\Events::trigger()
 8 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
10 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
CRITICAL - 2024-11-13 05:44:14 --> ErrorException: unlink(/home/<USER>/web/tiqbar.com/public_html/writable/debugbar/debugbar_1731476633.584771.json): No such file or directory
[Method: GET, Route: /]
in SYSTEMPATH/Debug/Toolbar/Collectors/History.php on line 82.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Debug/Toolbar/Collectors/History.php(82): unlink()
 3 SYSTEMPATH/Debug/Toolbar.php(520): CodeIgniter\Debug\Toolbar\Collectors\History->setFiles()
 4 SYSTEMPATH/Debug/Toolbar.php(497): CodeIgniter\Debug\Toolbar->format()
 5 APPPATH/Config/Events.php(48): CodeIgniter\Debug\Toolbar->respond()
 6 SYSTEMPATH/Events/Events.php(155): CodeIgniter\Events\Events::Config\{closure}()
 7 SYSTEMPATH/CodeIgniter.php(340): CodeIgniter\Events\Events::trigger()
 8 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
10 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:44:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:14 --> CSRF token verified.
INFO - 2024-11-13 05:44:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:14 --> CSRF token verified.
INFO - 2024-11-13 05:44:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:15 --> CSRF token verified.
INFO - 2024-11-13 05:44:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:16 --> CSRF token verified.
INFO - 2024-11-13 05:44:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:18 --> CSRF token verified.
INFO - 2024-11-13 05:44:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:24 --> CSRF token verified.
INFO - 2024-11-13 05:44:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:26 --> CSRF token verified.
INFO - 2024-11-13 05:44:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:26 --> CSRF token verified.
INFO - 2024-11-13 06:44:26 --> CSRF token verified.
INFO - 2024-11-13 05:44:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:27 --> CSRF token verified.
INFO - 2024-11-13 05:44:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:29 --> CSRF token verified.
INFO - 2024-11-13 05:44:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:29 --> CSRF token verified.
INFO - 2024-11-13 05:44:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:32 --> CSRF token verified.
INFO - 2024-11-13 05:44:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:35 --> CSRF token verified.
INFO - 2024-11-13 05:44:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 05:44:38 --> ErrorException: unlink(/home/<USER>/web/tiqbar.com/public_html/writable/debugbar/debugbar_1731476665.937366.json): No such file or directory
[Method: GET, Route: /]
in SYSTEMPATH/Debug/Toolbar/Collectors/History.php on line 82.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Debug/Toolbar/Collectors/History.php(82): unlink()
 3 SYSTEMPATH/Debug/Toolbar.php(520): CodeIgniter\Debug\Toolbar\Collectors\History->setFiles()
 4 SYSTEMPATH/Debug/Toolbar.php(497): CodeIgniter\Debug\Toolbar->format()
 5 APPPATH/Config/Events.php(48): CodeIgniter\Debug\Toolbar->respond()
 6 SYSTEMPATH/Events/Events.php(155): CodeIgniter\Events\Events::Config\{closure}()
 7 SYSTEMPATH/CodeIgniter.php(340): CodeIgniter\Events\Events::trigger()
 8 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
10 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:44:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:39 --> CSRF token verified.
INFO - 2024-11-13 05:44:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:39 --> CSRF token verified.
INFO - 2024-11-13 05:44:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:42 --> CSRF token verified.
INFO - 2024-11-13 05:44:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:42 --> CSRF token verified.
INFO - 2024-11-13 05:44:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:44:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:47 --> CSRF token verified.
INFO - 2024-11-13 05:44:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:54 --> CSRF token verified.
INFO - 2024-11-13 05:45:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:45:02 --> CSRF token verified.
INFO - 2024-11-13 05:45:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:45:05 --> CSRF token verified.
INFO - 2024-11-13 05:45:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:45:06 --> CSRF token verified.
INFO - 2024-11-13 05:45:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:45:08 --> CSRF token verified.
INFO - 2024-11-13 05:46:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:14 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:46:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:14 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:46:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:16 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:46:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:18 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:46:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:18 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:46:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:20 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:46:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:24 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:46:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:26 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:46:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:27 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:46:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:31 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:46:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:33 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:46:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:34 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:46:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:46:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:37 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:46:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:41 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:46:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:43 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:46:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:46:44 --> CSRF token verified.
INFO - 2024-11-13 05:46:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:44 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:46:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:46:53 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:47:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:47:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 05:47:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:47:11 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:47:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:47:17 --> CSRF token verified.
INFO - 2024-11-13 05:47:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:47:18 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:47:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:47:19 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:47:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:47:21 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:47:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:47:24 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:47:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:47:30 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:47:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:47:30 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:47:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:47:34 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:47:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:47:49 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:47:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:47:55 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:48:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:48:22 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:48:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:48:26 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:48:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:48:28 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:48:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:48:35 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:48:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:48:37 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:48:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:48:47 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:48:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:48:47 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:48:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:48:50 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 05:48:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 06:48:54 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 06:03:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:06:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:06:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:06:56 --> CSRF token verified.
INFO - 2024-11-13 06:07:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:07:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:24:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:24:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:24:09 --> CSRF token verified.
INFO - 2024-11-13 06:26:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:28:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:28:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:28:38 --> CSRF token verified.
INFO - 2024-11-13 06:29:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:34:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:34:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:34:08 --> CSRF token verified.
INFO - 2024-11-13 06:40:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:40:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:40:56 --> CSRF token verified.
INFO - 2024-11-13 06:41:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:44:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:44:45 --> CSRF token verified.
INFO - 2024-11-13 06:50:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:52:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 06:52:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:52:45 --> CSRF token verified.
INFO - 2024-11-13 06:57:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:02:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:03:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:03:02 --> CSRF token verified.
INFO - 2024-11-13 07:12:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:12:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:12:57 --> CSRF token verified.
INFO - 2024-11-13 07:19:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:19:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:19:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:19:29 --> CSRF token verified.
INFO - 2024-11-13 07:19:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:19:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:20:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:20:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:20:03 --> CSRF token verified.
INFO - 2024-11-13 07:20:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:20:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:20:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:20:51 --> CSRF token verified.
INFO - 2024-11-13 07:20:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:21:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:21:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:21:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:21:06 --> CSRF token verified.
INFO - 2024-11-13 07:21:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:21:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:21:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:21:12 --> CSRF token verified.
INFO - 2024-11-13 07:22:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:22:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:22:41 --> CSRF token verified.
INFO - 2024-11-13 07:27:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:27:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:27:22 --> CSRF token verified.
INFO - 2024-11-13 07:35:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:35:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:35:32 --> CSRF token verified.
INFO - 2024-11-13 07:42:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:42:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:42:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:42:55 --> CSRF token verified.
INFO - 2024-11-13 07:43:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:43:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:43:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:43:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:43:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:43:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:43:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:43:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:43:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:43:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:43:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:43:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:43:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:43:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:43:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:43:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:44:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:44:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:44:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:44:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:44:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:44:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:44:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:44:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:44:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:44:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:44:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:44:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:44:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:45:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:45:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:45:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:45:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:45:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:45:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:45:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:45:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:45:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:45:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:45:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:45:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:45:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:46:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:46:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:46:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:46:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:46:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:46:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:46:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:46:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:46:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:46:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:46:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:46:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:46:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:46:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:47:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:48:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:48:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:48:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:48:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:48:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:48:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:48:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:48:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:48:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:48:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:48:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:48:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:48:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:48:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:48:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:49:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:50:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:50:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:50:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:50:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:50:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:50:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:50:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:50:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:50:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:50:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 07:50:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:10:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:10:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:10:43 --> CSRF token verified.
INFO - 2024-11-13 08:16:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:16:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:16:10 --> CSRF token verified.
INFO - 2024-11-13 08:24:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:24:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:24:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:24:08 --> CSRF token verified.
INFO - 2024-11-13 08:24:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:24:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:24:13 --> CSRF token verified.
INFO - 2024-11-13 08:40:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:40:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:40:49 --> CSRF token verified.
INFO - 2024-11-13 08:41:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:41:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:41:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:41:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:41:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:41:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:41:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:58:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 08:59:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:59:06 --> CSRF token verified.
INFO - 2024-11-13 09:04:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:21:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-13 10:21:02 --> ErrorException: Attempt to read property "show_on_main_menu" on null
[Method: GET, Route: profile/dees-palace-of-beauty]
in APPPATH/Views/partials/_nav_main_profile.php on line 10.
 1 APPPATH/Views/partials/_nav_main_profile.php(10): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/View/View.php(234): include('/home/<USER>/web/tiqbar.com/public_html/app/Views/partials/_nav_main_profile.php')
 3 SYSTEMPATH/View/View.php(237): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH/Common.php(1195): CodeIgniter\View\View->render()
 5 APPPATH/Views/partials/_header.php(146): view()
 6 SYSTEMPATH/View/View.php(234): include('/home/<USER>/web/tiqbar.com/public_html/app/Views/partials/_header.php')
 7 SYSTEMPATH/View/View.php(237): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH/Common.php(1195): CodeIgniter\View\View->render()
 9 APPPATH/Controllers/ProfileController.php(82): view()
10 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\ProfileController->profile()
11 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
12 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
13 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
15 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-13 09:28:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:28:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:28:12 --> CSRF token verified.
INFO - 2024-11-13 09:28:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:28:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:29:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:32:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:32:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:32:51 --> CSRF token verified.
INFO - 2024-11-13 09:38:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:39:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:41:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:41:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:41:22 --> CSRF token verified.
INFO - 2024-11-13 09:49:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:58:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 09:58:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:58:23 --> CSRF token verified.
INFO - 2024-11-13 10:06:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:06:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:35 --> CSRF token verified.
INFO - 2024-11-13 10:12:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:12:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:12:25 --> CSRF token verified.
INFO - 2024-11-13 10:14:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:17:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:17:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:17:57 --> CSRF token verified.
INFO - 2024-11-13 10:25:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:26:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:26:11 --> CSRF token verified.
INFO - 2024-11-13 10:27:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:30:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:31:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:31:11 --> CSRF token verified.
INFO - 2024-11-13 10:33:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:44:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:46:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:46:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:50:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:55:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:57:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 10:57:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:57:37 --> CSRF token verified.
INFO - 2024-11-13 11:00:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:02:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:02:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:02:44 --> CSRF token verified.
INFO - 2024-11-13 11:03:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:03:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:03:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:03:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:03:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:03:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:03:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:03:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:03:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:03:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:03:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:03:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:03:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:03:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:03:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:04:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:04:41 --> CSRF token verified.
INFO - 2024-11-13 11:04:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:05:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:06:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:07:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:18:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:39:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:41:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 11:41:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:02:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:02:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:09:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:11:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:11:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:12:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:15:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:15:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:20:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:21:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:31:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:32:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:37:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:41:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:43:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:49:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:51:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:54:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:54:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:55:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 12:59:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:04:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:06:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:11:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:12:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:13:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:18:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:18:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:21:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:26:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:31:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:31:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:35:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:42:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:43:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:43:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:43:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:44:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:47:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:54:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:56:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:56:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:56:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 13:56:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:01:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:01:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:02:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:04:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:13:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:15:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:15:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:17:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:20:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:20:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:22:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:26:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:27:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:28:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:29:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:30:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:30:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:30:52 --> CSRF token verified.
INFO - 2024-11-13 14:39:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:41:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:43:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:43:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:46:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:47:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:49:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:49:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:50:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:50:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:50:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:50:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:50:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:50:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:50:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:50:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:50:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:50:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:50:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:50:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:50:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:50:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:50:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:51:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:54:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:56:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 14:59:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:00:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:00:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:05:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:10:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:11:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:12:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:14:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:15:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:18:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:28:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:28:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 16:28:10 --> CSRF token verified.
INFO - 2024-11-13 15:28:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:28:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:28:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:28:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 16:28:45 --> CSRF token verified.
INFO - 2024-11-13 15:28:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:28:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 16:28:49 --> CSRF token verified.
INFO - 2024-11-13 15:28:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 16:28:51 --> CSRF token verified.
INFO - 2024-11-13 15:30:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:30:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:30:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:32:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:41:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:42:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:48:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 15:53:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 16:01:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 16:08:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 16:48:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 16:57:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 16:57:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 17:57:45 --> CSRF token verified.
INFO - 2024-11-13 16:59:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 17:40:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 17:47:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 17:48:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 18:48:07 --> CSRF token verified.
INFO - 2024-11-13 18:32:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 18:53:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 18:54:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 18:54:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:54:24 --> CSRF token verified.
INFO - 2024-11-13 18:54:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 18:55:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 18:55:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 18:55:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:55:41 --> CSRF token verified.
INFO - 2024-11-13 18:55:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:07:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:09:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:09:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 20:09:34 --> CSRF token verified.
INFO - 2024-11-13 19:09:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:09:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:10:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 20:10:10 --> CSRF token verified.
INFO - 2024-11-13 19:22:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:23:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:23:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:23:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:23:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:23:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:23:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:23:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:23:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:27:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:34:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:41:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:41:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 19:51:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 20:15:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 20:17:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 20:57:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 20:58:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 21:58:04 --> CSRF token verified.
INFO - 2024-11-13 20:58:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 20:58:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 20:58:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 21:58:37 --> CSRF token verified.
INFO - 2024-11-13 21:04:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 21:13:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 21:14:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 22:14:21 --> CSRF token verified.
INFO - 2024-11-13 21:39:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 21:39:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 21:44:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 21:44:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 21:48:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 21:52:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 21:52:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 22:52:41 --> CSRF token verified.
INFO - 2024-11-13 21:55:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 21:57:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 21:57:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 22:07:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 22:31:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:04:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:04:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:04:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:04:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:04:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:04:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:04:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:04:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:04:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:04:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:06:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:06:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:55:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:56:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:57:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:57:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:57:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:57:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:57:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:57:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:57:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:57:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:57:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:57:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-13 23:57:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
