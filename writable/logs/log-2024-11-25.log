INFO - 2024-11-25 00:03:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:04:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:07:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 01:09:21 --> CSRF token verified.
INFO - 2024-11-25 00:09:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 01:09:22 --> CSRF token verified.
INFO - 2024-11-25 00:09:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 01:09:48 --> CSRF token verified.
INFO - 2024-11-25 00:09:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 01:09:50 --> CSRF token verified.
INFO - 2024-11-25 00:09:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 01:09:54 --> CSRF token verified.
INFO - 2024-11-25 00:09:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 01:09:57 --> CSRF token verified.
INFO - 2024-11-25 00:09:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:09:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:10:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:16:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:18:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:49:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:57:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 00:58:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 01:01:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 01:16:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 01:21:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 01:22:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 01:25:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 01:58:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 02:21:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 02:24:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 02:24:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:24:29 --> CSRF token verified.
INFO - 2024-11-25 02:28:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 02:28:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:28:24 --> CSRF token verified.
INFO - 2024-11-25 02:54:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:05:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:08:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:08:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:09:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:18:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:23:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:25:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:30:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:30:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:30:11 --> CSRF token verified.
INFO - 2024-11-25 03:30:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:30:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:30:39 --> CSRF token verified.
INFO - 2024-11-25 03:32:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:33:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:33:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:33:31 --> CSRF token verified.
INFO - 2024-11-25 03:37:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:38:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:38:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:38:23 --> CSRF token verified.
INFO - 2024-11-25 03:40:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:42:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:42:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 03:49:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:00:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:00:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:00:24 --> CSRF token verified.
INFO - 2024-11-25 04:08:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:12:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:20:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:31:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:40:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:41:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:41:05 --> CSRF token verified.
INFO - 2024-11-25 04:50:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:54:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:54:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:56:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:57:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:57:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:58:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:58:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:59:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 04:59:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:16:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:17:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:26:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:28:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:32:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:39:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:39:44 --> CSRF token verified.
INFO - 2024-11-25 05:40:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:40:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:43:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:43:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:43:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:43:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:43:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:44:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:44:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:44:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:44:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:44:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:44:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:44:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:44:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:44:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:44:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:44:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:44:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:44:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:48:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:48:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:48:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:48:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:48:52 --> CSRF token verified.
INFO - 2024-11-25 05:49:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 05:49:48 --> ErrorException: unlink(/home/<USER>/web/tiqbar.com/public_html/writable/debugbar/debugbar_1732513448.287439.json): No such file or directory
[Method: GET, Route: /]
in SYSTEMPATH/Debug/Toolbar/Collectors/History.php on line 82.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Debug/Toolbar/Collectors/History.php(82): unlink()
 3 SYSTEMPATH/Debug/Toolbar.php(520): CodeIgniter\Debug\Toolbar\Collectors\History->setFiles()
 4 SYSTEMPATH/Debug/Toolbar.php(497): CodeIgniter\Debug\Toolbar->format()
 5 APPPATH/Config/Events.php(48): CodeIgniter\Debug\Toolbar->respond()
 6 SYSTEMPATH/Events/Events.php(155): CodeIgniter\Events\Events::Config\{closure}()
 7 SYSTEMPATH/CodeIgniter.php(340): CodeIgniter\Events\Events::trigger()
 8 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
10 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:49:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:49:48 --> CSRF token verified.
INFO - 2024-11-25 05:49:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:49:49 --> CSRF token verified.
INFO - 2024-11-25 05:49:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:49:51 --> CSRF token verified.
INFO - 2024-11-25 05:49:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:49:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:49:57 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:49:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:49:58 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:50:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:50:01 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:50:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:03 --> CSRF token verified.
INFO - 2024-11-25 05:50:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:07 --> CSRF token verified.
INFO - 2024-11-25 05:50:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 05:50:10 --> ErrorException: unlink(/home/<USER>/web/tiqbar.com/public_html/writable/debugbar/debugbar_1732513787.319302.json): No such file or directory
[Method: GET, Route: /]
in SYSTEMPATH/Debug/Toolbar/Collectors/History.php on line 82.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Debug/Toolbar/Collectors/History.php(82): unlink()
 3 SYSTEMPATH/Debug/Toolbar.php(520): CodeIgniter\Debug\Toolbar\Collectors\History->setFiles()
 4 SYSTEMPATH/Debug/Toolbar.php(497): CodeIgniter\Debug\Toolbar->format()
 5 APPPATH/Config/Events.php(48): CodeIgniter\Debug\Toolbar->respond()
 6 SYSTEMPATH/Events/Events.php(155): CodeIgniter\Events\Events::Config\{closure}()
 7 SYSTEMPATH/CodeIgniter.php(340): CodeIgniter\Events\Events::trigger()
 8 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
10 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
CRITICAL - 2024-11-25 05:50:10 --> ErrorException: unlink(/home/<USER>/web/tiqbar.com/public_html/writable/debugbar/debugbar_1732513787.319302.json): No such file or directory
[Method: GET, Route: /]
in SYSTEMPATH/Debug/Toolbar/Collectors/History.php on line 82.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Debug/Toolbar/Collectors/History.php(82): unlink()
 3 SYSTEMPATH/Debug/Toolbar.php(520): CodeIgniter\Debug\Toolbar\Collectors\History->setFiles()
 4 SYSTEMPATH/Debug/Toolbar.php(497): CodeIgniter\Debug\Toolbar->format()
 5 APPPATH/Config/Events.php(48): CodeIgniter\Debug\Toolbar->respond()
 6 SYSTEMPATH/Events/Events.php(155): CodeIgniter\Events\Events::Config\{closure}()
 7 SYSTEMPATH/CodeIgniter.php(340): CodeIgniter\Events\Events::trigger()
 8 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
10 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:50:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:10 --> CSRF token verified.
INFO - 2024-11-25 05:50:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:10 --> CSRF token verified.
INFO - 2024-11-25 05:50:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:11 --> CSRF token verified.
INFO - 2024-11-25 05:50:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:50:11 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:50:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:22 --> CSRF token verified.
INFO - 2024-11-25 05:50:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:23 --> CSRF token verified.
INFO - 2024-11-25 05:50:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:23 --> CSRF token verified.
INFO - 2024-11-25 05:50:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:27 --> CSRF token verified.
INFO - 2024-11-25 05:50:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:31 --> CSRF token verified.
INFO - 2024-11-25 05:50:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:34 --> CSRF token verified.
INFO - 2024-11-25 05:50:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:35 --> CSRF token verified.
INFO - 2024-11-25 05:50:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:35 --> CSRF token verified.
INFO - 2024-11-25 05:50:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:36 --> CSRF token verified.
INFO - 2024-11-25 05:50:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:38 --> CSRF token verified.
INFO - 2024-11-25 05:50:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:43 --> CSRF token verified.
INFO - 2024-11-25 05:50:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:50:45 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:50:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:50:45 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:50:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:46 --> CSRF token verified.
INFO - 2024-11-25 05:50:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:50:50 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:50:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:50 --> CSRF token verified.
INFO - 2024-11-25 05:50:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:50:50 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:50:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:50:51 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:50:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:50:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:50:54 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:50:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:50:54 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:50:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:56 --> CSRF token verified.
INFO - 2024-11-25 05:50:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:50:57 --> CSRF token verified.
INFO - 2024-11-25 05:51:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:51:01 --> CSRF token verified.
INFO - 2024-11-25 05:51:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:51:13 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:51:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:51:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:51:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:51:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:51:59 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:52:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:52:05 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:52:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:52:11 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:52:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:52:11 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:52:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:52:12 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:52:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:52:13 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:52:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:52:16 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:52:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:52:18 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:52:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:52:19 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:52:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:52:19 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:52:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:52:36 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:52:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:52:38 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:52:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:52:48 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:52:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:52:54 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:52:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:52:56 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:52:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:52:57 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:52:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:52:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:52:59 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:53:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:53:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:53:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:53:02 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:53:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:53:02 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:53:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:53:05 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:53:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:53:05 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:53:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:53:12 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:53:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:53:16 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:53:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 06:53:19 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 05:54:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:56:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:56:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:57:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:58:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 05:58:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:58:12 --> CSRF token verified.
INFO - 2024-11-25 05:58:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:00:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:06:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:07:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:07:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:07:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:07:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:07:22 --> CSRF token verified.
INFO - 2024-11-25 06:08:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:08:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:09:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:20:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:20:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:20:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:20:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:20:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:20:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:20:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:20:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:20:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:20:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:28:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:30:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:38:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 06:49:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:00:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:13:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:21:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:34:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:34:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:40:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:40:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:41:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:41:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:42:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:43:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:47:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:47:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:47:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:47:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:47:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:47:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:47:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:48:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:48:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:48:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:48:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:48:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:48:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:48:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:48:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:48:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:48:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:48:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:48:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:48:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 07:48:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 08:06:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 08:10:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 08:15:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 08:19:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 08:20:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 08:20:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 08:28:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 08:28:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 08:39:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 08:42:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 08:53:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 08:53:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 08:57:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 09:01:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 09:02:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 09:23:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 09:23:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 10:23:41 --> CSRF token verified.
INFO - 2024-11-25 09:25:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 09:31:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 09:38:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 09:40:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 09:40:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 09:40:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 09:43:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 09:46:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 09:47:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 09:48:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 10:48:02 --> CSRF token verified.
INFO - 2024-11-25 09:50:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 09:50:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 09:52:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 09:53:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 10:53:00 --> CSRF token verified.
INFO - 2024-11-25 09:56:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 10:00:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 10:00:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 10:00:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 10:01:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:01:26 --> CSRF token verified.
INFO - 2024-11-25 10:21:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 10:21:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 10:33:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 10:41:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 10:42:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 10:50:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 10:51:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:51:07 --> CSRF token verified.
INFO - 2024-11-25 10:54:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 10:54:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 10:58:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:06:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:10:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:10:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:20:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:20:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:22:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:26:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:26:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:31:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:36:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:38:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:43:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:48:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:48:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:53:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 11:54:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:00:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:00:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:00:45 --> CSRF token verified.
INFO - 2024-11-25 12:02:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:12:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:13:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:20:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:20:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:27:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:27:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:32:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:34:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:34:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:36:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:36:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:36:06 --> CSRF token verified.
INFO - 2024-11-25 12:36:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:39:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:49:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:50:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:54:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:58:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:58:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:59:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 12:59:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:00:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:01:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:01:01 --> CSRF token verified.
INFO - 2024-11-25 13:02:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:06:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:06:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:09:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:14:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:14:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:19:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:21:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:24:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:24:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:29:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:36:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:39:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:40:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:41:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:43:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:43:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:48:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:48:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:49:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:51:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:51:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:51:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:56:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:58:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:58:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 13:59:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:04:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:04:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:11:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:11:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:11:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:11:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:11:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:11:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:11:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:11:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:11:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:11:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:15:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:23:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:29:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:29:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:30:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:31:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:31:03 --> CSRF token verified.
INFO - 2024-11-25 14:36:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:41:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:41:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:41:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:41:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:41:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:41:22 --> CSRF token verified.
INFO - 2024-11-25 14:41:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:41:23 --> CSRF token verified.
INFO - 2024-11-25 14:41:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:41:24 --> CSRF token verified.
INFO - 2024-11-25 14:41:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:41:25 --> CSRF token verified.
INFO - 2024-11-25 14:49:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:54:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 14:57:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:00:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:01:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:01:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:06:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:13:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:25:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:30:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:30:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:30:55 --> CSRF token verified.
INFO - 2024-11-25 15:33:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:33:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:35:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:35:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:40:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:40:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:50:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:51:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:55:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:56:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:57:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:57:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:57:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 15:57:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:00:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:01:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:01:02 --> CSRF token verified.
INFO - 2024-11-25 16:03:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:03:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:03:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 17:03:48 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 16:03:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:04:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:04:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:10:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:11:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:16:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:17:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:19:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:19:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:19:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 17:19:39 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 16:21:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:21:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:21:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:22:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:22:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:22:35 --> CSRF token verified.
INFO - 2024-11-25 16:24:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:26:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:27:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:27:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:27:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:28:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:28:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:28:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:28:52 --> CSRF token verified.
INFO - 2024-11-25 16:29:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:30:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:30:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:31:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:31:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:31:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:32:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:32:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:33:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:34:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:35:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:36:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:37:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:39:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:40:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:40:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:40:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:41:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:41:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:41:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:42:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:42:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:42:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:43:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:44:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:45:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:45:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:45:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:45:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:46:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:47:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:49:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:50:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:50:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:51:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:52:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:53:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:54:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:55:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:57:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:58:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 16:59:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:00:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:01:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:02:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:02:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:03:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:03:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:03:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:03:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:03:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:04:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:05:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:05:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:05:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:06:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:06:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:06:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:06:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:06:38 --> CSRF token verified.
INFO - 2024-11-25 17:06:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:07:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:08:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:10:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:10:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:10:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:11:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:12:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:12:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:13:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:14:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:15:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:15:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:16:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:17:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:18:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:19:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:20:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:20:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:20:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:20:59 --> CSRF token verified.
INFO - 2024-11-25 17:21:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:21:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:23:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:24:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:25:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:25:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:25:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:26:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:27:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:28:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:28:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:28:47 --> CSRF token verified.
INFO - 2024-11-25 17:28:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:29:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:29:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:29:10 --> CSRF token verified.
INFO - 2024-11-25 17:29:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:30:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:30:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:30:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:31:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-25 18:31:01 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-25 17:31:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:31:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:33:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:34:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:35:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:37:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:37:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:38:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:39:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:39:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:40:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:40:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:41:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:41:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:42:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:43:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:44:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:46:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:46:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:46:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:47:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:48:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:48:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:48:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:48:29 --> CSRF token verified.
INFO - 2024-11-25 17:49:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:50:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:51:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:52:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:53:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:54:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:54:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:55:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:56:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:57:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:58:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 17:59:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:00:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:01:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:03:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:03:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:04:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:06:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:07:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:07:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:08:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:09:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:10:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:11:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:12:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:13:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:13:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:14:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:15:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:15:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:16:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:17:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:18:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:18:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:19:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:19:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:19:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:19:35 --> CSRF token verified.
INFO - 2024-11-25 18:20:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:21:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:22:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:23:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:24:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:25:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:25:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:25:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:26:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:26:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:27:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:28:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:28:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:28:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:29:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:30:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:31:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:32:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:32:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:33:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:34:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:35:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:35:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:36:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:37:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:38:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:39:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:39:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:39:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:40:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:41:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:42:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:43:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:44:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:45:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:45:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:46:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:47:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:48:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:48:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:49:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:51:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:52:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:54:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:55:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:56:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:57:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:57:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:57:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:58:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 18:59:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:00:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:00:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:00:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:00:12 --> CSRF token verified.
INFO - 2024-11-25 19:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:02:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:02:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:03:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:05:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:06:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:06:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:07:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:07:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:09:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:10:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:12:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:12:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:13:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:13:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:14:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:14:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:16:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:17:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:18:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:18:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:18:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:19:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:19:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:19:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:19:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:19:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:20:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:21:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:22:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:23:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:24:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:24:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:24:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:25:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:25:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:25:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:25:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:25:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:25:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:25:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:26:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:26:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:27:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:27:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:28:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:28:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:28:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:29:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:29:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:30:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:30:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:32:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:33:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:34:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:35:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:36:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:37:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:38:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:38:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:39:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:39:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:39:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:40:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:41:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:42:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:42:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:42:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:43:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:43:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:43:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:43:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:43:47 --> CSRF token verified.
INFO - 2024-11-25 19:43:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:43:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:44:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:45:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:45:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:46:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:46:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:46:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:47:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:49:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:51:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:51:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:53:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:53:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:54:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:55:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:57:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:57:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:58:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:59:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 19:59:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:00:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:01:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:02:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:03:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:03:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:05:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:06:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:08:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:10:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:11:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:12:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:13:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:14:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:16:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:17:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:17:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:18:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:19:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:19:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:21:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:22:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:23:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:25:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:25:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:25:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:26:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:26:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:27:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:28:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:29:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:30:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:31:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:32:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:33:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:35:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:35:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:36:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:38:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:39:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:39:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:41:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:41:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:42:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:42:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:45:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:45:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:45:52 --> CSRF token verified.
INFO - 2024-11-25 20:45:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:46:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:46:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:46:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:46:28 --> CSRF token verified.
INFO - 2024-11-25 20:46:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:46:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:46:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:46:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:46:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:46:43 --> CSRF token verified.
INFO - 2024-11-25 20:46:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:47:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:47:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:47:00 --> CSRF token verified.
INFO - 2024-11-25 20:47:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:47:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:47:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:47:02 --> CSRF token verified.
INFO - 2024-11-25 20:47:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:47:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:47:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:47:29 --> CSRF token verified.
INFO - 2024-11-25 20:47:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:47:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:47:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:47:34 --> CSRF token verified.
INFO - 2024-11-25 20:48:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:48:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:48:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:48:06 --> CSRF token verified.
INFO - 2024-11-25 20:48:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:48:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:48:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:48:28 --> CSRF token verified.
INFO - 2024-11-25 20:48:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:48:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:48:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:48:35 --> CSRF token verified.
INFO - 2024-11-25 20:48:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:48:45 --> CSRF token verified.
INFO - 2024-11-25 20:53:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:56:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 20:58:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:00:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:03:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:03:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:03:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:14:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:14:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:16:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:16:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:18:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:23:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:25:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:26:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:27:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:30:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:30:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:30:24 --> CSRF token verified.
INFO - 2024-11-25 21:32:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:49:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:51:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 21:53:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:06:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:06:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:06:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:06:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:06:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:06:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:06:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:06:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:06:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:06:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:10:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:11:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:15:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:15:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:15:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:19:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:24:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:42:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:43:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:46:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:54:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 22:54:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:03:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:03:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:06:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:14:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:17:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:19:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:26:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:30:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:31:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:33:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:33:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:34:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:34:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:34:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:34:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:37:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:37:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:39:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:41:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:45:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:45:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:47:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:50:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:50:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:51:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:53:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:53:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:54:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:54:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:54:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:54:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:54:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:54:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:54:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:54:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:54:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:54:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:54:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:54:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:54:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:54:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:54:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:54:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:55:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:56:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:57:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:57:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:57:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:57:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:57:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-25 23:57:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
