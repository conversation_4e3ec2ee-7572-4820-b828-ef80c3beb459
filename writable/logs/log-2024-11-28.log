INFO - 2024-11-28 00:07:35 --> CSRF token verified.
INFO - 2024-11-28 00:08:00 --> CSRF token verified.
INFO - 2024-11-28 00:08:13 --> CSRF token verified.
INFO - 2024-11-28 00:08:18 --> <PERSON><PERSON><PERSON> token verified.
INFO - 2024-11-28 00:08:26 --> CSRF token verified.
INFO - 2024-11-28 00:08:39 --> CSRF token verified.
INFO - 2024-11-28 00:08:43 --> CSRF token verified.
INFO - 2024-11-28 00:09:01 --> CSRF token verified.
INFO - 2024-11-28 00:09:16 --> CSRF token verified.
INFO - 2024-11-28 00:16:52 --> CS<PERSON><PERSON> token verified.
INFO - 2024-11-28 00:01:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:01:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:01:55 --> <PERSON><PERSON><PERSON> token verified.
INFO - 2024-11-28 00:05:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:05:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:11:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:11:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:11:39 --> CSRF token verified.
INFO - 2024-11-28 00:21:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:21:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:21:58 --> CSRF token verified.
INFO - 2024-11-28 00:22:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:24:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:26:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:27:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:27:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:28:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:30:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:37:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:37:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:38:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:44:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:46:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:50:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 00:54:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:01:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:03:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:03:51 --> CSRF token verified.
INFO - 2024-11-28 01:09:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:09:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:13:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:17:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:17:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:30:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:34:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:34:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:43:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:43:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:51:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:56:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 01:58:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:01:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:02:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:06:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:07:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:13:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:14:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:22:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:23:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:24:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:24:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:27:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:27:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:28:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:28:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:28:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:31:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:32:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:35:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:36:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:36:02 --> CSRF token verified.
INFO - 2024-11-28 02:38:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:38:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:39:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:39:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:39:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:39:24 --> CSRF token verified.
INFO - 2024-11-28 02:49:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:49:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:51:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:51:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:53:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:53:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 02:57:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:01:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:02:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:05:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:08:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:08:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:08:35 --> CSRF token verified.
INFO - 2024-11-28 03:12:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:13:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:13:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:14:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:14:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:23:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:23:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:27:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:27:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:31:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:36:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:36:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:36:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:36:13 --> CSRF token verified.
INFO - 2024-11-28 03:38:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:38:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:38:40 --> CSRF token verified.
INFO - 2024-11-28 03:44:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:47:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:49:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:51:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:51:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:55:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:56:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:58:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 03:59:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:01:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:03:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:03:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:12:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:12:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:12:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:12:29 --> CSRF token verified.
INFO - 2024-11-28 04:15:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:17:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:17:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:29:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:29:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:29:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:33:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:37:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:39:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:40:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:40:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:44:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:44:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:44:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:47:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:47:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:48:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:48:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:49:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:50:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:50:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:50:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:51:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 04:52:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:01:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:05:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:06:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:06:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:07:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:08:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:08:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:26:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:26:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:31:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:40:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:51:39 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:51:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:51:40 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:51:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 05:51:41 --> ErrorException: file_get_contents(/home/<USER>/web/tiqbar.com/public_html/writable/debugbar/debugbar_1732770445.075824.json): Failed to open stream: No such file or directory
[Method: GET, Route: /]
in SYSTEMPATH/Debug/Toolbar/Collectors/History.php on line 88.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Debug/Toolbar/Collectors/History.php(88): file_get_contents()
 3 SYSTEMPATH/Debug/Toolbar.php(520): CodeIgniter\Debug\Toolbar\Collectors\History->setFiles()
 4 SYSTEMPATH/Debug/Toolbar.php(497): CodeIgniter\Debug\Toolbar->format()
 5 APPPATH/Config/Events.php(48): CodeIgniter\Debug\Toolbar->respond()
 6 SYSTEMPATH/Events/Events.php(155): CodeIgniter\Events\Events::Config\{closure}()
 7 SYSTEMPATH/CodeIgniter.php(340): CodeIgniter\Events\Events::trigger()
 8 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
10 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:51:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:51:41 --> CSRF token verified.
INFO - 2024-11-28 05:51:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:51:42 --> CSRF token verified.
CRITICAL - 2024-11-28 05:51:43 --> ErrorException: unlink(/home/<USER>/web/tiqbar.com/public_html/writable/debugbar/debugbar_1732773090.986726.json): No such file or directory
[Method: GET, Route: /]
in SYSTEMPATH/Debug/Toolbar/Collectors/History.php on line 82.
 1 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/Debug/Toolbar/Collectors/History.php(82): unlink()
 3 SYSTEMPATH/Debug/Toolbar.php(520): CodeIgniter\Debug\Toolbar\Collectors\History->setFiles()
 4 SYSTEMPATH/Debug/Toolbar.php(497): CodeIgniter\Debug\Toolbar->format()
 5 APPPATH/Config/Events.php(48): CodeIgniter\Debug\Toolbar->respond()
 6 SYSTEMPATH/Events/Events.php(155): CodeIgniter\Events\Events::Config\{closure}()
 7 SYSTEMPATH/CodeIgniter.php(340): CodeIgniter\Events\Events::trigger()
 8 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
10 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:51:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:51:43 --> CSRF token verified.
INFO - 2024-11-28 05:51:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:51:43 --> CSRF token verified.
INFO - 2024-11-28 05:51:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:51:45 --> CSRF token verified.
INFO - 2024-11-28 05:51:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:51:47 --> CSRF token verified.
INFO - 2024-11-28 05:51:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:51:48 --> CSRF token verified.
INFO - 2024-11-28 05:51:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:51:50 --> CSRF token verified.
INFO - 2024-11-28 05:51:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:51:51 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:51:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:51:53 --> CSRF token verified.
INFO - 2024-11-28 05:51:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:51:53 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:51:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:51:54 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:51:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:51:54 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:51:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:51:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:51:59 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:51:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:51:59 --> CSRF token verified.
INFO - 2024-11-28 05:52:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:52:00 --> CSRF token verified.
INFO - 2024-11-28 05:52:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:52:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:52:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:52:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:52:02 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:52:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:52:02 --> CSRF token verified.
INFO - 2024-11-28 05:52:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:52:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:52:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:52:06 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:52:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:52:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:52:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:52:12 --> CSRF token verified.
INFO - 2024-11-28 05:52:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:52:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:52:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:52:16 --> CSRF token verified.
INFO - 2024-11-28 05:52:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:52:18 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:52:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:52:18 --> CSRF token verified.
INFO - 2024-11-28 05:52:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:52:22 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:52:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:52:26 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:52:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:52:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:52:36 --> CSRF token verified.
INFO - 2024-11-28 05:52:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:52:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:52:40 --> CSRF token verified.
INFO - 2024-11-28 05:52:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:53:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:53:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:53:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:53:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:53:40 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:53:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:53:40 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:53:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:53:40 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:53:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:53:40 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:53:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:53:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:53:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:53:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:53:41 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:53:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:53:41 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:53:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:53:41 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:53:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:53:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:53:41 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:53:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:53:44 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:53:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:53:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:53:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:53:50 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:53:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:53:51 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:53:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:53:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:53:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:53:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:53:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:53:52 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:53:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:53:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:53:57 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:53:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:53:58 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:53:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:53:58 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:54:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:54:01 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:54:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:54:08 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:54:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:54:09 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:54:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:54:15 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:54:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:54:15 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:54:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:54:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:54:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:54:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:54:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:54:25 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:54:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:54:25 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:54:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:54:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:54:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:54:36 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:54:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:54:39 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:54:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:54:43 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:54:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:54:46 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:57:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:57:34 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:57:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:57:34 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:57:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:57:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:57:36 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:57:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:57:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:57:36 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:57:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:57:36 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:57:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:57:40 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:57:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:57:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:57:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:57:43 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:57:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:57:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:57:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:57:47 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:57:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:57:48 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:58:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 06:58:11 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 05:58:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 05:59:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:02:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:08:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:16:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:20:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:24:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:35:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:39:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:40:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:44:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:52:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:52:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 06:52:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 07:01:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 07:24:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 07:29:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 07:30:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 07:31:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 08:31:05 --> CSRF token verified.
INFO - 2024-11-28 07:31:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 07:32:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 07:41:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 07:46:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 07:47:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 08:07:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 08:10:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 08:12:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 08:12:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 08:13:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 08:18:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 08:23:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 08:29:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 08:34:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 08:38:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 08:44:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 08:50:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 08:56:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 08:57:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 09:03:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 09:03:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 09:08:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 09:13:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 09:17:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 09:22:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 09:27:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 09:30:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 09:32:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 09:42:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 09:42:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 09:43:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:43:06 --> CSRF token verified.
INFO - 2024-11-28 09:51:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:06:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:06:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:06:41 --> CSRF token verified.
INFO - 2024-11-28 10:22:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:23:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:23:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:23:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:23:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:23:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:23:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:24:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:24:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:24:07 --> CSRF token verified.
INFO - 2024-11-28 10:24:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:24:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:24:21 --> CSRF token verified.
INFO - 2024-11-28 10:25:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:25:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:25:07 --> CSRF token verified.
INFO - 2024-11-28 10:25:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:25:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:25:47 --> CSRF token verified.
INFO - 2024-11-28 10:35:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:40:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:43:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:43:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:43:21 --> CSRF token verified.
INFO - 2024-11-28 10:43:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:51:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:53:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:53:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:54:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:58:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:58:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 10:58:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:00:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:01:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:01:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:01:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 12:01:02 --> CSRF token verified.
INFO - 2024-11-28 11:01:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:02:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:06:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:06:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 12:06:24 --> CSRF token verified.
INFO - 2024-11-28 11:07:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:09:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:17:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:21:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:28:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:31:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:37:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:38:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:38:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:39:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:43:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:44:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:44:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:49:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 11:50:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 12:01:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 12:01:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 12:01:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 13:01:54 --> CSRF token verified.
INFO - 2024-11-28 12:01:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 12:01:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 12:12:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 12:16:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 12:17:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 12:18:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 12:21:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 12:23:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 12:31:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 12:43:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 12:45:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 13:07:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 13:33:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 13:39:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 13:46:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 13:50:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 13:53:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 13:55:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 13:55:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 13:58:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 13:59:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:03:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:03:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:05:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:05:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:07:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:10:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:16:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:16:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:22:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:28:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:30:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:32:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:34:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:38:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:38:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:40:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:48:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:50:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:50:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:50:06 --> CSRF token verified.
INFO - 2024-11-28 14:50:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:58:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 14:58:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:00:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:02:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:08:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:09:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:14:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:18:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:18:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:19:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:20:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:25:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:25:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:37:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:42:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:43:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:43:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:43:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:43:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:44:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:44:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:45:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:45:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:47:28 --> CSRF token verified.
INFO - 2024-11-28 15:47:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:47:29 --> CSRF token verified.
INFO - 2024-11-28 15:47:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:47:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:48:04 --> CSRF token verified.
INFO - 2024-11-28 15:48:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:48:06 --> CSRF token verified.
INFO - 2024-11-28 15:48:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:48:11 --> CSRF token verified.
INFO - 2024-11-28 15:48:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:48:14 --> CSRF token verified.
INFO - 2024-11-28 15:48:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:48:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:52:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 15:58:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:02:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:05:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:06:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:06:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:10:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:13:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:18:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:23:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:24:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:25:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:29:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:35:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:35:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:40:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:40:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:42:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:44:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:49:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 16:55:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:00:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:03:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:05:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:07:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:08:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:08:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 18:08:14 --> CSRF token verified.
INFO - 2024-11-28 17:11:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:13:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:15:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:17:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:23:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:25:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:25:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:25:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:25:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:29:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:33:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:33:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:33:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 18:33:24 --> CSRF token verified.
INFO - 2024-11-28 17:35:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:35:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:41:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:43:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:44:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:47:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:48:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:50:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:50:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 17:58:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 18:07:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 18:07:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 19:07:32 --> CSRF token verified.
INFO - 2024-11-28 18:25:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 18:26:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 18:35:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 18:47:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
CRITICAL - 2024-11-28 19:47:50 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: Ajax/runEmailQueue]
in SYSTEMPATH/Security/Security.php on line 262.
 1 SYSTEMPATH/Security/Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH/Filters/CSRF.php(57): CodeIgniter\Security\Security->verify()
 3 SYSTEMPATH/Filters/Filters.php(205): CodeIgniter\Filters\CSRF->before()
 4 SYSTEMPATH/Filters/Filters.php(184): CodeIgniter\Filters\Filters->runBefore()
 5 SYSTEMPATH/CodeIgniter.php(481): CodeIgniter\Filters\Filters->run()
 6 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 7 SYSTEMPATH/Boot.php(325): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 9 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
INFO - 2024-11-28 18:51:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 19:02:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 19:08:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 19:08:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 19:08:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 19:08:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 19:08:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 19:08:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 19:08:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 19:08:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 19:08:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 19:17:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 19:20:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 19:43:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 19:47:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 19:54:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 20:01:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 20:03:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 20:10:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 20:10:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 20:11:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 20:19:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 20:33:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 20:45:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 20:47:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 20:49:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 20:49:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 20:55:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 21:01:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 21:18:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 21:31:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 21:31:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 21:32:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 21:34:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 21:34:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 21:42:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 21:43:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 21:43:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 21:55:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 21:57:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 21:58:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 22:00:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 22:15:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 22:28:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 22:32:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 22:32:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 22:32:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 23:32:19 --> CSRF token verified.
INFO - 2024-11-28 22:35:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 22:38:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 22:40:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 22:40:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 22:42:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 22:43:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 22:43:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 22:57:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 23:02:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 23:04:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 23:05:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 23:09:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 23:15:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 23:17:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 23:26:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 23:32:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 23:32:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 23:39:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 23:40:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
INFO - 2024-11-28 23:52:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\DatabaseHandler' driver.
