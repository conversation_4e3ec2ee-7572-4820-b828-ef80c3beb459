<?php

/**
 * Test script for Auto Title Generation (Phương án 1: Auto-generate hoàn toàn)
 *
 * This script tests the auto title generation functionality
 * Run this from the root directory: php test_auto_title_generation.php
 */

// Simple test without full CodeIgniter bootstrap
// Just test the helper functions directly

// Include the helper file directly
require_once 'app/Helpers/custom_field_helper.php';

echo "=== Testing Auto Title Generation (Phương án 1) ===\n\n";

// Test 1: Check if helper functions exist
echo "1. Checking helper functions...\n";

if (function_exists('generateTitleFromCustomFields')) {
    echo "✓ generateTitleFromCustomFields() function exists\n";
} else {
    echo "✗ generateTitleFromCustomFields() function missing\n";
}

if (function_exists('getCustomFieldValueForTitle')) {
    echo "✓ getCustomFieldValueForTitle() function exists\n";
} else {
    echo "✗ getCustomFieldValueForTitle() function missing\n";
}

if (function_exists('cleanTitleString')) {
    echo "✓ cleanTitleString() function exists\n";
} else {
    echo "✗ cleanTitleString() function missing\n";
}

echo "\n";

// Test 2: Test cleanTitleString function
echo "2. Testing cleanTitleString function...\n";

$testStrings = [
    "  Toyota   Camry   2020  " => "Toyota Camry 2020",
    "Honda Civic Black Automatic Manual" => "Honda Civic Black Automatic Manual",
    str_repeat("Very long title ", 20) => "Very long title Very long title Very long title Very long title Very long title Very long title Very long title Very long title Very long title Very...",
];

foreach ($testStrings as $input => $expected) {
    $result = cleanTitleString($input);
    $status = (strlen($result) <= 150 && trim($result) === trim($expected)) ? "✓" : "✗";
    echo "$status Input: '$input' => Output: '$result'\n";
}

echo "\n";

// Test 3: Check if files exist
echo "3. Checking implementation files...\n";

$files = [
    'app/Helpers/custom_field_helper.php' => 'Custom field helper',
    'app/Models/FieldModel.php' => 'Field model',
    'app/Models/ProductModel.php' => 'Product model'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "✓ $description exists ($file)\n";
    } else {
        echo "✗ $description missing ($file)\n";
    }
}

echo "\n=== Test Complete ===\n";

// Instructions for manual testing
echo "\nManual Testing Instructions:\n";
echo "1. Go to admin panel and add/edit a product\n";
echo "2. Select a category that has custom fields\n";
echo "3. Fill in some custom field values\n";
echo "4. Save the product\n";
echo "5. Check if the title was automatically generated from custom field values\n";
echo "6. The title should contain values from all custom fields in field_order sequence\n";
