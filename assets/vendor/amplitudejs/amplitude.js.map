{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///webpack/bootstrap c928b168697f944252b1", "webpack:///./src/config.js", "webpack:///./src/core/core.js", "webpack:///./src/visual/playPauseElements.js", "webpack:///./src/utilities/audioNavigation.js", "webpack:///./src/utilities/debug.js", "webpack:///./src/utilities/checks.js", "webpack:///./src/utilities/configState.js", "webpack:///./src/utilities/callbacks.js", "webpack:///./src/visual/metaDataElements.js", "webpack:///./src/visual/repeatElements.js", "webpack:///./src/visual/muteElements.js", "webpack:///./src/visual/volumeSliderElements.js", "webpack:///./src/utilities/repeater.js", "webpack:///./src/utilities/shuffler.js", "webpack:///./src/visual/songSliderElements.js", "webpack:///./src/visual/timeElements.js", "webpack:///./src/fx/visualizations.js", "webpack:///./src/soundcloud/soundcloud.js", "webpack:///./src/visual/playbackSpeedElements.js", "webpack:///./src/visual/shuffleElements.js", "webpack:///./src/visual/songPlayedProgressElements.js", "webpack:///./src/init/init.js", "webpack:///./src/fx/waveform.js", "webpack:///./src/utilities/time.js", "webpack:///./src/visual/bufferedProgressElements.js", "webpack:///./src/events/ended.js", "webpack:///./src/events/events.js", "webpack:///./src/events/keydown.js", "webpack:///./src/events/mute.js", "webpack:///./src/events/next.js", "webpack:///./src/events/pause.js", "webpack:///./src/events/play.js", "webpack:///./src/events/playPause.js", "webpack:///./src/events/playbackSpeed.js", "webpack:///./src/events/prev.js", "webpack:///./src/events/progress.js", "webpack:///./src/events/repeat.js", "webpack:///./src/events/repeatSong.js", "webpack:///./src/events/shuffle.js", "webpack:///./src/events/skipTo.js", "webpack:///./src/events/songSlider.js", "webpack:///./src/events/stop.js", "webpack:///./src/events/timeUpdate.js", "webpack:///./src/events/volumeDown.js", "webpack:///./src/events/volumeSlider.js", "webpack:///./src/events/volumeUp.js", "webpack:///./src/fx/fx.js", "webpack:///./src/index.js", "webpack:///./src/init/playlists.js", "webpack:///./src/visual/containerElements.js", "webpack:///./src/visual/time/currentHourElements.js", "webpack:///./src/visual/time/currentMinuteElements.js", "webpack:///./src/visual/time/currentSecondElements.js", "webpack:///./src/visual/time/currentTimeElements.js", "webpack:///./src/visual/time/durationCountDownTimeElements.js", "webpack:///./src/visual/time/durationHourElements.js", "webpack:///./src/visual/time/durationMinuteElements.js", "webpack:///./src/visual/time/durationSecondElements.js", "webpack:///./src/visual/time/durationTimeElements.js", "webpack:///./package.json"], "names": ["module", "exports", "version", "audio", "Audio", "active_metadata", "active_album", "active_index", "active_playlist", "playback_speed", "callbacks", "songs", "playlists", "start_song", "starting_playlist", "starting_playlist_song", "repeat", "repeat_song", "shuffle_list", "shuffle_on", "default_album_art", "default_playlist_art", "debug", "volume", "pre_mute_volume", "volume_increment", "volume_decrement", "soundcloud_client", "soundcloud_use_art", "soundcloud_song_count", "soundcloud_songs_ready", "is_touch_moving", "buffered", "bindings", "continue_next", "delay", "player_state", "web_audio_api_available", "context", "source", "analyser", "visualizations", "available", "active", "backup", "waveforms", "sample_rate", "built", "Core", "play", "Visualizations", "stop", "run", "config", "live", "reconnectStream", "test", "navigator", "userAgent", "paused", "playPromise", "undefined", "then", "catch", "playbackRate", "pause", "disconnectStream", "currentTime", "Callbacks", "setVolume", "volumeLevel", "muted", "setSongLocation", "songPercentage", "duration", "skipToLocation", "seconds", "addEventListener", "Debug", "writeMessage", "once", "src", "load", "url", "setPlaybackSpeed", "playbackSpeed", "PlayPauseElements", "sync", "syncGlobal", "syncPlaylist", "syncSong", "syncSongInPlaylist", "state", "playPauseElements", "document", "querySelectorAll", "i", "length", "playlist", "getAttribute", "song", "setElementPlay", "setElementPause", "playlistPlayPauseElements", "songPlayPauseElements", "activePlaylistIndex", "songInPlaylistPlayPauseElements", "syncToPause", "element", "classList", "add", "remove", "AudioNavigation", "setNext", "songEnded", "nextIndex", "nextSong", "endOfList", "index", "parseInt", "changeSong", "setNextPlaylist", "shuffle", "setActivePlaylist", "changeSongPlaylist", "set<PERSON>revious", "previousIndex", "previousSong", "setPreviousPlaylist", "prepareSongChange", "album", "afterSongChange", "SongSliderElements", "resetElements", "SongPlayedProgressElements", "TimeElements", "resetCurrentTimes", "Checks", "newAlbum", "MetaDataElements", "displayMetaData", "ContainerElements", "setActive", "resetDurationTimes", "message", "console", "log", "newSong", "songIndex", "newPlaylist", "isURL", "pattern", "isInt", "int", "isNaN", "Number", "ConfigState", "resetConfig", "setPlayerState", "initialize", "callback<PERSON><PERSON>", "callbackFunction", "error", "imageMetaDataKeys", "songInfoElements", "info", "val", "indexOf", "setAttribute", "innerHTML", "displayPlaylistMetaData", "playlistInfoElements", "setFirstSongInPlaylist", "elementPlaylist", "syncMetaData", "RepeatElements", "syncRepeat", "repeatClasses", "getElementsByClassName", "syncRepeatPlaylist", "repeatButtons", "syncRepeatSong", "repeatSongClasses", "Mute<PERSON><PERSON><PERSON>", "setMuted", "muteClasses", "VolumeSliderElements", "volumeSliders", "value", "<PERSON><PERSON><PERSON>", "setRepeat", "setRepeatPlaylist", "setRepeatSong", "<PERSON>ffle<PERSON>", "setShuffle", "shuffleSongs", "toggleShuffle", "setShufflePlaylist", "shufflePlaylistSongs", "toggleShufflePlaylist", "shuffleTemp", "Array", "randNum", "Math", "floor", "random", "shuffleSwap", "shuffleList", "original", "temp", "location", "sync<PERSON>ain", "mainSongSliders", "playlistSongSliders", "playlistAttribute", "songAttribute", "songSliders", "songInPlaylistSliders", "CurrentTimeElements", "resetTimes", "CurrentHourElements", "CurrentMinuteElements", "CurrentSecondElements", "syncCurrentTimes", "hours", "minutes", "DurationCountDownTimeElements", "DurationHourElements", "DurationMinuteElements", "DurationSecondElements", "DurationTimeElements", "syncDurationTimes", "songDuration", "visualizationElements", "Object", "keys", "runGlobalVisualization", "runPlaylistVisualization", "runSongVisualization", "runSongInPlaylistVisualization", "displayBackups", "globalVisualizationIndex", "visualization", "activeSongVisualizationIndex", "addToActiveVisualizations", "firstVisualization", "activePlaylistVisualizationIndex", "key", "setPreferences", "startVisualization", "push", "stopVisualization", "register", "preferences", "newVisualization", "getID", "x", "displayGlobalBackup", "displayPlaylistBackup", "displaySongBackup", "displaySongInPlaylistBackup", "style", "backgroundImage", "cover_art_url", "SoundCloud", "tempUserConfig", "loadSoundCloud", "userConfig", "head", "getElementsByTagName", "script", "createElement", "type", "onreadystatechange", "initSoundcloud", "onload", "append<PERSON><PERSON><PERSON>", "SC", "client_id", "getStreamableURLs", "soundcloud_regex", "match", "resolveStreamable", "resolveIndividualStreamableURL", "addToShuffleList", "get", "sound", "streamable", "stream_url", "artwork_url", "soundcloud_data", "AmplitudeHelpers", "writeDebugMessage", "name", "artist", "AmplitudeInitializer", "setConfig", "isSoundCloudURL", "PlaybackSpeedElements", "playbackSpeedClasses", "ShuffleElements", "shuffleButtons", "songPlayedPercentage", "percentage", "songPlayedProgressBars", "max", "Initializer", "ready", "Events", "setArt", "Fx", "webAudioAPIAvailable", "determineUsingAnyFX", "configureWebAudioAPI", "documentElement", "resume", "WaveForm", "init", "object", "params", "initializeDefaultLiveSettings", "initializeDefaultSongIndexes", "rebindDisplay", "countPlaylists", "PlaylistsInitializer", "preload", "initializeElements", "size", "hasOwnProperty", "buffer", "sampleRate", "peaks", "svg", "createElementNS", "g", "path", "build", "abs", "split", "reduce", "a", "b", "charCodeAt", "req", "XMLHttpRequest", "open", "responseType", "e", "readyState", "status", "decodeAudioData", "response", "bufferedAudio", "getPeaks", "process", "send", "displayWaveForms", "totalPeaks", "d", "peakNumber", "shift", "sampleSize", "sampleStep", "numberOfChannels", "mergedPeaks", "channelNumber", "channelData", "getChannelData", "start", "end", "min", "sampleIndex", "waveformElements", "displayGlobalWaveform", "displayPlaylistWaveform", "displaySongWaveform", "displaySongInPlaylistWaveform", "waveformPath", "querySelector", "determineIfUsingWaveforms", "Time", "computeCurrentTimes", "currentSeconds", "currentMinutes", "currentHours", "computeSongDuration", "songDurationSeconds", "songDurationMinutes", "songDurationHours", "toString", "computeSongCompletionPercentage", "setCurrentTime", "time", "isFinite", "BufferedProgressElements", "songBufferedProgressBars", "parseFloat", "songBufferedProgressBarsPlaylist", "songBufferedProgressBarsSongs", "songBufferedProgressBarsSongsInPlaylist", "reset", "Ended", "handle", "setTimeout", "AmplitudeCore", "bindTimeUpdate", "bindKeyDownEventHandlers", "bindSongEnded", "bindProgress", "bindPlay", "bindPause", "bindPlayPause", "bindStop", "bindMute", "bindVolumeUp", "bindVolumeDown", "bindSongSlider", "bindVolumeSlider", "bindNext", "bindPrev", "bindShuffle", "bindRepeat", "bindRepeatSong", "bindPlaybackSpeed", "bindSkipTo", "bindCanPlayThrough", "removeEventListener", "TimeUpdate", "KeyDown", "Progress", "play_classes", "Play", "pause_classes", "Pause", "play_pause_classes", "PlayPause", "stop_classes", "Stop", "mute_classes", "Mute", "volume_up_classes", "VolumeUp", "volume_down_classes", "VolumeDown", "ua", "window", "msie", "song_sliders", "SongSlider", "volume_sliders", "VolumeSlider", "next_classes", "Next", "prev_classes", "Prev", "shuffle_classes", "Shuffle", "repeat_classes", "Repeat", "repeat_song_classes", "RepeatSong", "playback_speed_classes", "PlaybackSpeed", "skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "SkipTo", "event", "runKeyEvent", "which", "runPlayPauseKeyDownEvent", "runNextKeyDownEvent", "runPrevKeyDownEvent", "runStopKeyDownEvent", "runShuffleKeyDownEvent", "runRepeatKeyDownEvent", "handleGlobalNext", "handlePlaylistNext", "songIndexAttribute", "handleGlobalPause", "handlePlaylistPause", "handleSongPause", "handleSongInPlaylistPause", "handleGlobalPlay", "handlePlaylistPlay", "handleSongPlay", "handleSongInPlaylistPlay", "handleGlobalPlayPause", "handlePlaylistPlayPause", "handleSongPlayPause", "handleSongInPlaylistPlayPause", "handleGlobalPrev", "handlePlaylistPrev", "bufferedEnd", "handleGlobalRepeat", "handlePlaylistRepeat", "handleGlobalShuffle", "handlePlaylistShuffle", "handleSkipToSong", "handleSkipToPlaylist", "locationPercentage", "computedTime", "handleGlobalSongSlider", "handlePlaylistSongSlider", "handleSongSongSlider", "handleSongInPlaylistSongSlider", "computeBufferedTime", "updateTimeInformation", "runTimeCallbacks", "songCompletionPercentage", "time_callbacks", "browserContext", "AudioContext", "webkitAudioContext", "mozAudioContext", "oAudioContext", "msAudioContext", "create<PERSON><PERSON>yser", "crossOrigin", "createMediaElementSource", "connect", "destination", "Amplitude", "getConfig", "bindNewElements", "getActivePlaylist", "getPlaybackSpeed", "speed", "getRepeat", "getRepeatPlaylist", "playlist<PERSON><PERSON>", "getShuffle", "getShufflePlaylist", "repeatState", "getDefaultAlbumArt", "getDefaultPlaylistArt", "setDefaultAlbumArt", "setDefaultPlaylistArt", "default_plalist_art", "getSongPlayedPercentage", "getSongPlayedSeconds", "getSongDuration", "setSongPlayedPercentage", "setDebug", "getActiveSongMetadata", "getActivePlaylistMetadata", "getSongAtIndex", "getSongAtPlaylistIndex", "addSong", "addSongToPlaylist", "addPlaylist", "data", "<PERSON><PERSON><PERSON><PERSON>", "dataKey", "removeSong", "splice", "removeSongFromPlaylist", "playNow", "playSongAtIndex", "playPlaylistSongAtIndex", "getAudio", "get<PERSON><PERSON><PERSON><PERSON>", "next", "nextData", "prev", "prevData", "getSongs", "getSongsInPlaylist", "getSongsState", "getSongsStatePlaylist", "getActiveIndex", "getVersion", "getBuffered", "skip<PERSON>o", "setSongMetaData", "metaData", "setPlaylistMetaData", "<PERSON><PERSON><PERSON><PERSON>", "get<PERSON>elay", "getPlayerState", "registerVisualization", "setPlaylistVisualization", "visualizationKey", "setSongVisualization", "setSongInPlaylistVisualization", "setGlobalVisualization", "getVolume", "copySongsToPlaylists", "grabSoundCloudData", "initializePlaylistActiveIndexes", "initializePlaylistShuffleStatuses", "initializePlaylistsRepeatStatuses", "initializePlaylistShuffleLists", "initializeFirstSongInPlaylistMetaData", "songContainers", "activeIndex", "hasAttribute", "currentHourSelectors", "currentHourPlaylistSelectors", "currentHourSongSelectors", "currentHourPlaylistSongSelectors", "hourSelectors", "currentMinuteSelectors", "currentMinutePlaylistSelectors", "currentMinuteSongSelectors", "currentMinutePlaylistSongSelectors", "minuteSelectors", "currentSecondSelectors", "currentSecondPlaylistSelectors", "currentSecondSongSelectors", "currentSecondPlaylistSongSelectors", "secondSelectors", "currentTimeSelectors", "timeText", "timeSelectors", "countDownTime", "timeRemaining", "computeTimeRemaining", "durationTimeRemainingSelectors", "totalCurrentSeconds", "totalDurationSeconds", "timeRemainingTotalSeconds", "remainingHours", "remainingMinutes", "remainingSeconds", "durationHourSelectors", "durationMinuteSelectors", "durationSecondSelectors", "durationTime", "durationText", "computeDurationText", "durationTimeSelectors"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;ACVA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA,mDAA2C,cAAc;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,mCAA2B,0BAA0B,EAAE;AACvD,yCAAiC,eAAe;AAChD;AACA;AACA;;AAEA;AACA,8DAAsD,+DAA+D;;AAErH;AACA;;AAEA;AACA;;;;;;;;;;ACdA;;AAEAA,OAAOC,OAAP,GAAiB;AACfC,WAASA,gBADM;;AAGfC,SAAO,IAAIC,KAAJ,EAHQ;;AAKfC,mBAAiB,EALF;;AAOfC,gBAAc,EAPC;;AASfC,gBAAc,CATC;;AAWfC,mBAAiB,IAXF;;AAafC,kBAAgB,GAbD;;AAefC,aAAW,EAfI;;AAiBfC,SAAO,EAjBQ;;AAmBfC,aAAW,EAnBI;;AAqBfC,cAAY,EArBG;;AAuBfC,qBAAmB,EAvBJ;;AAyBfC,0BAAwB,EAzBT;;AA2BfC,UAAQ,KA3BO;;AA6BfC,eAAa,KA7BE;;AA+BfC,gBAAc,EA/BC;;AAiCfC,cAAY,KAjCG;;AAmCfC,qBAAmB,EAnCJ;;AAqCfC,wBAAsB,EArCP;;AAuCfC,SAAO,KAvCQ;;AAyCfC,UAAQ,GAzCO;;AA2CfC,mBAAiB,GA3CF;;AA6CfC,oBAAkB,CA7CH;;AA+CfC,oBAAkB,CA/CH;;AAiDfC,qBAAmB,EAjDJ;;AAmDfC,sBAAoB,KAnDL;;AAqDfC,yBAAuB,CArDR;;AAuDfC,0BAAwB,CAvDT;;AAyDfC,mBAAiB,KAzDF;;AA2DfC,YAAU,CA3DK;;AA6DfC,YAAU,EA7DK;;AA+DfC,iBAAe,IA/DA;;AAiEfC,SAAO,CAjEQ;;AAmEfC,gBAAc,SAnEC;;AAqEfC,2BAAyB,KArEV;;AAuEfC,WAAS,IAvEM;;AAyEfC,UAAQ,IAzEO;;AA2EfC,YAAU,IA3EK;;AA6EfC,kBAAgB;AACdC,eAAW,EADG;;AAGdC,YAAQ,EAHM;;AAKdC,YAAQ;AALM,GA7ED;;AAqFfC,aAAW;AACTC,iBAAa,GADJ;;AAGTC,WAAO;AAHE;AArFI,CAAjB,C,CApDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACIA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;;;;;;AAZA;;;;;;AAZA;;;;;;AAZA;;;;AAZA;;;;AAyDA,IAAIC,OAAQ,YAAW;AACrB;;;;;;;;AAQA,WAASC,IAAT,GAAgB;AACdC,6BAAeC,IAAf;AACAD,6BAAeE,GAAf;;AAEA;;;AAGA,QAAIC,iBAAOhD,eAAP,CAAuBiD,IAA3B,EAAiC;AAC/BC;AACD;;AAED;;;;;;;AAOA,QACE,iEAAiEC,IAAjE,CACEC,UAAUC,SADZ,KAGA,CAACL,iBAAOM,MAJV,EAKE;AACAJ;AACD;;AAED;;;;AAIA,QAAIK,cAAcP,iBAAOlD,KAAP,CAAa8C,IAAb,EAAlB;;AAEA,QAAIW,gBAAgBC,SAApB,EAA+B;AAC7BD,kBAAYE,IAAZ,CAAkB,aAAK,CAEtB,CAFD,EAGCC,KAHD,CAGQ,iBAAS,CAEhB,CALD;AAMD;AACDV,qBAAOlD,KAAP,CAAa8C,IAAb;AACAI,qBAAOlD,KAAP,CAAa6D,YAAb,GAA4BX,iBAAO5C,cAAnC;AACD;;AAED;;;;;;;AAOA,WAASwD,KAAT,GAAiB;AACff,6BAAeC,IAAf;;AAEA;;;AAGAE,qBAAOlD,KAAP,CAAa8D,KAAb;;AAEA;;;AAGAZ,qBAAOM,MAAP,GAAgB,IAAhB;;AAEA;;;;AAIA,QAAIN,iBAAOhD,eAAP,CAAuBiD,IAA3B,EAAiC;AAC/BY;AACD;AACF;;AAED;;;;;;;;;AASA,WAASf,IAAT,GAAgB;AACdD,6BAAeC,IAAf;;AAEA;;;AAGA,QAAIE,iBAAOlD,KAAP,CAAagE,WAAb,IAA4B,CAAhC,EAAmC;AACjCd,uBAAOlD,KAAP,CAAagE,WAAb,GAA2B,CAA3B;AACD;;AAED;;;AAGAd,qBAAOlD,KAAP,CAAa8D,KAAb;;AAEA;;;AAGA,QAAIZ,iBAAOhD,eAAP,CAAuBiD,IAA3B,EAAiC;AAC/BY;AACD;;AAED;;;AAGAE,wBAAUhB,GAAV,CAAc,MAAd;AACD;;AAED;;;;;;;;;AASA,WAASiB,SAAT,CAAmBC,WAAnB,EAAgC;AAC9B;;;AAGA,QAAIA,eAAe,CAAnB,EAAsB;AACpBjB,uBAAOlD,KAAP,CAAaoE,KAAb,GAAqB,IAArB;AACD,KAFD,MAEO;AACLlB,uBAAOlD,KAAP,CAAaoE,KAAb,GAAqB,KAArB;AACD;;AAED;;;AAGAlB,qBAAO9B,MAAP,GAAgB+C,WAAhB;;AAEA;;;AAGAjB,qBAAOlD,KAAP,CAAaoB,MAAb,GAAsB+C,cAAc,GAApC;AACD;;AAED;;;;;;;;;;AAUA,WAASE,eAAT,CAAyBC,cAAzB,EAAyC;AACvC;;;;AAIA,QAAI,CAACpB,iBAAOhD,eAAP,CAAuBiD,IAA5B,EAAkC;AAChCD,uBAAOlD,KAAP,CAAagE,WAAb,GAA2Bd,iBAAOlD,KAAP,CAAauE,QAAb,IAAyBD,iBAAiB,GAA1C,CAA3B;AACD;AACF;;AAED;;;;;;;;AAQA,WAASE,cAAT,CAAwBC,OAAxB,EAAiC;AAC/B;;;;;AAKAvB,qBAAOlD,KAAP,CAAa0E,gBAAb,CACE,gBADF,EAEE,YAAW;AACT;;;;;AAKA,UAAIxB,iBAAOlD,KAAP,CAAauE,QAAb,IAAyBE,OAAzB,IAAoCA,UAAU,CAAlD,EAAqD;AACnDvB,yBAAOlD,KAAP,CAAagE,WAAb,GAA2BS,OAA3B;AACD,OAFD,MAEO;AACLE,wBAAMC,YAAN,CACE,0FADF;AAGD;AACF,KAfH,EAgBE,EAAEC,MAAM,IAAR,EAhBF;AAkBD;;AAED;;;;;;;AAOA,WAASd,gBAAT,GAA4B;AAC1Bb,qBAAOlD,KAAP,CAAa8E,GAAb,GAAmB,EAAnB;AACA5B,qBAAOlD,KAAP,CAAa+E,IAAb;AACD;;AAED;;;;;;;AAOA,WAAS3B,eAAT,GAA2B;AACzBF,qBAAOlD,KAAP,CAAa8E,GAAb,GAAmB5B,iBAAOhD,eAAP,CAAuB8E,GAA1C;AACA9B,qBAAOlD,KAAP,CAAa+E,IAAb;AACD;;AAED;;;;;AAKA,WAASE,gBAAT,CAA0BC,aAA1B,EAAyC;AACvC;;;AAGAhC,qBAAO5C,cAAP,GAAwB4E,aAAxB;;AAEA;;;AAGAhC,qBAAOlD,KAAP,CAAa6D,YAAb,GAA4BX,iBAAO5C,cAAnC;AACD;;AAED;;;AAGA,SAAO;AACLwC,UAAMA,IADD;AAELgB,WAAOA,KAFF;AAGLd,UAAMA,IAHD;AAILkB,eAAWA,SAJN;AAKLG,qBAAiBA,eALZ;AAMLG,oBAAgBA,cANX;AAOLT,sBAAkBA,gBAPb;AAQLX,qBAAiBA,eARZ;AASL6B,sBAAkBA;AATb,GAAP;AAWD,CAnQU,EAAX;;AAfA;;;;;;AAZA;;;;;;AAZA;;;;;;AAZA;;;;kBAwTepC,I;;;;;;;;;;;;;;AC1Tf;;;;;;AAEA;;;;AAIA,IAAIsC,oBAAqB,YAAW;AAClC;;;;;AAKA,WAASC,IAAT,GAAgB;AACdC;AACAC;AACAC;AACAC;AACD;;AAED;;;;;AAKA,WAASH,UAAT,GAAsB;AACpB;;;AAGA,QAAII,QAAQvC,iBAAOlD,KAAP,CAAawD,MAAb,GAAsB,QAAtB,GAAiC,SAA7C;;AAEA;;;AAGA,QAAMkC,oBAAoBC,SAASC,gBAAT,CACxB,uBADwB,CAA1B;;AAIA;;;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIH,kBAAkBI,MAAtC,EAA8CD,GAA9C,EAAmD;AACjD;;;AAGA,UAAIE,WAAWL,kBAAkBG,CAAlB,EAAqBG,YAArB,CACb,yBADa,CAAf;AAGA,UAAIC,OAAOP,kBAAkBG,CAAlB,EAAqBG,YAArB,CAAkC,2BAAlC,CAAX;;AAEA;;;;;AAKA,UAAID,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpC;;;;AAIA,gBAAQR,KAAR;AACE,eAAK,SAAL;AACES,2BAAeR,kBAAkBG,CAAlB,CAAf;AACA;AACF,eAAK,QAAL;AACEM,4BAAgBT,kBAAkBG,CAAlB,CAAhB;AACA;AANJ;AAQD;AACF;AACF;;AAED;;;;;AAKA,WAASP,YAAT,GAAwB;AACtB,QAAIG,QAAQvC,iBAAOlD,KAAP,CAAawD,MAAb,GAAsB,QAAtB,GAAiC,SAA7C;;AAEA;;;AAGA,QAAM4C,4BAA4BT,SAASC,gBAAT,CAChC,oDACE1C,iBAAO7C,eADT,GAEE,IAH8B,CAAlC;;AAMA;;;AAGA,SAAK,IAAIwF,IAAI,CAAb,EAAgBA,IAAIO,0BAA0BN,MAA9C,EAAsDD,GAAtD,EAA2D;AACzD;;;AAGA,UAAII,OAAOG,0BAA0BP,CAA1B,EAA6BG,YAA7B,CACT,2BADS,CAAX;;AAIA;;;;;AAKA,UAAIC,QAAQ,IAAZ,EAAkB;AAChB;;;;AAIA,gBAAQR,KAAR;AACE,eAAK,SAAL;AACES,2BAAeE,0BAA0BP,CAA1B,CAAf;AACA;AACF,eAAK,QAAL;AACEM,4BAAgBC,0BAA0BP,CAA1B,CAAhB;AACA;AANJ;AAQD;AACF;AACF;;AAED;;;;;AAKA,WAASN,QAAT,GAAoB;AAClB,QAAIE,QAAQvC,iBAAOlD,KAAP,CAAawD,MAAb,GAAsB,QAAtB,GAAiC,SAA7C;;AAEA;;;;AAIA,QAAI6C,wBAAwBV,SAASC,gBAAT,CAC1B,sDACE1C,iBAAO9C,YADT,GAEE,IAHwB,CAA5B;;AAMA;;;AAGA,SAAK,IAAIyF,IAAI,CAAb,EAAgBA,IAAIQ,sBAAsBP,MAA1C,EAAkDD,GAAlD,EAAuD;AACrD;;;AAGA,UAAIE,WAAWM,sBAAsBR,CAAtB,EAAyBG,YAAzB,CACb,yBADa,CAAf;;AAIA;;;AAGA,UAAID,YAAY,IAAhB,EAAsB;AACpB;;;;AAIA,gBAAQN,KAAR;AACE,eAAK,SAAL;AACES,2BAAeG,sBAAsBR,CAAtB,CAAf;AACA;AACF,eAAK,QAAL;AACEM,4BAAgBE,sBAAsBR,CAAtB,CAAhB;AACA;AANJ;AAQD;AACF;AACF;;AAED;;;;;;AAMA,WAASL,kBAAT,GAA8B;AAC5B,QAAIC,QAAQvC,iBAAOlD,KAAP,CAAawD,MAAb,GAAsB,QAAtB,GAAiC,SAA7C;;AAEA,QAAI8C,sBACFpD,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA1D,GACI6C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAD7C,GAEI,IAHN;;AAKA;;;;;AAKA,QAAImG,kCAAkCZ,SAASC,gBAAT,CACpC,sDACEU,mBADF,GAEE,8BAFF,GAGEpD,iBAAO7C,eAHT,GAIE,IALkC,CAAtC;;AAQA;;;;AAIA,SAAK,IAAIwF,IAAI,CAAb,EAAgBA,IAAIU,gCAAgCT,MAApD,EAA4DD,GAA5D,EAAiE;AAC/D;;;;AAIA,cAAQJ,KAAR;AACE,aAAK,SAAL;AACES,yBAAeK,gCAAgCV,CAAhC,CAAf;AACA;AACF,aAAK,QAAL;AACEM,0BAAgBI,gCAAgCV,CAAhC,CAAhB;AACA;AANJ;AAQD;AACF;;AAED;;;;;AAKA,WAASW,WAAT,GAAuB;AACrB;;;AAGA,QAAId,oBAAoBC,SAASC,gBAAT,CAA0B,uBAA1B,CAAxB;;AAEA;;;AAGA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIH,kBAAkBI,MAAtC,EAA8CD,GAA9C,EAAmD;AACjDM,sBAAgBT,kBAAkBG,CAAlB,CAAhB;AACD;AACF;;AAED;;;;;;;AAOA,WAASK,cAAT,CAAwBO,OAAxB,EAAiC;AAC/BA,YAAQC,SAAR,CAAkBC,GAAlB,CAAsB,mBAAtB;AACAF,YAAQC,SAAR,CAAkBE,MAAlB,CAAyB,kBAAzB;AACD;;AAED;;;;;;;AAOA,WAAST,eAAT,CAAyBM,OAAzB,EAAkC;AAChCA,YAAQC,SAAR,CAAkBE,MAAlB,CAAyB,mBAAzB;AACAH,YAAQC,SAAR,CAAkBC,GAAlB,CAAsB,kBAAtB;AACD;;AAED;;;AAGA,SAAO;AACLvB,UAAMA,IADD;AAELC,gBAAYA,UAFP;AAGLC,kBAAcA,YAHT;AAILC,cAAUA,QAJL;AAKLC,wBAAoBA,kBALf;AAMLgB,iBAAaA;AANR,GAAP;AAQD,CA1QuB,EAAxB,C,CAVA;;;;kBAsRerB,iB;;;;;;;;;;;;;;AClRf;;;;AAOA;;;;AAOA;;;;AAOA;;;;AAOA;;;;AAOA;;;;AAOA;;;;AAOA;;;;AAOA;;;;AAOA;;;;;;AAEA;;;;;;;AAdA;;;;;;;AAdA;;;;;;;AAdA;;;;;;;AAdA;;;;;AAbA;;;;AA0EA,IAAI0B,kBAAmB,YAAW;AAChC;;;;;;;AAOA,WAASC,OAAT,GAAoC;AAAA,QAAnBC,SAAmB,uEAAP,KAAO;;AAClC;;;;AAIA,QAAIC,YAAY,IAAhB;AACA,QAAIC,WAAW,EAAf;;AAEA;;;AAGA,QAAIC,YAAY,KAAhB;;AAEA;;;;AAIA,QAAIhE,iBAAOpC,WAAX,EAAwB;AACtB;;;AAGA,UAAIoC,iBAAOlC,UAAX,EAAuB;AACrBgG,oBAAY9D,iBAAOnC,YAAP,CAAqBmC,iBAAO9C,YAA5B,EAA2C+G,KAAvD;AACAF,mBAAW/D,iBAAOnC,YAAP,CAAoBiG,SAApB,CAAX;AACD,OAHD,MAGO;AACLA,oBAAY9D,iBAAO9C,YAAnB;AACA6G,mBAAW/D,iBAAO1C,KAAP,CAAawG,SAAb,CAAX;AACD;AACF,KAXD,MAWO;AACL;;;;AAIA,UAAI9D,iBAAOlC,UAAX,EAAuB;AACrB;;;;;AAKA,YAAIoG,SAASlE,iBAAO9C,YAAhB,IAAgC,CAAhC,GAAoC8C,iBAAOnC,YAAP,CAAoB+E,MAA5D,EAAoE;AAClE;;;AAGAkB,sBAAYI,SAAUlE,iBAAO9C,YAAjB,IAAkC,CAA9C;AACD,SALD,MAKO;AACL4G,sBAAY,CAAZ;AACAE,sBAAY,IAAZ;AACD;;AAEDD,mBAAW/D,iBAAOnC,YAAP,CAAqBiG,SAArB,CAAX;AACD,OAjBD,MAiBO;AACL;;;;;AAKA,YAAII,SAASlE,iBAAO9C,YAAhB,IAAgC,CAAhC,GAAoC8C,iBAAO1C,KAAP,CAAasF,MAArD,EAA6D;AAC3DkB,sBAAYI,SAASlE,iBAAO9C,YAAhB,IAAgC,CAA5C;AACD,SAFD,MAEO;AACL4G,sBAAY,CAAZ;AACAE,sBAAY,IAAZ;AACD;;AAED;;;AAGAD,mBAAW/D,iBAAO1C,KAAP,CAAawG,SAAb,CAAX;AACD;AACF;;AAED;;;AAGAK,eAAWJ,QAAX,EAAqBD,SAArB;;AAEA;;;AAGA,QAAIE,aAAa,CAAChE,iBAAOrC,MAAzB,EAAiC,CAChC,CADD,MACO;AACL;;;AAGA,UAAI,EAAEkG,aAAa,CAAC7D,iBAAOrC,MAArB,IAA+BqG,SAAjC,CAAJ,EAAiD;AAC/CrE,uBAAKC,IAAL;AACD;AACF;;AAED;;;;AAIAqC,gCAAkBC,IAAlB;AACAnB,wBAAUhB,GAAV,CAAc,MAAd;;AAEA;;;AAGA,QAAIC,iBAAOpC,WAAX,EAAwB;AACtBmD,0BAAUhB,GAAV,CAAc,eAAd;AACD;AACF;;AAED;;;;;;;AAOA,WAASqE,eAAT,CAAyBvB,QAAzB,EAAsD;AAAA,QAAnBgB,SAAmB,uEAAP,KAAO;;AACpD;;;AAGA,QAAIC,YAAY,IAAhB;AACA,QAAIC,WAAW,EAAf;;AAEA;;;AAGA,QAAIC,YAAY,KAAhB;;AAEA;;;AAGA,QAAIhE,iBAAOpC,WAAX,EAAwB;AACtB;;;AAGA,UAAIoC,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BwB,OAA/B,EAAwC;AACtCP,oBAAY9D,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2B3F,YAAvC;AACA6G,mBAAW/D,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BhF,YAA3B,CAAwCiG,SAAxC,CAAX;AACD,OAHD,MAGO;AACLA,oBAAY9D,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2B3F,YAAvC;AACA6G,mBAAW/D,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCwG,SAAjC,CAAX;AACD;AACF,KAXD,MAWO;AACL;;;AAGA,UAAI9D,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BwB,OAA/B,EAAwC;AACtC;;;;AAIA,YACEH,SAASlE,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2B3F,YAApC,IAAoD,CAApD,GACA8C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BhF,YAA3B,CAAwC+E,MAF1C,EAGE;AACA;;;AAGAkB,sBAAY9D,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2B3F,YAA3B,GAA0C,CAAtD;AACD,SARD,MAQO;AACL4G,sBAAY,CAAZ;AACAE,sBAAY,IAAZ;AACD;;AAEDD,mBAAW/D,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BhF,YAA3B,CAAwCiG,SAAxC,CAAX;AACD,OAnBD,MAmBO;AACL;;;;AAIA,YACEI,SAASlE,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2B3F,YAApC,IAAoD,CAApD,GACA8C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCsF,MAFnC,EAGE;AACAkB,sBAAYI,SAASlE,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2B3F,YAApC,IAAoD,CAAhE;AACD,SALD,MAKO;AACL4G,sBAAY,CAAZ;AACAE,sBAAY,IAAZ;AACD;;AAED;;;AAGAD,mBAAW/D,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCwG,SAAjC,CAAX;AACD;AACF;;AAED;;;AAGAQ,sBAAkBzB,QAAlB;;AAEA;;;AAGA0B,uBAAmB1B,QAAnB,EAA6BkB,QAA7B,EAAuCD,SAAvC;;AAEA;;;AAGA,QAAIE,aAAa,CAAChE,iBAAOrC,MAAzB,EAAiC,CAChC,CADD,MACO;AACL,UAAI,EAAEkG,aAAa,CAAC7D,iBAAOrC,MAArB,IAA+BqG,SAAjC,CAAJ,EAAiD;AAC/CrE,uBAAKC,IAAL;AACD;AACF;;AAED;;;AAGAqC,gCAAkBC,IAAlB;AACAnB,wBAAUhB,GAAV,CAAc,MAAd;;AAEA;;;AAGA,QAAIC,iBAAOpC,WAAX,EAAwB;AACtBmD,0BAAUhB,GAAV,CAAc,eAAd;AACD;AACF;;AAED;;;;;AAKA,WAASyE,WAAT,GAAuB;AACrB;;;AAGA,QAAIC,gBAAgB,IAApB;AACA,QAAIC,eAAe,EAAnB;;AAEA;;;AAGA,QAAI1E,iBAAOpC,WAAX,EAAwB;AACtB;;;AAGA,UAAIoC,iBAAOlC,UAAX,EAAuB;AACrB2G,wBAAgBzE,iBAAO9C,YAAvB;AACAwH,uBAAe1E,iBAAOnC,YAAP,CAAoB4G,aAApB,CAAf;AACD,OAHD,MAGO;AACLA,wBAAgBzE,iBAAO9C,YAAvB;AACAwH,uBAAe1E,iBAAO1C,KAAP,CAAamH,aAAb,CAAf;AACD;AACF,KAXD,MAWO;AACL;;;;AAIA,UAAIP,SAASlE,iBAAO9C,YAAhB,IAAgC,CAAhC,IAAqC,CAAzC,EAA4C;AAC1CuH,wBAAgBP,SAASlE,iBAAO9C,YAAP,GAAsB,CAA/B,CAAhB;AACD,OAFD,MAEO;AACLuH,wBAAgBP,SAASlE,iBAAO1C,KAAP,CAAasF,MAAb,GAAsB,CAA/B,CAAhB;AACD;;AAED;;;AAGA,UAAI5C,iBAAOlC,UAAX,EAAuB;AACrB;;;AAGA4G,uBAAe1E,iBAAOnC,YAAP,CAAoB4G,aAApB,CAAf;AACD,OALD,MAKO;AACL;;;AAGAC,uBAAe1E,iBAAO1C,KAAP,CAAamH,aAAb,CAAf;AACD;AACF;AACD;;;AAGAN,eAAWO,YAAX,EAAyBD,aAAzB;;AAEA;;;AAGA9E,mBAAKC,IAAL;;AAEA;;;;AAIAqC,gCAAkBC,IAAlB;AACAnB,wBAAUhB,GAAV,CAAc,MAAd;;AAEA;;;AAGA,QAAIC,iBAAOpC,WAAX,EAAwB;AACtBmD,0BAAUhB,GAAV,CAAc,eAAd;AACD;AACF;;AAED;;;;;;;AAOA,WAAS4E,mBAAT,CAA6B9B,QAA7B,EAAuC;AACrC;;;AAGA,QAAI4B,gBAAgB,IAApB;AACA,QAAIC,eAAe,EAAnB;;AAEA;;;AAGA,QAAI1E,iBAAOpC,WAAX,EAAwB;AACtB;;;AAGA,UAAIoC,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BwB,OAA/B,EAAwC;AACtCI,wBAAgBzE,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2B3F,YAA3C;AACAwH,uBAAe1E,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BhF,YAA3B,CAAwC4G,aAAxC,CAAf;AACD,OAHD,MAGO;AACLA,wBAAgBzE,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2B3F,YAA3C;AACAwH,uBAAe1E,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCmH,aAAjC,CAAf;AACD;AACF,KAXD,MAWO;AACL;;;;AAIA,UAAIP,SAASlE,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2B3F,YAApC,IAAoD,CAApD,IAAyD,CAA7D,EAAgE;AAC9DuH,wBAAgBP,SAASlE,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2B3F,YAA3B,GAA0C,CAAnD,CAAhB;AACD,OAFD,MAEO;AACLuH,wBAAgBP,SAASlE,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCsF,MAAjC,GAA0C,CAAnD,CAAhB;AACD;;AAED;;;AAGA,UAAI5C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BwB,OAA/B,EAAwC;AACtC;;;AAGAK,uBAAe1E,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BhF,YAA3B,CAAwC4G,aAAxC,CAAf;AACD,OALD,MAKO;AACL;;;AAGAC,uBAAe1E,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCmH,aAAjC,CAAf;AACD;AACF;;AAED;;;AAGAH,sBAAkBzB,QAAlB;;AAEA;;;AAGA0B,uBAAmB1B,QAAnB,EAA6B6B,YAA7B,EAA2CD,aAA3C;;AAEA;;;AAGA9E,mBAAKC,IAAL;;AAEA;;;AAGAqC,gCAAkBC,IAAlB;AACAnB,wBAAUhB,GAAV,CAAc,MAAd;;AAEA;;;AAGA,QAAIC,iBAAOpC,WAAX,EAAwB;AACtBmD,0BAAUhB,GAAV,CAAc,eAAd;AACD;AACF;;AAED;;;;;;;AAOA,WAASoE,UAAT,CAAoBpB,IAApB,EAA0BkB,KAA1B,EAAiC;AAC/B;;;AAGAW,sBAAkB7B,IAAlB;;AAEA;;;AAGA/C,qBAAOlD,KAAP,CAAa8E,GAAb,GAAmBmB,KAAKjB,GAAxB;AACA9B,qBAAOhD,eAAP,GAAyB+F,IAAzB;AACA/C,qBAAO/C,YAAP,GAAsB8F,KAAK8B,KAA3B;;AAEA7E,qBAAO9C,YAAP,GAAsBgH,SAASD,KAAT,CAAtB;;AAEA;;;AAGAa;AACD;;AAED;;;;;;;;AAQA,WAASP,kBAAT,CAA4B1B,QAA5B,EAAsCE,IAAtC,EAA4CkB,KAA5C,EAAmD;AACjD;;;AAGAW,sBAAkB7B,IAAlB;;AAEA;;;AAGA/C,qBAAOlD,KAAP,CAAa8E,GAAb,GAAmBmB,KAAKjB,GAAxB;AACA9B,qBAAOhD,eAAP,GAAyB+F,IAAzB;AACA/C,qBAAO/C,YAAP,GAAsB8F,KAAK8B,KAA3B;AACA7E,qBAAO9C,YAAP,GAAsB,IAAtB;;AAEA8C,qBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2B3F,YAA3B,GAA0CgH,SAASD,KAAT,CAA1C;;AAEA;;;AAGAa;AACD;;AAED;;;;;;AAMA,WAASF,iBAAT,CAA2B7B,IAA3B,EAAiC;AAC/B;;;AAGApD,mBAAKG,IAAL;;AAEA;;;AAGAmC,gCAAkBqB,WAAlB;AACAyB,iCAAmBC,aAAnB;AACAC,yCAA2BD,aAA3B;AACAE,2BAAaC,iBAAb;;AAEA;;;AAGA,QAAIC,iBAAOC,QAAP,CAAgBtC,IAAhB,CAAJ,EAA2B;AACzBhC,0BAAUhB,GAAV,CAAc,cAAd;AACD;AACF;;AAED;;;;;AAKA,WAAS+E,eAAT,GAA2B;AACzBQ,+BAAiBC,eAAjB;AACAC,gCAAkBC,SAAlB;AACAP,2BAAaQ,kBAAb;;AAEA;;;AAGA3E,wBAAUhB,GAAV,CAAc,aAAd;AACD;;AAED;;;;;;AAMA,WAASuE,iBAAT,CAA2BzB,QAA3B,EAAqC;AACnC;;;;AAIA,QAAI7C,iBAAO7C,eAAP,IAA0B0F,QAA9B,EAAwC;AACtC9B,0BAAUhB,GAAV,CAAc,kBAAd;AACA;;;;AAIAC,uBAAO7C,eAAP,GAAyB0F,QAAzB;;AAEA,UAAIA,YAAY,IAAhB,EAAsB;AACpB7C,yBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2B3F,YAA3B,GAA0C,CAA1C;AACD;AACF;AACF;;AAED;;;AAGA,SAAO;AACL0G,aAASA,OADJ;AAELQ,qBAAiBA,eAFZ;AAGLI,iBAAaA,WAHR;AAILG,yBAAqBA,mBAJhB;AAKLR,gBAAYA,UALP;AAMLI,wBAAoBA,kBANf;AAOLD,uBAAmBA;AAPd,GAAP;AASD,CA3gBqB,EAAtB;;AAZA;;;;;;;AAdA;;;;;;;AAdA;;;;;;;AAdA;;;;;;;AAdA;;;;;kBAilBeX,e;;;;;;;;;;;;;;ACnlBf;;;;;;AAEA;;;;AAIA,IAAIlC,QAAS,YAAW;AACtB;;;;;;;;AAQA,WAASC,YAAT,CAAsBiE,OAAtB,EAA+B;AAC7B;;;;AAIA,QAAI3F,iBAAO/B,KAAX,EAAkB;AAChB2H,cAAQC,GAAR,CAAYF,OAAZ;AACD;AACF;;AAED;;;AAGA,SAAO;AACLjE,kBAAcA;AADT,GAAP;AAGD,CAzBW,EAAZ,C,CAVA;;;;kBAqCeD,K;;;;;;;;;;;;;;ACjCf;;;;;;AAEA;;;;;AAKA,IAAI2D,SAAU,YAAW;AACvB;;;;;;;;;;;;AAYA,WAASU,OAAT,CAAiBjD,QAAjB,EAA2BkD,SAA3B,EAAsC;AACpC;;;AAGA,QAAI/F,iBAAO7C,eAAP,IAA0B0F,QAA9B,EAAwC;AACtC,aAAO,IAAP;AACD,KAFD,MAEO;AACL;;;AAGA,UAAI7C,iBAAO7C,eAAP,IAA0B,IAA1B,IAAkC0F,YAAY,IAAlD,EAAwD;AACtD;;;AAGA,YAAI7C,iBAAO9C,YAAP,IAAuB6I,SAA3B,EAAsC;AACpC,iBAAO,IAAP;AACD,SAFD,MAEO;AACL,iBAAO,KAAP;AACD;AACF,OATD,MASO;AACL;;;;AAIA,YACE/F,iBAAO7C,eAAP,IAA0B0F,QAA1B,IACA7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2B3F,YAA3B,IAA2C6I,SAF7C,EAGE;AACA,iBAAO,IAAP;AACD,SALD,MAKO;AACL,iBAAO,KAAP;AACD;AACF;AACF;AACF;;AAED;;;;;;;;;AASA,WAASV,QAAT,CAAkBR,KAAlB,EAAyB;AACvB,QAAI7E,iBAAO/C,YAAP,IAAuB4H,KAA3B,EAAkC;AAChC,aAAO,IAAP;AACD,KAFD,MAEO;AACL,aAAO,KAAP;AACD;AACF;;AAED;;;;;;;;;AASA,WAASmB,WAAT,CAAqBnD,QAArB,EAA+B;AAC7B,QAAI7C,iBAAO7C,eAAP,IAA0B0F,QAA9B,EAAwC;AACtC,aAAO,IAAP;AACD,KAFD,MAEO;AACL,aAAO,KAAP;AACD;AACF;;AAED;;;;;;;;;AASA,WAASoD,KAAT,CAAenE,GAAf,EAAoB;AAClB;;;AAGA,QAAIoE,UAAU,mFAAd;;AAEA,WAAOA,QAAQ/F,IAAR,CAAa2B,GAAb,CAAP;AACD;;AAED;;;;;;;;;AASA,WAASqE,KAAT,CAAeC,GAAf,EAAoB;AAClB,WACE,CAACC,MAAMD,GAAN,CAAD,IAAelC,SAASoC,OAAOF,GAAP,CAAT,KAAyBA,GAAxC,IAA+C,CAACC,MAAMnC,SAASkC,GAAT,EAAc,EAAd,CAAN,CADlD;AAGD;;AAED;;;AAGA,SAAO;AACLN,aAASA,OADJ;AAELT,cAAUA,QAFL;AAGLW,iBAAaA,WAHR;AAILC,WAAOA,KAJF;AAKLE,WAAOA;AALF,GAAP;AAOD,CA9HY,EAAb,C,CAXA;;;;kBA2Ief,M;;;;;;;;;;;;;;ACvIf;;;;;;AAEA;;;;;AAKA,IAAImB,cAAe,YAAW;AAC5B;;;;;;;;AAQA,WAASC,WAAT,GAAuB;AACrBxG,qBAAOlD,KAAP,GAAe,IAAIC,KAAJ,EAAf;AACAiD,qBAAOhD,eAAP,GAAyB,EAAzB;AACAgD,qBAAO/C,YAAP,GAAsB,EAAtB;AACA+C,qBAAO9C,YAAP,GAAsB,CAAtB;AACA8C,qBAAO7C,eAAP,GAAyB,IAAzB;AACA6C,qBAAO5C,cAAP,GAAwB,GAAxB;AACA4C,qBAAO3C,SAAP,GAAmB,EAAnB;AACA2C,qBAAO1C,KAAP,GAAe,EAAf;AACA0C,qBAAOzC,SAAP,GAAmB,EAAnB;AACAyC,qBAAOxC,UAAP,GAAoB,EAApB;AACAwC,qBAAOvC,iBAAP,GAA2B,EAA3B;AACAuC,qBAAOtC,sBAAP,GAAgC,EAAhC;AACAsC,qBAAOrC,MAAP,GAAgB,KAAhB;AACAqC,qBAAOnC,YAAP,GAAsB,EAAtB;AACAmC,qBAAOlC,UAAP,GAAoB,KAApB;AACAkC,qBAAOjC,iBAAP,GAA2B,EAA3B;AACAiC,qBAAOhC,oBAAP,GAA8B,EAA9B;AACAgC,qBAAO/B,KAAP,GAAe,KAAf;AACA+B,qBAAO9B,MAAP,GAAgB,GAAhB;AACA8B,qBAAO7B,eAAP,GAAyB,GAAzB;AACA6B,qBAAO5B,gBAAP,GAA0B,CAA1B;AACA4B,qBAAO3B,gBAAP,GAA0B,CAA1B;AACA2B,qBAAO1B,iBAAP,GAA2B,EAA3B;AACA0B,qBAAOzB,kBAAP,GAA4B,KAA5B;AACAyB,qBAAOxB,qBAAP,GAA+B,CAA/B;AACAwB,qBAAOvB,sBAAP,GAAgC,CAAhC;AACAuB,qBAAOnB,aAAP,GAAuB,IAAvB;AACD;;AAED;;;AAGA,WAAS4H,cAAT,GAA0B;AACxB;;;AAGA,QAAIzG,iBAAOlD,KAAP,CAAawD,MAAb,IAAuBN,iBAAOlD,KAAP,CAAagE,WAAb,IAA4B,CAAvD,EAA0D;AACxDd,uBAAOjB,YAAP,GAAsB,SAAtB;AACD;;AAED;;;;AAIA,QAAIiB,iBAAOlD,KAAP,CAAawD,MAAb,IAAuBN,iBAAOlD,KAAP,CAAagE,WAAb,GAA2B,CAAtD,EAAyD;AACvDd,uBAAOjB,YAAP,GAAsB,QAAtB;AACD;;AAED;;;AAGA,QAAI,CAACiB,iBAAOlD,KAAP,CAAawD,MAAlB,EAA0B;AACxBN,uBAAOjB,YAAP,GAAsB,SAAtB;AACD;AACF;;AAED;;;AAGA,SAAO;AACLyH,iBAAaA,WADR;AAELC,oBAAgBA;AAFX,GAAP;AAID,CAzEiB,EAAlB,C,CAXA;;;;kBAsFeF,W;;;;;;;;;;;;;;AClFf;;;;AAMA;;;;;;AAEA;;;;;AAZA;;;;AAiBA,IAAIxF,YAAa,YAAW;AAC1B;;;AAGA,WAAS2F,UAAT,GAAsB;AACpB;;;;AAIA1G,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,OAA9B,EAAuC,YAAW;AAChDzB,UAAI,OAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,OAA9B,EAAuC,YAAW;AAChDzB,UAAI,OAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,YAA9B,EAA4C,YAAW;AACrDzB,UAAI,YAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,gBAA9B,EAAgD,YAAW;AACzDzB,UAAI,gBAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,WAA9B,EAA2C,YAAW;AACpDzB,UAAI,WAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,OAA9B,EAAuC,YAAW;AAChDzB,UAAI,OAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,SAA9B,EAAyC,YAAW;AAClDzB,UAAI,SAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,MAA9B,EAAsC,YAAW;AAC/CzB,UAAI,MAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,UAA9B,EAA0C,YAAW;AACnDzB,UAAI,UAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,YAA9B,EAA4C,YAAW;AACrDzB,UAAI,YAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,QAA9B,EAAwC,YAAW;AACjDzB,UAAI,QAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,SAA9B,EAAyC,YAAW;AAClDzB,UAAI,SAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,SAA9B,EAAyC,YAAW;AAClDzB,UAAI,SAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,SAA9B,EAAyC,YAAW;AAClDzB,UAAI,SAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,YAA9B,EAA4C,YAAW;AACrDzB,UAAI,YAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,cAA9B,EAA8C,YAAW;AACvDzB,UAAI,cAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,SAA9B,EAAyC,YAAW;AAClDzB,UAAI,SAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,SAA9B,EAAyC,YAAW;AAClDzB,UAAI,SAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,gBAA9B,EAAgD,YAAW;AACzDzB,UAAI,gBAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,gBAA9B,EAAgD,YAAW;AACzDzB,UAAI,gBAAJ;AACD,KAFD;;AAIA;;;;AAIAC,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,OAA9B,EAAuC,YAAW;AAChDzB,UAAI,OAAJ;AACD,KAFD;AAGD;;AAED;;;;;;;;AAQA,WAASA,GAAT,CAAa4G,YAAb,EAA2B;AACzB;;;;AAIA,QAAI3G,iBAAO3C,SAAP,CAAiBsJ,YAAjB,CAAJ,EAAoC;AAClC;;;AAGA,UAAIC,mBAAmB5G,iBAAO3C,SAAP,CAAiBsJ,YAAjB,CAAvB;;AAEA;;;AAGAlF,sBAAMC,YAAN,CAAmB,uBAAuBiF,YAA1C;;AAEA;;;AAGA,UAAI;AACFC;AACD,OAFD,CAEE,OAAOC,KAAP,EAAc;AACd,YAAIA,MAAMlB,OAAN,IAAiB,cAArB,EAAqC;AACnC,gBAAMkB,KAAN;AACD,SAFD,MAEO;AACLpF,0BAAMC,YAAN,CAAmB,qBAAqBmF,MAAMlB,OAA9C;AACD;AACF;AACF;AACF;;AAED,SAAO;AACLe,gBAAYA,UADP;AAEL3G,SAAKA;AAFA,GAAP;AAID,CAzNe,EAAhB;;AAXA;;;;kBAsOegB,S;;;;;;;;;;;;;;ACxOf;;;;;;AAEA;;;;;AAKA,IAAIuE,mBAAoB,YAAW;AACjC;;;;;;;;AAQA,WAASC,eAAT,GAA2B;AACzB;;;;AAIA,QAAIuB,oBAAoB,CACtB,eADsB,EAEtB,iBAFsB,EAGtB,+BAHsB,CAAxB;;AAMA;;;AAGA,QAAIC,mBAAmBtE,SAASC,gBAAT,CACrB,4BADqB,CAAvB;;AAIA;;;;;AAKA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIoE,iBAAiBnE,MAArC,EAA6CD,GAA7C,EAAkD;AAChD;;;;AAIA,UAAIqE,OAAOD,iBAAiBpE,CAAjB,EAAoBG,YAApB,CAAiC,0BAAjC,CAAX;;AAEA;;;AAGA,UAAID,WAAWkE,iBAAiBpE,CAAjB,EAAoBG,YAApB,CACb,yBADa,CAAf;AAGA,UAAIiD,YAAYgB,iBAAiBpE,CAAjB,EAAoBG,YAApB,CACd,2BADc,CAAhB;;AAIA;;;;;;;AAOA,UACEiD,aAAa,IAAb,KACC/F,iBAAO7C,eAAP,IAA0B0F,QAA1B,IACEA,YAAY,IAAZ,IAAoBkD,aAAa,IAFpC,CADF,EAIE;AACA;;;;;AAKA,YAAIkB,MAAOjH,iBAAOhD,eAAP,CAAuBgK,IAAvB,KAAgCxG,SAAjC,GAA8CR,iBAAOhD,eAAP,CAAuBgK,IAAvB,CAA9C,GAA6E,IAAvF;AACA,YAAIF,kBAAkBI,OAAlB,CAA0BF,IAA1B,KAAmC,CAAvC,EAA0C;AACxCC,gBAAMA,OAAOjH,iBAAOjC,iBAApB;AACAgJ,2BAAiBpE,CAAjB,EAAoBwE,YAApB,CACE,KADF,EAEEF,GAFF;AAID,SAND,MAMO;AACLA,gBAAMA,OAAO,EAAb;AACAF,2BAAiBpE,CAAjB,EAAoByE,SAApB,GAAgCH,GAAhC;AACD;AACF;AACF;AACF;;AAED;;;AAGA,WAASI,uBAAT,GAAmC;AACjC;;;;AAIA,QAAIP,oBAAoB,CAAC,WAAD,CAAxB;;AAEA;;;AAGA,QAAIQ,uBAAuB7E,SAASC,gBAAT,CACzB,gCADyB,CAA3B;;AAIA;;;;;AAKA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAI2E,qBAAqB1E,MAAzC,EAAiDD,GAAjD,EAAsD;AACpD;;;;AAIA,UAAIqE,OAAOM,qBAAqB3E,CAArB,EAAwBG,YAAxB,CACT,8BADS,CAAX;AAGA,UAAID,WAAWyE,qBAAqB3E,CAArB,EAAwBG,YAAxB,CACb,yBADa,CAAf;;AAIA,UAAI9C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BmE,IAA3B,KAAoCxG,SAAxC,EAAmD;AACjD,YAAIsG,kBAAkBI,OAAlB,CAA0BF,IAA1B,KAAmC,CAAvC,EAA0C;AACxCM,+BAAqB3E,CAArB,EAAwBwE,YAAxB,CACE,KADF,EAEEnH,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BmE,IAA3B,CAFF;AAID,SALD,MAKO;AACLM,+BAAqB3E,CAArB,EAAwByE,SAAxB,GAAoCpH,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BmE,IAA3B,CAApC;AACD;AACF,OATD,MASO;AACL;;;;;;AAMA,YAAIF,kBAAkBI,OAAlB,CAA0BF,IAA1B,KAAmC,CAAvC,EAA0C;AACxC,cAAIhH,iBAAOhC,oBAAP,IAA+B,EAAnC,EAAuC;AACrCsJ,iCAAqB3E,CAArB,EAAwBwE,YAAxB,CACE,KADF,EAEEnH,iBAAOhC,oBAFT;AAID,WALD,MAKO;AACLsJ,iCAAqB3E,CAArB,EAAwBwE,YAAxB,CAAqC,KAArC,EAA4C,EAA5C;AACD;AACF,SATD,MASO;AACLG,+BAAqB3E,CAArB,EAAwByE,SAAxB,GAAoC,EAApC;AACD;AACF;AACF;AACF;;AAED;;;;;;;AAOA,WAASG,sBAAT,CAAgCxE,IAAhC,EAAsCF,QAAtC,EAAgD;AAC9C;;;;AAIA,QAAIiE,oBAAoB,CACtB,eADsB,EAEtB,iBAFsB,EAGtB,+BAHsB,CAAxB;;AAMA;;;AAGA,QAAIC,mBAAmBtE,SAASC,gBAAT,CACrB,yDAAyDG,QAAzD,GAAoE,IAD/C,CAAvB;;AAIA;;;;;AAKA,SAAK,IAAIF,IAAI,CAAb,EAAgBA,IAAIoE,iBAAiBnE,MAArC,EAA6CD,GAA7C,EAAkD;AAChD;;;;AAIA,UAAIqE,OAAOD,iBAAiBpE,CAAjB,EAAoBG,YAApB,CAAiC,0BAAjC,CAAX;;AAEA;;;AAGA,UAAI0E,kBAAkBT,iBAAiBpE,CAAjB,EAAoBG,YAApB,CACpB,yBADoB,CAAtB;;AAIA;;;;AAIA,UAAI0E,mBAAmB3E,QAAvB,EAAiC;AAC/B;;;;;AAKA,YAAIE,KAAKiE,IAAL,KAAcxG,SAAlB,EAA6B;AAC3B,cAAIsG,kBAAkBI,OAAlB,CAA0BF,IAA1B,KAAmC,CAAvC,EAA0C;AACxCD,6BAAiBpE,CAAjB,EAAoBwE,YAApB,CAAiC,KAAjC,EAAwCpE,KAAKiE,IAAL,CAAxC;AACD,WAFD,MAEO;AACLD,6BAAiBpE,CAAjB,EAAoByE,SAApB,GAAgCrE,KAAKiE,IAAL,CAAhC;AACD;AACF,SAND,MAMO;AACL;;;;;;AAMA,cAAIF,kBAAkBI,OAAlB,CAA0BF,IAA1B,KAAmC,CAAvC,EAA0C;AACxC,gBAAIjE,KAAKhF,iBAAL,IAA0B,EAA9B,EAAkC;AAChCgJ,+BAAiBpE,CAAjB,EAAoBwE,YAApB,CAAiC,KAAjC,EAAwCpE,KAAKhF,iBAA7C;AACD,aAFD,MAEO;AACLgJ,+BAAiBpE,CAAjB,EAAoBwE,YAApB,CAAiC,KAAjC,EAAwC,EAAxC;AACD;AACF,WAND,MAMO;AACLJ,6BAAiBpE,CAAjB,EAAoByE,SAApB,GAAgC,EAAhC;AACD;AACF;AACF;AACF;AACF;;AAED;;;AAGA,WAASK,YAAT,GAAwB;AACtB;;;;AAIA,QAAIX,oBAAoB,CACtB,eADsB,EAEtB,iBAFsB,EAGtB,+BAHsB,CAAxB;;AAMA;;;AAGA,QAAIC,mBAAmBtE,SAASC,gBAAT,CACrB,4BADqB,CAAvB;;AAIA;;;;;AAKA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIoE,iBAAiBnE,MAArC,EAA6CD,GAA7C,EAAkD;AAChD,UAAIoD,YAAYgB,iBAAiBpE,CAAjB,EAAoBG,YAApB,CACd,2BADc,CAAhB;AAGA,UAAID,WAAWkE,iBAAiBpE,CAAjB,EAAoBG,YAApB,CACb,yBADa,CAAf;;AAIA,UAAIiD,aAAa,IAAb,IAAqBlD,YAAY,IAArC,EAA2C;AACzC,YAAImE,OAAOD,iBAAiBpE,CAAjB,EAAoBG,YAApB,CAAiC,0BAAjC,CAAX;;AAEA;;;;;;;;AAQA,YAAImE,MAAMjH,iBAAO1C,KAAP,CAAayI,SAAb,EAAwBiB,IAAxB,KAAiCxG,SAAjC,GAA6CR,iBAAO1C,KAAP,CAAayI,SAAb,EAAwBiB,IAAxB,CAA7C,GAA6E,IAAvF;AACA;;;;AAIA,YAAIF,kBAAkBI,OAAlB,CAA0BF,IAA1B,KAAmC,CAAvC,EAA0C;AACxC;;;;AAIAC,gBAAMA,OAAOjH,iBAAOjC,iBAApB;AACAgJ,2BAAiBpE,CAAjB,EAAoBwE,YAApB,CACE,KADF,EAEEF,GAFF;AAID,SAVD,MAUO;AACLF,2BAAiBpE,CAAjB,EAAoByE,SAApB,GAAgCH,GAAhC;AACD;AACF;;AAED;;;AAGA,UAAIlB,aAAa,IAAb,IAAqBlD,YAAY,IAArC,EAA2C;AACzC;;;AAGA,YAAImE,QAAOD,iBAAiBpE,CAAjB,EAAoBG,YAApB,CAAiC,0BAAjC,CAAX;;AAEA;;;AAGA,YAAI9C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCyI,SAAjC,EAA4CiB,KAA5C,KAAqDxG,SAAzD,EAAoE;AAClE,cAAIsG,kBAAkBI,OAAlB,CAA0BF,KAA1B,KAAmC,CAAvC,EAA0C;AACxCD,6BAAiBpE,CAAjB,EAAoBwE,YAApB,CACE,KADF,EAEEnH,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCyI,SAAjC,EAA4CiB,KAA5C,CAFF;AAID,WALD,MAKO;AACLD,6BAAiBpE,CAAjB,EAAoByE,SAApB,GACEpH,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCyI,SAAjC,EAA4CiB,KAA5C,CADF;AAED;AACF;AACF;AACF;;AAED;;;AAGAK;AACD;;AAED;;;AAGA,SAAO;AACL9B,qBAAiBA,eADZ;AAELgC,4BAAwBA,sBAFnB;AAGLE,kBAAcA,YAHT;AAILJ,6BAAyBA;AAJpB,GAAP;AAMD,CA9UsB,EAAvB,C,CAXA;;;;kBA2Ve/B,gB;;;;;;;;;;;;;;ACvVf;;;;;;AAEA;;;;;;AAMA,IAAIoC,iBAAkB,YAAW;AAC/B;;;;;;AAMA,WAASC,UAAT,GAAsB;AACpB;;;AAGA,QAAIC,gBAAgBnF,SAASoF,sBAAT,CAAgC,kBAAhC,CAApB;;AAEA;;;;;;AAMA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAIiF,cAAchF,MAAlC,EAA0CD,GAA1C,EAA+C;AAC7C,UAAI3C,iBAAOrC,MAAX,EAAmB;AACjBiK,sBAAcjF,CAAd,EAAiBa,SAAjB,CAA2BC,GAA3B,CAA+B,qBAA/B;AACAmE,sBAAcjF,CAAd,EAAiBa,SAAjB,CAA2BE,MAA3B,CAAkC,sBAAlC;AACD,OAHD,MAGO;AACLkE,sBAAcjF,CAAd,EAAiBa,SAAjB,CAA2BE,MAA3B,CAAkC,qBAAlC;AACAkE,sBAAcjF,CAAd,EAAiBa,SAAjB,CAA2BC,GAA3B,CAA+B,sBAA/B;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASqE,kBAAT,CAA4BjF,QAA5B,EAAsC;AACpC;;;AAGA,QAAIkF,gBAAgBtF,SAASoF,sBAAT,CAAgC,kBAAhC,CAApB;;AAEA;;;AAGA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAIoF,cAAcnF,MAAlC,EAA0CD,GAA1C,EAA+C;AAC7C;;;;AAIA,UACEoF,cAAcpF,CAAd,EAAiBG,YAAjB,CAA8B,yBAA9B,KAA4DD,QAD9D,EAEE;AACA;;;;;;AAMA,YAAI7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BlF,MAA/B,EAAuC;AACrCoK,wBAAcpF,CAAd,EAAiBa,SAAjB,CAA2BC,GAA3B,CAA+B,qBAA/B;AACAsE,wBAAcpF,CAAd,EAAiBa,SAAjB,CAA2BE,MAA3B,CAAkC,sBAAlC;AACD,SAHD,MAGO;AACLqE,wBAAcpF,CAAd,EAAiBa,SAAjB,CAA2BC,GAA3B,CAA+B,sBAA/B;AACAsE,wBAAcpF,CAAd,EAAiBa,SAAjB,CAA2BE,MAA3B,CAAkC,qBAAlC;AACD;AACF;AACF;AACF;;AAED;;;;;;AAMA,WAASsE,cAAT,GAA0B;AACxB;;;AAGA,QAAIC,oBAAoBxF,SAASoF,sBAAT,CACtB,uBADsB,CAAxB;;AAIA;;;;;;AAMA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAIsF,kBAAkBrF,MAAtC,EAA8CD,GAA9C,EAAmD;AACjD,UAAI3C,iBAAOpC,WAAX,EAAwB;AACtBqK,0BAAkBtF,CAAlB,EAAqBa,SAArB,CAA+BC,GAA/B,CAAmC,0BAAnC;AACAwE,0BAAkBtF,CAAlB,EAAqBa,SAArB,CAA+BE,MAA/B,CAAsC,2BAAtC;AACD,OAHD,MAGO;AACLuE,0BAAkBtF,CAAlB,EAAqBa,SAArB,CAA+BE,MAA/B,CAAsC,0BAAtC;AACAuE,0BAAkBtF,CAAlB,EAAqBa,SAArB,CAA+BC,GAA/B,CAAmC,2BAAnC;AACD;AACF;AACF;;AAED;;;AAGA,SAAO;AACLkE,gBAAYA,UADP;AAELG,wBAAoBA,kBAFf;AAGLE,oBAAgBA;AAHX,GAAP;AAKD,CA5GoB,EAArB,C,CAZA;;;;kBA0HeN,c;;;;;;;;;;;;;AC1Hf;;;;;AAKA,IAAIQ,eAAgB,YAAW;AAC7B;;;;;;;AAOA,WAASC,QAAT,CAAkB5F,KAAlB,EAAyB;AACvB;;;AAGA,QAAI6F,cAAc3F,SAASoF,sBAAT,CAAgC,gBAAhC,CAAlB;;AAEA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAIyF,YAAYxF,MAAhC,EAAwCD,GAAxC,EAA6C;AAC3C,UAAI,CAACJ,KAAL,EAAY;AACV6F,oBAAYzF,CAAZ,EAAea,SAAf,CAAyBC,GAAzB,CAA6B,qBAA7B;AACA2E,oBAAYzF,CAAZ,EAAea,SAAf,CAAyBE,MAAzB,CAAgC,iBAAhC;AACD,OAHD,MAGO;AACL0E,oBAAYzF,CAAZ,EAAea,SAAf,CAAyBE,MAAzB,CAAgC,qBAAhC;AACA0E,oBAAYzF,CAAZ,EAAea,SAAf,CAAyBC,GAAzB,CAA6B,iBAA7B;AACD;AACF;AACF;;AAED,SAAO;AACL0E,cAAUA;AADL,GAAP;AAGD,CAjCkB,EAAnB;;kBAmCeD,Y;;;;;;;;;;;;;;ACpCf;;;;;;AAEA;;;;AAIA,IAAIG,uBAAwB,YAAW;AACrC;;;;;;AAMA,WAASnG,IAAT,GAAgB;AACd,QAAIoG,gBAAgB7F,SAASoF,sBAAT,CAClB,yBADkB,CAApB;;AAIA;;;;AAIA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAI2F,cAAc1F,MAAlC,EAA0CD,GAA1C,EAA+C;AAC7C2F,oBAAc3F,CAAd,EAAiB4F,KAAjB,GAAyBvI,iBAAOlD,KAAP,CAAaoB,MAAb,GAAsB,GAA/C;AACD;AACF;;AAED;;;AAGA,SAAO;AACLgE,UAAMA;AADD,GAAP;AAGD,CA3B0B,EAA3B,C,CAVA;;;;kBAuCemG,oB;;;;;;;;;;;;;;ACnCf;;;;;;AAEA;;;;;AAKA,IAAIG,WAAY,YAAW;AACzB;;;;;;AAMA,WAASC,SAAT,CAAmB9K,MAAnB,EAA2B;AACzB;;;AAGAqC,qBAAOrC,MAAP,GAAgBA,MAAhB;AACD;;AAED;;;;;;;AAOA,WAAS+K,iBAAT,CAA2B/K,MAA3B,EAAmCkF,QAAnC,EAA6C;AAC3C;;;AAGA7C,qBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BlF,MAA3B,GAAoCA,MAApC;AACD;;AAED;;;;;;AAMA,WAASgL,aAAT,CAAuBhL,MAAvB,EAA+B;AAC7BqC,qBAAOpC,WAAP,GAAqBD,MAArB;AACD;;AAED;;;AAGA,SAAO;AACL8K,eAAWA,SADN;AAELC,uBAAmBA,iBAFd;AAGLC,mBAAeA;AAHV,GAAP;AAKD,CA9Cc,EAAf,C,CAXA;;;;kBA2DeH,Q;;;;;;;;;;;;;;ACvDf;;;;;;AAEA;;;;;;AAMA,IAAII,WAAY,YAAW;AACzB;;;;;;AAMA,WAASC,UAAT,CAAoBxE,OAApB,EAA6B;AAC3BrE,qBAAOlC,UAAP,GAAoBuG,OAApB;;AAEA,QAAIA,OAAJ,EAAa;AACXyE;AACD,KAFD,MAEO;AACL9I,uBAAOnC,YAAP,GAAsB,EAAtB;AACD;AACF;;AAED;;;;;AAKA,WAASkL,aAAT,GAAyB;AACvB;;;;AAIA,QAAI/I,iBAAOlC,UAAX,EAAuB;AACrBkC,uBAAOlC,UAAP,GAAoB,KAApB;AACAkC,uBAAOnC,YAAP,GAAsB,EAAtB;AACD,KAHD,MAGO;AACLmC,uBAAOlC,UAAP,GAAoB,IAApB;AACAgL;AACD;AACF;;AAED;;;;;;;AAOA,WAASE,kBAAT,CAA4BnG,QAA5B,EAAsCwB,OAAtC,EAA+C;AAC7CrE,qBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BwB,OAA3B,GAAqCA,OAArC;;AAEA,QAAIrE,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BwB,OAA/B,EAAwC;AACtC4E,2BAAqBpG,QAArB;AACD,KAFD,MAEO;AACL7C,uBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BhF,YAA3B,GAA0C,EAA1C;AACD;AACF;;AAED;;;;;;AAMA,WAASqL,qBAAT,CAA+BrG,QAA/B,EAAyC;AACvC;;;;AAIA,QAAI7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BwB,OAA/B,EAAwC;AACtCrE,uBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BwB,OAA3B,GAAqC,KAArC;AACArE,uBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BhF,YAA3B,GAA0C,EAA1C;AACD,KAHD,MAGO;AACLmC,uBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BwB,OAA3B,GAAqC,IAArC;AACA4E,2BAAqBpG,QAArB;AACD;AACF;;AAED;;;;;;;;AAQA,WAASiG,YAAT,GAAwB;AACtB;;;AAGA,QAAIK,cAAc,IAAIC,KAAJ,CAAUpJ,iBAAO1C,KAAP,CAAasF,MAAvB,CAAlB;;AAEA;;;AAGA,SAAK,IAAID,IAAI,CAAb,EAAgBA,IAAI3C,iBAAO1C,KAAP,CAAasF,MAAjC,EAAyCD,GAAzC,EAA8C;AAC5CwG,kBAAYxG,CAAZ,IAAiB3C,iBAAO1C,KAAP,CAAaqF,CAAb,CAAjB;AACD;;AAED;;;;AAIA,SAAK,IAAIA,KAAI3C,iBAAO1C,KAAP,CAAasF,MAAb,GAAsB,CAAnC,EAAsCD,KAAI,CAA1C,EAA6CA,IAA7C,EAAkD;AAChD,UAAI0G,UAAUC,KAAKC,KAAL,CAAWD,KAAKE,MAAL,KAAgBxJ,iBAAO1C,KAAP,CAAasF,MAA7B,GAAsC,CAAjD,CAAd;AACA6G,kBAAYN,WAAZ,EAAyBxG,EAAzB,EAA4B0G,UAAU,CAAtC;AACD;;AAED;;;AAGArJ,qBAAOnC,YAAP,GAAsBsL,WAAtB;AACD;;AAED;;;;;;;;AAQA,WAASF,oBAAT,CAA8BpG,QAA9B,EAAwC;AACtC;;;AAGA,QAAIsG,cAAc,IAAIC,KAAJ,CAAUpJ,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCsF,MAA3C,CAAlB;;AAEA;;;AAGA,SAAK,IAAID,IAAI,CAAb,EAAgBA,IAAI3C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCsF,MAArD,EAA6DD,GAA7D,EAAkE;AAChEwG,kBAAYxG,CAAZ,IAAiB3C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCqF,CAAjC,CAAjB;AACD;;AAED;;;;AAIA,SAAK,IAAIA,MAAI3C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCsF,MAAjC,GAA0C,CAAvD,EAA0DD,MAAI,CAA9D,EAAiEA,KAAjE,EAAsE;AACpE,UAAI0G,UAAUC,KAAKC,KAAL,CACZD,KAAKE,MAAL,KAAgBxJ,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCsF,MAAjD,GAA0D,CAD9C,CAAd;AAGA6G,kBAAYN,WAAZ,EAAyBxG,GAAzB,EAA4B0G,UAAU,CAAtC;AACD;;AAED;;;AAGArJ,qBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BhF,YAA3B,GAA0CsL,WAA1C;AACD;;AAED;;;;;;;;AAQA,WAASM,WAAT,CAAqBC,WAArB,EAAkCC,QAAlC,EAA4CH,MAA5C,EAAoD;AAClD,QAAII,OAAOF,YAAYC,QAAZ,CAAX;AACAD,gBAAYC,QAAZ,IAAwBD,YAAYF,MAAZ,CAAxB;AACAE,gBAAYF,MAAZ,IAAsBI,IAAtB;AACD;;AAED;;;AAGA,SAAO;AACLf,gBAAYA,UADP;AAELE,mBAAeA,aAFV;AAGLC,wBAAoBA,kBAHf;AAILE,2BAAuBA,qBAJlB;AAKLJ,kBAAcA,YALT;AAMLG,0BAAsBA;AANjB,GAAP;AAQD,CA5Kc,EAAf,C,CAZA;;;;kBA0LeL,Q;;;;;;;;;;;;;;ACtLf;;;;;;AAEA;;;;AAIA,IAAI7D,qBAAsB,YAAW;AACnC;;;;;;;;AAQA,WAAS7C,IAAT,CAAc2H,QAAd,EAAwBhH,QAAxB,EAAkCkD,SAAlC,EAA6C;AAC3C+D,aAASD,QAAT;AACAzH,iBAAayH,QAAb,EAAuBhH,QAAvB;AACAR,aAASwH,QAAT,EAAmB9D,SAAnB;AACAzD,uBAAmBuH,QAAnB,EAA6BhH,QAA7B;AACD;;AAED;;;;;;AAMA,WAASiH,QAAT,CAAkBD,QAAlB,EAA4B;AAC1B;;;AAGAA,eAAW,CAACxD,MAAMwD,QAAN,CAAD,GAAmBA,QAAnB,GAA8B,CAAzC;;AAEA;;;AAGA,QAAME,kBAAkBtH,SAASC,gBAAT,CAA0B,wBAA1B,CAAxB;;AAEA;;;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIoH,gBAAgBnH,MAApC,EAA4CD,GAA5C,EAAiD;AAC/C;;;AAGA,UAAIE,WAAWkH,gBAAgBpH,CAAhB,EAAmBG,YAAnB,CAAgC,yBAAhC,CAAf;AACA,UAAIC,OAAOgH,gBAAgBpH,CAAhB,EAAmBG,YAAnB,CAAgC,2BAAhC,CAAX;;AAEA;;;;;AAKA,UAAID,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCgH,wBAAgBpH,CAAhB,EAAmB4F,KAAnB,GAA2BsB,QAA3B;AACD;AACF;AACF;;AAED;;;;;;;AAOA,WAASzH,YAAT,CAAsByH,QAAtB,EAAgChH,QAAhC,EAA0C;AACxC;;;AAGAgH,eAAW,CAACxD,MAAMwD,QAAN,CAAD,GAAmBA,QAAnB,GAA8B,CAAzC;;AAEA;;;AAGA,QAAMG,sBAAsBvH,SAASC,gBAAT,CAC1B,qDAAqDG,QAArD,GAAgE,IADtC,CAA5B;;AAIA;;;;AAIA,SAAK,IAAIF,IAAI,CAAb,EAAgBA,IAAIqH,oBAAoBpH,MAAxC,EAAgDD,GAAhD,EAAqD;AACnD;;;AAGA,UAAIsH,oBAAoBD,oBAAoBrH,CAApB,EAAuBG,YAAvB,CACtB,yBADsB,CAAxB;AAGA,UAAIoH,gBAAgBF,oBAAoBrH,CAApB,EAAuBG,YAAvB,CAClB,2BADkB,CAApB;;AAIA;;;;;AAKA,UAAImH,qBAAqBpH,QAArB,IAAiCqH,iBAAiB,IAAtD,EAA4D;AAC1DF,4BAAoBrH,CAApB,EAAuB4F,KAAvB,GAA+BsB,QAA/B;AACD;AACF;AACF;;AAED;;;;;;;AAOA,WAASxH,QAAT,CAAkBwH,QAAlB,EAA4B9D,SAA5B,EAAuC;AACrC;;;AAGA,QAAI/F,iBAAO7C,eAAP,IAA0B,IAA9B,EAAoC;AAClC;;;AAGA0M,iBAAW,CAACxD,MAAMwD,QAAN,CAAD,GAAmBA,QAAnB,GAA8B,CAAzC;;AAEA;;;AAGA,UAAMM,cAAc1H,SAASC,gBAAT,CAClB,uDAAuDqD,SAAvD,GAAmE,IADjD,CAApB;;AAIA;;;;AAIA,WAAK,IAAIpD,IAAI,CAAb,EAAgBA,IAAIwH,YAAYvH,MAAhC,EAAwCD,GAAxC,EAA6C;AAC3C;;;AAGA,YAAIsH,oBAAoBE,YAAYxH,CAAZ,EAAeG,YAAf,CACtB,yBADsB,CAAxB;AAGA,YAAIoH,gBAAgBC,YAAYxH,CAAZ,EAAeG,YAAf,CAClB,2BADkB,CAApB;;AAIA;;;;;AAKA,YAAImH,qBAAqB,IAArB,IAA6BC,iBAAiBnE,SAAlD,EAA6D;AAC3DoE,sBAAYxH,CAAZ,EAAe4F,KAAf,GAAuBsB,QAAvB;AACD;AACF;AACF;AACF;;AAED;;;;;;;AAOA,WAASvH,kBAAT,CAA4BuH,QAA5B,EAAsChH,QAAtC,EAAgD;AAC9C;;;AAGAgH,eAAW,CAACxD,MAAMwD,QAAN,CAAD,GAAmBA,QAAnB,GAA8B,CAAzC;;AAEA,QAAIzG,sBACFpD,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA1D,GACI6C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAD7C,GAEI,IAHN;;AAKA;;;AAGA,QAAMkN,wBAAwB3H,SAASC,gBAAT,CAC5B,qDACEG,QADF,GAEE,gCAFF,GAGEO,mBAHF,GAIE,IAL0B,CAA9B;;AAQA;;;;AAIA,SAAK,IAAIT,IAAI,CAAb,EAAgBA,IAAIyH,sBAAsBxH,MAA1C,EAAkDD,GAAlD,EAAuD;AACrDyH,4BAAsBzH,CAAtB,EAAyB4F,KAAzB,GAAiCsB,QAAjC;AACD;AACF;;AAED;;;;;;AAMA,WAAS7E,aAAT,GAAyB;AACvB,QAAImF,cAAc1H,SAASoF,sBAAT,CAAgC,uBAAhC,CAAlB;;AAEA;;;;AAIA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAIwH,YAAYvH,MAAhC,EAAwCD,GAAxC,EAA6C;AAC3CwH,kBAAYxH,CAAZ,EAAe4F,KAAf,GAAuB,CAAvB;AACD;AACF;;AAED;;;AAGA,SAAO;AACLrG,UAAMA,IADD;AAEL4H,cAAUA,QAFL;AAGL1H,kBAAcA,YAHT;AAILC,cAAUA,QAJL;AAKLC,wBAAoBA,kBALf;AAML0C,mBAAeA;AANV,GAAP;AAQD,CA3NwB,EAAzB,C,CAVA;;;;kBAuOeD,kB;;;;;;;;;;;;;;ACnOf;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;;AAZA;;;;;;AAZA;;;;;;AAZA;;;;;;AAZA;;;;AAqDA,IAAIG,eAAgB,YAAW;AAC7B;;;AAGA,WAASC,iBAAT,GAA6B;AAC3BkF,kCAAoBC,UAApB;AACAC,kCAAoBD,UAApB;AACAE,oCAAsBF,UAAtB;AACAG,oCAAsBH,UAAtB;AACD;;AAED;;;;;AAKA,WAASI,gBAAT,CAA0B5J,WAA1B,EAAuC;AACrCuJ,kCAAoBnI,IAApB,CAAyBpB,WAAzB;AACAyJ,kCAAoBrI,IAApB,CAAyBpB,YAAY6J,KAArC;AACAH,oCAAsBtI,IAAtB,CAA2BpB,YAAY8J,OAAvC;AACAH,oCAAsBvI,IAAtB,CAA2BpB,YAAYS,OAAvC;AACD;;AAED;;;AAGA,WAASmE,kBAAT,GAA8B;AAC5BmF,4CAA8BP,UAA9B;AACAQ,mCAAqBR,UAArB;AACAS,qCAAuBT,UAAvB;AACAU,qCAAuBV,UAAvB;AACAW,mCAAqBX,UAArB;AACD;;AAED;;;;;;AAMA,WAASY,iBAAT,CAA2BpK,WAA3B,EAAwCqK,YAAxC,EAAsD;AACpDN,4CAA8B3I,IAA9B,CAAmCpB,WAAnC,EAAgDqK,YAAhD;AACAF,mCAAqB/I,IAArB,CAA0BiJ,YAA1B;AACAL,mCAAqB5I,IAArB,CAA0BiJ,aAAaR,KAAvC;AACAI,qCAAuB7I,IAAvB,CAA4BiJ,aAAaP,OAAzC;AACAI,qCAAuB9I,IAAvB,CAA4BiJ,aAAa5J,OAAzC;AACD;;AAED;;;AAGA,SAAO;AACL4D,uBAAmBA,iBADd;AAELuF,sBAAkBA,gBAFb;AAGLhF,wBAAoBA,kBAHf;AAILwF,uBAAmBA;AAJd,GAAP;AAMD,CAzDkB,EAAnB;;AAXA;;;;;;AAZA;;;;;;AAZA;;;;;;AAZA;;;;AAZA;;;;kBAsHehG,Y;;;;;;;;;;;;;;AClHf;;;;AAMA;;;;;;AAEA;;;;;AAZA;;;;AAiBA,IAAIrF,iBAAkB,YAAW;AAC/B;;;AAGA,WAASE,GAAT,GAAe;AACb;;;AAGA,QAAIqL,wBAAwB3I,SAASC,gBAAT,CAC1B,0BAD0B,CAA5B;;AAIA;;;AAGA,QAAI1C,iBAAOhB,uBAAX,EAAoC;AAClC;;;;AAIA,UACEqM,OAAOC,IAAP,CAAYtL,iBAAOZ,cAAP,CAAsBC,SAAlC,EAA6CuD,MAA7C,GAAsD,CAAtD,IACAwI,sBAAsBxI,MAAtB,GAA+B,CAFjC,EAGE;AACA;;;;AAIA,aAAK,IAAID,IAAI,CAAb,EAAgBA,IAAIyI,sBAAsBxI,MAA1C,EAAkDD,GAAlD,EAAuD;AACrD;;;;AAIA,cAAIE,WAAWuI,sBAAsBzI,CAAtB,EAAyBG,YAAzB,CACb,yBADa,CAAf;AAGA,cAAIC,OAAOqI,sBAAsBzI,CAAtB,EAAyBG,YAAzB,CACT,2BADS,CAAX;;AAIA;;;AAGA,cAAID,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCwI,mCAAuBH,sBAAsBzI,CAAtB,CAAvB;AACD;;AAED;;;;AAIA,cAAIE,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCyI,qCAAyBJ,sBAAsBzI,CAAtB,CAAzB,EAAmDE,QAAnD;AACD;;AAED;;;AAGA,cAAIA,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpC0I,iCAAqBL,sBAAsBzI,CAAtB,CAArB,EAA+CI,IAA/C;AACD;;AAED;;;;AAIA,cAAIF,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpC2I,2CACEN,sBAAsBzI,CAAtB,CADF,EAEEE,QAFF,EAGEE,IAHF;AAKD;AACF;AACF;AACF,KA5DD,MA4DO;AACL4I;AACD;AACF;;AAED;;;;;AAKA,WAASJ,sBAAT,CAAgChI,OAAhC,EAAyC;AACvC;;;;AAIA,QAAIqI,2BAA2B5L,iBAAO6L,aAAtC;AACA,QAAIC,+BACF9L,iBAAO9C,YAAP,IAAuB,IAAvB,GACI8C,iBAAO1C,KAAP,CAAa0C,iBAAO9C,YAApB,EAAkC2O,aADtC,GAEI7L,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCG,KAAzC,CACE0C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAD3C,EAEE2O,aALR;;AAOA;;;;AAIA,QACEC,gCAAgCtL,SAAhC,IACAR,iBAAOZ,cAAP,CAAsBC,SAAtB,CAAgCyM,4BAAhC,KAAiEtL,SAFnE,EAGE;AACAuL,gCAA0BD,4BAA1B,EAAwDvI,OAAxD;;AAEA;;;AAGD,KATD,MASO,IACLqI,4BAA4BpL,SAA5B,IACAR,iBAAOZ,cAAP,CAAsBC,SAAtB,CAAgCuM,wBAAhC,KAA6DpL,SAFxD,EAGL;AACAuL,gCAA0BH,wBAA1B,EAAoDrI,OAApD;;AAEA;;;;AAID,KAVM,MAUA;AACL;;;AAGA,UAAIyI,qBACFX,OAAOC,IAAP,CAAYtL,iBAAOZ,cAAP,CAAsBC,SAAlC,EAA6CuD,MAA7C,GAAsD,CAAtD,GACIyI,OAAOC,IAAP,CAAYtL,iBAAOZ,cAAP,CAAsBC,SAAlC,EAA6C,CAA7C,CADJ,GAEI,IAHN;;AAKA,UAAI2M,sBAAsB,IAA1B,EAAgC;AAC9BD,kCAA0BC,kBAA1B,EAA8CzI,OAA9C;AACD;AACF;AACF;;AAED;;;;;;AAMA,WAASiI,wBAAT,CAAkCjI,OAAlC,EAA2CV,QAA3C,EAAqD;AACnD;;;AAGA,QAAIA,YAAY7C,iBAAO7C,eAAvB,EAAwC;AACtC;;;;AAIA,UAAI2O,+BACF9L,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCG,KAAzC,CACE0C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAD3C,EAEE2O,aAHJ;AAIA,UAAII,mCACFjM,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyC0O,aAD3C;AAEA,UAAID,2BAA2B5L,iBAAO6L,aAAtC;;AAEA;;;AAGA,UACEC,gCAAgCtL,SAAhC,IACAR,iBAAOZ,cAAP,CAAsBC,SAAtB,CAAgCyM,4BAAhC,KACEtL,SAHJ,EAIE;AACAuL,kCAA0BD,4BAA1B,EAAwDvI,OAAxD;;AAEA;;;AAGD,OAVD,MAUO,IACL0I,oCAAoCzL,SAApC,IACAR,iBAAOZ,cAAP,CAAsBC,SAAtB,CAAgC4M,gCAAhC,KACEzL,SAHG,EAIL;AACAuL,kCAA0BE,gCAA1B,EAA4D1I,OAA5D;;AAEA;;;AAGD,OAVM,MAUA,IACLqI,4BAA4BpL,SAA5B,IACAR,iBAAOZ,cAAP,CAAsBC,SAAtB,CAAgCuM,wBAAhC,KAA6DpL,SAFxD,EAGL;AACAuL,kCAA0BH,wBAA1B,EAAoDrI,OAApD;AACD,OALM,MAKA;AACL;;;AAGA,YAAIyI,qBACFX,OAAOC,IAAP,CAAYtL,iBAAOZ,cAAP,CAAsBC,SAAlC,EAA6CuD,MAA7C,GAAsD,CAAtD,GACIyI,OAAOC,IAAP,CAAYtL,iBAAOZ,cAAP,CAAsBC,SAAlC,EAA6C,CAA7C,CADJ,GAEI,IAHN;;AAKA,YAAI2M,sBAAsB,IAA1B,EAAgC;AAC9BD,oCAA0BC,kBAA1B,EAA8CzI,OAA9C;AACD;AACF;AACF;AACF;;AAED;;;;;;AAMA,WAASkI,oBAAT,CAA8BlI,OAA9B,EAAuCR,IAAvC,EAA6C;AAC3C;;;AAGA,QAAIA,QAAQ/C,iBAAO9C,YAAnB,EAAiC;AAC/B;;;AAGA,UAAI4O,+BACF9L,iBAAO1C,KAAP,CAAa0C,iBAAO9C,YAApB,EAAkC2O,aADpC;AAEA,UAAID,2BAA2B5L,iBAAO6L,aAAtC;;AAEA;;;AAGA,UACEC,gCAAgCtL,SAAhC,IACAR,iBAAOZ,cAAP,CAAsBC,SAAtB,CAAgCyM,4BAAhC,KACEtL,SAHJ,EAIE;AACAuL,kCAA0BD,4BAA1B,EAAwDvI,OAAxD;;AAEA;;;AAGD,OAVD,MAUO,IACLqI,4BAA4BpL,SAA5B,IACAR,iBAAOZ,cAAP,CAAsBC,SAAtB,CAAgCuM,wBAAhC,KAA6DpL,SAFxD,EAGL;AACAuL,kCAA0BH,wBAA1B,EAAoDrI,OAApD;AACD,OALM,MAKA;AACL;;;AAGA,YAAIyI,qBACFX,OAAOC,IAAP,CAAYtL,iBAAOZ,cAAP,CAAsBC,SAAlC,EAA6CuD,MAA7C,GAAsD,CAAtD,GACIyI,OAAOC,IAAP,CAAYtL,iBAAOZ,cAAP,CAAsBC,SAAlC,EAA6C,CAA7C,CADJ,GAEI,IAHN;;AAKA,YAAI2M,sBAAsB,IAA1B,EAAgC;AAC9BD,oCAA0BC,kBAA1B,EAA8CzI,OAA9C;AACD;AACF;AACF;AACF;;AAED;;;;;;;AAOA,WAASmI,8BAAT,CAAwCnI,OAAxC,EAAiDV,QAAjD,EAA2DE,IAA3D,EAAiE;AAC/D;;;;AAIA,QACEF,YAAY7C,iBAAO7C,eAAnB,IACA6C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2B3F,YAA3B,IAA2C6F,IAF7C,EAGE;AACA;;;;AAIA,UAAI+I,+BACF9L,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCG,KAAzC,CACE0C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAD3C,EAEE2O,aAHJ;AAIA,UAAII,mCACFjM,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyC0O,aAD3C;AAEA,UAAID,2BAA2B5L,iBAAO6L,aAAtC;;AAEA;;;AAGA,UACEC,gCAAgCtL,SAAhC,IACAR,iBAAOZ,cAAP,CAAsBC,SAAtB,CAAgCyM,4BAAhC,KACEtL,SAHJ,EAIE;AACAuL,kCAA0BD,4BAA1B,EAAwDvI,OAAxD;;AAEA;;;AAGD,OAVD,MAUO,IACL0I,oCAAoCzL,SAApC,IACAR,iBAAOZ,cAAP,CAAsBC,SAAtB,CAAgC4M,gCAAhC,KACEzL,SAHG,EAIL;AACAuL,kCAA0BE,gCAA1B,EAA4D1I,OAA5D;;AAEA;;;AAGD,OAVM,MAUA,IACLqI,4BAA4BpL,SAA5B,IACAR,iBAAOZ,cAAP,CAAsBC,SAAtB,CAAgCuM,wBAAhC,KAA6DpL,SAFxD,EAGL;AACAuL,kCAA0BH,wBAA1B,EAAoDrI,OAApD;AACD,OALM,MAKA;AACL;;;AAGA,YAAIyI,qBACFX,OAAOC,IAAP,CAAYtL,iBAAOZ,cAAP,CAAsBC,SAAlC,EAA6CuD,MAA7C,GAAsD,CAAtD,GACIyI,OAAOC,IAAP,CAAYtL,iBAAOZ,cAAP,CAAsBC,SAAlC,EAA6C,CAA7C,CADJ,GAEI,IAHN;;AAKA,YAAI2M,sBAAsB,IAA1B,EAAgC;AAC9BD,oCAA0BC,kBAA1B,EAA8CzI,OAA9C;AACD;AACF;AACF;AACF;;AAED;;;;;;AAMA,WAASwI,yBAAT,CAAmCG,GAAnC,EAAwC3I,OAAxC,EAAiD;AAC/C,QAAIsI,gBAAgB,IAAI7L,iBAAOZ,cAAP,CAAsBC,SAAtB,CAAgC6M,GAAhC,EAAqC,QAArC,CAAJ,EAApB;AACAL,kBAAcM,cAAd,CACEnM,iBAAOZ,cAAP,CAAsBC,SAAtB,CAAgC6M,GAAhC,EAAqC,aAArC,CADF;AAGAL,kBAAcO,kBAAd,CAAiC7I,OAAjC;AACAvD,qBAAOZ,cAAP,CAAsBE,MAAtB,CAA6B+M,IAA7B,CAAkCR,aAAlC;AACD;;AAED;;;AAGA,WAAS/L,IAAT,GAAgB;AACd;;;AAGA,SAAK,IAAI6C,IAAI,CAAb,EAAgBA,IAAI3C,iBAAOZ,cAAP,CAAsBE,MAAtB,CAA6BsD,MAAjD,EAAyDD,GAAzD,EAA8D;AAC5D3C,uBAAOZ,cAAP,CAAsBE,MAAtB,CAA6BqD,CAA7B,EAAgC2J,iBAAhC;AACD;;AAED;;;AAGAtM,qBAAOZ,cAAP,CAAsBE,MAAtB,GAA+B,EAA/B;AACD;;AAED;;;;;;AAMA,WAASiN,QAAT,CAAkBV,aAAlB,EAAiCW,WAAjC,EAA8C;AAC5C;;;AAGA,QAAIC,mBAAmB,IAAIZ,aAAJ,EAAvB;;AAEA;;;;;;AAOA7L,qBAAOZ,cAAP,CAAsBC,SAAtB,CAAgCoN,iBAAiBC,KAAjB,EAAhC,IAA4D,IAAItD,KAAJ,EAA5D;AACApJ,qBAAOZ,cAAP,CAAsBC,SAAtB,CAAgCoN,iBAAiBC,KAAjB,EAAhC,EACE,QADF,IAEIb,aAFJ;AAGA7L,qBAAOZ,cAAP,CAAsBC,SAAtB,CAAgCoN,iBAAiBC,KAAjB,EAAhC,EACE,aADF,IAEIF,WAFJ;AAGD;;AAED;;;AAGA,WAASb,cAAT,GAA0B;AACxB;;;AAGA,QAAIP,wBAAwB3I,SAASC,gBAAT,CAC1B,0BAD0B,CAA5B;;AAIA,QAAI0I,sBAAsBxI,MAAtB,GAA+B,CAAnC,EAAsC;AACpC,WAAK,IAAI+J,IAAI,CAAb,EAAgBA,IAAIvB,sBAAsBxI,MAA1C,EAAkD+J,GAAlD,EAAuD;AACrD;;;;AAIA,YAAI9J,WAAWuI,sBAAsBuB,CAAtB,EAAyB7J,YAAzB,CACb,yBADa,CAAf;AAGA,YAAIC,OAAOqI,sBAAsBuB,CAAtB,EAAyB7J,YAAzB,CACT,2BADS,CAAX;;AAIA;;;AAGA,YAAID,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpC6J,8BAAoBxB,sBAAsBuB,CAAtB,CAApB;AACD;;AAED;;;;AAIA,YAAI9J,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpC8J,gCAAsBzB,sBAAsBuB,CAAtB,CAAtB,EAAgD9J,QAAhD;AACD;;AAED;;;AAGA,YAAIA,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpC+J,4BAAkB1B,sBAAsBuB,CAAtB,CAAlB,EAA4C5J,IAA5C;AACD;;AAED;;;;AAIA,YAAIF,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCgK,sCAA4B3B,sBAAsBuB,CAAtB,CAA5B,EAAsD9J,QAAtD,EAAgEE,IAAhE;AACD;AACF;AACF;AACF;;AAED;;;;;;AAMA,WAAS6J,mBAAT,CAA6BrJ,OAA7B,EAAsC;AACpCA,YAAQyJ,KAAR,CAAcC,eAAd,GACE,SAASjN,iBAAOhD,eAAP,CAAuBkQ,aAAhC,GAAgD,GADlD;AAED;;AAED;;;;;;AAMA,WAASL,qBAAT,CAA+BtJ,OAA/B,EAAwCV,QAAxC,EAAkD;AAChD,QAAI7C,iBAAO7C,eAAP,IAA0B0F,QAA9B,EAAwC;AACtCU,cAAQyJ,KAAR,CAAcC,eAAd,GACE,SAASjN,iBAAOhD,eAAP,CAAuBkQ,aAAhC,GAAgD,GADlD;AAED;AACF;;AAED;;;;;;AAMA,WAASJ,iBAAT,CAA2BvJ,OAA3B,EAAoCR,IAApC,EAA0C;AACxC,QAAI/C,iBAAO9C,YAAP,IAAuB6F,IAA3B,EAAiC;AAC/BQ,cAAQyJ,KAAR,CAAcC,eAAd,GACE,SAASjN,iBAAOhD,eAAP,CAAuBkQ,aAAhC,GAAgD,GADlD;AAED;AACF;;AAED;;;;;;AAMA,WAASH,2BAAT,CAAqCxJ,OAArC,EAA8CV,QAA9C,EAAwDE,IAAxD,EAA8D;AAC5D,QACE/C,iBAAO7C,eAAP,IAA0B0F,QAA1B,IACA7C,iBAAOzC,SAAP,CAAiBJ,eAAjB,EAAkCD,YAAlC,IAAkD6F,IAFpD,EAGE;AACAQ,cAAQyJ,KAAR,CAAcC,eAAd,GACE,SAASjN,iBAAOhD,eAAP,CAAuBkQ,aAAhC,GAAgD,GADlD;AAED;AACF;;AAED;;;AAGA,SAAO;AACLnN,SAAKA,GADA;AAELD,UAAMA,IAFD;AAGLyM,cAAUA;AAHL,GAAP;AAKD,CAzfoB,EAArB;;AAXA;;;;kBAsgBe1M,c;;;;;;;;;;;;;;ACxgBf;;;;AAMA;;;;;;AAEA;;;;;;;AAZA;;;;AAmBA,IAAIsN,aAAc,YAAW;AAC3B;;;;AAIA,MAAIC,iBAAiB,EAArB;;AAEA;;;;;;;;AAQA,WAASC,cAAT,CAAwBC,UAAxB,EAAoC;AAClC;;;;AAIAF,qBAAiBE,UAAjB;;AAEA;;;AAGA,QAAIC,OAAO9K,SAAS+K,oBAAT,CAA8B,MAA9B,EAAsC,CAAtC,CAAX;AACA,QAAIC,SAAShL,SAASiL,aAAT,CAAuB,QAAvB,CAAb;;AAEAD,WAAOE,IAAP,GAAc,iBAAd;;AAEA;;;AAGAF,WAAO7L,GAAP,GAAa,uCAAb;AACA6L,WAAOG,kBAAP,GAA4BC,cAA5B;AACAJ,WAAOK,MAAP,GAAgBD,cAAhB;;AAEA;;;AAGAN,SAAKQ,WAAL,CAAiBN,MAAjB;AACD;;AAED;;;;;AAKA,WAASI,cAAT,GAA0B;AACxB;;;;;AAKAG,OAAGtH,UAAH,CAAc;AACZuH,iBAAWjO,iBAAO1B;AADN,KAAd;;AAIA;;;;;;AAMA4P;AACD;;AAED;;;;;;;;AAQA,WAASA,iBAAT,GAA6B;AAC3B;;;AAGA,QAAIC,mBAAmB,4CAAvB;;AAEA,SAAK,IAAIxL,IAAI,CAAb,EAAgBA,IAAI3C,iBAAO1C,KAAP,CAAasF,MAAjC,EAAyCD,GAAzC,EAA8C;AAC5C;;;;;AAKA,UAAI3C,iBAAO1C,KAAP,CAAaqF,CAAb,EAAgBb,GAAhB,CAAoBsM,KAApB,CAA0BD,gBAA1B,CAAJ,EAAiD;AAC/CnO,yBAAOxB,qBAAP;AACA6P,0BAAkBrO,iBAAO1C,KAAP,CAAaqF,CAAb,EAAgBb,GAAlC,EAAuCa,CAAvC;AACD;AACF;AACF;;AAED;;;;;;;;;AASA,WAAS2L,8BAAT,CACExM,GADF,EAEEe,QAFF,EAGEoB,KAHF,EAKE;AAAA,QADAsK,gBACA,uEADmB,KACnB;;AACAP,OAAGQ,GAAH,CAAO,mBAAmB1M,GAA1B,EAA+B,UAAS2M,KAAT,EAAgB;AAC7C;;;;;AAKA,UAAIA,MAAMC,UAAV,EAAsB;AACpB,YAAI7L,YAAY,IAAhB,EAAsB;AACpB7C,2BAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiC2G,KAAjC,EAAwCnC,GAAxC,GACE2M,MAAME,UAAN,GAAmB,aAAnB,GAAmC3O,iBAAO1B,iBAD5C;;AAGA,cAAIiQ,gBAAJ,EAAsB;AACpBvO,6BAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BhF,YAA3B,CAAwCoG,KAAxC,EAA+CnC,GAA/C,GACE2M,MAAME,UAAN,GAAmB,aAAnB,GAAmC3O,iBAAO1B,iBAD5C;AAED;AACD;;;;AAIA,cAAI0B,iBAAOzB,kBAAX,EAA+B;AAC7ByB,6BAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiC2G,KAAjC,EAAwCiJ,aAAxC,GACEuB,MAAMG,WADR;;AAGA,gBAAIL,gBAAJ,EAAsB;AACpBvO,+BAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BhF,YAA3B,CAAwCoG,KAAxC,EAA+CiJ,aAA/C,GACEuB,MAAMG,WADR;AAED;AACF;;AAED;;;;;AAKA5O,2BAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiC2G,KAAjC,EAAwC4K,eAAxC,GAA0DJ,KAA1D;;AAEA,cAAIF,gBAAJ,EAAsB;AACpBvO,6BAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BhF,YAA3B,CACEoG,KADF,EAEE4K,eAFF,GAEoBJ,KAFpB;AAGD;AACF,SAlCD,MAkCO;AACLzO,2BAAO1C,KAAP,CAAa2G,KAAb,EAAoBnC,GAApB,GACE2M,MAAME,UAAN,GAAmB,aAAnB,GAAmC3O,iBAAO1B,iBAD5C;;AAGA,cAAIiQ,gBAAJ,EAAsB;AACpBvO,6BAAOnC,YAAP,CAAoBoG,KAApB,EAA2B0K,UAA3B,GACE,aADF,GAEE3O,iBAAO1B,iBAFT;AAGD;;AAED;;;;AAIA,cAAI0B,iBAAOzB,kBAAX,EAA+B;AAC7ByB,6BAAO1C,KAAP,CAAa2G,KAAb,EAAoBiJ,aAApB,GAAoCuB,MAAMG,WAA1C;;AAEA,gBAAIL,gBAAJ,EAAsB;AACpBvO,+BAAOnC,YAAP,CAAoBoG,KAApB,EAA2BiJ,aAA3B,GAA2CuB,MAAMG,WAAjD;AACD;AACF;;AAED;;;;;AAKA5O,2BAAO1C,KAAP,CAAa2G,KAAb,EAAoB4K,eAApB,GAAsCJ,KAAtC;;AAEA,cAAIF,gBAAJ,EAAsB;AACpBvO,6BAAOnC,YAAP,CAAoBoG,KAApB,EAA2B4K,eAA3B,GAA6CJ,KAA7C;AACD;AACF;AACF,OApED,MAoEO;AACL,YAAI5L,YAAY,IAAhB,EAAsB;AACpBiM,2BAAiBC,iBAAjB,CACE/O,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiC2G,KAAjC,EAAwC+K,IAAxC,GACE,MADF,GAEEhP,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiC2G,KAAjC,EAAwCgL,MAF1C,GAGE,0CAJJ;AAMD,SAPD,MAOO;AACL;;;;;AAKAH,2BAAiBC,iBAAjB,CACE/O,iBAAO1C,KAAP,CAAa2G,KAAb,EAAoB+K,IAApB,GACE,MADF,GAEEhP,iBAAO1C,KAAP,CAAa2G,KAAb,EAAoBgL,MAFtB,GAGE,0CAJJ;AAMD;AACF;AACF,KAhGD;AAiGD;;AAED;;;;;;;;;AASA,WAASZ,iBAAT,CAA2BvM,GAA3B,EAAgCmC,KAAhC,EAAuC;AACrC+J,OAAGQ,GAAH,CAAO,mBAAmB1M,GAA1B,EAA+B,UAAS2M,KAAT,EAAgB;AAC7C;;;;;AAKA,UAAIA,MAAMC,UAAV,EAAsB;AACpB1O,yBAAO1C,KAAP,CAAa2G,KAAb,EAAoBnC,GAApB,GACE2M,MAAME,UAAN,GAAmB,aAAnB,GAAmC3O,iBAAO1B,iBAD5C;;AAGA;;;;AAIA,YAAI0B,iBAAOzB,kBAAX,EAA+B;AAC7ByB,2BAAO1C,KAAP,CAAa2G,KAAb,EAAoBiJ,aAApB,GAAoCuB,MAAMG,WAA1C;AACD;;AAED;;;;;AAKA5O,yBAAO1C,KAAP,CAAa2G,KAAb,EAAoB4K,eAApB,GAAsCJ,KAAtC;AACD,OAlBD,MAkBO;AACL;;;;;AAKAK,yBAAiBC,iBAAjB,CACE/O,iBAAO1C,KAAP,CAAa2G,KAAb,EAAoB+K,IAApB,GACE,MADF,GAEEhP,iBAAO1C,KAAP,CAAa2G,KAAb,EAAoBgL,MAFtB,GAGE,0CAJJ;AAMD;AACD;;;AAGAjP,uBAAOvB,sBAAP;;AAEA;;;;AAIA,UAAIuB,iBAAOvB,sBAAP,IAAiCuB,iBAAOxB,qBAA5C,EAAmE;AACjE0Q,uBAAqBC,SAArB,CAA+B/B,cAA/B;AACD;AACF,KAjDD;AAkDD;;AAED;;;;;AAKA,WAASgC,eAAT,CAAyBtN,GAAzB,EAA8B;AAC5B,QAAIqM,mBAAmB,4CAAvB;;AAEA,WAAOrM,IAAIsM,KAAJ,CAAUD,gBAAV,CAAP;AACD;;AAED;;;AAGA,SAAO;AACLd,oBAAgBA,cADX;AAELiB,oCAAgCA,8BAF3B;AAGLc,qBAAiBA;AAHZ,GAAP;AAKD,CAjSgB,EAAjB;;AAbA;;;;kBAgTejC,U;;;;;;;;;;;;;;AClTf;;;;;;AAEA;;;;AAIA,IAAIkC,wBAAyB,YAAW;AACtC;;;;;;;AAOA,WAASnN,IAAT,GAAgB;AACd;;;AAGA,QAAIoN,uBAAuB7M,SAASoF,sBAAT,CACzB,0BADyB,CAA3B;;AAIA;;;;AAIA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAI2M,qBAAqB1M,MAAzC,EAAiDD,GAAjD,EAAsD;AACpD;;;AAGA2M,2BAAqB3M,CAArB,EAAwBa,SAAxB,CAAkCE,MAAlC,CAAyC,6BAAzC;AACA4L,2BAAqB3M,CAArB,EAAwBa,SAAxB,CAAkCE,MAAlC,CAAyC,6BAAzC;AACA4L,2BAAqB3M,CAArB,EAAwBa,SAAxB,CAAkCE,MAAlC,CAAyC,6BAAzC;;AAEA;;;;AAIA,cAAQ1D,iBAAO5C,cAAf;AACE,aAAK,CAAL;AACEkS,+BAAqB3M,CAArB,EAAwBa,SAAxB,CAAkCC,GAAlC,CAAsC,6BAAtC;AACA;AACF,aAAK,GAAL;AACE6L,+BAAqB3M,CAArB,EAAwBa,SAAxB,CAAkCC,GAAlC,CAAsC,6BAAtC;AACA;AACF,aAAK,CAAL;AACE6L,+BAAqB3M,CAArB,EAAwBa,SAAxB,CAAkCC,GAAlC,CAAsC,6BAAtC;AACA;AATJ;AAWD;AACF;;AAED;;;AAGA,SAAO;AACLvB,UAAMA;AADD,GAAP;AAGD,CApD2B,EAA5B,C,CAVA;;;;kBAgEemN,qB;;;;;;;;;;;;;;AC5Df;;;;;;AAEA;;;;AAIA,IAAIE,kBAAmB,YAAW;AAChC;;;;;AAKA,WAASzF,QAAT,GAAoB;AAClB;;;AAGA,QAAI0F,iBAAiB/M,SAASoF,sBAAT,CAAgC,mBAAhC,CAArB;;AAEA;;;AAGA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAI6M,eAAe5M,MAAnC,EAA2CD,GAA3C,EAAgD;AAC9C;;;;AAIA,UAAI6M,eAAe7M,CAAf,EAAkBG,YAAlB,CAA+B,yBAA/B,KAA6D,IAAjE,EAAuE;AACrE;;;;;;AAMA,YAAI9C,iBAAOlC,UAAX,EAAuB;AACrB0R,yBAAe7M,CAAf,EAAkBa,SAAlB,CAA4BC,GAA5B,CAAgC,sBAAhC;AACA+L,yBAAe7M,CAAf,EAAkBa,SAAlB,CAA4BE,MAA5B,CAAmC,uBAAnC;AACD,SAHD,MAGO;AACL8L,yBAAe7M,CAAf,EAAkBa,SAAlB,CAA4BC,GAA5B,CAAgC,uBAAhC;AACA+L,yBAAe7M,CAAf,EAAkBa,SAAlB,CAA4BE,MAA5B,CAAmC,sBAAnC;AACD;AACF;AACF;AACF;;AAED;;;;;;AAMA,WAAStB,YAAT,CAAsBS,QAAtB,EAAgC;AAC9B;;;AAGA,QAAI2M,iBAAiB/M,SAASC,gBAAT,CACnB,iDAAiDG,QAAjD,GAA4D,IADzC,CAArB;;AAIA;;;AAGA,SAAK,IAAIF,IAAI,CAAb,EAAgBA,IAAI6M,eAAe5M,MAAnC,EAA2CD,GAA3C,EAAgD;AAC9C;;;;;;AAMA,UAAI3C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BwB,OAA/B,EAAwC;AACtCmL,uBAAe7M,CAAf,EAAkBa,SAAlB,CAA4BC,GAA5B,CAAgC,sBAAhC;AACA+L,uBAAe7M,CAAf,EAAkBa,SAAlB,CAA4BE,MAA5B,CAAmC,uBAAnC;AACD,OAHD,MAGO;AACL8L,uBAAe7M,CAAf,EAAkBa,SAAlB,CAA4BC,GAA5B,CAAgC,uBAAhC;AACA+L,uBAAe7M,CAAf,EAAkBa,SAAlB,CAA4BE,MAA5B,CAAmC,sBAAnC;AACD;AACF;AACF;;AAED;;;AAGA,SAAO;AACLoG,cAAUA,QADL;AAEL1H,kBAAcA;AAFT,GAAP;AAID,CA/EqB,EAAtB,C,CAVA;;;;kBA2FemN,e;;;;;;;;;;;;;;ACvFf;;;;;;AAEA;;;;;AAKA,IAAItK,6BAA8B,YAAW;AAC3C;;;;;;AAMA,WAAS/C,IAAT,CAAcuN,oBAAd,EAAoC;AAClCtN,eAAWsN,oBAAX;AACArN,iBAAaqN,oBAAb;AACApN,aAASoN,oBAAT;AACAnN,uBAAmBmN,oBAAnB;AACD;;AAED;;;;;;AAMA,WAAStN,UAAT,CAAoBuN,UAApB,EAAgC;AAC9B;;;AAGA,QAAI,CAACrJ,MAAMqJ,UAAN,CAAL,EAAwB;AACtB;;;AAGA,UAAIC,yBAAyBlN,SAASC,gBAAT,CAC3B,iCAD2B,CAA7B;;AAIA,WAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIgN,uBAAuB/M,MAA3C,EAAmDD,GAAnD,EAAwD;AACtD,YAAIE,WAAW8M,uBAAuBhN,CAAvB,EAA0BG,YAA1B,CACb,yBADa,CAAf;AAGA,YAAIiD,YAAY4J,uBAAuBhN,CAAvB,EAA0BG,YAA1B,CACd,2BADc,CAAhB;;AAIA,YAAID,YAAY,IAAZ,IAAoBkD,aAAa,IAArC,EAA2C;AACzC,cAAI6J,MAAMD,uBAAuBhN,CAAvB,EAA0BiN,GAApC;;AAEAD,iCAAuBhN,CAAvB,EAA0B4F,KAA1B,GAAmCmH,aAAa,GAAd,GAAqBE,GAAvD;AACD;AACF;AACF;AACF;;AAED;;;;;;AAMA,WAASxN,YAAT,CAAsBsN,UAAtB,EAAkC;AAChC;;;AAGA,QAAI,CAACrJ,MAAMqJ,UAAN,CAAL,EAAwB;AACtB;;;AAGA,UAAIC,yBAAyBlN,SAASC,gBAAT,CAC3B,8DACE1C,iBAAO7C,eADT,GAEE,IAHyB,CAA7B;;AAMA,WAAK,IAAIwF,IAAI,CAAb,EAAgBA,IAAIgN,uBAAuB/M,MAA3C,EAAmDD,GAAnD,EAAwD;AACtD,YAAII,OAAO4M,uBAAuBhN,CAAvB,EAA0BG,YAA1B,CACT,2BADS,CAAX;;AAIA,YAAIC,QAAQ,IAAZ,EAAkB;AAChB,cAAI6M,MAAMD,uBAAuBhN,CAAvB,EAA0BiN,GAApC;;AAEAD,iCAAuBhN,CAAvB,EAA0B4F,KAA1B,GAAmCmH,aAAa,GAAd,GAAqBE,GAAvD;AACD;AACF;AACF;AACF;;AAED;;;;;;AAMA,WAASvN,QAAT,CAAkBqN,UAAlB,EAA8B;AAC5B,QAAI1P,iBAAO7C,eAAP,IAA0B,IAA9B,EAAoC;AAClC;;;AAGA,UAAI,CAACkJ,MAAMqJ,UAAN,CAAL,EAAwB;AACtB;;;AAGA,YAAIC,yBAAyBlN,SAASC,gBAAT,CAC3B,gEACE1C,iBAAO9C,YADT,GAEE,IAHyB,CAA7B;;AAMA,aAAK,IAAIyF,IAAI,CAAb,EAAgBA,IAAIgN,uBAAuB/M,MAA3C,EAAmDD,GAAnD,EAAwD;AACtD,cAAIE,WAAW8M,uBAAuBhN,CAAvB,EAA0BG,YAA1B,CACb,yBADa,CAAf;;AAIA,cAAID,YAAY,IAAhB,EAAsB;AACpB,gBAAI+M,MAAMD,uBAAuBhN,CAAvB,EAA0BiN,GAApC;;AAEAD,mCAAuBhN,CAAvB,EAA0B4F,KAA1B,GAAmCmH,aAAa,GAAd,GAAqBE,GAAvD;AACD;AACF;AACF;AACF;AACF;;AAED;;;;;;AAMA,WAAStN,kBAAT,CAA4BoN,UAA5B,EAAwC;AACtC;;;AAGA,QAAI,CAACrJ,MAAMqJ,UAAN,CAAL,EAAwB;AACtB,UAAItM,sBACFpD,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA1D,GACI6C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAD7C,GAEI,IAHN;;AAKA;;;AAGA,UAAIyS,yBAAyBlN,SAASC,gBAAT,CAC3B,8DACE1C,iBAAO7C,eADT,GAEE,gCAFF,GAGEiG,mBAHF,GAIE,IALyB,CAA7B;;AAQA;;;;AAIA,WAAK,IAAIT,IAAI,CAAb,EAAgBA,IAAIgN,uBAAuB/M,MAA3C,EAAmDD,GAAnD,EAAwD;AACtD,YAAIE,WAAW8M,uBAAuBhN,CAAvB,EAA0BG,YAA1B,CACb,yBADa,CAAf;AAGA,YAAIiD,YAAY4J,uBAAuBhN,CAAvB,EAA0BG,YAA1B,CACd,2BADc,CAAhB;;AAIA,YAAID,YAAY,IAAZ,IAAoBkD,aAAa,IAArC,EAA2C;AACzC,cAAI6J,MAAMD,uBAAuBhN,CAAvB,EAA0BiN,GAApC;;AAEAD,iCAAuBhN,CAAvB,EAA0B4F,KAA1B,GAAmCmH,aAAa,GAAd,GAAqBE,GAAvD;AACD;AACF;AACF;AACF;;AAED;;;;;AAKA,WAAS5K,aAAT,GAAyB;AACvB,QAAI2K,yBAAyBlN,SAASoF,sBAAT,CAC3B,gCAD2B,CAA7B;;AAIA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAIgN,uBAAuB/M,MAA3C,EAAmDD,GAAnD,EAAwD;AACtDgN,6BAAuBhN,CAAvB,EAA0B4F,KAA1B,GAAkC,CAAlC;AACD;AACF;;AAED,SAAO;AACLrG,UAAMA,IADD;AAEL8C,mBAAeA;AAFV,GAAP;AAID,CA1LgC,EAAjC,C,CAXA;;;;kBAuMeC,0B;;;;;;;;;;;;;;8QCvMf;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;AAGA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AA7HA;;;;AAMA;;;;AAMA;;;;AASA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;AAMA,IAAI4K,cAAe,YAAW;AAC5B;;;;;;;;AAQA,WAASnJ,UAAT,CAAoB4G,UAApB,EAAgC;AAC9B,QAAIwC,QAAQ,KAAZ;;AAEA;;;;AAIAvJ,0BAAYC,WAAZ;;AAEA;;;;;AAKAuJ,qBAAOrJ,UAAP;;AAEA;;;AAGA3F,wBAAU2F,UAAV;;AAEA;;;;AAIA1G,qBAAO/B,KAAP,GAAeqP,WAAWrP,KAAX,IAAoBuC,SAApB,GAAgC8M,WAAWrP,KAA3C,GAAmD,KAAlE;;AAEA;;;AAGA+R,WAAO1C,UAAP;;AAEA;;;AAGA,QAAIA,WAAWhQ,KAAf,EAAsB;AACpB;;;AAGA,UAAIgQ,WAAWhQ,KAAX,CAAiBsF,MAAjB,IAA2B,CAA/B,EAAkC;AAChC;;;;AAIA5C,yBAAO1C,KAAP,GAAegQ,WAAWhQ,KAA1B;AACA;;;AAGAwS,gBAAQ,IAAR;AACD,OAVD,MAUO;AACLrO,wBAAMC,YAAN,CAAmB,8CAAnB;AACD;AACF,KAjBD,MAiBO;AACLD,sBAAMC,YAAN,CACE,uDADF;AAGD;;AAED;;;;AAIA,QAAIuO,aAAGC,oBAAH,EAAJ,EAA+B;AAC7B,UAAID,aAAGE,mBAAH,EAAJ,EAA8B;AAC5B;;;AAGAF,qBAAGG,oBAAH;;AAEA;;;AAGA3N,iBAAS4N,eAAT,CAAyB7O,gBAAzB,CACE,WADF,EACe,YAAU;AACrB,cAAIxB,iBAAOf,OAAP,CAAesD,KAAf,KAAyB,SAA7B,EAAwC;AACtCvC,6BAAOf,OAAP,CAAeqR,MAAf;AACD;AACF,SALH;;AAOE7N,iBAAS4N,eAAT,CAAyB7O,gBAAzB,CACA,SADA,EACW,YAAU;AACnB,cAAIxB,iBAAOf,OAAP,CAAesD,KAAf,KAAyB,SAA7B,EAAwC;AACtCvC,6BAAOf,OAAP,CAAeqR,MAAf;AACD;AACF,SALD;;AAOA7N,iBAAS4N,eAAT,CAAyB7O,gBAAzB,CACE,OADF,EACW,YAAU;AACjB,cAAIxB,iBAAOf,OAAP,CAAesD,KAAf,KAAyB,SAA7B,EAAwC;AACtCvC,6BAAOf,OAAP,CAAeqR,MAAf;AACD;AACJ,SALD;;AASA;;;AAGA,YACEhD,WAAW9N,SAAX,IAAwBgB,SAAxB,IACA8M,WAAW9N,SAAX,CAAqBC,WAArB,IAAoCe,SAFtC,EAGE;AACAR,2BAAOR,SAAP,CAAiBC,WAAjB,GAA+B6N,WAAW9N,SAAX,CAAqBC,WAApD;AACD;;AAED;;;AAGA8Q,2BAASC,IAAT;;AAEA;;;;AAIA,YACElD,WAAWlO,cAAX,IAA6BoB,SAA7B,IACA8M,WAAWlO,cAAX,CAA0BwD,MAA1B,GAAmC,CAFrC,EAGE;AACA;;;;AAIA,eAAK,IAAID,IAAI,CAAb,EAAgBA,IAAI2K,WAAWlO,cAAX,CAA0BwD,MAA9C,EAAsDD,GAAtD,EAA2D;AACzD9C,qCAAe0M,QAAf,CACEe,WAAWlO,cAAX,CAA0BuD,CAA1B,EAA6B8N,MAD/B,EAEEnD,WAAWlO,cAAX,CAA0BuD,CAA1B,EAA6B+N,MAF/B;AAID;AACF;AACJ;AACF,KApED,MAoEO;AACLjP,sBAAMC,YAAN,CACE,yFADF;AAGD;;AAED;;;AAGAiP;;AAEA;;;AAGAC;;AAEA;;;AAGA,QAAId,KAAJ,EAAW;AACT;;;;AAIA9P,uBAAO1B,iBAAP,GACEgP,WAAWhP,iBAAX,IAAgCkC,SAAhC,GACI8M,WAAWhP,iBADf,GAEI,EAHN;;AAKA;;;AAGA0B,uBAAOzB,kBAAP,GACE+O,WAAW/O,kBAAX,IAAiCiC,SAAjC,GACI8M,WAAW/O,kBADf,GAEI,EAHN;;AAKA;;;;;;;AAOA,UAAI6O,iBAAiB,EAArB;;AAEA;;;;AAIA,UAAIpN,iBAAO1B,iBAAP,IAA4B,EAAhC,EAAoC;AAClC8O,yBAAiBE,UAAjB;;AAEA;;;AAGAH,6BAAWE,cAAX,CAA0BD,cAA1B;AACD,OAPD,MAOO;AACL;;;;AAIA+B,kBAAU7B,UAAV;AACD;AACF;;AAED;;;AAGA7L,oBAAMC,YAAN,CAAmB,oBAAnB;AACAD,oBAAMC,YAAN,CAAmB1B,gBAAnB;AACD;;AAED;;;;;;AAMA,WAAS6Q,aAAT,GAAyB;AACvBd,qBAAOrJ,UAAP;AACApB,+BAAiBC,eAAjB;AACD;;AAED;;;;;;;;;;;;;;;;AAgBA,WAAS4J,SAAT,CAAmB7B,UAAnB,EAA+B;AAC7B;;;;AAIA,QAAIA,WAAW/P,SAAX,IAAwBuT,eAAexD,WAAW/P,SAA1B,IAAuC,CAAnE,EAAsE;AACpEwT,0BAAqBrK,UAArB,CAAgC4G,WAAW/P,SAA3C;AACD;;AAED;;;AAGA,QAAI+P,WAAW9P,UAAX,IAAyBgD,SAAzB,IAAsC8M,WAAW7P,iBAArD,EAAwE;AACtE;;;AAGA,UAAI2H,iBAAOe,KAAP,CAAamH,WAAW9P,UAAxB,CAAJ,EAAyC;AACvCmG,kCAAgBQ,UAAhB,CACEnE,iBAAO1C,KAAP,CAAagQ,WAAW9P,UAAxB,CADF,EAEE8P,WAAW9P,UAFb;AAID,OALD,MAKO;AACLiE,wBAAMC,YAAN,CACE,qDADF;AAGD;AACF,KAdD,MAcO;AACLiC,gCAAgBQ,UAAhB,CAA2BnE,iBAAO1C,KAAP,CAAa,CAAb,CAA3B,EAA4C,CAA5C;AACD;;AAED;;;;AAIA,QAAIgQ,WAAWxP,UAAX,IAAyB0C,SAAzB,IAAsC8M,WAAWxP,UAArD,EAAiE;AAC/DkC,uBAAOlC,UAAP,GAAoB,IAApB;AACA8K,yBAASE,YAAT;;AAEAnF,gCAAgBQ,UAAhB,CAA2BnE,iBAAOnC,YAAP,CAAoB,CAApB,CAA3B,EAAmD,CAAnD;AACD;;AAED;;;;;AAKAmC,qBAAOnB,aAAP,GACEyO,WAAWzO,aAAX,IAA4B2B,SAA5B,GAAwC8M,WAAWzO,aAAnD,GAAmE,IADrE;;AAGA;;;;;AAKAmB,qBAAO5C,cAAP,GACEkQ,WAAWlQ,cAAX,IAA6BoD,SAA7B,GAAyC8M,WAAWlQ,cAApD,GAAqE,GADvE;;AAGA;;;AAGAuC,mBAAKoC,gBAAL,CAAsB/B,iBAAO5C,cAA7B;;AAEA;;;;;AAKA4C,qBAAOlD,KAAP,CAAakU,OAAb,GACE1D,WAAW0D,OAAX,IAAsBxQ,SAAtB,GAAkC8M,WAAW0D,OAA7C,GAAuD,MADzD;;AAGA;;;;;AAKAhR,qBAAO3C,SAAP,GACEiQ,WAAWjQ,SAAX,IAAwBmD,SAAxB,GAAoC8M,WAAWjQ,SAA/C,GAA2D,EAD7D;;AAGA;;;;;AAKA2C,qBAAOpB,QAAP,GACE0O,WAAW1O,QAAX,IAAuB4B,SAAvB,GAAmC8M,WAAW1O,QAA9C,GAAyD,EAD3D;;AAGA;;;;;;AAMAoB,qBAAO9B,MAAP,GAAgBoP,WAAWpP,MAAX,IAAqBsC,SAArB,GAAiC8M,WAAWpP,MAA5C,GAAqD,EAArE;;AAEA;;;AAGA8B,qBAAOlB,KAAP,GAAewO,WAAWxO,KAAX,IAAoB0B,SAApB,GAAgC8M,WAAWxO,KAA3C,GAAmD,CAAlE;;AAEA;;;;;AAKAkB,qBAAO5B,gBAAP,GACEkP,WAAWlP,gBAAX,IAA+BoC,SAA/B,GACI8M,WAAWlP,gBADf,GAEI,CAHN;;AAKA4B,qBAAO3B,gBAAP,GACEiP,WAAWjP,gBAAX,IAA+BmC,SAA/B,GACI8M,WAAWjP,gBADf,GAEI,CAHN;;AAKA;;;;AAIAsB,mBAAKqB,SAAL,CAAehB,iBAAO9B,MAAtB;;AAEA;;;AAGA8R,WAAO1C,UAAP;;AAEA;;;AAGA2D;;AAEA;;;;AAIA,QACE3D,WAAW7P,iBAAX,IAAgC+C,SAAhC,IACA8M,WAAW7P,iBAAX,IAAgC,EAFlC,EAGE;AACA;;;AAGAuC,uBAAO7C,eAAP,GAAyBmQ,WAAW7P,iBAApC;;AAEA;;;AAGA,UACE6P,WAAW5P,sBAAX,IAAqC8C,SAArC,IACA8M,WAAW5P,sBAAX,IAAqC,EAFvC,EAGE;AACA;;;AAGA,YACE,QAAO4P,WAAW/P,SAAX,CAAqB+P,WAAW7P,iBAAhC,EAAmDH,KAAnD,CACL4G,SAASoJ,WAAW5P,sBAApB,CADK,CAAP,KAEK8C,SAHP,EAIE;AACA;;;AAGAmD,oCAAgBY,kBAAhB,CACEvE,iBAAO7C,eADT,EAEEmQ,WAAW/P,SAAX,CAAqB+P,WAAW7P,iBAAhC,EAAmDH,KAAnD,CACE4G,SAASoJ,WAAW5P,sBAApB,CADF,CAFF,EAKEwG,SAASoJ,WAAW5P,sBAApB,CALF;AAOD,SAfD,MAeO;AACL;;;AAGAiG,oCAAgBY,kBAAhB,CACEvE,iBAAO7C,eADT,EAEEmQ,WAAW/P,SAAX,CAAqB+P,WAAW7P,iBAAhC,EAAmDH,KAAnD,CAAyD,CAAzD,CAFF,EAGE,CAHF;AAKA;;;AAGAmE,0BAAMC,YAAN,CACE,kBACE4L,WAAW5P,sBADb,GAEE,kCAFF,GAGE4P,WAAW7P,iBAJf;AAMD;AACF,OAzCD,MAyCO;AACL;;;AAGAkG,kCAAgBQ,UAAhB,CACEnE,iBAAO7C,eADT,EAEEmQ,WAAW/P,SAAX,CAAqB+P,WAAW7P,iBAAhC,EAAmDH,KAAnD,CAAyD,CAAzD,CAFF,EAGE,CAHF;AAKD;;AAED;;;AAGA2E,kCAAkBC,IAAlB;AACD;;AAED;;;AAGAnB,wBAAUhB,GAAV,CAAc,aAAd;AACD;;AAED;;;;;;;AAOA,WAASiQ,MAAT,CAAgB1C,UAAhB,EAA2B;AACzB;;;;AAIA,QAAIA,WAAWvP,iBAAX,IAAgCyC,SAApC,EAA+C;AAC7CR,uBAAOjC,iBAAP,GAA2BuP,WAAWvP,iBAAtC;AACD,KAFD,MAEO;AACLiC,uBAAOjC,iBAAP,GAA2B,EAA3B;AACD;;AAED;;;;AAIA,QAAIuP,WAAWtP,oBAAX,IAAmCwC,SAAvC,EAAkD;AAChDR,uBAAOhC,oBAAP,GAA8BsP,WAAWtP,oBAAzC;AACD,KAFD,MAEO;AACLgC,uBAAOhC,oBAAP,GAA8B,EAA9B;AACD;AACF;;AAED;;;;;;AAMA,WAASiT,kBAAT,GAA8B;AAC5B;;;AAGA1B,8BAAgBzF,QAAhB;;AAEA;;;AAGA5B,2BAAaC,QAAb,CAAsBnI,iBAAO9B,MAAP,IAAiB,CAAjB,GAAqB,IAArB,GAA4B,KAAlD;;AAEA;;;AAGAmK,mCAAqBnG,IAArB;;AAEA;;;AAGAmN,oCAAsBnN,IAAtB;;AAEA;;;AAGAgD,2BAAaC,iBAAb;;AAEA;;;AAGAlD,gCAAkBqB,WAAlB;;AAEA;;;AAGAgC,+BAAiBmC,YAAjB;;AAEA;;;AAGAC,6BAAeM,cAAf;AACD;;AAED;;;;;;;;;AASA,WAAS8I,cAAT,CAAwBvT,SAAxB,EAAmC;AACjC;;;;AAIA,QAAI2T,OAAO,CAAX;AAAA,QACEhF,YADF;;AAGA;;;;AAIA,SAAKA,GAAL,IAAY3O,SAAZ,EAAuB;AACrB,UAAIA,UAAU4T,cAAV,CAAyBjF,GAAzB,CAAJ,EAAmC;AACjCgF;AACD;AACF;;AAED;;;AAGAzP,oBAAMC,YAAN,CAAmB,cAAcwP,IAAd,GAAqB,6BAAxC;;AAEA;;;AAGA,WAAOA,IAAP;AACD;;AAED;;;;;AAKA,WAASP,6BAAT,GAAyC;AACvC,SAAK,IAAIhO,IAAI,CAAb,EAAgBA,IAAI3C,iBAAO1C,KAAP,CAAasF,MAAjC,EAAyCD,GAAzC,EAA8C;AAC5C,UAAI3C,iBAAO1C,KAAP,CAAaqF,CAAb,EAAgB1C,IAAhB,IAAwBO,SAA5B,EAAuC;AACrCR,yBAAO1C,KAAP,CAAaqF,CAAb,EAAgB1C,IAAhB,GAAuB,KAAvB;AACD;AACF;AACF;;AAED;;;;;;AAMA,WAAS2Q,4BAAT,GAAuC;AACrC,SAAK,IAAIjO,IAAI,CAAb,EAAgBA,IAAI3C,iBAAO1C,KAAP,CAAasF,MAAjC,EAAyCD,GAAzC,EAA8C;AAC5C3C,uBAAO1C,KAAP,CAAaqF,CAAb,EAAgBsB,KAAhB,GAAwBtB,CAAxB;AACD;AACF;;AAGD;;;AAGA,SAAO;AACL+D,gBAAYA,UADP;AAELyI,eAAWA,SAFN;AAGL0B,mBAAeA;AAHV,GAAP;AAKD,CAtlBiB,EAAlB;;kBAwlBehB,W;;;;;;;;;;;;;;ACjuBf;;;;;;AAEA;;;;;AAKA,IAAIU,WAAY,YAAW;AACzB;;;AAGA,MAAIa,SAAS,EAAb;AACA,MAAIC,aAAarR,iBAAOR,SAAP,CAAiBC,WAAlC;AACA,MAAI6R,QAAQ,EAAZ;;AAEA,WAASd,IAAT,GAAgB;AACd;;;AAGA,QAAIhR,YAAYiD,SAASC,gBAAT,CAA0B,sBAA1B,CAAhB;;AAEA;;;;AAIA,QAAIlD,UAAUoD,MAAV,GAAmB,CAAvB,EAA0B;AACxB;;;AAGA,WAAK,IAAID,IAAI,CAAb,EAAgBA,IAAInD,UAAUoD,MAA9B,EAAsCD,GAAtC,EAA2C;AACzC;;;AAGAnD,kBAAUmD,CAAV,EAAayE,SAAb,GAAyB,EAAzB;;AAEA;;;AAGA,YAAImK,MAAM9O,SAAS+O,eAAT,CAAyB,4BAAzB,EAAuD,KAAvD,CAAV;AACAD,YAAIpK,YAAJ,CAAiB,SAAjB,EAA4B,UAAUkK,UAAV,GAAuB,IAAnD;AACAE,YAAIpK,YAAJ,CAAiB,qBAAjB,EAAwC,MAAxC;;AAEA;;;AAGA,YAAIsK,IAAIhP,SAAS+O,eAAT,CAAyB,4BAAzB,EAAuD,GAAvD,CAAR;AACAD,YAAIxD,WAAJ,CAAgB0D,CAAhB;;AAEA;;;AAGA,YAAIC,OAAOjP,SAAS+O,eAAT,CACT,4BADS,EAET,MAFS,CAAX;AAIAE,aAAKvK,YAAL,CAAkB,GAAlB,EAAuB,EAAvB;AACAuK,aAAKvK,YAAL,CAAkB,IAAlB,EAAwB,UAAxB;;AAEAsK,UAAE1D,WAAF,CAAc2D,IAAd;;AAEA;;;AAGAlS,kBAAUmD,CAAV,EAAaoL,WAAb,CAAyBwD,GAAzB;AACD;AACF;AACF;;AAED;;;AAGA,WAASI,KAAT,GAAiB;AACf,QAAI3R,iBAAOhB,uBAAX,EAAoC;AAClC;;;;AAIA,UACEgB,iBAAOR,SAAP,CAAiBE,KAAjB,CACE4J,KAAKsI,GAAL,CACE5R,iBAAOlD,KAAP,CAAa8E,GAAb,CAAiBiQ,KAAjB,CAAuB,EAAvB,EAA2BC,MAA3B,CAAkC,UAASC,CAAT,EAAYC,CAAZ,EAAe;AAC/CD,YAAI,CAACA,KAAK,CAAN,IAAWA,CAAX,GAAeC,EAAEC,UAAF,CAAa,CAAb,CAAnB;AACA,eAAOF,IAAIA,CAAX;AACD,OAHD,EAGG,CAHH,CADF,CADF,KAOKvR,SARP,EASE;AACA;;;AAGA,YAAI0R,MAAM,IAAIC,cAAJ,EAAV;;AAEA;;;AAGAD,YAAIE,IAAJ,CAAS,KAAT,EAAgBpS,iBAAOlD,KAAP,CAAa8E,GAA7B,EAAkC,IAAlC;AACAsQ,YAAIG,YAAJ,GAAmB,aAAnB;;AAEA;;;;AAIAH,YAAItE,kBAAJ,GAAyB,UAAS0E,CAAT,EAAY;AACnC;;;;AAIA,cAAIJ,IAAIK,UAAJ,IAAkB,CAAtB,EAAyB;AACvB;;;;AAIA,gBAAIL,IAAIM,MAAJ,IAAc,GAAlB,EAAuB;AACrB;;;AAGAxS,+BAAOf,OAAP,CAAewT,eAAf,CAA+BP,IAAIQ,QAAnC,EAA6C,UAC3CC,aAD2C,EAE3C;AACA;;;AAGAvB,yBAASuB,aAAT;;AAEA;;;AAGArB,wBAAQsB,SAASvB,UAAT,EAAqBD,MAArB,CAAR;;AAEA;;;AAGAyB,wBAAQxB,UAAR,EAAoBD,MAApB,EAA4BE,KAA5B;AACD,eAjBD;AAkBD;AACF;AACF,SAlCD;AAmCAY,YAAIY,IAAJ;AACD,OA7DD,MA6DO;AACL;;;;;AAKAC,yBACE/S,iBAAOR,SAAP,CAAiBE,KAAjB,CACE4J,KAAKsI,GAAL,CACE5R,iBAAOlD,KAAP,CAAa8E,GAAb,CAAiBiQ,KAAjB,CAAuB,EAAvB,EAA2BC,MAA3B,CAAkC,UAASC,CAAT,EAAYC,CAAZ,EAAe;AAC/CD,cAAI,CAACA,KAAK,CAAN,IAAWA,CAAX,GAAeC,EAAEC,UAAF,CAAa,CAAb,CAAnB;AACA,iBAAOF,IAAIA,CAAX;AACD,SAHD,EAGG,CAHH,CADF,CADF,CADF;AAUD;AACF;AACF;;AAED;;;;;;;AAOA,WAASc,OAAT,CAAiBxB,UAAjB,EAA6BD,MAA7B,EAAqCE,KAArC,EAA4C;AAC1C;;;AAGA,QAAIF,MAAJ,EAAY;AACV;;;AAGA,UAAI4B,aAAa1B,MAAM1O,MAAvB;;AAEA;;;AAGA,UAAIqQ,IAAI,EAAR;AACA,WAAK,IAAIC,aAAa,CAAtB,EAAyBA,aAAaF,UAAtC,EAAkDE,YAAlD,EAAgE;AAC9D,YAAIA,aAAa,CAAb,KAAmB,CAAvB,EAA0B;AACxBD,sBAAU,CAAC,EAAEC,aAAa,CAAf,CAAX,UAAiC5B,MAAM6B,KAAN,EAAjC;AACD,SAFD,MAEO;AACLF,sBAAU,CAAC,EAAEC,aAAa,CAAf,CAAX,UAAiC5B,MAAM6B,KAAN,EAAjC;AACD;AACF;;AAED;;;AAGAnT,uBAAOR,SAAP,CAAiBE,KAAjB,CACE4J,KAAKsI,GAAL,CACE5R,iBAAOlD,KAAP,CAAa8E,GAAb,CAAiBiQ,KAAjB,CAAuB,EAAvB,EAA2BC,MAA3B,CAAkC,UAASC,CAAT,EAAYC,CAAZ,EAAe;AAC/CD,YAAI,CAACA,KAAK,CAAN,IAAWA,CAAX,GAAeC,EAAEC,UAAF,CAAa,CAAb,CAAnB;AACA,eAAOF,IAAIA,CAAX;AACD,OAHD,EAGG,CAHH,CADF,CADF,IAOIkB,CAPJ;;AASA;;;AAGAF,uBACE/S,iBAAOR,SAAP,CAAiBE,KAAjB,CACE4J,KAAKsI,GAAL,CACE5R,iBAAOlD,KAAP,CAAa8E,GAAb,CAAiBiQ,KAAjB,CAAuB,EAAvB,EAA2BC,MAA3B,CAAkC,UAASC,CAAT,EAAYC,CAAZ,EAAe;AAC/CD,YAAI,CAACA,KAAK,CAAN,IAAWA,CAAX,GAAeC,EAAEC,UAAF,CAAa,CAAb,CAAnB;AACA,eAAOF,IAAIA,CAAX;AACD,OAHD,EAGG,CAHH,CADF,CADF,CADF;AAUD;AACF;;AAED;;;;;;AAMA,WAASa,QAAT,CAAkBhQ,MAAlB,EAA0BwO,MAA1B,EAAkC;AAChC;;;AAGA,QAAMgC,aAAahC,OAAOxO,MAAP,GAAgBA,MAAnC;AACA,QAAMyQ,aAAa,CAAC,EAAED,aAAa,EAAf,CAAD,IAAuB,CAA1C;AACA,QAAME,mBAAmBlC,OAAOkC,gBAAhC;AACA,QAAMC,cAAc,EAApB;;AAEA;;;AAGA,SACE,IAAIC,gBAAgB,CADtB,EAEEA,gBAAgBF,gBAFlB,EAGEE,eAHF,EAIE;AACA;;;;AAIA,UAAMlC,SAAQ,EAAd;AACA,UAAMmC,cAAcrC,OAAOsC,cAAP,CAAsBF,aAAtB,CAApB;;AAEA;;;AAGA,WAAK,IAAIN,aAAa,CAAtB,EAAyBA,aAAatQ,MAAtC,EAA8CsQ,YAA9C,EAA4D;AAC1D;;;AAGA,YAAMS,QAAQ,CAAC,EAAET,aAAaE,UAAf,CAAf;AACA,YAAMQ,MAAM,CAAC,EAAED,QAAQP,UAAV,CAAb;;AAEA;;;AAGA,YAAIS,MAAMJ,YAAY,CAAZ,CAAV;AACA,YAAI7D,MAAM6D,YAAY,CAAZ,CAAV;;AAEA;;;;AAIA,aACE,IAAIK,cAAcH,KADpB,EAEEG,cAAcF,GAFhB,EAGEE,eAAeT,UAHjB,EAIE;AACA,cAAM9K,QAAQkL,YAAYK,WAAZ,CAAd;;AAEA,cAAIvL,QAAQqH,GAAZ,EAAiB;AACfA,kBAAMrH,KAAN;AACD;AACD,cAAIA,QAAQsL,GAAZ,EAAiB;AACfA,kBAAMtL,KAAN;AACD;AACF;;AAED;;;AAGA+I,eAAM,IAAI4B,UAAV,IAAwBtD,GAAxB;AACA0B,eAAM,IAAI4B,UAAJ,GAAiB,CAAvB,IAA4BW,GAA5B;;AAEA;;;AAGA,YAAIL,kBAAkB,CAAlB,IAAuB5D,MAAM2D,YAAY,IAAIL,UAAhB,CAAjC,EAA8D;AAC5DK,sBAAY,IAAIL,UAAhB,IAA8BtD,GAA9B;AACD;;AAED,YAAI4D,kBAAkB,CAAlB,IAAuBK,MAAMN,YAAY,IAAIL,UAAJ,GAAiB,CAA7B,CAAjC,EAAkE;AAChEK,sBAAY,IAAIL,UAAJ,GAAiB,CAA7B,IAAkCW,GAAlC;AACD;AACF;AACF;;AAED;;;AAGA,WAAON,WAAP;AACD;;AAED;;;;;AAKA,WAASR,gBAAT,CAA0BxB,GAA1B,EAA+B;AAC7B,QAAIwC,mBAAmBtR,SAASC,gBAAT,CAA0B,sBAA1B,CAAvB;;AAEA;;;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIoR,iBAAiBnR,MAArC,EAA6CD,GAA7C,EAAkD;AAChD;;;AAGA,UAAIE,WAAWkR,iBAAiBpR,CAAjB,EAAoBG,YAApB,CACb,yBADa,CAAf;;AAIA;;;AAGA,UAAIC,OAAOgR,iBAAiBpR,CAAjB,EAAoBG,YAApB,CAAiC,2BAAjC,CAAX;;AAEA;;;AAGA,UAAID,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCiR,8BAAsBD,iBAAiBpR,CAAjB,CAAtB,EAA2C4O,GAA3C;AACD;;AAED;;;AAGA,UAAI1O,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCkR,gCAAwBF,iBAAiBpR,CAAjB,CAAxB,EAA6C4O,GAA7C,EAAkD1O,QAAlD;AACD;;AAED;;;;AAIA,UAAIA,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCmR,4BAAoBH,iBAAiBpR,CAAjB,CAApB,EAAyC4O,GAAzC,EAA8CxO,IAA9C;AACD;;AAED;;;AAGA,UAAIF,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCoR,sCAA8BJ,iBAAiBpR,CAAjB,CAA9B,EAAmD4O,GAAnD,EAAwD1O,QAAxD,EAAkEE,IAAlE;AACD;AACF;AACF;;AAED;;;;;;AAMA,WAASiR,qBAAT,CAA+BzQ,OAA/B,EAAwCgO,GAAxC,EAA6C;AAC3C,QAAI6C,eAAe7Q,QAAQ8Q,aAAR,CAAsB,YAAtB,CAAnB;;AAEAD,iBAAajN,YAAb,CAA0B,GAA1B,EAA+BoK,GAA/B;AACD;;AAED;;;;;;;AAOA,WAAS0C,uBAAT,CAAiC1Q,OAAjC,EAA0CgO,GAA1C,EAA+C1O,QAA/C,EAAyD;AACvD;;;AAGA,QAAI7C,iBAAO7C,eAAP,IAA0B0F,QAA9B,EAAwC;AACtC,UAAIuR,eAAe7Q,QAAQ8Q,aAAR,CAAsB,YAAtB,CAAnB;;AAEAD,mBAAajN,YAAb,CAA0B,GAA1B,EAA+BoK,GAA/B;AACD;AACF;;AAED;;;;;;;;AAQA,WAAS2C,mBAAT,CAA6B3Q,OAA7B,EAAsCgO,GAAtC,EAA2CxO,IAA3C,EAAiD;AAC/C;;;AAGA,QAAI/C,iBAAO9C,YAAP,IAAuB6F,IAA3B,EAAiC;AAC/B,UAAIqR,eAAe7Q,QAAQ8Q,aAAR,CAAsB,YAAtB,CAAnB;;AAEAD,mBAAajN,YAAb,CAA0B,GAA1B,EAA+BoK,GAA/B;AACD;AACF;;AAED;;;;;;;;AAQA,WAAS4C,6BAAT,CAAuC5Q,OAAvC,EAAgDgO,GAAhD,EAAqD1O,QAArD,EAA+DE,IAA/D,EAAqE;AACnE;;;AAGA,QACE/C,iBAAO7C,eAAP,IAA0B0F,QAA1B,IACA7C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAAzC,IAAyD6F,IAF3D,EAGE;AACA,UAAIqR,eAAe7Q,QAAQ8Q,aAAR,CAAsB,YAAtB,CAAnB;;AAEAD,mBAAajN,YAAb,CAA0B,GAA1B,EAA+BoK,GAA/B;AACD;AACF;;AAED;;;AAGA,WAAS+C,yBAAT,GAAoC;AAClC,QAAI9U,YAAYiD,SAASC,gBAAT,CAA0B,sBAA1B,CAAhB;;AAEA,QAAIlD,UAAUoD,MAAV,GAAmB,CAAvB,EAA0B;AACxB,aAAO,IAAP;AACD,KAFD,MAEK;AACH,aAAO,KAAP;AACD;AACF;;AAED;;;AAGA,SAAO;AACL4N,UAAMA,IADD;AAELmB,WAAOA,KAFF;AAGL2C,+BAA2BA;AAHtB,GAAP;AAKD,CA9bc,EAAf,C,CAXA;;;;kBA2ce/D,Q;;;;;;;;;;;;;;ACvcf;;;;;;AAEA;;;;AAIA,IAAIgE,OAAQ,YAAW;AACrB;;;;;;AAMA,WAASC,mBAAT,GAA+B;AAC7B;;;AAGA,QAAI1T,cAAc,EAAlB;;AAEA;;;AAGA,QAAI2T,iBACF,CAACnL,KAAKC,KAAL,CAAWvJ,iBAAOlD,KAAP,CAAagE,WAAb,GAA2B,EAAtC,IAA4C,EAA5C,GAAiD,GAAjD,GAAuD,EAAxD,IACAwI,KAAKC,KAAL,CAAWvJ,iBAAOlD,KAAP,CAAagE,WAAb,GAA2B,EAAtC,CAFF;;AAIA;;;AAGA,QAAI4T,iBAAiBpL,KAAKC,KAAL,CAAWvJ,iBAAOlD,KAAP,CAAagE,WAAb,GAA2B,EAAtC,CAArB;;AAEA;;;AAGA,QAAI6T,eAAe,IAAnB;;AAEA;;;AAGA,QAAID,iBAAiB,EAArB,EAAyB;AACvBA,uBAAiB,MAAMA,cAAvB;AACD;;AAED;;;;AAIA,QAAIA,kBAAkB,EAAtB,EAA0B;AACxBC,qBAAerL,KAAKC,KAAL,CAAWmL,iBAAiB,EAA5B,CAAf;AACAA,uBAAiBA,iBAAiB,EAAlC;;AAEA;;;;AAIA,UAAIA,iBAAiB,EAArB,EAAyB;AACvBA,yBAAiB,MAAMA,cAAvB;AACD;AACF;;AAED;;;AAGA5T,gBAAYS,OAAZ,GAAsBkT,cAAtB;AACA3T,gBAAY8J,OAAZ,GAAsB8J,cAAtB;AACA5T,gBAAY6J,KAAZ,GAAoBgK,YAApB;;AAEA,WAAO7T,WAAP;AACD;;AAED;;;;;;AAMA,WAAS8T,mBAAT,GAA+B;AAC7B;;;AAGA,QAAIzJ,eAAe,EAAnB;;AAEA;;;AAGA,QAAI0J,sBACF,CAACvL,KAAKC,KAAL,CAAWvJ,iBAAOlD,KAAP,CAAauE,QAAb,GAAwB,EAAnC,IAAyC,EAAzC,GAA8C,GAA9C,GAAoD,EAArD,IACAiI,KAAKC,KAAL,CAAWvJ,iBAAOlD,KAAP,CAAauE,QAAb,GAAwB,EAAnC,CAFF;;AAIA;;;AAGA,QAAIyT,sBAAsBxL,KAAKC,KAAL,CAAWvJ,iBAAOlD,KAAP,CAAauE,QAAb,GAAwB,EAAnC,CAA1B;;AAEA;;;AAGA,QAAI0T,oBAAoB,IAAxB;;AAEA;;;AAGA,QAAID,sBAAsB,EAA1B,EAA8B;AAC5BA,4BAAsB,MAAMA,mBAA5B;AACD;;AAED;;;;AAIA,QAAIA,uBAAuB,EAA3B,EAA+B;AAC7BC,0BAAoBzL,KAAKC,KAAL,CAAWuL,sBAAsB,EAAjC,CAApB;AACAA,4BAAsBA,sBAAsB,EAA5C;;AAEA;;;;AAIA,UAAIA,sBAAsB,EAA1B,EAA8B;AAC5BA,8BAAsB,MAAMA,mBAA5B;AACD;AACF;;AAED;;;AAGA3J,iBAAa5J,OAAb,GAAuB8E,MAAMwO,mBAAN,IACnB,IADmB,GAEnBA,mBAFJ;AAGA1J,iBAAaP,OAAb,GAAuBvE,MAAMyO,mBAAN,IACnB,IADmB,GAEnBA,mBAFJ;AAGA3J,iBAAaR,KAAb,GAAqBtE,MAAM0O,iBAAN,IACjB,IADiB,GAEjBA,kBAAkBC,QAAlB,EAFJ;;AAIA,WAAO7J,YAAP;AACD;;AAED;;;;;AAKA,WAAS8J,+BAAT,GAA2C;AACzC,WAAQjV,iBAAOlD,KAAP,CAAagE,WAAb,GAA2Bd,iBAAOlD,KAAP,CAAauE,QAAzC,GAAqD,GAA5D;AACD;;AAED;;;;;AAKA,WAAS6T,cAAT,CAAwBC,IAAxB,EAA8B;AAC5B;;;AAGA,QAAI,CAACnV,iBAAOhD,eAAP,CAAuBiD,IAA5B,EAAkC;AAChC;;;AAGA,UAAImV,SAASD,IAAT,CAAJ,EAAoB;AAClBnV,yBAAOlD,KAAP,CAAagE,WAAb,GAA2BqU,IAA3B;AACD;AACF;AACF;;AAED;;;AAGA,SAAO;AACLX,yBAAqBA,mBADhB;AAELI,yBAAqBA,mBAFhB;AAGLK,qCAAiCA,+BAH5B;AAILC,oBAAgBA;AAJX,GAAP;AAMD,CA1KU,EAAX,C,CAVA;;;;kBAsLeX,I;;;;;;;;;;;;;;AClLf;;;;;;AAEA;;;;;AAKA,IAAIc,2BAA4B,YAAW;AACzC;;;;;AAKA,WAASnT,IAAT,GAAgB;AACdC;AACAC;AACAC;AACAC;AACD;;AAED;;;AAGA,WAASH,UAAT,GAAsB;AACpB;;;AAGA,QAAMmT,2BAA2B7S,SAASoF,sBAAT,CAC/B,6BAD+B,CAAjC;;AAIA;;;;AAIA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAI2S,yBAAyB1S,MAA7C,EAAqDD,GAArD,EAA0D;AACxD,UAAIE,WAAWyS,yBAAyB3S,CAAzB,EAA4BG,YAA5B,CACb,yBADa,CAAf;AAGA,UAAIC,OAAOuS,yBAAyB3S,CAAzB,EAA4BG,YAA5B,CACT,2BADS,CAAX;;AAIA,UAAID,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCuS,iCAAyB3S,CAAzB,EAA4B4F,KAA5B,GAAoCgN,WAClCA,WAAWvV,iBAAOrB,QAAlB,IAA8B,GADI,CAApC;AAGD;AACF;AACF;;AAED;;;AAGA,WAASyD,YAAT,GAAwB;AACtB;;;AAGA,QAAMoT,mCAAmC/S,SAASC,gBAAT,CACvC,2DACE1C,iBAAO7C,eADT,GAEE,IAHqC,CAAzC;;AAMA;;;;AAIA,SAAK,IAAIwF,IAAI,CAAb,EAAgBA,IAAI6S,iCAAiC5S,MAArD,EAA6DD,GAA7D,EAAkE;AAChE,UAAII,OAAOyS,iCAAiC7S,CAAjC,EAAoCG,YAApC,CACT,2BADS,CAAX;;AAIA,UAAIC,QAAQ,IAAZ,EAAkB;AAChByS,yCAAiC7S,CAAjC,EAAoC4F,KAApC,GAA4CgN,WAC1CA,WAAWvV,iBAAOrB,QAAlB,IAA8B,GADY,CAA5C;AAGD;AACF;AACF;;AAED;;;AAGA,WAAS0D,QAAT,GAAoB;AAClB;;;AAGA,QAAMoT,gCAAgChT,SAASC,gBAAT,CACpC,6DACE1C,iBAAO9C,YADT,GAEE,IAHkC,CAAtC;;AAMA;;;;AAIA,SAAK,IAAIyF,IAAI,CAAb,EAAgBA,IAAI8S,8BAA8B7S,MAAlD,EAA0DD,GAA1D,EAA+D;AAC7D,UAAIE,WAAW4S,8BAA8B9S,CAA9B,EAAiCG,YAAjC,CACb,yBADa,CAAf;;AAIA,UAAID,YAAY,IAAhB,EAAsB;AACpB4S,sCAA8B9S,CAA9B,EAAiC4F,KAAjC,GAAyCgN,WACvCA,WAAWvV,iBAAOrB,QAAlB,IAA8B,GADS,CAAzC;AAGD;AACF;AACF;;AAED;;;AAGA,WAAS2D,kBAAT,GAA8B;AAC5B,QAAIc,sBACFpD,iBAAO7C,eAAP,IAA0B,IAA1B,IAAkC6C,iBAAO7C,eAAP,IAA0B,EAA5D,GACI6C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAD7C,GAEI,IAHN;;AAKA;;;AAGA,QAAMwY,0CAA0CjT,SAASC,gBAAT,CAC9C,6DACEU,mBADF,GAEE,8BAFF,GAGEpD,iBAAO7C,eAHT,GAIE,IAL4C,CAAhD;;AAQA;;;;AAIA,SAAK,IAAIwF,IAAI,CAAb,EAAgBA,IAAI+S,wCAAwC9S,MAA5D,EAAoED,GAApE,EAAyE;AACvE+S,8CAAwC/S,CAAxC,EAA2C4F,KAA3C,GAAmDgN,WACjDA,WAAWvV,iBAAOrB,QAAlB,IAA8B,GADmB,CAAnD;AAGD;AACF;;AAED;;;;;AAKA,WAASgX,KAAT,GAAiB;AACf;;;AAGA,QAAIL,2BAA2B7S,SAASoF,sBAAT,CAC7B,6BAD6B,CAA/B;;AAIA;;;;AAIA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAI2S,yBAAyB1S,MAA7C,EAAqDD,GAArD,EAA0D;AACxD2S,+BAAyB3S,CAAzB,EAA4B4F,KAA5B,GAAoC,CAApC;AACD;AACF;;AAED;;;AAGA,SAAO;AACLrG,UAAMA,IADD;AAELyT,WAAOA;AAFF,GAAP;AAID,CApK8B,EAA/B,C,CAXA;;;;kBAiLeN,wB;;;;;;;;;;;;;;AC7Kf;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;;AAZA;;;;;;AAZA;;;;AA6BA,IAAIO,QAAS,YAAW;AACtB;;;;;;;AAOA,WAASC,MAAT,GAAkB;AAChB;;;;AAIAC,eAAW,YAAW;AACpB;;;AAGA,UAAI9V,iBAAOnB,aAAX,EAA0B;AACxB;;;;AAIA,YAAImB,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA9D,EAAoE;AAClEwG,oCAAgBC,OAAhB,CAAwB,IAAxB;AACD,SAFD,MAEO;AACLD,oCAAgBS,eAAhB,CAAgCpE,iBAAO7C,eAAvC,EAAwD,IAAxD;AACD;AACF,OAVD,MAUO;AACL,YAAI,CAAC6C,iBAAOtB,eAAZ,EAA6B;AAC3B;;;AAGAqX,wBAAcjW,IAAd;;AAEA;;;AAGAmC,sCAAkBC,IAAlB;AACD;AACF;AACF,KA3BD,EA2BGlC,iBAAOlB,KA3BV;AA4BD;;AAED;;;AAGA,SAAO;AACL+W,YAAQA;AADH,GAAP;AAGD,CAjDW,EAAZ;;AAXA;;;;;;AAZA;;;;AAZA;;;;kBAsFeD,K;;;;;;;;;;;;;;AClFf;;;;AAKA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AAKA;;;;;;AAEA;;;;;;;;AA9BA;;;AAoCA,IAAI7F,SAAU,YAAW;AACvB;;;;;AAKA,WAASrJ,UAAT,GAAsB;AACpB;;;AAGAjF,oBAAMC,YAAN,CAAmB,8CAAnB;;AAEA;;;AAGAe,aAASjB,gBAAT,CAA0B,WAA1B,EAAuC,YAAW;AAChDxB,uBAAOtB,eAAP,GAAyB,IAAzB;AACD,KAFD;;AAIA;;;;AAIA+D,aAASjB,gBAAT,CAA0B,UAA1B,EAAsC,YAAW;AAC/C,UAAIxB,iBAAOtB,eAAX,EAA4B;AAC1BsB,yBAAOtB,eAAP,GAAyB,KAAzB;AACD;AACF,KAJD;;AAMA;;;;AAIAsX;;AAEA;;;AAGAC;;AAEA;;;;;;;AAOAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;AACD;;AAED;;;;;;AAMA,WAASpB,cAAT,GAA0B;AACxB;;;AAGAhW,qBAAOlD,KAAP,CAAaua,mBAAb,CAAiC,YAAjC,EAA+CC,qBAAWzB,MAA1D;AACA7V,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,YAA9B,EAA4C8V,qBAAWzB,MAAvD;;AAEA;;;AAGA7V,qBAAOlD,KAAP,CAAaua,mBAAb,CAAiC,gBAAjC,EAAmDC,qBAAWzB,MAA9D;AACA7V,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,gBAA9B,EAAgD8V,qBAAWzB,MAA3D;AACD;;AAED;;;;;;;AAOA,WAASI,wBAAT,GAAoC;AAClCxT,aAAS4U,mBAAT,CAA6B,SAA7B,EAAwCE,kBAAQ1B,MAAhD;AACApT,aAASjB,gBAAT,CAA0B,SAA1B,EAAqC+V,kBAAQ1B,MAA7C;AACD;;AAED;;;;;;;;;AASA,WAASK,aAAT,GAAyB;AACvBlW,qBAAOlD,KAAP,CAAaua,mBAAb,CAAiC,OAAjC,EAA0CzB,gBAAMC,MAAhD;AACA7V,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,OAA9B,EAAuCoU,gBAAMC,MAA7C;AACD;;AAED;;;;;;;AAOA,WAASM,YAAT,GAAwB;AACtBnW,qBAAOlD,KAAP,CAAaua,mBAAb,CAAiC,UAAjC,EAA6CG,mBAAS3B,MAAtD;AACA7V,qBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,UAA9B,EAA0CgW,mBAAS3B,MAAnD;AACD;;AAED;;;;;AAKA,WAASO,QAAT,GAAoB;AAClB;;;AAGA,QAAIqB,eAAehV,SAASoF,sBAAT,CAAgC,gBAAhC,CAAnB;;AAEA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAI8U,aAAa7U,MAAjC,EAAyCD,GAAzC,EAA8C;AAC5C,UACE,iEAAiExC,IAAjE,CACEC,UAAUC,SADZ,CADF,EAIE;AACAoX,qBAAa9U,CAAb,EAAgB0U,mBAAhB,CAAoC,UAApC,EAAgDK,eAAK7B,MAArD;AACA4B,qBAAa9U,CAAb,EAAgBnB,gBAAhB,CAAiC,UAAjC,EAA6CkW,eAAK7B,MAAlD;AACD,OAPD,MAOO;AACL4B,qBAAa9U,CAAb,EAAgB0U,mBAAhB,CAAoC,OAApC,EAA6CK,eAAK7B,MAAlD;AACA4B,qBAAa9U,CAAb,EAAgBnB,gBAAhB,CAAiC,OAAjC,EAA0CkW,eAAK7B,MAA/C;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASQ,SAAT,GAAqB;AACnB;;;AAGA,QAAIsB,gBAAgBlV,SAASoF,sBAAT,CAAgC,iBAAhC,CAApB;;AAEA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAIgV,cAAc/U,MAAlC,EAA0CD,GAA1C,EAA+C;AAC7C,UACE,iEAAiExC,IAAjE,CACEC,UAAUC,SADZ,CADF,EAIE;AACAsX,sBAAchV,CAAd,EAAiB0U,mBAAjB,CAAqC,UAArC,EAAiDO,gBAAM/B,MAAvD;AACA8B,sBAAchV,CAAd,EAAiBnB,gBAAjB,CAAkC,UAAlC,EAA8CoW,gBAAM/B,MAApD;AACD,OAPD,MAOO;AACL8B,sBAAchV,CAAd,EAAiB0U,mBAAjB,CAAqC,OAArC,EAA8CO,gBAAM/B,MAApD;AACA8B,sBAAchV,CAAd,EAAiBnB,gBAAjB,CAAkC,OAAlC,EAA2CoW,gBAAM/B,MAAjD;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASS,aAAT,GAAyB;AACvB;;;AAGA,QAAIuB,qBAAqBpV,SAASoF,sBAAT,CACvB,sBADuB,CAAzB;;AAIA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAIkV,mBAAmBjV,MAAvC,EAA+CD,GAA/C,EAAoD;AAClD,UACE,iEAAiExC,IAAjE,CACEC,UAAUC,SADZ,CADF,EAIE;AACAwX,2BAAmBlV,CAAnB,EAAsB0U,mBAAtB,CAA0C,UAA1C,EAAsDS,oBAAUjC,MAAhE;AACAgC,2BAAmBlV,CAAnB,EAAsBnB,gBAAtB,CAAuC,UAAvC,EAAmDsW,oBAAUjC,MAA7D;AACD,OAPD,MAOO;AACLgC,2BAAmBlV,CAAnB,EAAsB0U,mBAAtB,CAA0C,OAA1C,EAAmDS,oBAAUjC,MAA7D;AACAgC,2BAAmBlV,CAAnB,EAAsBnB,gBAAtB,CAAuC,OAAvC,EAAgDsW,oBAAUjC,MAA1D;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASU,QAAT,GAAoB;AAClB;;;AAGA,QAAIwB,eAAetV,SAASoF,sBAAT,CAAgC,gBAAhC,CAAnB;;AAEA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAIoV,aAAanV,MAAjC,EAAyCD,GAAzC,EAA8C;AAC5C,UACE,iEAAiExC,IAAjE,CACEC,UAAUC,SADZ,CADF,EAIE;AACA0X,qBAAapV,CAAb,EAAgB0U,mBAAhB,CAAoC,UAApC,EAAgDW,eAAKnC,MAArD;AACAkC,qBAAapV,CAAb,EAAgBnB,gBAAhB,CAAiC,UAAjC,EAA6CwW,eAAKnC,MAAlD;AACD,OAPD,MAOO;AACLkC,qBAAapV,CAAb,EAAgB0U,mBAAhB,CAAoC,OAApC,EAA6CW,eAAKnC,MAAlD;AACAkC,qBAAapV,CAAb,EAAgBnB,gBAAhB,CAAiC,OAAjC,EAA0CwW,eAAKnC,MAA/C;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASW,QAAT,GAAoB;AAClB;;;AAGA,QAAIyB,eAAexV,SAASoF,sBAAT,CAAgC,gBAAhC,CAAnB;;AAEA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAIsV,aAAarV,MAAjC,EAAyCD,GAAzC,EAA8C;AAC5C;;;;;AAKA,UACE,iEAAiExC,IAAjE,CACEC,UAAUC,SADZ,CADF,EAIE;AACA;;;;AAIA,YAAI,oBAAoBF,IAApB,CAAyBC,UAAUC,SAAnC,CAAJ,EAAmD;AACjDoB,0BAAMC,YAAN,CACE,wQADF;AAGD,SAJD,MAIO;AACLuW,uBAAatV,CAAb,EAAgB0U,mBAAhB,CAAoC,UAApC,EAAgDa,eAAKrC,MAArD;AACAoC,uBAAatV,CAAb,EAAgBnB,gBAAhB,CAAiC,UAAjC,EAA6C0W,eAAKrC,MAAlD;AACD;AACF,OAjBD,MAiBO;AACLoC,qBAAatV,CAAb,EAAgB0U,mBAAhB,CAAoC,OAApC,EAA6Ca,eAAKrC,MAAlD;AACAoC,qBAAatV,CAAb,EAAgBnB,gBAAhB,CAAiC,OAAjC,EAA0C0W,eAAKrC,MAA/C;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASY,YAAT,GAAwB;AACtB;;;AAGA,QAAI0B,oBAAoB1V,SAASoF,sBAAT,CACtB,qBADsB,CAAxB;;AAIA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAIwV,kBAAkBvV,MAAtC,EAA8CD,GAA9C,EAAmD;AACjD;;;;;AAKA,UACE,iEAAiExC,IAAjE,CACEC,UAAUC,SADZ,CADF,EAIE;AACA;;;;AAIA,YAAI,oBAAoBF,IAApB,CAAyBC,UAAUC,SAAnC,CAAJ,EAAmD;AACjDoB,0BAAMC,YAAN,CACE,wQADF;AAGD,SAJD,MAIO;AACLyW,4BAAkBxV,CAAlB,EAAqB0U,mBAArB,CAAyC,UAAzC,EAAqDe,mBAASvC,MAA9D;AACAsC,4BAAkBxV,CAAlB,EAAqBnB,gBAArB,CAAsC,UAAtC,EAAkD4W,mBAASvC,MAA3D;AACD;AACF,OAjBD,MAiBO;AACLsC,0BAAkBxV,CAAlB,EAAqB0U,mBAArB,CAAyC,OAAzC,EAAkDe,mBAASvC,MAA3D;AACAsC,0BAAkBxV,CAAlB,EAAqBnB,gBAArB,CAAsC,OAAtC,EAA+C4W,mBAASvC,MAAxD;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASa,cAAT,GAA0B;AACxB;;;AAGA,QAAI2B,sBAAsB5V,SAASoF,sBAAT,CACxB,uBADwB,CAA1B;;AAIA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAI0V,oBAAoBzV,MAAxC,EAAgDD,GAAhD,EAAqD;AACnD;;;;;AAKA,UACE,iEAAiExC,IAAjE,CACEC,UAAUC,SADZ,CADF,EAIE;AACA;;;;AAIA,YAAI,oBAAoBF,IAApB,CAAyBC,UAAUC,SAAnC,CAAJ,EAAmD;AACjDoB,0BAAMC,YAAN,CACE,wQADF;AAGD,SAJD,MAIO;AACL2W,8BAAoB1V,CAApB,EAAuB0U,mBAAvB,CACE,UADF,EAEEiB,qBAAWzC,MAFb;AAIAwC,8BAAoB1V,CAApB,EAAuBnB,gBAAvB,CACE,UADF,EAEE8W,qBAAWzC,MAFb;AAID;AACF,OAvBD,MAuBO;AACLwC,4BAAoB1V,CAApB,EAAuB0U,mBAAvB,CAA2C,OAA3C,EAAoDiB,qBAAWzC,MAA/D;AACAwC,4BAAoB1V,CAApB,EAAuBnB,gBAAvB,CAAwC,OAAxC,EAAiD8W,qBAAWzC,MAA5D;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASc,cAAT,GAA0B;AACxB;;;;AAIA,QAAI4B,KAAKC,OAAOpY,SAAP,CAAiBC,SAA1B;AACA,QAAIoY,OAAOF,GAAGrR,OAAH,CAAW,OAAX,CAAX;;AAEA;;;AAGA,QAAIwR,eAAejW,SAASoF,sBAAT,CAAgC,uBAAhC,CAAnB;;AAEA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAI+V,aAAa9V,MAAjC,EAAyCD,GAAzC,EAA8C;AAC5C,UAAI8V,OAAO,CAAP,IAAY,CAAC,CAACrY,UAAUC,SAAV,CAAoB+N,KAApB,CAA0B,mBAA1B,CAAlB,EAAkE;AAChEsK,qBAAa/V,CAAb,EAAgB0U,mBAAhB,CAAoC,QAApC,EAA8CsB,qBAAW9C,MAAzD;AACA6C,qBAAa/V,CAAb,EAAgBnB,gBAAhB,CAAiC,QAAjC,EAA2CmX,qBAAW9C,MAAtD;AACD,OAHD,MAGO;AACL6C,qBAAa/V,CAAb,EAAgB0U,mBAAhB,CAAoC,OAApC,EAA6CsB,qBAAW9C,MAAxD;AACA6C,qBAAa/V,CAAb,EAAgBnB,gBAAhB,CAAiC,OAAjC,EAA0CmX,qBAAW9C,MAArD;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASe,gBAAT,GAA4B;AAC1B;;;;AAIA,QAAI2B,KAAKC,OAAOpY,SAAP,CAAiBC,SAA1B;AACA,QAAIoY,OAAOF,GAAGrR,OAAH,CAAW,OAAX,CAAX;;AAEA;;;AAGA,QAAI0R,iBAAiBnW,SAASoF,sBAAT,CACnB,yBADmB,CAArB;;AAIA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAIiW,eAAehW,MAAnC,EAA2CD,GAA3C,EAAgD;AAC9C;;;;;AAKA,UAAI,oBAAoBxC,IAApB,CAAyBC,UAAUC,SAAnC,CAAJ,EAAmD;AACjDoB,wBAAMC,YAAN,CACE,wQADF;AAGD,OAJD,MAIO;AACL,YAAI+W,OAAO,CAAP,IAAY,CAAC,CAACrY,UAAUC,SAAV,CAAoB+N,KAApB,CAA0B,mBAA1B,CAAlB,EAAkE;AAChEwK,yBAAejW,CAAf,EAAkB0U,mBAAlB,CAAsC,QAAtC,EAAgDwB,uBAAahD,MAA7D;AACA+C,yBAAejW,CAAf,EAAkBnB,gBAAlB,CAAmC,QAAnC,EAA6CqX,uBAAahD,MAA1D;AACD,SAHD,MAGO;AACL+C,yBAAejW,CAAf,EAAkB0U,mBAAlB,CAAsC,OAAtC,EAA+CwB,uBAAahD,MAA5D;AACA+C,yBAAejW,CAAf,EAAkBnB,gBAAlB,CAAmC,OAAnC,EAA4CqX,uBAAahD,MAAzD;AACD;AACF;AACF;AACF;;AAED;;;;;AAKA,WAASgB,QAAT,GAAoB;AAClB;;;AAGA,QAAIiC,eAAerW,SAASoF,sBAAT,CAAgC,gBAAhC,CAAnB;;AAEA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAImW,aAAalW,MAAjC,EAAyCD,GAAzC,EAA8C;AAC5C,UACE,iEAAiExC,IAAjE,CACEC,UAAUC,SADZ,CADF,EAIE;AACAyY,qBAAanW,CAAb,EAAgB0U,mBAAhB,CAAoC,UAApC,EAAgD0B,eAAKlD,MAArD;AACAiD,qBAAanW,CAAb,EAAgBnB,gBAAhB,CAAiC,UAAjC,EAA6CuX,eAAKlD,MAAlD;AACD,OAPD,MAOO;AACLiD,qBAAanW,CAAb,EAAgB0U,mBAAhB,CAAoC,OAApC,EAA6C0B,eAAKlD,MAAlD;AACAiD,qBAAanW,CAAb,EAAgBnB,gBAAhB,CAAiC,OAAjC,EAA0CuX,eAAKlD,MAA/C;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASiB,QAAT,GAAoB;AAClB;;;AAGA,QAAIkC,eAAevW,SAASoF,sBAAT,CAAgC,gBAAhC,CAAnB;;AAEA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAIqW,aAAapW,MAAjC,EAAyCD,GAAzC,EAA8C;AAC5C,UACE,iEAAiExC,IAAjE,CACEC,UAAUC,SADZ,CADF,EAIE;AACA2Y,qBAAarW,CAAb,EAAgB0U,mBAAhB,CAAoC,UAApC,EAAgD4B,eAAKpD,MAArD;AACAmD,qBAAarW,CAAb,EAAgBnB,gBAAhB,CAAiC,UAAjC,EAA6CyX,eAAKpD,MAAlD;AACD,OAPD,MAOO;AACLmD,qBAAarW,CAAb,EAAgB0U,mBAAhB,CAAoC,OAApC,EAA6C4B,eAAKpD,MAAlD;AACAmD,qBAAarW,CAAb,EAAgBnB,gBAAhB,CAAiC,OAAjC,EAA0CyX,eAAKpD,MAA/C;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASkB,WAAT,GAAuB;AACrB;;;AAGA,QAAImC,kBAAkBzW,SAASoF,sBAAT,CAAgC,mBAAhC,CAAtB;;AAEA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAIuW,gBAAgBtW,MAApC,EAA4CD,GAA5C,EAAiD;AAC/C;;;;AAIAuW,sBAAgBvW,CAAhB,EAAmBa,SAAnB,CAA6BE,MAA7B,CAAoC,sBAApC;AACAwV,sBAAgBvW,CAAhB,EAAmBa,SAAnB,CAA6BC,GAA7B,CAAiC,uBAAjC;;AAEA,UACE,iEAAiEtD,IAAjE,CACEC,UAAUC,SADZ,CADF,EAIE;AACA6Y,wBAAgBvW,CAAhB,EAAmB0U,mBAAnB,CAAuC,UAAvC,EAAmD8B,kBAAQtD,MAA3D;AACAqD,wBAAgBvW,CAAhB,EAAmBnB,gBAAnB,CAAoC,UAApC,EAAgD2X,kBAAQtD,MAAxD;AACD,OAPD,MAOO;AACLqD,wBAAgBvW,CAAhB,EAAmB0U,mBAAnB,CAAuC,OAAvC,EAAgD8B,kBAAQtD,MAAxD;AACAqD,wBAAgBvW,CAAhB,EAAmBnB,gBAAnB,CAAoC,OAApC,EAA6C2X,kBAAQtD,MAArD;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASmB,UAAT,GAAsB;AACpB;;;AAGA,QAAIoC,iBAAiB3W,SAASoF,sBAAT,CAAgC,kBAAhC,CAArB;;AAEA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAIyW,eAAexW,MAAnC,EAA2CD,GAA3C,EAAgD;AAC9C;;;;AAIAyW,qBAAezW,CAAf,EAAkBa,SAAlB,CAA4BE,MAA5B,CAAmC,qBAAnC;AACA0V,qBAAezW,CAAf,EAAkBa,SAAlB,CAA4BC,GAA5B,CAAgC,sBAAhC;;AAEA,UACE,iEAAiEtD,IAAjE,CACEC,UAAUC,SADZ,CADF,EAIE;AACA+Y,uBAAezW,CAAf,EAAkB0U,mBAAlB,CAAsC,UAAtC,EAAkDgC,iBAAOxD,MAAzD;AACAuD,uBAAezW,CAAf,EAAkBnB,gBAAlB,CAAmC,UAAnC,EAA+C6X,iBAAOxD,MAAtD;AACD,OAPD,MAOO;AACLuD,uBAAezW,CAAf,EAAkB0U,mBAAlB,CAAsC,OAAtC,EAA+CgC,iBAAOxD,MAAtD;AACAuD,uBAAezW,CAAf,EAAkBnB,gBAAlB,CAAmC,OAAnC,EAA4C6X,iBAAOxD,MAAnD;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASoB,cAAT,GAA0B;AACxB;;;AAGA,QAAIqC,sBAAsB7W,SAASoF,sBAAT,CACxB,uBADwB,CAA1B;;AAIA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAI2W,oBAAoB1W,MAAxC,EAAgDD,GAAhD,EAAqD;AACnD;;;;AAIA2W,0BAAoB3W,CAApB,EAAuBa,SAAvB,CAAiCE,MAAjC,CAAwC,qBAAxC;AACA4V,0BAAoB3W,CAApB,EAAuBa,SAAvB,CAAiCC,GAAjC,CAAqC,sBAArC;;AAEA,UACE,iEAAiEtD,IAAjE,CACEC,UAAUC,SADZ,CADF,EAIE;AACAiZ,4BAAoB3W,CAApB,EAAuB0U,mBAAvB,CACE,UADF,EAEEkC,qBAAW1D,MAFb;AAIAyD,4BAAoB3W,CAApB,EAAuBnB,gBAAvB,CAAwC,UAAxC,EAAoD+X,qBAAW1D,MAA/D;AACD,OAVD,MAUO;AACLyD,4BAAoB3W,CAApB,EAAuB0U,mBAAvB,CAA2C,OAA3C,EAAoDkC,qBAAW1D,MAA/D;AACAyD,4BAAoB3W,CAApB,EAAuBnB,gBAAvB,CAAwC,OAAxC,EAAiD+X,qBAAW1D,MAA5D;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASqB,iBAAT,GAA6B;AAC3B;;;AAGA,QAAIsC,yBAAyB/W,SAASoF,sBAAT,CAC3B,0BAD2B,CAA7B;;AAIA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAI6W,uBAAuB5W,MAA3C,EAAmDD,GAAnD,EAAwD;AACtD,UACE,iEAAiExC,IAAjE,CACEC,UAAUC,SADZ,CADF,EAIE;AACAmZ,+BAAuB7W,CAAvB,EAA0B0U,mBAA1B,CACE,UADF,EAEEoC,wBAAc5D,MAFhB;AAIA2D,+BAAuB7W,CAAvB,EAA0BnB,gBAA1B,CACE,UADF,EAEEiY,wBAAc5D,MAFhB;AAID,OAbD,MAaO;AACL2D,+BAAuB7W,CAAvB,EAA0B0U,mBAA1B,CACE,OADF,EAEEoC,wBAAc5D,MAFhB;AAIA2D,+BAAuB7W,CAAvB,EAA0BnB,gBAA1B,CACE,OADF,EAEEiY,wBAAc5D,MAFhB;AAID;AACF;AACF;;AAED;;;;;AAKA,WAASsB,UAAT,GAAsB;AACpB;;;AAGA,QAAIuC,gBAAgBjX,SAASoF,sBAAT,CAAgC,mBAAhC,CAApB;;AAEA;;;;;AAKA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAI+W,cAAc9W,MAAlC,EAA0CD,GAA1C,EAA+C;AAC7C,UACE,iEAAiExC,IAAjE,CACEC,UAAUC,SADZ,CADF,EAIE;AACAqZ,sBAAc/W,CAAd,EAAiB0U,mBAAjB,CAAqC,UAArC,EAAiDsC,iBAAO9D,MAAxD;AACA6D,sBAAc/W,CAAd,EAAiBnB,gBAAjB,CAAkC,UAAlC,EAA8CmY,iBAAO9D,MAArD;AACD,OAPD,MAOO;AACL6D,sBAAc/W,CAAd,EAAiB0U,mBAAjB,CAAqC,OAArC,EAA8CsC,iBAAO9D,MAArD;AACA6D,sBAAc/W,CAAd,EAAiBnB,gBAAjB,CAAkC,OAAlC,EAA2CmY,iBAAO9D,MAAlD;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASuB,kBAAT,GAA8B;AAC5B,QAAI7G,mBAAS+D,yBAAT,EAAJ,EAA0C;AACxCtU,uBAAOlD,KAAP,CAAaua,mBAAb,CAAiC,gBAAjC,EAAmD9G,mBAASoB,KAA5D;AACA3R,uBAAOlD,KAAP,CAAa0E,gBAAb,CAA8B,gBAA9B,EAAgD+O,mBAASoB,KAAzD;AACD;AACF;;AAED;;;AAGA,SAAO;AACLjL,gBAAYA;AADP,GAAP;AAGD,CA5zBY,EAAb;;AAXA;;;AA/BA;;;;kBAw2BeqJ,M;;;;;;;;;;;;;;ACp2Bf;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;;AAZA;;;;;;AAZA;;;;;;AAZA;;;;AAyCA,IAAIwH,UAAW,YAAW;AACxB;;;;;;;;;AASA,WAAS1B,MAAT,CAAgB+D,KAAhB,EAAuB;AACrBC,gBAAYD,MAAME,KAAlB;AACD;;AAED;;;;;;AAMA,WAASD,WAAT,CAAqB3N,GAArB,EAA0B;AACxB;;;AAGA,QAAIlM,iBAAOpB,QAAP,CAAgBsN,GAAhB,KAAwB1L,SAA5B,EAAuC;AACrC;;;AAGA,cAAQR,iBAAOpB,QAAP,CAAgBsN,GAAhB,CAAR;AACE;;;AAGA,aAAK,YAAL;AACE6N;AACA;;AAEF;;;AAGA,aAAK,MAAL;AACEC;AACA;;AAEF;;;AAGA,aAAK,MAAL;AACEC;AACA;;AAEF;;;AAGA,aAAK,MAAL;AACEC;AACA;;AAEF;;;AAGA,aAAK,SAAL;AACEC;AACA;;AAEF;;;AAGA,aAAK,QAAL;AACEC;AACA;AAzCJ;AA2CD;AACF;;AAED;;;AAGA,WAASL,wBAAT,GAAoC;AAClC;;;;AAIA,QAAI/Z,iBAAOlD,KAAP,CAAawD,MAAjB,EAAyB;AACvBX,qBAAKC,IAAL;AACD,KAFD,MAEO;AACLD,qBAAKiB,KAAL;AACD;;AAED;;;;;AAKAqB,gCAAkBC,IAAlB;AACD;;AAED;;;AAGA,WAAS8X,mBAAT,GAA+B;AAC7B;;;;AAIA,QAAIha,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA9D,EAAoE;AAClEwG,gCAAgBC,OAAhB;AACD,KAFD,MAEO;AACLD,gCAAgBS,eAAhB,CAAgCpE,iBAAO7C,eAAvC;AACD;AACF;;AAED;;;AAGA,WAAS8c,mBAAT,GAA+B;AAC7B;;;;AAIA,QAAIja,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA9D,EAAoE;AAClEwG,gCAAgBa,WAAhB;AACD,KAFD,MAEO;AACLb,gCAAgBgB,mBAAhB,CAAoC3E,iBAAO7C,eAA3C;AACD;AACF;;AAED;;;AAGA,WAAS+c,mBAAT,GAA+B;AAC7B;;;AAGAjY,gCAAkBqB,WAAlB;;AAEA;;;AAGA3D,mBAAKG,IAAL;AACD;;AAED;;;AAGA,WAASqa,sBAAT,GAAkC;AAChC;;;;AAIA,QAAIna,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA9D,EAAoE;AAClEgc,yBAAQpQ,aAAR;AACD,KAFD,MAEO;AACLoQ,yBAAQjQ,qBAAR,CAA8BlJ,iBAAO7C,eAArC;AACD;AACF;;AAED;;;AAGA,WAASid,qBAAT,GAAiC;AAC/B;;;AAGA5R,uBAASC,SAAT,CAAmB,CAACzI,iBAAOrC,MAA3B;;AAEA;;;AAGA+J,6BAAeC,UAAf;AACD;;AAED;;;AAGA,SAAO;AACLkO,YAAQA;AADH,GAAP;AAGD,CAjLa,EAAd;;AAXA;;;;;;AAZA;;;;;;AAZA;;;;AAZA;;;;kBAkOe0B,O;;;;;;;;;;;;;;AC/Nf;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;AAZA;;;;AAXA;;;AA2BA,IAAIW,OAAQ,YAAW;AACrB;;;;;;;AAOA,WAASrC,MAAT,GAAkB;AAChB;;;;AAIA,QAAI,CAAC7V,iBAAOtB,eAAZ,EAA6B;AAC3B;;;;;;;AAQA,UAAIsB,iBAAO9B,MAAP,IAAiB,CAArB,EAAwB;AACtByB,uBAAKqB,SAAL,CAAehB,iBAAO7B,eAAtB;AACD,OAFD,MAEO;AACL6B,yBAAO7B,eAAP,GAAyB6B,iBAAO9B,MAAhC;AACAyB,uBAAKqB,SAAL,CAAe,CAAf;AACD;;AAED;;;AAGAkH,6BAAaC,QAAb,CAAsBnI,iBAAO9B,MAAP,IAAiB,CAAjB,GAAqB,IAArB,GAA4B,KAAlD;;AAEA;;;;;AAKAmK,qCAAqBnG,IAArB;AACD;AACF;;AAED;;;AAGA,SAAO;AACL2T,YAAQA;AADH,GAAP;AAGD,CAjDU,EAAX;;AAVA;;;;;;AAZA;;;;kBAyEeqC,I;;;;;;;;;;;;;;AC1Ef;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;;AAZA;;;;;;AAZA;;;;AAZA;;;;AAyCA,IAAIa,OAAQ,YAAW;AACrB;;;;;;;;;;AAUA,WAASlD,MAAT,GAAkB;AAChB;;;;AAIA,QAAI,CAAC7V,iBAAOtB,eAAZ,EAA6B;AAC3B;;;AAGA,UAAImE,WAAW,KAAKC,YAAL,CAAkB,yBAAlB,CAAf;;AAEA;;;AAGA,UAAID,YAAY,IAAhB,EAAsB;AACpBwX;AACD;;AAED;;;AAGA,UAAIxX,YAAY,IAAhB,EAAsB;AACpByX,2BAAmBzX,QAAnB;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASwX,gBAAT,GAA4B;AAC1B;;;;;AAKA,QAAIra,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA9D,EAAoE;AAClEwG,gCAAgBC,OAAhB;AACD,KAFD,MAEO;AACLD,gCAAgBS,eAAhB,CAAgCpE,iBAAO7C,eAAvC;AACD;AACF;;AAED;;;;;;AAMA,WAASmd,kBAAT,CAA4BzX,QAA5B,EAAsC;AACpC;;;;AAIA,QAAIA,YAAY7C,iBAAO7C,eAAvB,EAAwC;AACtCwG,gCAAgBS,eAAhB,CAAgCvB,QAAhC;AACD,KAFD,MAEO;AACLpB,sBAAMC,YAAN,CACE,yEADF;AAGD;AACF;;AAED;;;AAGA,SAAO;AACLmU,YAAQA;AADH,GAAP;AAGD,CAlFU,EAAX;;AAXA;;;;;;AAZA;;;;;;AAZA;;;;kBAuHekD,I;;;;;;;;;;;;;;ACzHf;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;AAZA;;;;AAZA;;;;AA4BA,IAAInB,QAAS,YAAW;AACtB;;;;;;;;;;;;AAYA,WAAS/B,MAAT,GAAkB;AAChB;;;;AAIA,QAAI,CAAC7V,iBAAOtB,eAAZ,EAA6B;AAC3B;;;;;;;AAOA,UAAI6b,qBAAqB,KAAKzX,YAAL,CAAkB,2BAAlB,CAAzB;AACA,UAAImH,oBAAoB,KAAKnH,YAAL,CAAkB,yBAAlB,CAAxB;;AAEA;;;AAGA,UAAImH,qBAAqB,IAArB,IAA6BsQ,sBAAsB,IAAvD,EAA6D;AAC3DC;AACD;;AAED;;;AAGA,UAAIvQ,qBAAqB,IAArB,IAA6BsQ,sBAAsB,IAAvD,EAA6D;AAC3DE,4BAAoBxQ,iBAApB;AACD;;AAED;;;AAGA,UAAIA,qBAAqB,IAArB,IAA6BsQ,sBAAsB,IAAvD,EAA6D;AAC3DG,wBAAgBH,kBAAhB;AACD;;AAED;;;AAGA,UAAItQ,qBAAqB,IAArB,IAA6BsQ,sBAAsB,IAAvD,EAA6D;AAC3DI,kCAA0B1Q,iBAA1B,EAA6CsQ,kBAA7C;AACD;;AAEDhU,4BAAYE,cAAZ;AACD;AACF;;AAED;;;;;;AAMA,WAAS+T,iBAAT,GAA6B;AAC3B;;;AAGA7a,mBAAKiB,KAAL;;AAEA;;;AAGAqB,gCAAkBC,IAAlB;AACD;;AAED;;;;;;AAMA,WAASuY,mBAAT,CAA6B5X,QAA7B,EAAuC;AACrC;;;AAGA,QAAI7C,iBAAO7C,eAAP,IAA0B0F,QAA9B,EAAwC;AACtC;;;AAGAlD,qBAAKiB,KAAL;;AAEA;;;AAGAqB,kCAAkBC,IAAlB;AACD;AACF;;AAED;;;;;;AAMA,WAASwY,eAAT,CAAyB3X,IAAzB,EAA+B;AAC7B;;;;AAIA,QACE,CAAC/C,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA3D,KACA6C,iBAAO9C,YAAP,IAAuB6F,IAFzB,EAGE;AACA;;;AAGApD,qBAAKiB,KAAL;;AAEA;;;AAGAqB,kCAAkBC,IAAlB;AACD;AACF;;AAED;;;;;;;AAOA,WAASyY,yBAAT,CAAmC9X,QAAnC,EAA6CE,IAA7C,EAAmD;AACjD;;;;;AAKA,QACE/C,iBAAO7C,eAAP,IAA0B0F,QAA1B,IACA7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2B3F,YAA3B,IAA2C6F,IAF7C,EAGE;AACA;;;AAGApD,qBAAKiB,KAAL;;AAEA;;;AAGAqB,kCAAkBC,IAAlB;AACD;AACF;;AAED;;;AAGA,SAAO;AACL2T,YAAQA;AADH,GAAP;AAGD,CApKW,EAAZ;;AAVA;;;;;;AAZA;;;;kBA4Le+B,K;;;;;;;;;;;;;;AC9Lf;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;AAZA;;;;;;AAZA;;;;AAZA;;;;AAwCA,IAAIF,OAAQ,YAAW;AACrB;;;;;;;;;;;;AAYA,WAAS7B,MAAT,GAAkB;AAChB;;;;AAIA,QAAI,CAAC7V,iBAAOtB,eAAZ,EAA6B;AAC3B;;;;;;;AAOA,UAAI6b,qBAAqB,KAAKzX,YAAL,CAAkB,2BAAlB,CAAzB;AACA,UAAImH,oBAAoB,KAAKnH,YAAL,CAAkB,yBAAlB,CAAxB;;AAEA;;;AAGA,UAAImH,qBAAqB,IAArB,IAA6BsQ,sBAAsB,IAAvD,EAA6D;AAC3DK;AACD;;AAED;;;AAGA,UAAI3Q,qBAAqB,IAArB,IAA6BsQ,sBAAsB,IAAvD,EAA6D;AAC3DM,2BAAmB5Q,iBAAnB;AACD;;AAED;;;AAGA,UAAIA,qBAAqB,IAArB,IAA6BsQ,sBAAsB,IAAvD,EAA6D;AAC3DO,uBAAeP,kBAAf;AACD;;AAED;;;AAGA,UAAItQ,qBAAqB,IAArB,IAA6BsQ,sBAAsB,IAAvD,EAA6D;AAC3DQ,iCAAyB9Q,iBAAzB,EAA4CsQ,kBAA5C;AACD;;AAEDhU,4BAAYE,cAAZ;AACD;AACF;;AAED;;;;;;AAMA,WAASmU,gBAAT,GAA4B;AAC1B;;;AAGAjb,mBAAKC,IAAL;;AAEA;;;AAGAqC,gCAAkBC,IAAlB;AACD;;AAED;;;;;;AAMA,WAAS2Y,kBAAT,CAA4BhY,QAA5B,EAAsC;AACpC;;;AAGA,QAAIuC,iBAAOY,WAAP,CAAmBnD,QAAnB,CAAJ,EAAkC;AAChC;;;AAGAc,gCAAgBW,iBAAhB,CAAkCzB,QAAlC;;AAEA;;;;;;AAOA,UAAI7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BwB,OAA/B,EAAwC;AACtCV,kCAAgBY,kBAAhB,CACE1B,QADF,EAEE7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BhF,YAA3B,CAAwC,CAAxC,CAFF,EAGE,CAHF;AAKD,OAND,MAMO;AACL8F,kCAAgBY,kBAAhB,CACE1B,QADF,EAEE7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiC,CAAjC,CAFF,EAGE,CAHF;AAKD;AACF;;AAED;;;AAGAqC,mBAAKC,IAAL;;AAEA;;;;AAIAqC,gCAAkBC,IAAlB;AACD;;AAED;;;;;;AAMA,WAAS4Y,cAAT,CAAwB/X,IAAxB,EAA8B;AAC5B;;;;;;AAMA;;;;AAIA,QAAIqC,iBAAOY,WAAP,CAAmB,IAAnB,CAAJ,EAA8B;AAC5B;;;;AAIArC,gCAAgBW,iBAAhB,CAAkC,IAAlC;;AAEA;;;AAGAX,gCAAgBQ,UAAhB,CAA2BnE,iBAAO1C,KAAP,CAAayF,IAAb,CAA3B,EAA+CA,IAA/C;AACD;;AAED;;;;;;AAMA,QAAIqC,iBAAOU,OAAP,CAAe,IAAf,EAAqB/C,IAArB,CAAJ,EAAgC;AAC9B;;;;AAIAY,gCAAgBQ,UAAhB,CAA2BnE,iBAAO1C,KAAP,CAAayF,IAAb,CAA3B,EAA+CA,IAA/C;AACD;;AAED;;;AAGApD,mBAAKC,IAAL;;AAEA;;;;AAIAqC,gCAAkBC,IAAlB;AACD;;AAED;;;;;;;AAOA,WAAS6Y,wBAAT,CAAkClY,QAAlC,EAA4CE,IAA5C,EAAkD;AAChD;;;;;;AAMA;;;;AAIA,QAAIqC,iBAAOY,WAAP,CAAmBnD,QAAnB,CAAJ,EAAkC;AAChC;;;;AAIAc,gCAAgBW,iBAAhB,CAAkCzB,QAAlC;;AAEA;;;AAGAc,gCAAgBY,kBAAhB,CACE1B,QADF,EAEE7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCyF,IAAjC,CAFF,EAGEA,IAHF;AAKD;;AAED;;;;;;AAMA,QAAIqC,iBAAOU,OAAP,CAAejD,QAAf,EAAyBE,IAAzB,CAAJ,EAAoC;AAClC;;;;AAIAY,gCAAgBY,kBAAhB,CACE1B,QADF,EAEE7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCyF,IAAjC,CAFF,EAGEA,IAHF;AAKD;;AAED;;;AAGApD,mBAAKC,IAAL;;AAEA;;;;;AAKAqC,gCAAkBC,IAAlB;AACD;;AAED;;;AAGA,SAAO;AACL2T,YAAQA;AADH,GAAP;AAGD,CAjQU,EAAX;;AAVA;;;;;;AAZA;;;;;;AAZA;;;;kBAqSe6B,I;;;;;;;;;;;;;;ACvSf;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;AAZA;;;;;;AAZA;;;;AAZA;;;;AAwCA,IAAII,YAAa,YAAW;AAC1B;;;;;;;;;;;;AAYA,WAASjC,MAAT,GAAkB;AAChB;;;;AAIA,QAAI,CAAC7V,iBAAOtB,eAAZ,EAA6B;AAC3B;;;;AAIA,UAAImE,WAAW,KAAKC,YAAL,CAAkB,yBAAlB,CAAf;AACA,UAAIC,OAAO,KAAKD,YAAL,CAAkB,2BAAlB,CAAX;;AAEA;;;AAGA,UAAID,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCiY;AACD;;AAED;;;AAGA,UAAInY,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCkY,gCAAwBpY,QAAxB;AACD;;AAED;;;AAGA,UAAIA,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCmY,4BAAoBnY,IAApB;AACD;;AAED;;;AAGA,UAAIF,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCoY,sCAA8BtY,QAA9B,EAAwCE,IAAxC;AACD;;AAEDwD,4BAAYE,cAAZ;AACD;AACF;;AAED;;;;AAIA,WAASuU,qBAAT,GAAiC;AAC/B;;;;AAIA,QAAIhb,iBAAOlD,KAAP,CAAawD,MAAjB,EAAyB;AACvBX,qBAAKC,IAAL;AACD,KAFD,MAEO;AACLD,qBAAKiB,KAAL;AACD;;AAED;;;;;AAKAqB,gCAAkBC,IAAlB;AACD;;AAED;;;;;AAKA,WAAS+Y,uBAAT,CAAiCpY,QAAjC,EAA2C;AACzC;;;;;AAKA,QAAIuC,iBAAOY,WAAP,CAAmBnD,QAAnB,CAAJ,EAAkC;AAChC;;;AAGAc,gCAAgBW,iBAAhB,CAAkCzB,QAAlC;;AAEA;;;;;;AAOA,UAAI7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BwB,OAA/B,EAAwC;AACtCV,kCAAgBY,kBAAhB,CACE1B,QADF,EAEE7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BhF,YAA3B,CAAwC,CAAxC,CAFF,EAGE,CAHF;AAKD,OAND,MAMO;AACL8F,kCAAgBY,kBAAhB,CACE1B,QADF,EAEE7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiC,CAAjC,CAFF,EAGE,CAHF;AAKD;AACF;;AAED;;;;AAIA,QAAI0C,iBAAOlD,KAAP,CAAawD,MAAjB,EAAyB;AACvBX,qBAAKC,IAAL;AACD,KAFD,MAEO;AACLD,qBAAKiB,KAAL;AACD;;AAED;;;;;AAKAqB,gCAAkBC,IAAlB;AACD;;AAED;;;;;AAKA,WAASgZ,mBAAT,CAA6BnY,IAA7B,EAAmC;AACjC;;;;;;AAMA;;;;AAIA,QAAIqC,iBAAOY,WAAP,CAAmB,IAAnB,CAAJ,EAA8B;AAC5B;;;;AAIArC,gCAAgBW,iBAAhB,CAAkC,IAAlC;;AAEA;;;AAGAX,gCAAgBQ,UAAhB,CAA2BnE,iBAAO1C,KAAP,CAAayF,IAAb,CAA3B,EAA+CA,IAA/C;AACD;;AAED;;;;;;AAMA,QAAIqC,iBAAOU,OAAP,CAAe,IAAf,EAAqB/C,IAArB,CAAJ,EAAgC;AAC9B;;;;AAIAY,gCAAgBQ,UAAhB,CAA2BnE,iBAAO1C,KAAP,CAAayF,IAAb,CAA3B,EAA+CA,IAA/C;AACD;;AAED;;;;AAIA,QAAI/C,iBAAOlD,KAAP,CAAawD,MAAjB,EAAyB;AACvBX,qBAAKC,IAAL;AACD,KAFD,MAEO;AACLD,qBAAKiB,KAAL;AACD;;AAED;;;;;AAKAqB,gCAAkBC,IAAlB;AACD;;AAED;;;;;;;AAOA,WAASiZ,6BAAT,CAAuCtY,QAAvC,EAAiDE,IAAjD,EAAuD;AACrD;;;;;;AAMA;;;;AAIA,QAAIqC,iBAAOY,WAAP,CAAmBnD,QAAnB,CAAJ,EAAkC;AAChC;;;;AAIAc,gCAAgBW,iBAAhB,CAAkCzB,QAAlC;;AAEA;;;AAGAc,gCAAgBY,kBAAhB,CACE1B,QADF,EAEE7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCyF,IAAjC,CAFF,EAGEA,IAHF;AAKD;;AAED;;;;;;AAMA,QAAIqC,iBAAOU,OAAP,CAAejD,QAAf,EAAyBE,IAAzB,CAAJ,EAAoC;AAClC;;;;AAIAY,gCAAgBY,kBAAhB,CACE1B,QADF,EAEE7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCyF,IAAjC,CAFF,EAGEA,IAHF;AAKD;;AAED;;;;AAIA,QAAI/C,iBAAOlD,KAAP,CAAawD,MAAjB,EAAyB;AACvBX,qBAAKC,IAAL;AACD,KAFD,MAEO;AACLD,qBAAKiB,KAAL;AACD;;AAED;;;;;AAKAqB,gCAAkBC,IAAlB;AACD;;AAED;;;AAGA,SAAO;AACL2T,YAAQA;AADH,GAAP;AAGD,CApRe,EAAhB;;AAVA;;;;;;AAZA;;;;;;AAZA;;;;kBAwTeiC,S;;;;;;;;;;;;;;AC1Tf;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;;AAZA;;;;AAiBA,IAAI2B,gBAAiB,YAAW;AAC9B;;;;;;;AAOA,WAAS5D,MAAT,GAAkB;AAChB,QAAI,CAAC7V,iBAAOtB,eAAZ,EAA6B;AAC3B;;;;;;AAMA,cAAQsB,iBAAO5C,cAAf;AACE,aAAK,CAAL;AACEuC,yBAAKoC,gBAAL,CAAsB,GAAtB;AACA;AACF,aAAK,GAAL;AACEpC,yBAAKoC,gBAAL,CAAsB,CAAtB;AACA;AACF,aAAK,CAAL;AACEpC,yBAAKoC,gBAAL,CAAsB,CAAtB;AACA;AATJ;;AAYA;;;AAGAsN,sCAAsBnN,IAAtB;AACD;AACF;;AAED;;;AAGA,SAAO;AACL2T,YAAQA;AADH,GAAP;AAGD,CAzCmB,EAApB;;AAXA;;;;AAZA;;;;kBAkEe4D,a;;;;;;;;;;;;;;AC9Df;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;;AAZA;;;;AAiBA,IAAIR,OAAQ,YAAW;AACrB;;;;;;;;;;AAUA,WAASpD,MAAT,GAAkB;AAChB;;;;AAIA,QAAI,CAAC7V,iBAAOtB,eAAZ,EAA6B;AAC3B;;;AAGA,UAAImE,WAAW,KAAKC,YAAL,CAAkB,yBAAlB,CAAf;;AAEA;;;AAGA,UAAID,YAAY,IAAhB,EAAsB;AACpBuY;AACD;;AAED;;;AAGA,UAAIvY,YAAY,IAAhB,EAAsB;AACpBwY,2BAAmBxY,QAAnB;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASuY,gBAAT,GAA4B;AAC1B;;;;;AAKA,QAAIpb,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA9D,EAAoE;AAClEwG,gCAAgBa,WAAhB;AACD,KAFD,MAEO;AACLb,gCAAgBgB,mBAAhB,CAAoC3E,iBAAO7C,eAA3C;AACD;AACF;;AAED;;;;;;AAMA,WAASke,kBAAT,CAA4BxY,QAA5B,EAAsC;AACpC;;;;AAIA,QAAIA,YAAY7C,iBAAO7C,eAAvB,EAAwC;AACtCwG,gCAAgBgB,mBAAhB,CAAoC3E,iBAAO7C,eAA3C;AACD,KAFD,MAEO;AACLsE,sBAAMC,YAAN,CACE,6EADF;AAGD;AACF;;AAED;;;AAGA,SAAO;AACLmU,YAAQA;AADH,GAAP;AAGD,CAlFU,EAAX;;AAXA;;;;AAZA;;;;kBA2GeoD,I;;;;;;;;;;;;;;ACvGf;;;;AAMA;;;;;;AAEA;;;;;AAZA;;;;AAiBA,IAAIzB,WAAY,YAAW;AACzB;;;;;;;;AAQA,WAAS3B,MAAT,GAAkB;AAChB;;;AAGA,QAAI7V,iBAAOlD,KAAP,CAAa6B,QAAb,CAAsBiE,MAAtB,GAA+B,CAA/B,IAAoC,CAAxC,EAA2C;AACzC,UAAI0Y,cAActb,iBAAOlD,KAAP,CAAa6B,QAAb,CAAsBiV,GAAtB,CAChB5T,iBAAOlD,KAAP,CAAa6B,QAAb,CAAsBiE,MAAtB,GAA+B,CADf,CAAlB;AAGA,UAAIvB,WAAWrB,iBAAOlD,KAAP,CAAauE,QAA5B;;AAEA;;;AAGArB,uBAAOrB,QAAP,GAAmB2c,cAAcja,QAAf,GAA2B,GAA7C;AACD;;AAED;;;AAGAgU,uCAAyBnT,IAAzB;AACD;;AAED;;;AAGA,SAAO;AACL2T,YAAQA;AADH,GAAP;AAGD,CArCc,EAAf;;AAXA;;;;kBAkDe2B,Q;;;;;;;;;;;;;;ACpDf;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;;AAZA;;;;AAiBA,IAAI6B,SAAU,YAAW;AACvB;;;;;;;;;;AAUA,WAASxD,MAAT,GAAkB;AAChB;;;;AAIA,QAAI,CAAC7V,iBAAOtB,eAAZ,EAA6B;AAC3B;;;AAGA,UAAImE,WAAW,KAAKC,YAAL,CAAkB,yBAAlB,CAAf;;AAEA;;;AAGA,UAAID,YAAY,IAAhB,EAAsB;AACpB0Y;AACD;;AAED;;;AAGA,UAAI1Y,YAAY,IAAhB,EAAsB;AACpB2Y,6BAAqB3Y,QAArB;AACD;AACF;AACF;;AAED;;;;;AAKA,WAAS0Y,kBAAT,GAA8B;AAC5B;;;AAGA/S,uBAASC,SAAT,CAAmB,CAACzI,iBAAOrC,MAA3B;;AAEA;;;AAGA+J,6BAAeC,UAAf;AACD;;AAED;;;;;;AAMA,WAAS6T,oBAAT,CAA8B3Y,QAA9B,EAAwC;AACtC;;;AAGA2F,uBAASE,iBAAT,CAA2B,CAAC1I,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BlF,MAAvD,EAA+DkF,QAA/D;;AAEA;;;AAGA6E,6BAAeI,kBAAf,CAAkCjF,QAAlC;AACD;;AAED;;;AAGA,SAAO;AACLgT,YAAQA;AADH,GAAP;AAGD,CA/EY,EAAb;;AAXA;;;;AAZA;;;;kBAwGewD,M;;;;;;;;;;;;;;ACpGf;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;;AAZA;;;;AAiBA,IAAIE,aAAc,YAAW;AAC3B;;;;;;;AAOA,WAAS1D,MAAT,GAAkB;AAChB;;;;AAIA,QAAI,CAAC7V,iBAAOtB,eAAZ,EAA6B;AAC3B;;;AAGA8J,yBAASG,aAAT,CAAuB,CAAC3I,iBAAOpC,WAA/B;;AAEA;;;AAGA8J,+BAAeM,cAAf;AACD;AACF;;AAED;;;AAGA,SAAO;AACL6N,YAAQA;AADH,GAAP;AAGD,CAhCgB,EAAjB;;AAXA;;;;AAZA;;;;kBAyDe0D,U;;;;;;;;;;;;;;ACrDf;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;AAZA;;;;AAgBA,IAAIJ,UAAW,YAAW;AACxB;;;;;;;;;;AAUA,WAAStD,MAAT,GAAkB;AAChB;;;;AAIA,QAAI,CAAC7V,iBAAOtB,eAAZ,EAA6B;AAC3B;;;AAGA,UAAImE,WAAW,KAAKC,YAAL,CAAkB,yBAAlB,CAAf;;AAEA;;;AAGA,UAAID,YAAY,IAAhB,EAAsB;AACpB4Y;AACD,OAFD,MAEO;AACLC,8BAAsB7Y,QAAtB;AACD;AACF;AACF;;AAED;;;AAGA,WAAS4Y,mBAAT,GAA+B;AAC7B;;;AAGA7S,uBAASG,aAAT;;AAEA;;;AAGAwG,8BAAgBzF,QAAhB,CAAyB9J,iBAAOlC,UAAhC;AACD;;AAED;;;;;AAKA,WAAS4d,qBAAT,CAA+B7Y,QAA/B,EAAyC;AACvC;;;AAGA+F,uBAASM,qBAAT,CAA+BrG,QAA/B;;AAEA;;;AAGA0M,8BAAgBnN,YAAhB,CAA6BS,QAA7B;AACD;;AAED;;;AAGA,SAAO;AACLgT,YAAQA;AADH,GAAP;AAGD,CAvEa,EAAd;;AAVA;;;;AAZA;;;;kBA+FesD,O;;;;;;;;;;;;;;AC3Ff;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;;AAZA;;;;;;AAZA;;;;AAZA;;;;AAyCA,IAAIQ,SAAU,YAAW;AACvB;;;;;;;;;;AAUA,WAAS9D,MAAT,GAAkB;AAChB;;;;AAIA,QAAI,CAAC7V,iBAAOtB,eAAZ,EAA6B;AAC3B;;;AAGA,UAAImE,WAAW,KAAKC,YAAL,CAAkB,yBAAlB,CAAf;AACA,UAAIiD,YAAY,KAAKjD,YAAL,CAAkB,2BAAlB,CAAhB;AACA,UAAI+G,WAAW,KAAK/G,YAAL,CAAkB,yBAAlB,CAAf;;AAEA;;;;AAIA,UAAI+G,YAAY,IAAhB,EAAsB;AACpBpI,wBAAMC,YAAN,CACE,qGADF;AAGD;;AAED;;;;AAIA,UAAIqE,aAAa,IAAjB,EAAuB;AACrBtE,wBAAMC,YAAN,CACE,4FADF;AAGD;;AAED;;;AAGA,UAAImI,YAAY,IAAZ,IAAoB9D,aAAa,IAArC,EAA2C;AACzC;;;AAGA,YAAIlD,YAAY,IAAhB,EAAsB;AACpB8Y,2BAAiBzX,SAAS6B,SAAT,CAAjB,EAAsC7B,SAAS2F,QAAT,CAAtC;AACD,SAFD,MAEO;AACL+R,+BACE/Y,QADF,EAEEqB,SAAS6B,SAAT,CAFF,EAGE7B,SAAS2F,QAAT,CAHF;AAKD;AACF;AACF;AACF;;AAED;;;;;;;AAOA,WAAS8R,gBAAT,CAA0B5V,SAA1B,EAAqC8D,QAArC,EAA+C;AAC7C;;;;AAIAlG,8BAAgBQ,UAAhB,CAA2BnE,iBAAO1C,KAAP,CAAayI,SAAb,CAA3B,EAAoDA,SAApD;AACApG,mBAAKC,IAAL;;AAEA;;;AAGAqC,gCAAkBE,UAAlB;AACAF,gCAAkBI,QAAlB;;AAEA;;;AAGA1C,mBAAK2B,cAAL,CAAoBuI,QAApB;AACD;;AAED;;;;;;;;AAQA,WAAS+R,oBAAT,CAA8B/Y,QAA9B,EAAwCkD,SAAxC,EAAmD8D,QAAnD,EAA6D;AAC3D;;;AAGA,QAAIzE,iBAAOY,WAAP,CAAmBnD,QAAnB,CAAJ,EAAkC;AAChCc,gCAAgBW,iBAAhB,CAAkCzB,QAAlC;AACD;;AAED;;;;AAIAc,8BAAgBY,kBAAhB,CACE1B,QADF,EAEE7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCyI,SAAjC,CAFF,EAGEA,SAHF;AAKApG,mBAAKC,IAAL;;AAEA;;;AAGAqC,gCAAkBE,UAAlB;AACAF,gCAAkBG,YAAlB;AACAH,gCAAkBI,QAAlB;;AAEA;;;AAGA1C,mBAAK2B,cAAL,CAAoBuI,QAApB;AACD;;AAED;;;AAGA,SAAO;AACLgM,YAAQA;AADH,GAAP;AAGD,CAzIY,EAAb;;AAXA;;;;;;AAZA;;;;;;AAZA;;;;kBA8Ke8D,M;;;;;;;;;;;;;;AChLf;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;;AAZA;;;;AAiBA,IAAIhB,aAAc,YAAW;AAC3B;;;;;;;;;;;;AAYA,WAAS9C,MAAT,GAAkB;AAChB;;;AAGA,QAAIgG,qBAAqB,KAAKtT,KAA9B;;AAEA;;;AAGA,QAAIuT,eAAe9b,iBAAOlD,KAAP,CAAauE,QAAb,IAAyBwa,qBAAqB,GAA9C,CAAnB;;AAEA;;;AAGA,QAAIhZ,WAAW,KAAKC,YAAL,CAAkB,yBAAlB,CAAf;AACA,QAAIC,OAAO,KAAKD,YAAL,CAAkB,2BAAlB,CAAX;;AAEA;;;AAGA,QAAID,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCgZ,6BAAuBD,YAAvB,EAAqCD,kBAArC;AACD;;AAED;;;AAGA,QAAIhZ,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCiZ,+BAAyBF,YAAzB,EAAuCD,kBAAvC,EAA2DhZ,QAA3D;AACD;;AAED;;;AAGA,QAAIA,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCkZ,2BAAqBH,YAArB,EAAmCD,kBAAnC,EAAuD9Y,IAAvD;AACD;;AAED;;;;AAIA,QAAIF,YAAY,IAAZ,IAAoBE,QAAQ,IAAhC,EAAsC;AACpCmZ,qCACEJ,YADF,EAEED,kBAFF,EAGEhZ,QAHF,EAIEE,IAJF;AAMD;AACF;;AAED;;;;;;;AAOA,WAASgZ,sBAAT,CAAgCD,YAAhC,EAA8CD,kBAA9C,EAAkE;AAChE;;;AAGA,QAAI,CAAC7b,iBAAOhD,eAAP,CAAuBiD,IAA5B,EAAkC;AAChCsU,qBAAKW,cAAL,CAAoB4G,YAApB;;AAEA;;;AAGA/W,mCAAmB7C,IAAnB,CACE2Z,kBADF,EAEE7b,iBAAO7C,eAFT,EAGE6C,iBAAO9C,YAHT;AAKD;AACF;;AAED;;;;;;;;AAQA,WAAS8e,wBAAT,CACEF,YADF,EAEED,kBAFF,EAGEhZ,QAHF,EAIE;AACA;;;;AAIA,QAAI7C,iBAAO7C,eAAP,IAA0B0F,QAA9B,EAAwC;AACtC;;;AAGA,UAAI,CAAC7C,iBAAOhD,eAAP,CAAuBiD,IAA5B,EAAkC;AAChCsU,uBAAKW,cAAL,CAAoB4G,YAApB;;AAEA;;;AAGA/W,qCAAmB7C,IAAnB,CACE2Z,kBADF,EAEEhZ,QAFF,EAGE7C,iBAAO9C,YAHT;AAKD;AACF;AACF;;AAED;;;;;;;;AAQA,WAAS+e,oBAAT,CAA8BH,YAA9B,EAA4CD,kBAA5C,EAAgE9V,SAAhE,EAA2E;AACzE;;;;AAIA,QAAI/F,iBAAO9C,YAAP,IAAuB6I,SAAvB,IAAoC/F,iBAAO7C,eAAP,IAA0B,IAAlE,EAAwE;AACtE;;;AAGA,UAAI,CAAC6C,iBAAOhD,eAAP,CAAuBiD,IAA5B,EAAkC;AAChCsU,uBAAKW,cAAL,CAAoB4G,YAApB;;AAEA;;;AAGA/W,qCAAmB7C,IAAnB,CACE2Z,kBADF,EAEE7b,iBAAO7C,eAFT,EAGE4I,SAHF;AAKD;AACF;AACF;;AAED;;;;;;;;;AASA,WAASmW,8BAAT,CACEJ,YADF,EAEED,kBAFF,EAGEhZ,QAHF,EAIEkD,SAJF,EAKE;AACA;;;;;AAKA,QACE/F,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2B3F,YAA3B,IAA2C6I,SAA3C,IACA/F,iBAAO7C,eAAP,IAA0B0F,QAF5B,EAGE;AACA;;;AAGA,UAAI,CAAC7C,iBAAOhD,eAAP,CAAuBiD,IAA5B,EAAkC;AAChCsU,uBAAKW,cAAL,CAAoB4G,YAApB;;AAEA;;;AAGA/W,qCAAmB7C,IAAnB,CAAwB2Z,kBAAxB,EAA4ChZ,QAA5C,EAAsDkD,SAAtD;AACD;AACF;AACF;;AAED;;;AAGA,SAAO;AACL8P,YAAQA;AADH,GAAP;AAGD,CA1MgB,EAAjB;;AAXA;;;;AAZA;;;;kBAmOe8C,U;;;;;;;;;;;;;;AC/Nf;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;AAZA;;;;AAZA;;;;AA4BA,IAAIX,OAAQ,YAAW;AACrB;;;;;;;AAOA,WAASnC,MAAT,GAAkB;AAChB;;;;AAIA,QAAI,CAAC7V,iBAAOtB,eAAZ,EAA6B;AAC3B;;;AAGAuD,kCAAkBqB,WAAlB;;AAEA;;;AAGA3D,qBAAKG,IAAL;;AAEA;;;AAGAyG,4BAAYE,cAAZ;AACD;AACF;;AAED;;;AAGA,SAAO;AACLoP,YAAQA;AADH,GAAP;AAGD,CArCU,EAAX;;AAVA;;;;;;AAZA;;;;kBA6DemC,I;;;;;;;;;;;;;;AC/Df;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;;AAZA;;;;;;AAZA;;;;;;AAZA;;;;AAyCA,IAAIV,aAAc,YAAW;AAC3B;;;;;;;AAOA,WAASzB,MAAT,GAAkB;AAChB;;;AAGAsG;;AAEA;;;AAGA9G,uCAAyBnT,IAAzB;;AAEA;;;AAGAka;;AAEA;;;AAGAC;AACD;;AAED;;;AAGA,WAASF,mBAAT,GAA+B;AAC7B;;;AAGA,QAAInc,iBAAOlD,KAAP,CAAa6B,QAAb,CAAsBiE,MAAtB,GAA+B,CAA/B,IAAoC,CAAxC,EAA2C;AACzC,UAAI0Y,cAActb,iBAAOlD,KAAP,CAAa6B,QAAb,CAAsBiV,GAAtB,CAChB5T,iBAAOlD,KAAP,CAAa6B,QAAb,CAAsBiE,MAAtB,GAA+B,CADf,CAAlB;AAGA,UAAIvB,WAAWrB,iBAAOlD,KAAP,CAAauE,QAA5B;;AAEArB,uBAAOrB,QAAP,GAAmB2c,cAAcja,QAAf,GAA2B,GAA7C;AACD;AACF;;AAED;;;;AAIA,WAAS+a,qBAAT,GAAiC;AAC/B;;;;;;AAMA,QAAI,CAACpc,iBAAOhD,eAAP,CAAuBiD,IAA5B,EAAkC;AAChC;;;AAGA,UAAIa,cAAcyT,eAAKC,mBAAL,EAAlB;;AAEA;;;AAGA,UAAI8H,2BAA2B/H,eAAKU,+BAAL,EAA/B;;AAEA;;;AAGA,UAAI9J,eAAeoJ,eAAKK,mBAAL,EAAnB;;AAEA;;;;;AAKA1P,6BAAawF,gBAAb,CAA8B5J,WAA9B;;AAEA;;;AAGAiE,mCAAmB7C,IAAnB,CACEoa,wBADF,EAEEtc,iBAAO7C,eAFT,EAGE6C,iBAAO9C,YAHT;;AAMA;;;AAGA+H,2CAA2B/C,IAA3B,CAAgCoa,wBAAhC;;AAEA;;;AAGApX,6BAAagG,iBAAb,CAA+BpK,WAA/B,EAA4CqK,YAA5C;AACD;AACF;;AAED;;;AAGA,WAASkR,gBAAT,GAA4B;AAC1B;;;AAGA,QAAI5H,iBAAiBnL,KAAKC,KAAL,CAAWvJ,iBAAOlD,KAAP,CAAagE,WAAxB,CAArB;;AAEA;;;AAGA,QACEd,iBAAOhD,eAAP,CAAuBuf,cAAvB,IAAyC/b,SAAzC,IACAR,iBAAOhD,eAAP,CAAuBuf,cAAvB,CAAsC9H,cAAtC,KAAyDjU,SAF3D,EAGE;AACA;;;;AAIA,UAAI,CAACR,iBAAOhD,eAAP,CAAuBuf,cAAvB,CAAsC9H,cAAtC,EAAsD1U,GAA3D,EAAgE;AAC9DC,yBAAOhD,eAAP,CAAuBuf,cAAvB,CAAsC9H,cAAtC,EAAsD1U,GAAtD,GAA4D,IAA5D;AACAC,yBAAOhD,eAAP,CAAuBuf,cAAvB,CAAsC9H,cAAtC;AACD;AACF,KAZD,MAYO;AACL;;;;;;AAMA,WAAK,IAAIlT,OAAT,IAAoBvB,iBAAOhD,eAAP,CAAuBuf,cAA3C,EAA2D;AACzD,YAAIvc,iBAAOhD,eAAP,CAAuBuf,cAAvB,CAAsCpL,cAAtC,CAAqD5P,OAArD,CAAJ,EAAmE;AACjEvB,2BAAOhD,eAAP,CAAuBuf,cAAvB,CAAsChb,OAAtC,EAA+CxB,GAA/C,GAAqD,KAArD;AACD;AACF;AACF;AACF;AACD;;;AAGA,SAAO;AACL8V,YAAQA;AADH,GAAP;AAGD,CAlJgB,EAAjB;;AAXA;;;;;;AAZA;;;;;;AAZA;;;;AAZA;;;;kBAmMeyB,U;;;;;;;;;;;;;;AChMf;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;AAZA;;;;AAXA;;;AA2BA,IAAIgB,aAAc,YAAW;AAC3B;;;;;;;AAOA,WAASzC,MAAT,GAAkB;AAChB;;;;AAIA,QAAI,CAAC7V,iBAAOtB,eAAZ,EAA6B;AAC3B;;;;;;;;AAUA,UAAIR,SAAS,IAAb;;AAEA,UAAI8B,iBAAO9B,MAAP,GAAgB8B,iBAAO5B,gBAAvB,GAA0C,CAA9C,EAAiD;AAC/CF,iBAAS8B,iBAAO9B,MAAP,GAAgB8B,iBAAO5B,gBAAhC;AACD,OAFD,MAEO;AACLF,iBAAS,CAAT;AACD;;AAED;;;;AAIAyB,qBAAKqB,SAAL,CAAe9C,MAAf;;AAEA;;;AAGAgK,6BAAaC,QAAb,CAAsBnI,iBAAO9B,MAAP,IAAiB,CAAjB,GAAqB,IAArB,GAA4B,KAAlD;;AAEA;;;AAGAmK,qCAAqBnG,IAArB;AACD;AACF;;AAED;;;AAGA,SAAO;AACL2T,YAAQA;AADH,GAAP;AAGD,CAxDgB,EAAjB;;AAVA;;;;;;AAZA;;;;kBAgFeyC,U;;;;;;;;;;;;;;AClFf;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;AAZA;;;;AAXA;;;AA2BA,IAAIO,eAAgB,YAAW;AAC7B;;;;;;;AAOA,WAAShD,MAAT,GAAkB;AAChB;;;;AAIAlW,mBAAKqB,SAAL,CAAe,KAAKuH,KAApB;;AAEA;;;AAGAL,2BAAaC,QAAb,CAAsBnI,iBAAO9B,MAAP,IAAiB,CAAjB,GAAqB,IAArB,GAA4B,KAAlD;;AAEA;;;AAGAmK,mCAAqBnG,IAArB;AACD;;AAED;;;AAGA,SAAO;AACL2T,YAAQA;AADH,GAAP;AAGD,CAhCkB,EAAnB;;AAVA;;;;;;AAZA;;;;kBAwDegD,Y;;;;;;;;;;;;;;AC1Df;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;AAZA;;;;AAXA;;;AA2BA,IAAIT,WAAY,YAAW;AACzB;;;;;;;AAOA,WAASvC,MAAT,GAAkB;AAChB;;;;AAIA,QAAI,CAAC7V,iBAAOtB,eAAZ,EAA6B;AAC3B;;;;;;;;AAUA,UAAIR,SAAS,IAAb;;AAEA,UAAI8B,iBAAO9B,MAAP,GAAgB8B,iBAAO5B,gBAAvB,IAA2C,GAA/C,EAAoD;AAClDF,iBAAS8B,iBAAO9B,MAAP,GAAgB8B,iBAAO5B,gBAAhC;AACD,OAFD,MAEO;AACLF,iBAAS,GAAT;AACD;;AAED;;;;AAIAyB,qBAAKqB,SAAL,CAAe9C,MAAf;;AAEA;;;AAGAgK,6BAAaC,QAAb,CAAsBnI,iBAAO9B,MAAP,IAAiB,CAAjB,GAAqB,IAArB,GAA4B,KAAlD;;AAEA;;;AAGAmK,qCAAqBnG,IAArB;AACD;AACF;;AAED;;;AAGA,SAAO;AACL2T,YAAQA;AADH,GAAP;AAGD,CAxDc,EAAf;;AAVA;;;;;;AAZA;;;;kBAgFeuC,Q;;;;;;;;;;;;;;ACjFf;;;;;;AAEA;;;;;;;AAOA,IAAInI,KAAM,YAAW;AACnB;;;AAGA,WAASG,oBAAT,GAAgC;AAC9B;;;;AAIA,QAAIoM,iBACFhE,OAAOiE,YAAP,IACAjE,OAAOkE,kBADP,IAEAlE,OAAOmE,eAFP,IAGAnE,OAAOoE,aAHP,IAIApE,OAAOqE,cALT;;AAOA;;;;AAIA,QAAIL,cAAJ,EAAoB;AAClB;;;AAGAxc,uBAAOf,OAAP,GAAiB,IAAIud,cAAJ,EAAjB;;AAEA;;;AAGAxc,uBAAOb,QAAP,GAAkBa,iBAAOf,OAAP,CAAe6d,cAAf,EAAlB;;AAEA;;;;AAIA9c,uBAAOlD,KAAP,CAAaigB,WAAb,GAA2B,WAA3B;;AAEA;;;AAGA/c,uBAAOd,MAAP,GAAgBc,iBAAOf,OAAP,CAAe+d,wBAAf,CAAwChd,iBAAOlD,KAA/C,CAAhB;;AAEA;;;AAGAkD,uBAAOd,MAAP,CAAc+d,OAAd,CAAsBjd,iBAAOb,QAA7B;;AAEA;;;AAGAa,uBAAOb,QAAP,CAAgB8d,OAAhB,CAAwBjd,iBAAOf,OAAP,CAAeie,WAAvC;AACD,KA/BD,MA+BO;AACLpO,uBAAiBC,iBAAjB,CACE,oGADF;AAGD;AACF;;AAED;;;AAGA,WAASmB,oBAAT,GAAgC;AAC9B;;;;AAIA,QAAIsM,iBACFhE,OAAOiE,YAAP,IACAjE,OAAOkE,kBADP,IAEAlE,OAAOmE,eAFP,IAGAnE,OAAOoE,aAHP,IAIApE,OAAOqE,cALT;AAMA7c,qBAAOhB,uBAAP,GAAiC,KAAjC;;AAEA;;;AAGA,QAAIwd,cAAJ,EAAoB;AAClB;;;AAGAxc,uBAAOhB,uBAAP,GAAiC,IAAjC;AACA,aAAO,IAAP;AACD,KAND,MAMO;AACL;;;AAGAgB,uBAAOhB,uBAAP,GAAiC,KAAjC;AACA,aAAO,KAAP;AACD;AACF;;AAED;;;AAGA,WAASmR,mBAAT,GAA8B;AAC5B,QAAI3Q,YAAYiD,SAASC,gBAAT,CAA0B,sBAA1B,CAAhB;AACA,QAAI0I,wBAAwB3I,SAASC,gBAAT,CAC1B,0BAD0B,CAA5B;;AAIA,QAAIlD,UAAUoD,MAAV,GAAmB,CAAnB,IAAwBwI,sBAAsBxI,MAAtB,GAA+B,CAA3D,EAA8D;AAC5D,aAAO,IAAP;AACD,KAFD,MAEK;AACH,aAAO,KAAP;AACD;AACF;;AAED;;;AAGA,SAAO;AACLwN,0BAAsBA,oBADjB;AAELF,0BAAsBA,oBAFjB;AAGLC,yBAAqBA;AAHhB,GAAP;AAKD,CApHQ,EAAT,C,CAbA;;;;kBAmIeF,E;;;;;;;;;;;;;;AC1Hf;;;;AASA;;;;AAUA;;;;AASA;;;;AAMA;;;;AAMA;;;;AAOA;;;;AAMA;;;;AASA;;;;AASA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAEA;;;;AAEA;;;;;;AAEA;;;;;;;;AAhBA;;;;;;AAZA;;;;;;AAZA;;;;;;AAfA;;;AAGA;;;;;;AAlBA;;;;;;AAbA;;;;;;AAfA;;;AAGA;;;;;;AAtBA;;;AAGA;;;;AAwHA,IAAIkN,YAAa,YAAW;AAC1B;;;;;;;;;AASA,WAAS3M,IAAT,CAAclD,UAAd,EAA0B;AACxBuC,mBAAYnJ,UAAZ,CAAuB4G,UAAvB;AACD;;AAED;;;AAGA,WAAS8P,SAAT,GAAqB;AACnB,WAAOpd,gBAAP;AACD;;AAED;;;;;;;AAOA,WAASqd,eAAT,GAA2B;AACzBxN,mBAAYgB,aAAZ;AACD;;AAED;;;;;;;AAOA,WAASyM,iBAAT,GAA6B;AAC3B,WAAOtd,iBAAO7C,eAAd;AACD;;AAED;;;;;;;AAOA,WAASogB,gBAAT,GAA4B;AAC1B,WAAOvd,iBAAO5C,cAAd;AACD;;AAED;;;;;;;AAOA,WAAS2E,gBAAT,CAA2Byb,KAA3B,EAAmC;AACjC;;;;;;;AAQA7d,mBAAKoC,gBAAL,CAAuByb,KAAvB;;AAEA;;;AAGAnO,oCAAsBnN,IAAtB;AACD;;AAED;;;;;;;AAOA,WAASub,SAAT,GAAqB;AACnB,WAAOzd,iBAAOrC,MAAd;AACD;;AAED;;;;;;;AAOA,WAAS+f,iBAAT,CAA2BC,WAA3B,EAAwC;AACtC,WAAO3d,iBAAOzC,SAAP,CAAiBogB,WAAjB,EAA8BhgB,MAArC;AACD;;AAED;;;;;;;AAOA,WAASigB,UAAT,GAAsB;AACpB,WAAO5d,iBAAOlC,UAAd;AACD;;AAED;;;;;;;;AAQA,WAAS+f,kBAAT,CAA4Bhb,QAA5B,EAAsC;AACpC,WAAO7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BwB,OAAlC;AACD;;AAED;;;;;;;;;AASA,WAASwE,UAAT,CAAoBxE,OAApB,EAA6B;AAC3BuE,uBAASC,UAAT,CAAoBxE,OAApB;;AAEAkL,8BAAgBzF,QAAhB;AACD;;AAED;;;;;;;;;AASA,WAASd,kBAAT,CAA4BnG,QAA5B,EAAsCwB,OAAtC,EAA+C;AAC7CuE,uBAASI,kBAAT,CAA4BnG,QAA5B,EAAsCwB,OAAtC;;AAEAkL,8BAAgBzF,QAAhB;AACAyF,8BAAgBnN,YAAhB,CAA6BS,QAA7B;AACD;;AAED;;;;;;;;AAQA,WAAS4F,SAAT,CAAmBqV,WAAnB,EAAgC;AAC9BtV,uBAASC,SAAT,CAAmBqV,WAAnB;AACApW,6BAAeC,UAAf;AACD;;AAED;;;;;;;;;AASA,WAASe,iBAAT,CAA2B7F,QAA3B,EAAqCib,WAArC,EAAkD;AAChDtV,uBAASE,iBAAT,CAA2BoV,WAA3B,EAAwCjb,QAAxC;AACA6E,6BAAeI,kBAAf,CAAkCjF,QAAlC;AACD;;AAED;;;;;;;;AAQA,WAAS8F,aAAT,CAAuBmV,WAAvB,EAAoC;AAClC,QAAI,CAAC9d,iBAAOtB,eAAZ,EAA6B;AAC3B;;;AAGA8J,yBAASG,aAAT,CAAuB,CAAC3I,iBAAOpC,WAA/B;;AAEA;;;AAGA8J,+BAAeM,cAAf;AACD;AACF;;AAED;;;;;;;AAOA,WAAS+V,kBAAT,GAA8B;AAC5B,WAAO/d,iBAAOjC,iBAAd;AACD;;AAED;;;;;;;AAOA,WAASigB,qBAAT,GAAgC;AAC9B,WAAOhe,iBAAOhC,oBAAd;AACD;;AAED;;;;;;;;AAQA,WAASigB,kBAAT,CAA4Bnc,GAA5B,EAAiC;AAC/B9B,qBAAOjC,iBAAP,GAA2B+D,GAA3B;AACD;;AAED;;;;;;;;AAQC,WAASoc,qBAAT,CAA+Bpc,GAA/B,EAAmC;AACjC9B,qBAAOme,mBAAP,GAA6Brc,GAA7B;AACD;;AAEF;;;;;;;AAOA,WAASsc,uBAAT,GAAmC;AACjC;;;AAGA,WAAQpe,iBAAOlD,KAAP,CAAagE,WAAb,GAA2Bd,iBAAOlD,KAAP,CAAauE,QAAzC,GAAqD,GAA5D;AACD;;AAED;;;;;;;AAOA,WAASgd,oBAAT,GAAgC;AAC9B;;;AAGA,WAAOre,iBAAOlD,KAAP,CAAagE,WAApB;AACD;;AAED;;;;;;;AAOA,WAASwd,eAAT,GAA2B;AACzB;;;AAGA,WAAOte,iBAAOlD,KAAP,CAAauE,QAApB;AACD;;AAED;;;;;;;;;AASA,WAASkd,uBAAT,CAAiC7O,UAAjC,EAA6C;AAC3C;;;AAGA,QAAI,OAAOA,UAAP,IAAqB,QAArB,IAAkCA,aAAa,CAAb,IAAkBA,aAAa,GAArE,EAA2E;AACzE;;;AAGA1P,uBAAOlD,KAAP,CAAagE,WAAb,GAA2Bd,iBAAOlD,KAAP,CAAauE,QAAb,IAAyBqO,aAAa,GAAtC,CAA3B;AACD;AACF;;AAED;;;;;;;;AAQA,WAAS8O,QAAT,CAAkBjc,KAAlB,EAAyB;AACvB;;;AAGAvC,qBAAO/B,KAAP,GAAesE,KAAf;AACD;;AAED;;;;;;;;;AASA,WAASkc,qBAAT,GAAiC;AAC/B,WAAOze,iBAAOhD,eAAd;AACD;;AAED;;;;;;;;AAQA,WAAS0hB,yBAAT,GAAqC;AACnC,WAAO1e,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,CAAP;AACD;;AAED;;;;;;;;;AASA,WAASwhB,cAAT,CAAwB1a,KAAxB,EAA+B;AAC7B,WAAOjE,iBAAO1C,KAAP,CAAa2G,KAAb,CAAP;AACD;;AAED;;;;;;;;;;AAUA,WAAS2a,sBAAT,CAAgC/b,QAAhC,EAA0CoB,KAA1C,EAAiD;AAC/C,QAAIlB,OAAO/C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiC2G,KAAjC,CAAX;;AAEA,WAAOlB,IAAP;AACD;;AAED;;;;;;;;;;AAUA,WAAS8b,OAAT,CAAiB9b,IAAjB,EAAuB;AACrB;;;AAGA,QAAI/C,iBAAO1C,KAAP,IAAgBkD,SAApB,EAA+B;AAC7BR,uBAAO1C,KAAP,GAAe,EAAf;AACD;;AAED0C,qBAAO1C,KAAP,CAAa+O,IAAb,CAAkBtJ,IAAlB;;AAEA,QAAI/C,iBAAOlC,UAAX,EAAuB;AACrBkC,uBAAOnC,YAAP,CAAoBwO,IAApB,CAAyBtJ,IAAzB;AACD;;AAED,QAAIoK,qBAAWiC,eAAX,CAA2BrM,KAAKjB,GAAhC,CAAJ,EAA0C;AACxCqL,2BAAWmB,8BAAX,CACEvL,KAAKjB,GADP,EAEE,IAFF,EAGE9B,iBAAO1C,KAAP,CAAasF,MAAb,GAAsB,CAHxB,EAIE5C,iBAAOlC,UAJT;AAMD;;AAED,WAAOkC,iBAAO1C,KAAP,CAAasF,MAAb,GAAsB,CAA7B;AACD;;AAED;;;;;;;;;;;AAWA,WAASkc,iBAAT,CAA2B/b,IAA3B,EAAiCF,QAAjC,EAA2C;AACzC,QAAI7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,KAA8BrC,SAAlC,EAA6C;AAC3CR,uBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiC+O,IAAjC,CAAsCtJ,IAAtC;;AAEA,UAAI/C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BwB,OAA/B,EAAwC;AACtCrE,yBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BhF,YAA3B,CAAwCwO,IAAxC,CAA6CtJ,IAA7C;AACD;;AAED,UAAIoK,qBAAWiC,eAAX,CAA2BrM,KAAKjB,GAAhC,CAAJ,EAA0C;AACxCqL,6BAAWmB,8BAAX,CACEvL,KAAKjB,GADP,EAEEe,QAFF,EAGE7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCsF,MAAjC,GAA0C,CAH5C,EAIE5C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BwB,OAJ7B;AAMD;;AAED,aAAOrE,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCsF,MAAjC,GAA0C,CAAjD;AACD,KAjBD,MAiBO;AACLnB,sBAAMC,YAAN,CAAmB,yBAAnB;AACA,aAAO,IAAP;AACD;AACF;;AAED;;;;;;;AAOA,WAASqd,WAAT,CAAqB7S,GAArB,EAA0B8S,IAA1B,EAAgC1hB,KAAhC,EAAuC;AACrC;;;AAGA,QAAI0C,iBAAOzC,SAAP,CAAiB2O,GAAjB,KAAyB1L,SAA7B,EAAwC;AACtC;;;AAGAR,uBAAOzC,SAAP,CAAiB2O,GAAjB,IAAwB,EAAxB;;AAEA;;;AAGA,UAAI+S,cAAc,CAAC,QAAD,EAAW,SAAX,EAAsB,cAAtB,EAAsC,OAAtC,EAA+C,KAA/C,CAAlB;;AAEA;;;;AAIA,WAAK,IAAIC,OAAT,IAAoBF,IAApB,EAA0B;AACxB,YAAIC,YAAY/X,OAAZ,CAAoBgY,OAApB,IAA+B,CAAnC,EAAsC;AACpClf,2BAAOzC,SAAP,CAAiB2O,GAAjB,EAAsBgT,OAAtB,IAAiCF,KAAKE,OAAL,CAAjC;AACD;AACF;;AAED;;;AAGAlf,uBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAAtB,GAA8BA,KAA9B;AACA0C,uBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsBhP,YAAtB,GAAqC,IAArC;AACA8C,uBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsBvO,MAAtB,GAA+B,KAA/B;AACAqC,uBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB7H,OAAtB,GAAgC,KAAhC;AACArE,uBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsBrO,YAAtB,GAAqC,EAArC;;AAEA,aAAOmC,iBAAOzC,SAAP,CAAiB2O,GAAjB,CAAP;AACD,KA/BD,MA+BO;AACLzK,sBAAMC,YAAN,CAAmB,0CAAnB;AACA,aAAO,IAAP;AACD;AACF;;AAED;;;;;;;;;AASA,WAASyd,UAAT,CAAoBlb,KAApB,EAA2B;AACzBjE,qBAAO1C,KAAP,CAAa8hB,MAAb,CAAoBnb,KAApB,EAA2B,CAA3B;AACD;;AAED;;;;;;;;;;AAUA,WAASob,sBAAT,CAAgCpb,KAAhC,EAAuCpB,QAAvC,EAAiD;AAC/C,QAAI7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,KAA8BrC,SAAlC,EAA6C;AAC3CR,uBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiC8hB,MAAjC,CAAwCnb,KAAxC,EAA+C,CAA/C;AACD;AACF;;AAED;;;;;;;;;;AAUA,WAASqb,OAAT,CAAiBvc,IAAjB,EAAuB;AACrB;;;;AAIA,QAAIA,KAAKjB,GAAT,EAAc;AACZ9B,uBAAOlD,KAAP,CAAa8E,GAAb,GAAmBmB,KAAKjB,GAAxB;AACA9B,uBAAOhD,eAAP,GAAyB+F,IAAzB;AACA/C,uBAAO/C,YAAP,GAAsB8F,KAAK8B,KAA3B;AACD,KAJD,MAIO;AACL;;;;AAIApD,sBAAMC,YAAN,CAAmB,+BAAnB;AACD;;AAED;;;AAGA/B,mBAAKC,IAAL;;AAEA;;;AAGAqC,gCAAkBC,IAAlB;;AAEA;;;AAGAoD,+BAAiBC,eAAjB;;AAEA;;;;AAIAR,iCAAmBC,aAAnB;;AAEA;;;AAGAC,yCAA2BD,aAA3B;;AAEA;;;AAGAE,2BAAaC,iBAAb;;AAEA;;;AAGAD,2BAAaQ,kBAAb;;AAEA;;;AAGAa,0BAAYE,cAAZ;AACD;;AAED;;;;;;;;AAQA,WAAS8Y,eAAT,CAAyBtb,KAAzB,EAAgC;AAC9B;;;AAGAtE,mBAAKG,IAAL;;AAEA;;;AAGA,QAAIsF,iBAAOY,WAAP,CAAmB,IAAnB,CAAJ,EAA8B;AAC5BrC,gCAAgBW,iBAAhB,CAAkC,IAAlC;;AAEAX,gCAAgBQ,UAAhB,CAA2BnE,iBAAO1C,KAAP,CAAa2G,KAAb,CAA3B,EAAgDA,KAAhD;AACD;;AAED;;;AAGA,QAAImB,iBAAOU,OAAP,CAAe,IAAf,EAAqB7B,KAArB,CAAJ,EAAiC;AAC/BN,gCAAgBQ,UAAhB,CAA2BnE,iBAAO1C,KAAP,CAAa2G,KAAb,CAA3B,EAAgDA,KAAhD;AACD;;AAED;;;AAGAtE,mBAAKC,IAAL;;AAEA;;;AAGA2G,0BAAYE,cAAZ;;AAEA;;;AAGAxE,gCAAkBC,IAAlB;AACD;;AAED;;;;;;;;;AASA,WAASsd,uBAAT,CAAiCvb,KAAjC,EAAwCpB,QAAxC,EAAkD;AAChDlD,mBAAKG,IAAL;;AAEA;;;AAGA,QAAIsF,iBAAOY,WAAP,CAAmBnD,QAAnB,CAAJ,EAAkC;AAChCc,gCAAgBW,iBAAhB,CAAkCzB,QAAlC;;AAEAc,gCAAgBY,kBAAhB,CACE1B,QADF,EAEE7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiC2G,KAAjC,CAFF,EAGEA,KAHF;AAKD;;AAED;;;AAGA,QAAImB,iBAAOU,OAAP,CAAejD,QAAf,EAAyBoB,KAAzB,CAAJ,EAAqC;AACnCN,gCAAgBY,kBAAhB,CACE1B,QADF,EAEE7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiC2G,KAAjC,CAFF,EAGEA,KAHF;AAKD;;AAED;;;AAGAhC,gCAAkBC,IAAlB;;AAEA;;;AAGAvC,mBAAKC,IAAL;;AAEA;;;AAGA2G,0BAAYE,cAAZ;AACD;;AAED;;;;;;;;;AASA,WAAS7G,IAAT,GAAgB;AACdD,mBAAKC,IAAL;;AAEA2G,0BAAYE,cAAZ;AACD;;AAED;;;;;;;;;AASA,WAAS7F,KAAT,GAAiB;AACfjB,mBAAKiB,KAAL;;AAEA2F,0BAAYE,cAAZ;AACD;;AAED;;;;;;;AAOA,WAASgZ,QAAT,GAAoB;AAClB,WAAOzf,iBAAOlD,KAAd;AACD;;AAED;;;;;;;AAOA,WAAS4iB,WAAT,GAAuB;AACrB,WAAO1f,iBAAOb,QAAd;AACD;;AAED;;;;;;;;AAQA,WAASwgB,IAAT,GAA+B;AAAA,QAAjB9c,QAAiB,uEAAN,IAAM;;AAC7B,QAAI+c,WAAW,EAAf;AACA;;;;AAIA,QAAI/c,YAAY,EAAZ,IAAkBA,YAAY,IAAlC,EAAwC;AACtC;;;;AAIA,UAAI7C,iBAAO7C,eAAP,IAA0B,IAA1B,IAAkC6C,iBAAO7C,eAAP,IAA0B,EAAhE,EAAoE;AAClEwG,kCAAgBC,OAAhB;AACD,OAFD,MAEO;AACLD,kCAAgBS,eAAhB,CAAgCpE,iBAAO7C,eAAvC;AACD;AACF,KAVD,MAUO;AACLwG,gCAAgBS,eAAhB,CAAgCvB,QAAhC;AACD;AACF;;AAED;;;;;;;;AAQA,WAASgd,IAAT,GAA+B;AAAA,QAAjBhd,QAAiB,uEAAN,IAAM;;AAC7B,QAAIid,WAAW,EAAf;;AAEA;;;;AAIA,QAAIjd,YAAY,EAAZ,IAAkBA,YAAY,IAAlC,EAAwC;AACtC;;;;AAIA,UAAI7C,iBAAO7C,eAAP,IAA0B,IAA1B,IAAkC6C,iBAAO7C,eAAP,IAA0B,EAAhE,EAAoE;AAClEwG,kCAAgBa,WAAhB;AACD,OAFD,MAEO;AACLb,kCAAgBgB,mBAAhB,CAAoC3E,iBAAO7C,eAA3C;AACD;AACF,KAVD,MAUO;AACLwG,gCAAgBgB,mBAAhB,CAAoC9B,QAApC;AACD;AACF;;AAED;;;;;;;AAOA,WAASkd,QAAT,GAAoB;AAClB,WAAO/f,iBAAO1C,KAAd;AACD;;AAED;;;;;;;;AAQA,WAAS0iB,kBAAT,CAA4Bnd,QAA5B,EAAsC;AACpC,WAAO7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAAlC;AACD;;AAED;;;;;;;;AAQA,WAAS2iB,aAAT,GAAyB;AACvB,QAAIjgB,iBAAOlC,UAAX,EAAuB;AACrB,aAAOkC,iBAAOnC,YAAd;AACD,KAFD,MAEO;AACL,aAAOmC,iBAAO1C,KAAd;AACD;AACF;;AAED;;;;;;;;;AASA,WAAS4iB,qBAAT,CAA+Brd,QAA/B,EAAyC;AACvC,QAAI7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BwB,OAA/B,EAAwC;AACtC,aAAOrE,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BhF,YAAlC;AACD,KAFD,MAEO;AACL,aAAOmC,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAAlC;AACD;AACF;;AAED;;;;;;;AAOA,WAAS6iB,cAAT,GAA0B;AACxB,WAAOjc,SAASlE,iBAAO9C,YAAhB,CAAP;AACD;;AAED;;;;;;;AAOA,WAASkjB,UAAT,GAAsB;AACpB,WAAOpgB,iBAAOnD,OAAd;AACD;;AAED;;;;;;;AAOA,WAASwjB,WAAT,GAAuB;AACrB,WAAOrgB,iBAAOrB,QAAd;AACD;;AAED;;;;;;;;;;AAUA,WAAS2hB,MAAT,CAAgB/e,OAAhB,EAAyBwE,SAAzB,EAAqD;AAAA,QAAjBlD,QAAiB,uEAAN,IAAM;;AACnDtB,cAAU2C,SAAS3C,OAAT,CAAV;;AAEA,QAAIsB,YAAY,IAAhB,EAAsB;AACpB;;;AAGA,UAAIuC,iBAAOY,WAAP,CAAmBnD,QAAnB,CAAJ,EAAkC;AAChCc,kCAAgBW,iBAAhB,CAAkCzB,QAAlC;AACD;;AAED;;;;AAIAc,gCAAgBY,kBAAhB,CACE1B,QADF,EAEE7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCyI,SAAjC,CAFF,EAGEA,SAHF;AAKApG,qBAAKC,IAAL;;AAEA;;;AAGAqC,kCAAkBE,UAAlB;AACAF,kCAAkBG,YAAlB;AACAH,kCAAkBI,QAAlB;;AAEA;;;AAGA1C,qBAAK2B,cAAL,CAAoBC,OAApB;AACD,KA9BD,MA8BO;AACL;;;;AAIAoC,gCAAgBQ,UAAhB,CAA2BnE,iBAAO1C,KAAP,CAAayI,SAAb,CAA3B,EAAoDA,SAApD;AACApG,qBAAKC,IAAL;;AAEA;;;AAGAqC,kCAAkBE,UAAlB;AACAF,kCAAkBI,QAAlB;;AAEA;;;AAGA1C,qBAAK2B,cAAL,CAAoBC,OAApB;AACD;AACF;;AAED;;;;;;;;;;;;AAYA,WAASgf,eAAT,CAAyBtc,KAAzB,EAAgCuc,QAAhC,EAA2D;AAAA,QAAjB3d,QAAiB,uEAAN,IAAM;;AACzD;;;AAGA,QACEA,YAAY,EAAZ,IACAA,YAAY,IADZ,IAEA7C,iBAAOzC,SAAP,CAAiBsF,QAAjB,KAA8BrC,SAHhC,EAIE;AACA;;;AAGA,WAAK,IAAI0L,GAAT,IAAgBsU,QAAhB,EAA0B;AACxB,YAAIA,SAASrP,cAAT,CAAwBjF,GAAxB,CAAJ,EAAkC;AAChC,cAAIA,OAAO,KAAP,IAAgBA,OAAO,KAAvB,IAAgCA,OAAO,MAAvC,IAAiDA,OAAO,MAA5D,EAAoE;AAClElM,6BAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiC2G,KAAjC,EAAwCiI,GAAxC,IAA+CsU,SAAStU,GAAT,CAA/C;AACD;AACF;AACF;AACF,KAfD,MAeO;AACL;;;AAGA,WAAK,IAAIA,GAAT,IAAgBsU,QAAhB,EAA0B;AACxB,YAAIA,SAASrP,cAAT,CAAwBjF,GAAxB,CAAJ,EAAkC;AAChC,cAAIA,OAAO,KAAP,IAAgBA,OAAO,KAAvB,IAAgCA,OAAO,MAAvC,IAAiDA,OAAO,MAA5D,EAAoE;AAClElM,6BAAO1C,KAAP,CAAa2G,KAAb,EAAoBiI,GAApB,IAA2BsU,SAAStU,GAAT,CAA3B;AACD;AACF;AACF;AACF;;AAED;;;AAGA5G,+BAAiBC,eAAjB;AACAD,+BAAiBmC,YAAjB;AACD;;AAED,WAASgZ,mBAAT,CAA6B5d,QAA7B,EAAuC2d,QAAvC,EAAiD;AAC/C,QAAIxgB,iBAAOzC,SAAP,CAAiBsF,QAAjB,KAA8BrC,SAAlC,EAA6C;AAC3C;;;;AAIA,UAAIye,cAAc,CAAC,QAAD,EAAW,SAAX,EAAsB,cAAtB,EAAsC,OAAtC,EAA+C,KAA/C,CAAlB;;AAEA,WAAK,IAAI/S,GAAT,IAAgBsU,QAAhB,EAA0B;AACxB,YAAIA,SAASrP,cAAT,CAAwBjF,GAAxB,CAAJ,EAAkC;AAChC,cAAI+S,YAAY/X,OAAZ,CAAoBgF,GAApB,IAA2B,CAA/B,EAAkC;AAChClM,6BAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BqJ,GAA3B,IAAkCsU,SAAStU,GAAT,CAAlC;AACD;AACF;AACF;;AAED5G,iCAAiB+B,uBAAjB;AACD,KAhBD,MAgBO;AACL5F,sBAAMC,YAAN,CAAmB,wCAAnB;AACD;AACF;;AAED;;;;;;;;AAQA,WAASgf,QAAT,CAAkBvL,IAAlB,EAAwB;AACtBnV,qBAAOlB,KAAP,GAAeqW,IAAf;AACD;;AAED;;;;;;;AAOA,WAASwL,QAAT,GAAoB;AAClB,WAAO3gB,iBAAOlB,KAAd;AACD;;AAED;;;;;AAKA,WAAS8hB,cAAT,GAA0B;AACxB,WAAO5gB,iBAAOjB,YAAd;AACD;;AAED;;;;;;;;;;;;AAYA,WAAS8hB,qBAAT,CAA+BhV,aAA/B,EAA8CW,WAA9C,EAA2D;AACzD3M,6BAAe0M,QAAf,CAAwBV,aAAxB,EAAuCW,WAAvC;AACD;;AAED;;;;;;AAMA,WAASsU,wBAAT,CAAkCje,QAAlC,EAA4Cke,gBAA5C,EAA8D;AAC5D,QAAI/gB,iBAAOzC,SAAP,CAAiBsF,QAAjB,KAA8BrC,SAAlC,EAA6C;AAC3C,UAAIR,iBAAOZ,cAAP,CAAsBC,SAAtB,CAAgC0hB,gBAAhC,KAAqDvgB,SAAzD,EAAoE;AAClER,yBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BgJ,aAA3B,GAA2CkV,gBAA3C;AACD,OAFD,MAEO;AACLtf,wBAAMC,YAAN,CACE,sDADF;AAGD;AACF,KARD,MAQO;AACLD,sBAAMC,YAAN,CAAmB,kDAAnB;AACD;AACF;;AAED;;;;;;AAMA,WAASsf,oBAAT,CAA8Bjb,SAA9B,EAAyCgb,gBAAzC,EAA2D;AACzD,QAAI/gB,iBAAO1C,KAAP,CAAayI,SAAb,CAAJ,EAA6B;AAC3B,UAAI/F,iBAAOZ,cAAP,CAAsBC,SAAtB,CAAgC0hB,gBAAhC,KAAqDvgB,SAAzD,EAAoE;AAClER,yBAAO1C,KAAP,CAAayI,SAAb,EAAwB8F,aAAxB,GAAwCkV,gBAAxC;AACD,OAFD,MAEO;AACLtf,wBAAMC,YAAN,CACE,sDADF;AAGD;AACF,KARD,MAQO;AACLD,sBAAMC,YAAN,CAAmB,mCAAnB;AACD;AACF;;AAED;;;;;;;AAOA,WAASuf,8BAAT,CACEpe,QADF,EAEEkD,SAFF,EAGEgb,gBAHF,EAIE;AACA,QAAI/gB,iBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CAAiCyI,SAAjC,KAA+CvF,SAAnD,EAA8D;AAC5D,UAAIR,iBAAOZ,cAAP,CAAsBC,SAAtB,CAAgC0hB,gBAAhC,KAAqDvgB,SAAzD,EAAoE;AAClER,yBAAOzC,SAAP,CAAiBsF,QAAjB,EAA2BvF,KAA3B,CACEyI,SADF,EAEE8F,aAFF,GAEkBkV,gBAFlB;AAGD,OAJD,MAIO;AACLtf,wBAAMC,YAAN,CACE,sDADF;AAGD;AACF,KAVD,MAUO;AACLD,sBAAMC,YAAN,CAAmB,qDAAnB;AACD;AACF;;AAED;;;AAGA,WAASwf,sBAAT,CAAgCH,gBAAhC,EAAkD;AAChD,QAAI/gB,iBAAOZ,cAAP,CAAsBC,SAAtB,CAAgC0hB,gBAAhC,KAAqDvgB,SAAzD,EAAoE;AAClER,uBAAO6L,aAAP,GAAuBkV,gBAAvB;AACD,KAFD,MAEO;AACLtf,sBAAMC,YAAN,CACE,sDADF;AAGD;AACF;;AAED;;;;;AAKA,WAASV,SAAT,CAAoBC,WAApB,EAAiC;AAC/BtB,mBAAKqB,SAAL,CAAgBC,WAAhB;AACD;;AAED;;;AAGA,WAASkgB,SAAT,GAAoB;AAClB,WAAOnhB,iBAAO9B,MAAd;AACD;;AAED;;;AAGA,SAAO;AACLsS,UAAMA,IADD;AAEL4M,eAAWA,SAFN;AAGLC,qBAAiBA,eAHZ;AAILC,uBAAmBA,iBAJd;AAKLC,sBAAkBA,gBALb;AAMLxb,sBAAkBA,gBANb;AAOL0b,eAAWA,SAPN;AAQLC,uBAAmBA,iBARd;AASLE,gBAAYA,UATP;AAULC,wBAAoBA,kBAVf;AAWLhV,gBAAYA,UAXP;AAYLG,wBAAoBA,kBAZf;AAaLP,eAAWA,SAbN;AAcLE,mBAAeA,aAdV;AAeLD,uBAAmBA,iBAfd;AAgBLqV,wBAAoBA,kBAhBf;AAiBLE,wBAAoBA,kBAjBf;AAkBLD,2BAAuBA,qBAlBlB;AAmBLE,2BAAuBA,qBAnBlB;AAoBLE,6BAAyBA,uBApBpB;AAqBLG,6BAAyBA,uBArBpB;AAsBLF,0BAAsBA,oBAtBjB;AAuBLC,qBAAiBA,eAvBZ;AAwBLE,cAAUA,QAxBL;AAyBLC,2BAAuBA,qBAzBlB;AA0BLC,+BAA2BA,yBA1BtB;AA2BLC,oBAAgBA,cA3BX;AA4BLC,4BAAwBA,sBA5BnB;AA6BLC,aAASA,OA7BJ;AA8BLC,uBAAmBA,iBA9Bd;AA+BLK,gBAAYA,UA/BP;AAgCLE,4BAAwBA,sBAhCnB;AAiCLC,aAASA,OAjCJ;AAkCLC,qBAAiBA,eAlCZ;AAmCLC,6BAAyBA,uBAnCpB;AAoCL5f,UAAMA,IApCD;AAqCLgB,WAAOA,KArCF;AAsCL6e,cAAUA,QAtCL;AAuCLC,iBAAaA,WAvCR;AAwCLC,UAAMA,IAxCD;AAyCLE,UAAMA,IAzCD;AA0CLE,cAAUA,QA1CL;AA2CLC,wBAAoBA,kBA3Cf;AA4CLC,mBAAeA,aA5CV;AA6CLC,2BAAuBA,qBA7ClB;AA8CLC,oBAAgBA,cA9CX;AA+CLC,gBAAYA,UA/CP;AAgDLC,iBAAaA,WAhDR;AAiDLC,YAAQA,MAjDH;AAkDLC,qBAAiBA,eAlDZ;AAmDLE,yBAAqBA,mBAnDhB;AAoDLC,cAAUA,QApDL;AAqDLC,cAAUA,QArDL;AAsDLC,oBAAgBA,cAtDX;AAuDL7B,iBAAaA,WAvDR;AAwDL8B,2BAAuBA,qBAxDlB;AAyDLC,8BAA0BA,wBAzDrB;AA0DLE,0BAAsBA,oBA1DjB;AA2DLC,oCAAgCA,8BA3D3B;AA4DLC,4BAAwBA,sBA5DnB;AA6DLC,eAAWA,SA7DN;AA8DLngB,eAAWA;AA9DN,GAAP;AAgED,CA5tCe,EAAhB;;AAhBA;;;;;;AAZA;;;;;;AAZA;;;;;;AAZA;;;;;;AAlBA;;;AAGA;;;;;;AAhBA;;;;;;;AAZA;;;;;;AAnBA;;;AAGA;;;;;AAvBA;;;;AAIA;;;;;kBAg2Cemc,S;;;;;;;;;;;;;;ACh2Cf;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;;;AAEA;;;;;;;AAZA;;;;;;AAZA;;;;AA6BA,IAAIpM,uBAAwB,YAAW;AACrC;;;;;AAKA,WAASrK,UAAT,CAAoBnJ,SAApB,EAA+B;AAC7B;;;AAGAyC,qBAAOzC,SAAP,GAAmBA,SAAnB;;AAEA;;;AAGA6jB;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;;AAEA;;;AAGAC;AACD;;AAED;;;;;AAKA,WAASJ,+BAAT,GAA2C;AACzC;;;;AAIA,SAAK,IAAIpV,GAAT,IAAgBlM,iBAAOzC,SAAvB,EAAkC;AAChCyC,uBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsBhP,YAAtB,GAAqC,IAArC;AACD;AACF;;AAED;;;;;;;AAOA,WAASkkB,oBAAT,GAAgC;AAC9B;;;AAGA,SAAK,IAAIlV,GAAT,IAAgBlM,iBAAOzC,SAAvB,EAAkC;AAChC;;;AAGA,UAAIyC,iBAAOzC,SAAP,CAAiB4T,cAAjB,CAAgCjF,GAAhC,CAAJ,EAA0C;AACxC;;;AAGA,YAAIlM,iBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAA1B,EAAiC;AAC/B;;;AAGA,eAAK,IAAIqF,IAAI,CAAb,EAAgBA,IAAI3C,iBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAAtB,CAA4BsF,MAAhD,EAAwDD,GAAxD,EAA6D;AAC3D,gBAAIyC,iBAAOe,KAAP,CAAanG,iBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAAtB,CAA4BqF,CAA5B,CAAb,CAAJ,EAAkD;AAChD3C,+BAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAAtB,CAA4BqF,CAA5B,IACE3C,iBAAO1C,KAAP,CAAa0C,iBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAAtB,CAA4BqF,CAA5B,CAAb,CADF;;AAGA3C,+BAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAAtB,CAA4BqF,CAA5B,EAA+BsB,KAA/B,GAAuCtB,CAAvC;AACD;AACD;;;;AAIA,gBACEyC,iBAAOe,KAAP,CAAanG,iBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAAtB,CAA4BqF,CAA5B,CAAb,KACA,CAAC3C,iBAAO1C,KAAP,CAAa0C,iBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAAtB,CAA4BqF,CAA5B,CAAb,CAFH,EAGE;AACAlB,8BAAMC,YAAN,CACE,qBACE1B,iBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAAtB,CAA4BqF,CAA5B,CADF,GAEE,yBAFF,GAGEuJ,GAHF,GAIE,sCALJ;AAOD;;AAED;;;AAGA,gBAAI,CAAC9G,iBAAOe,KAAP,CAAanG,iBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAAtB,CAA4BqF,CAA5B,CAAb,CAAL,EAAmD;AACjD3C,+BAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAAtB,CAA4BqF,CAA5B,EAA+BsB,KAA/B,GAAuCtB,CAAvC;AACD;AACF;AACF;AACF;AACF;AACF;;AAED;;;;;;AAMA,WAAS0e,kBAAT,GAA8B;AAC5B;;;AAGA,SAAK,IAAInV,GAAT,IAAgBlM,iBAAOzC,SAAvB,EAAkC;AAChC;;;AAGA,UAAIyC,iBAAOzC,SAAP,CAAiB4T,cAAjB,CAAgCjF,GAAhC,CAAJ,EAA0C;AACxC;;;;AAIA,aAAK,IAAIvJ,IAAI,CAAb,EAAgBA,IAAI3C,iBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAAtB,CAA4BsF,MAAhD,EAAwDD,GAAxD,EAA6D;AAC3D;;;AAGA,cAAIwK,qBAAWiC,eAAX,CAA2BpP,iBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAAtB,CAA4BqF,CAA5B,EAA+Bb,GAA1D,CAAJ,EAAoE;AAClE;;;;;AAKA,gBAAI9B,iBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAAtB,CAA4BqF,CAA5B,EAA+BkM,eAA/B,IAAkDrO,SAAtD,EAAiE;AAC/D2M,mCAAWmB,8BAAX,CACEtO,iBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAAtB,CAA4BqF,CAA5B,EAA+Bb,GADjC,EAEEoK,GAFF,EAGEvJ,CAHF;AAKD;AACF;AACF;AACF;AACF;AACF;;AAED;;;;;;AAMA,WAAS4e,iCAAT,GAA6C;AAC3C;;;;;AAKA,SAAK,IAAIrV,GAAT,IAAgBlM,iBAAOzC,SAAvB,EAAkC;AAChCyC,uBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB7H,OAAtB,GAAgC,KAAhC;AACD;AACF;;AAED;;;;;;AAMA,WAASmd,iCAAT,GAA6C;AAC3C;;;;;AAKA,SAAK,IAAItV,GAAT,IAAgBlM,iBAAOzC,SAAvB,EAAkC;AAChCyC,uBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsBvO,MAAtB,GAA+B,KAA/B;AACD;AACF;;AAED;;;;;;AAMA,WAAS8jB,8BAAT,GAA0C;AACxC;;;;;AAKA,SAAK,IAAIvV,GAAT,IAAgBlM,iBAAOzC,SAAvB,EAAkC;AAChCyC,uBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsBrO,YAAtB,GAAqC,EAArC;AACD;AACF;;AAED;;;;;AAKA,WAAS6jB,qCAAT,GAAiD;AAC/C;;;;AAIA,SAAK,IAAIxV,GAAT,IAAgBlM,iBAAOzC,SAAvB,EAAkC;AAChC+H,iCAAiBiC,sBAAjB,CACEvH,iBAAOzC,SAAP,CAAiB2O,GAAjB,EAAsB5O,KAAtB,CAA4B,CAA5B,CADF,EAEE4O,GAFF;AAID;AACF;;AAED;;;AAGA,SAAO;AACLxF,gBAAYA;AADP,GAAP;AAGD,CA/O0B,EAA3B;;AAXA;;;;;;AAZA;;;;AAZA;;;;kBAoReqK,oB;;;;;;;;;;;;;;AChRf;;;;;;AAEA;;;;;AAKA,IAAIvL,oBAAqB,YAAW;AAClC;;;;;;AAMA,WAASC,SAAT,GAAqB;AACnB;;;AAGA,QAAIkc,iBAAiBlf,SAASoF,sBAAT,CACnB,0BADmB,CAArB;;AAIA;;;AAGA,SAAK,IAAIlF,IAAI,CAAb,EAAgBA,IAAIgf,eAAe/e,MAAnC,EAA2CD,GAA3C,EAAgD;AAC9Cgf,qBAAehf,CAAf,EAAkBa,SAAlB,CAA4BE,MAA5B,CAAmC,iCAAnC;AACD;;AAED;;;;AAIA,QAAI1D,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA9D,EAAoE;AAClE,UAAIykB,cAAc,EAAlB;;AAEA,UAAI5hB,iBAAOlC,UAAX,EAAuB;AACrB8jB,sBAAc5hB,iBAAOnC,YAAP,CAAqBmC,iBAAO9C,YAA5B,EAA2C+G,KAAzD;AACD,OAFD,MAEK;AACH2d,sBAAc5hB,iBAAO9C,YAArB;AACD;;AAED,UACEuF,SAASC,gBAAT,CACE,0DACEkf,WADF,GAEE,IAHJ,CADF,EAME;AACA,YAAID,kBAAiBlf,SAASC,gBAAT,CACnB,0DACEkf,WADF,GAEE,IAHiB,CAArB;;AAMA,aAAK,IAAIjf,KAAI,CAAb,EAAgBA,KAAIgf,gBAAe/e,MAAnC,EAA2CD,IAA3C,EAAgD;AAC9C,cAAI,CAACgf,gBAAehf,EAAf,EAAkBkf,YAAlB,CAA+B,yBAA/B,CAAL,EAAgE;AAC9DF,4BAAehf,EAAf,EAAkBa,SAAlB,CAA4BC,GAA5B,CAAgC,iCAAhC;AACD;AACF;AACF;AACF,KA5BD,MA4BO;AACL,UAAIzD,iBAAO7C,eAAP,IAA0B,IAA1B,IAAkC6C,iBAAO7C,eAAP,IAA0B,EAAhE,EAAoE;AAClE,YAAIiG,sBAAsBpD,iBAAOzC,SAAP,CAAkByC,iBAAO7C,eAAzB,EAA2CD,YAArE;AACD,OAFD,MAEK;AACH,YAAIkG,sBAAsB,EAA1B;;AAEA,YAAIpD,iBAAOzC,SAAP,CAAkByC,iBAAO7C,eAAzB,EAA2CkH,OAA/C,EAAwD;AACtDjB,gCAAsBpD,iBAAOzC,SAAP,CAAkByC,iBAAO7C,eAAzB,EAA2CU,YAA3C,CAAyDmC,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAAlG,EAAiH+G,KAAvI;AACD,SAFD,MAEK;AACHb,gCAAsBpD,iBAAOzC,SAAP,CAAkByC,iBAAO7C,eAAzB,EAA2CD,YAAjE;AACD;AACF;;AAED,UACEuF,SAASC,gBAAT,CACE,0DACEU,mBADF,GAEE,8BAFF,GAGEpD,iBAAO7C,eAHT,GAIE,IALJ,CADF,EAQE;AACA,YAAIwkB,mBAAiBlf,SAASC,gBAAT,CACnB,0DACEU,mBADF,GAEE,8BAFF,GAGEpD,iBAAO7C,eAHT,GAIE,IALiB,CAArB;;AAQA,aAAK,IAAIwF,MAAI,CAAb,EAAgBA,MAAIgf,iBAAe/e,MAAnC,EAA2CD,KAA3C,EAAgD;AAC9Cgf,2BAAehf,GAAf,EAAkBa,SAAlB,CAA4BC,GAA5B,CAAgC,iCAAhC;AACD;AACF;AACF;AACF;;AAED,SAAO;AACLgC,eAAWA;AADN,GAAP;AAGD,CA9FuB,EAAxB,C,CAXA;;;;kBA2GeD,iB;;;;;;;;;;;;;;ACvGf;;;;;;AAEA;;;;;AAKA,IAAI+E,sBAAuB,YAAW;AACpC,WAASrI,IAAT,CAAcyI,KAAd,EAAqB;AACnBxI,eAAWwI,KAAX;AACAvI,iBAAauI,KAAb;AACAtI,aAASsI,KAAT;AACArI,uBAAmBqI,KAAnB;AACD;;AAED;;;;;;AAMA,WAASxI,UAAT,CAAoBwI,KAApB,EAA2B;AACzB;;;AAGA,QAAMmX,uBAAuBrf,SAASC,gBAAT,CAC3B,0BAD2B,CAA7B;;AAIA;;;AAGA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAImf,qBAAqBlf,MAAzC,EAAiDD,GAAjD,EAAsD;AACpD,UAAIE,WAAWif,qBAAqBnf,CAArB,EAAwBG,YAAxB,CACb,yBADa,CAAf;AAGA,UAAIiD,YAAY+b,qBAAqBnf,CAArB,EAAwBG,YAAxB,CACd,2BADc,CAAhB;;AAIA;;;AAGA,UAAID,YAAY,IAAZ,IAAoBkD,aAAa,IAArC,EAA2C;AACzC+b,6BAAqBnf,CAArB,EAAwByE,SAAxB,GAAoCuD,KAApC;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASvI,YAAT,CAAsBuI,KAAtB,EAA6B;AAC3B;;;AAGA,QAAMoX,+BAA+Btf,SAASC,gBAAT,CACnC,uDACE1C,iBAAO7C,eADT,GAEE,IAHiC,CAArC;;AAMA;;;AAGA,SAAK,IAAIwF,IAAI,CAAb,EAAgBA,IAAIof,6BAA6Bnf,MAAjD,EAAyDD,GAAzD,EAA8D;AAC5D,UAAIoD,YAAYgc,6BAA6Bpf,CAA7B,EAAgCG,YAAhC,CACd,2BADc,CAAhB;;AAIA;;;AAGA,UAAIiD,aAAa,IAAjB,EAAuB;AACrBgc,qCAA6Bpf,CAA7B,EAAgCyE,SAAhC,GAA4CuD,KAA5C;AACD;AACF;AACF;;AAED;;;;;AAKA,WAAStI,QAAT,CAAkBsI,KAAlB,EAAyB;AACvB,QAAI3K,iBAAO7C,eAAP,IAA0B,IAA9B,EAAoC;AAClC;;;AAGA,UAAM6kB,2BAA2Bvf,SAASC,gBAAT,CAC/B,yDACE1C,iBAAO9C,YADT,GAEE,IAH6B,CAAjC;;AAMA;;;AAGA,WAAK,IAAIyF,IAAI,CAAb,EAAgBA,IAAIqf,yBAAyBpf,MAA7C,EAAqDD,GAArD,EAA0D;AACxD,YAAIE,WAAWmf,yBAAyBrf,CAAzB,EAA4BG,YAA5B,CACb,yBADa,CAAf;;AAIA;;;AAGA,YAAID,YAAY,IAAhB,EAAsB;AACpBmf,mCAAyBrf,CAAzB,EAA4ByE,SAA5B,GAAwCuD,KAAxC;AACD;AACF;AACF;AACF;;AAED;;;;;AAKA,WAASrI,kBAAT,CAA4BqI,KAA5B,EAAmC;AACjC,QAAIvH,sBACFpD,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA1D,GACI6C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAD7C,GAEI,IAHN;AAIA;;;AAGA,QAAM+kB,mCAAmCxf,SAASC,gBAAT,CACvC,uDACE1C,iBAAO7C,eADT,GAEE,gCAFF,GAGEiG,mBAHF,GAIE,IALqC,CAAzC;;AAQA;;;AAGA,SAAK,IAAIT,IAAI,CAAb,EAAgBA,IAAIsf,iCAAiCrf,MAArD,EAA6DD,GAA7D,EAAkE;AAChEsf,uCAAiCtf,CAAjC,EAAoCyE,SAApC,GAAgDuD,KAAhD;AACD;AACF;;AAED;;;AAGA,WAASL,UAAT,GAAsB;AACpB;;;AAGA,QAAI4X,gBAAgBzf,SAASC,gBAAT,CAA0B,0BAA1B,CAApB;;AAEA;;;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIuf,cAActf,MAAlC,EAA0CD,GAA1C,EAA+C;AAC7Cuf,oBAAcvf,CAAd,EAAiByE,SAAjB,GAA6B,IAA7B;AACD;AACF;;AAED;;;AAGA,SAAO;AACLlF,UAAMA,IADD;AAELoI,gBAAYA;AAFP,GAAP;AAID,CAlKyB,EAA1B,C,CAXA;;;;kBA+KeC,mB;;;;;;;;;;;;;;AC3Kf;;;;;;AAEA;;;;;AAKA,IAAIC,wBAAyB,YAAW;AACtC;;;;;AAKA,WAAStI,IAAT,CAAc0I,OAAd,EAAuB;AACrBzI,eAAWyI,OAAX;AACAxI,iBAAawI,OAAb;AACAvI,aAASuI,OAAT;AACAtI,uBAAmBsI,OAAnB;AACD;;AAED;;;;;AAKA,WAASzI,UAAT,CAAoByI,OAApB,EAA6B;AAC3B;;;AAGA,QAAMuX,yBAAyB1f,SAASC,gBAAT,CAC7B,4BAD6B,CAA/B;;AAIA;;;AAGA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIwf,uBAAuBvf,MAA3C,EAAmDD,GAAnD,EAAwD;AACtD,UAAIE,WAAWsf,uBAAuBxf,CAAvB,EAA0BG,YAA1B,CACb,yBADa,CAAf;AAGA,UAAIiD,YAAYoc,uBAAuBxf,CAAvB,EAA0BG,YAA1B,CACd,2BADc,CAAhB;;AAIA;;;AAGA,UAAID,YAAY,IAAZ,IAAoBkD,aAAa,IAArC,EAA2C;AACzCoc,+BAAuBxf,CAAvB,EAA0ByE,SAA1B,GAAsCwD,OAAtC;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASxI,YAAT,CAAsBwI,OAAtB,EAA+B;AAC7B;;;AAGA,QAAMwX,iCAAiC3f,SAASC,gBAAT,CACrC,yDACE1C,iBAAO7C,eADT,GAEE,IAHmC,CAAvC;;AAMA;;;AAGA,SAAK,IAAIwF,IAAI,CAAb,EAAgBA,IAAIyf,+BAA+Bxf,MAAnD,EAA2DD,GAA3D,EAAgE;AAC9D,UAAIoD,YAAYqc,+BAA+Bzf,CAA/B,EAAkCG,YAAlC,CACd,2BADc,CAAhB;;AAIA;;;AAGA,UAAIiD,aAAa,IAAjB,EAAuB;AACrBqc,uCAA+Bzf,CAA/B,EAAkCyE,SAAlC,GAA8CwD,OAA9C;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASvI,QAAT,CAAkBuI,OAAlB,EAA2B;AACzB,QAAI5K,iBAAO7C,eAAP,IAA0B,IAA9B,EAAoC;AAClC;;;AAGA,UAAMklB,6BAA6B5f,SAASC,gBAAT,CACjC,2DACE1C,iBAAO9C,YADT,GAEE,IAH+B,CAAnC;;AAMA;;;AAGA,WAAK,IAAIyF,IAAI,CAAb,EAAgBA,IAAI0f,2BAA2Bzf,MAA/C,EAAuDD,GAAvD,EAA4D;AAC1D,YAAIE,WAAWwf,2BAA2B1f,CAA3B,EAA8BG,YAA9B,CACb,yBADa,CAAf;;AAIA;;;AAGA,YAAID,YAAY,IAAhB,EAAsB;AACpBwf,qCAA2B1f,CAA3B,EAA8ByE,SAA9B,GAA0CwD,OAA1C;AACD;AACF;AACF;AACF;;AAED;;;;;AAKA,WAAStI,kBAAT,CAA4BsI,OAA5B,EAAqC;AACnC,QAAIxH,sBACFpD,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA1D,GACI6C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAD7C,GAEI,IAHN;;AAKA;;;AAGA,QAAMolB,qCAAqC7f,SAASC,gBAAT,CACzC,yDACE1C,iBAAO7C,eADT,GAEE,gCAFF,GAGEiG,mBAHF,GAIE,IALuC,CAA3C;;AAQA;;;AAGA,SAAK,IAAIT,IAAI,CAAb,EAAgBA,IAAI2f,mCAAmC1f,MAAvD,EAA+DD,GAA/D,EAAoE;AAClE2f,yCAAmC3f,CAAnC,EAAsCyE,SAAtC,GAAkDwD,OAAlD;AACD;AACF;;AAED;;;AAGA,WAASN,UAAT,GAAsB;AACpB;;;AAGA,QAAIiY,kBAAkB9f,SAASC,gBAAT,CACpB,4BADoB,CAAtB;;AAIA;;;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAI4f,gBAAgB3f,MAApC,EAA4CD,GAA5C,EAAiD;AAC/C4f,sBAAgB5f,CAAhB,EAAmByE,SAAnB,GAA+B,IAA/B;AACD;AACF;;AAED;;;AAGA,SAAO;AACLlF,UAAMA,IADD;AAELoI,gBAAYA;AAFP,GAAP;AAID,CAzK2B,EAA5B,C,CAXA;;;;kBAsLeE,qB;;;;;;;;;;;;;;AClLf;;;;;;AAEA;;;;;AAKA,IAAIC,wBAAyB,YAAW;AACtC;;;;;AAKA,WAASvI,IAAT,CAAcX,OAAd,EAAuB;AACrBY,eAAWZ,OAAX;AACAa,iBAAab,OAAb;AACAc,aAASd,OAAT;AACAe,uBAAmBf,OAAnB;AACD;;AAED;;;;;AAKA,WAASY,UAAT,CAAoBZ,OAApB,EAA6B;AAC3B;;;AAGA,QAAMihB,yBAAyB/f,SAASC,gBAAT,CAC7B,4BAD6B,CAA/B;;AAIA;;;AAGA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAI6f,uBAAuB5f,MAA3C,EAAmDD,GAAnD,EAAwD;AACtD,UAAIE,WAAW2f,uBAAuB7f,CAAvB,EAA0BG,YAA1B,CACb,yBADa,CAAf;AAGA,UAAIiD,YAAYyc,uBAAuB7f,CAAvB,EAA0BG,YAA1B,CACd,2BADc,CAAhB;;AAIA;;;AAGA,UAAID,YAAY,IAAZ,IAAoBkD,aAAa,IAArC,EAA2C;AACzCyc,+BAAuB7f,CAAvB,EAA0ByE,SAA1B,GAAsC7F,OAAtC;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASa,YAAT,CAAsBb,OAAtB,EAA+B;AAC7B;;;AAGA,QAAMkhB,iCAAiChgB,SAASC,gBAAT,CACrC,yDACE1C,iBAAO7C,eADT,GAEE,IAHmC,CAAvC;;AAMA;;;AAGA,SAAK,IAAIwF,IAAI,CAAb,EAAgBA,IAAI8f,+BAA+B7f,MAAnD,EAA2DD,GAA3D,EAAgE;AAC9D,UAAIoD,YAAY0c,+BAA+B9f,CAA/B,EAAkCG,YAAlC,CACd,2BADc,CAAhB;;AAIA;;;AAGA,UAAIiD,aAAa,IAAjB,EAAuB;AACrB0c,uCAA+B9f,CAA/B,EAAkCyE,SAAlC,GAA8C7F,OAA9C;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASc,QAAT,CAAkBd,OAAlB,EAA2B;AACzB,QAAIvB,iBAAO7C,eAAP,IAA0B,IAA9B,EAAoC;AAClC;;;AAGA,UAAMulB,6BAA6BjgB,SAASC,gBAAT,CACjC,2DACE1C,iBAAO9C,YADT,GAEE,IAH+B,CAAnC;;AAMA;;;AAGA,WAAK,IAAIyF,IAAI,CAAb,EAAgBA,IAAI+f,2BAA2B9f,MAA/C,EAAuDD,GAAvD,EAA4D;AAC1D,YAAIE,WAAW6f,2BAA2B/f,CAA3B,EAA8BG,YAA9B,CACb,yBADa,CAAf;;AAIA;;;AAGA,YAAID,YAAY,IAAhB,EAAsB;AACpB6f,qCAA2B/f,CAA3B,EAA8ByE,SAA9B,GAA0C7F,OAA1C;AACD;AACF;AACF;AACF;;AAED;;;;;AAKA,WAASe,kBAAT,CAA4Bf,OAA5B,EAAqC;AACnC,QAAI6B,sBACFpD,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA1D,GACI6C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAD7C,GAEI,IAHN;AAIA;;;AAGA,QAAMylB,qCAAqClgB,SAASC,gBAAT,CACzC,yDACE1C,iBAAO7C,eADT,GAEE,gCAFF,GAGEiG,mBAHF,GAIE,IALuC,CAA3C;;AAQA;;;AAGA,SAAK,IAAIT,IAAI,CAAb,EAAgBA,IAAIggB,mCAAmC/f,MAAvD,EAA+DD,GAA/D,EAAoE;AAClEggB,yCAAmChgB,CAAnC,EAAsCyE,SAAtC,GAAkD7F,OAAlD;AACD;AACF;;AAED;;;AAGA,WAAS+I,UAAT,GAAsB;AACpB;;;AAGA,QAAIsY,kBAAkBngB,SAASC,gBAAT,CACpB,4BADoB,CAAtB;;AAIA;;;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIigB,gBAAgBhgB,MAApC,EAA4CD,GAA5C,EAAiD;AAC/CigB,sBAAgBjgB,CAAhB,EAAmByE,SAAnB,GAA+B,IAA/B;AACD;AACF;;AAED;;;AAGA,SAAO;AACLlF,UAAMA,IADD;AAELoI,gBAAYA;AAFP,GAAP;AAID,CAxK2B,EAA5B,C,CAXA;;;;kBAqLeG,qB;;;;;;;;;;;;;;ACjLf;;;;;;AAEA;;;;;AAKA,IAAIJ,sBAAuB,YAAW;AACpC;;;;;;;AAOA,WAASnI,IAAT,CAAcpB,WAAd,EAA2B;AACzB;;;AAGAqB,eAAWrB,WAAX;AACAsB,iBAAatB,WAAb;AACAuB,aAASvB,WAAT;AACAwB,uBAAmBxB,WAAnB;AACD;;AAED;;;;;;;AAOA,WAASqB,UAAT,CAAoBgT,IAApB,EAA0B;AACxB;;;AAGA,QAAI0N,uBAAuBpgB,SAASC,gBAAT,CACzB,yBADyB,CAA3B;;AAIA;;;;AAIA,QAAIogB,WAAW3N,KAAKvK,OAAL,GAAe,GAAf,GAAqBuK,KAAK5T,OAAzC;;AAEA,QAAI4T,KAAKxK,KAAL,GAAa,CAAjB,EAAoB;AAClBmY,iBAAW3N,KAAKxK,KAAL,GAAa,GAAb,GAAmBmY,QAA9B;AACD;;AAED,SAAK,IAAIngB,IAAI,CAAb,EAAgBA,IAAIkgB,qBAAqBjgB,MAAzC,EAAiDD,GAAjD,EAAsD;AACpD,UAAIE,WAAWggB,qBAAqBlgB,CAArB,EAAwBG,YAAxB,CACb,yBADa,CAAf;AAGA,UAAIiD,YAAY8c,qBAAqBlgB,CAArB,EAAwBG,YAAxB,CACd,2BADc,CAAhB;;AAIA,UAAID,YAAY,IAAZ,IAAoBkD,aAAa,IAArC,EAA2C;AACzC8c,6BAAqBlgB,CAArB,EAAwByE,SAAxB,GAAoC0b,QAApC;AACD;AACF;AACF;;AAED;;;;;;;AAOA,WAAS1gB,YAAT,CAAsB+S,IAAtB,EAA4B;AAC1B;;;AAGA,QAAI0N,uBAAuBpgB,SAASC,gBAAT,CACzB,sDACE1C,iBAAO7C,eADT,GAEE,IAHuB,CAA3B;;AAMA;;;;AAIA,QAAI2lB,WAAW3N,KAAKvK,OAAL,GAAe,GAAf,GAAqBuK,KAAK5T,OAAzC;;AAEA,QAAI4T,KAAKxK,KAAL,GAAa,CAAjB,EAAoB;AAClBmY,iBAAW3N,KAAKxK,KAAL,GAAa,GAAb,GAAmBmY,QAA9B;AACD;;AAED,SAAK,IAAIngB,IAAI,CAAb,EAAgBA,IAAIkgB,qBAAqBjgB,MAAzC,EAAiDD,GAAjD,EAAsD;AACpD,UAAIoD,YAAY8c,qBAAqBlgB,CAArB,EAAwBG,YAAxB,CACd,2BADc,CAAhB;;AAIA,UAAIiD,aAAa,IAAjB,EAAuB;AACrB8c,6BAAqBlgB,CAArB,EAAwByE,SAAxB,GAAoC0b,QAApC;AACD;AACF;AACF;;AAED;;;;;;;AAOA,WAASzgB,QAAT,CAAkB8S,IAAlB,EAAwB;AACtB,QAAInV,iBAAO7C,eAAP,IAA0B,IAA9B,EAAoC;AAClC;;;AAGA,UAAI0lB,uBAAuBpgB,SAASC,gBAAT,CACzB,wDACE1C,iBAAO9C,YADT,GAEE,IAHuB,CAA3B;;AAMA;;;;AAIA,UAAI4lB,WAAW3N,KAAKvK,OAAL,GAAe,GAAf,GAAqBuK,KAAK5T,OAAzC;;AAEA,UAAI4T,KAAKxK,KAAL,GAAa,CAAjB,EAAoB;AAClBmY,mBAAW3N,KAAKxK,KAAL,GAAa,GAAb,GAAmBmY,QAA9B;AACD;;AAED,WAAK,IAAIngB,IAAI,CAAb,EAAgBA,IAAIkgB,qBAAqBjgB,MAAzC,EAAiDD,GAAjD,EAAsD;AACpD,YAAIE,WAAWggB,qBAAqBlgB,CAArB,EAAwBG,YAAxB,CACb,yBADa,CAAf;;AAIA,YAAID,YAAY,IAAhB,EAAsB;AACpBggB,+BAAqBlgB,CAArB,EAAwByE,SAAxB,GAAoC0b,QAApC;AACD;AACF;AACF;AACF;;AAED;;;;;;;AAOA,WAASxgB,kBAAT,CAA4B6S,IAA5B,EAAkC;AAChC,QAAI/R,sBACFpD,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA1D,GACI6C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAD7C,GAEI,IAHN;AAIA;;;AAGA,QAAI2lB,uBAAuBpgB,SAASC,gBAAT,CACzB,sDACE1C,iBAAO7C,eADT,GAEE,gCAFF,GAGEiG,mBAHF,GAIE,IALuB,CAA3B;;AAQA;;;;AAIA,QAAI0f,WAAW3N,KAAKvK,OAAL,GAAe,GAAf,GAAqBuK,KAAK5T,OAAzC;;AAEA,QAAI4T,KAAKxK,KAAL,GAAa,CAAjB,EAAoB;AAClBmY,iBAAW3N,KAAKxK,KAAL,GAAa,GAAb,GAAmBmY,QAA9B;AACD;;AAED,SAAK,IAAIngB,IAAI,CAAb,EAAgBA,IAAIkgB,qBAAqBjgB,MAAzC,EAAiDD,GAAjD,EAAsD;AACpDkgB,2BAAqBlgB,CAArB,EAAwByE,SAAxB,GAAoC0b,QAApC;AACD;AACF;;AAED;;;;;AAKA,WAASxY,UAAT,GAAsB;AACpB;;;AAGA,QAAIyY,gBAAgBtgB,SAASC,gBAAT,CAA0B,yBAA1B,CAApB;;AAEA;;;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIogB,cAAcngB,MAAlC,EAA0CD,GAA1C,EAA+C;AAC7CogB,oBAAcpgB,CAAd,EAAiByE,SAAjB,GAA6B,OAA7B;AACD;AACF;;AAED;;;AAGA,SAAO;AACLlF,UAAMA,IADD;AAELoI,gBAAYA;AAFP,GAAP;AAID,CAxMyB,EAA1B,C,CAXA;;;;kBAqNeD,mB;;;;;;;;;;;;;;ACjNf;;;;;;AAEA;;;;;AAKA,IAAIQ,gCAAiC,YAAW;AAC9C;;;;;;AAMA,WAAS3I,IAAT,CAAc8gB,aAAd,EAA6B7X,YAA7B,EAA2C;AACzC,QAAI8X,gBAAgBC,qBAAqBF,aAArB,EAAoC7X,YAApC,CAApB;;AAEAhJ,eAAW8gB,aAAX;AACA7gB,iBAAa6gB,aAAb;AACA5gB,aAAS4gB,aAAT;AACA3gB,uBAAmB2gB,aAAnB;AACD;;AAED;;;;;AAKA,WAAS9gB,UAAT,CAAoB8gB,aAApB,EAAmC;AACjC,QAAIE,iCAAiC1gB,SAASC,gBAAT,CACnC,2BADmC,CAArC;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIwgB,+BAA+BvgB,MAAnD,EAA2DD,GAA3D,EAAgE;AAC9D,UAAIE,WAAWsgB,+BAA+BxgB,CAA/B,EAAkCG,YAAlC,CACb,yBADa,CAAf;AAGA,UAAIiD,YAAYod,+BAA+BxgB,CAA/B,EAAkCG,YAAlC,CACd,2BADc,CAAhB;;AAIA,UAAID,YAAY,IAAZ,IAAoBkD,aAAa,IAArC,EAA2C;AACzCod,uCAA+BxgB,CAA/B,EAAkCyE,SAAlC,GAA8C6b,aAA9C;AACD;AACF;AACF;;AAED;;;;;AAKA,WAAS7gB,YAAT,CAAsB6gB,aAAtB,EAAqC;AACnC,QAAIE,iCAAiC1gB,SAASC,gBAAT,CACnC,wDACE1C,iBAAO7C,eADT,GAEE,IAHiC,CAArC;;AAMA,SAAK,IAAIwF,IAAI,CAAb,EAAgBA,IAAIwgB,+BAA+BvgB,MAAnD,EAA2DD,GAA3D,EAAgE;AAC9D,UAAIoD,YAAYod,+BAA+BxgB,CAA/B,EAAkCG,YAAlC,CACd,2BADc,CAAhB;;AAIA,UAAIiD,aAAa,IAAjB,EAAuB;AACrBod,uCAA+BxgB,CAA/B,EAAkCyE,SAAlC,GAA8C6b,aAA9C;AACD;AACF;AACF;;AAED;;;;;AAKA,WAAS5gB,QAAT,CAAkB4gB,aAAlB,EAAiC;AAC/B,QAAIjjB,iBAAO7C,eAAP,IAA0B,IAA9B,EAAoC;AAClC,UAAIgmB,iCAAiC1gB,SAASC,gBAAT,CACnC,0DACE1C,iBAAO9C,YADT,GAEE,IAHiC,CAArC;;AAMA,WAAK,IAAIyF,IAAI,CAAb,EAAgBA,IAAIwgB,+BAA+BvgB,MAAnD,EAA2DD,GAA3D,EAAgE;AAC9D,YAAIE,WAAWsgB,+BAA+BxgB,CAA/B,EAAkCG,YAAlC,CACb,yBADa,CAAf;;AAIA,YAAID,YAAY,IAAhB,EAAsB;AACpBsgB,yCAA+BxgB,CAA/B,EAAkCyE,SAAlC,GAA8C6b,aAA9C;AACD;AACF;AACF;AACF;;AAED;;;;;AAKA,WAAS3gB,kBAAT,CAA4B2gB,aAA5B,EAA2C;AACzC,QAAI7f,sBACFpD,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA1D,GACI6C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAD7C,GAEI,IAHN;;AAKA,QAAIimB,iCAAiC1gB,SAASC,gBAAT,CACnC,wDACE1C,iBAAO7C,eADT,GAEE,gCAFF,GAGEiG,mBAHF,GAIE,IALiC,CAArC;;AAQA,SAAK,IAAIT,IAAI,CAAb,EAAgBA,IAAIwgB,+BAA+BvgB,MAAnD,EAA2DD,GAA3D,EAAgE;AAC9DwgB,qCAA+BxgB,CAA/B,EAAkCyE,SAAlC,GAA8C6b,aAA9C;AACD;AACF;;AAED;;;AAGA,WAAS3Y,UAAT,GAAsB;AACpB,QAAI6Y,iCAAiC1gB,SAASC,gBAAT,CACnC,2BADmC,CAArC;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIwgB,+BAA+BvgB,MAAnD,EAA2DD,GAA3D,EAAgE;AAC9DwgB,qCAA+BxgB,CAA/B,EAAkCyE,SAAlC,GAA8C,IAA9C;AACD;AACF;;AAED;;;;;;AAMA,WAAS8b,oBAAT,CAA8BpiB,WAA9B,EAA2CqK,YAA3C,EAAyD;AACvD,QAAI8X,gBAAgB,OAApB;;AAEA;;;AAGA,QAAIG,sBACFlf,SAASpD,YAAYS,OAArB,IACA2C,SAASpD,YAAY8J,OAArB,IAAgC,EADhC,GAEA1G,SAASpD,YAAY6J,KAArB,IAA8B,EAA9B,GAAmC,EAHrC;AAIA,QAAI0Y,uBACFnf,SAASiH,aAAa5J,OAAtB,IACA2C,SAASiH,aAAaP,OAAtB,IAAiC,EADjC,GAEA1G,SAASiH,aAAaR,KAAtB,IAA+B,EAA/B,GAAoC,EAHtC;;AAKA;;;AAGA,QAAI,CAACtE,MAAM+c,mBAAN,CAAD,IAA+B,CAAC/c,MAAMgd,oBAAN,CAApC,EAAiE;AAC/D;;;AAGA,UAAIC,4BACFD,uBAAuBD,mBADzB;;AAGA,UAAIG,iBAAiBja,KAAKC,KAAL,CAAW+Z,4BAA4B,IAAvC,CAArB;AACA,UAAIE,mBAAmBla,KAAKC,KAAL,CACrB,CAAC+Z,4BAA4BC,iBAAiB,IAA9C,IAAsD,EADjC,CAAvB;AAGA,UAAIE,mBACFH,4BACAC,iBAAiB,IADjB,GAEAC,mBAAmB,EAHrB;;AAKAP,sBACE,CAACO,mBAAmB,EAAnB,GAAwB,MAAMA,gBAA9B,GAAiDA,gBAAlD,IACA,GADA,IAECC,mBAAmB,EAAnB,GAAwB,MAAMA,gBAA9B,GAAiDA,gBAFlD,CADF;;AAKA,UAAIF,iBAAiB,CAArB,EAAwB;AACtBN,wBAAgBM,iBAAiB,GAAjB,GAAuBN,aAAvC;AACD;AACF;;AAED,WAAOA,aAAP;AACD;;AAED;;;AAGA,SAAO;AACL/gB,UAAMA,IADD;AAELoI,gBAAYA;AAFP,GAAP;AAID,CAzLmC,EAApC,C,CAXA;;;;kBAsMeO,6B;;;;;;;;;;;;;;AClMf;;;;;;AAEA;;;;;AAKA,IAAIC,uBAAwB,YAAW;AACrC;;;;;AAKA,WAAS5I,IAAT,CAAcyI,KAAd,EAAqB;AACnBxI,eAAWwI,KAAX;AACAvI,iBAAauI,KAAb;AACAtI,aAASsI,KAAT;AACArI,uBAAmBqI,KAAnB;AACD;;AAED;;;;;AAKA,WAASxI,UAAT,CAAoBwI,KAApB,EAA2B;AACzB,QAAI+Y,wBAAwBjhB,SAASC,gBAAT,CAC1B,2BAD0B,CAA5B;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAI+gB,sBAAsB9gB,MAA1C,EAAkDD,GAAlD,EAAuD;AACrD,UAAIE,WAAW6gB,sBAAsB/gB,CAAtB,EAAyBG,YAAzB,CACb,yBADa,CAAf;AAGA,UAAIiD,YAAY2d,sBAAsB/gB,CAAtB,EAAyBG,YAAzB,CACd,2BADc,CAAhB;;AAIA,UAAID,YAAY,IAAZ,IAAoBkD,aAAa,IAArC,EAA2C;AACzC2d,8BAAsB/gB,CAAtB,EAAyByE,SAAzB,GAAqCuD,KAArC;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASvI,YAAT,CAAsBuI,KAAtB,EAA6B;AAC3B,QAAI+Y,wBAAwBjhB,SAASC,gBAAT,CAC1B,wDACE1C,iBAAO7C,eADT,GAEE,IAHwB,CAA5B;;AAMA,SAAK,IAAIwF,IAAI,CAAb,EAAgBA,IAAI+gB,sBAAsB9gB,MAA1C,EAAkDD,GAAlD,EAAuD;AACrD,UAAIoD,YAAY2d,sBAAsB/gB,CAAtB,EAAyBG,YAAzB,CACd,2BADc,CAAhB;;AAIA,UAAIiD,aAAa,IAAjB,EAAuB;AACrB2d,8BAAsB/gB,CAAtB,EAAyByE,SAAzB,GAAqCuD,KAArC;AACD;AACF;AACF;;AAED;;;;;AAKA,WAAStI,QAAT,CAAkBsI,KAAlB,EAAyB;AACvB,QAAI3K,iBAAO7C,eAAP,IAA0B,IAA9B,EAAoC;AAClC,UAAIumB,wBAAwBjhB,SAASC,gBAAT,CAC1B,0DACE1C,iBAAO9C,YADT,GAEE,IAHwB,CAA5B;;AAMA,WAAK,IAAIyF,IAAI,CAAb,EAAgBA,IAAI+gB,sBAAsB9gB,MAA1C,EAAkDD,GAAlD,EAAuD;AACrD,YAAIE,WAAW6gB,sBAAsB/gB,CAAtB,EAAyBG,YAAzB,CACb,yBADa,CAAf;;AAIA,YAAID,YAAY,IAAhB,EAAsB;AACpB6gB,gCAAsB/gB,CAAtB,EAAyByE,SAAzB,GAAqCuD,KAArC;AACD;AACF;AACF;AACF;;AAED;;;;;AAKA,WAASrI,kBAAT,CAA4BqI,KAA5B,EAAmC;AACjC,QAAIvH,sBACFpD,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA1D,GACI6C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAD7C,GAEI,IAHN;;AAKA,QAAIwmB,wBAAwBjhB,SAASC,gBAAT,CAC1B,wDACE1C,iBAAO7C,eADT,GAEE,gCAFF,GAGEiG,mBAHF,GAIE,IALwB,CAA5B;;AAQA,SAAK,IAAIT,IAAI,CAAb,EAAgBA,IAAI+gB,sBAAsB9gB,MAA1C,EAAkDD,GAAlD,EAAuD;AACrD+gB,4BAAsB/gB,CAAtB,EAAyByE,SAAzB,GAAqCuD,KAArC;AACD;AACF;;AAED;;;AAGA,WAASL,UAAT,GAAsB;AACpB,QAAIoZ,wBAAwBjhB,SAASC,gBAAT,CAC1B,2BAD0B,CAA5B;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAI+gB,sBAAsB9gB,MAA1C,EAAkDD,GAAlD,EAAuD;AACrD+gB,4BAAsB/gB,CAAtB,EAAyByE,SAAzB,GAAqC,IAArC;AACD;AACF;;AAED;;;AAGA,SAAO;AACLlF,UAAMA,IADD;AAELoI,gBAAYA;AAFP,GAAP;AAID,CAjI0B,EAA3B,C,CAXA;;;;kBA8IeQ,oB;;;;;;;;;;;;;;AC1If;;;;;;AAEA;;;;;AAKA,IAAIC,yBAA0B,YAAW;AACvC;;;;;AAKA,WAAS7I,IAAT,CAAc0I,OAAd,EAAuB;AACrBzI,eAAWyI,OAAX;AACAxI,iBAAawI,OAAb;AACAvI,aAASuI,OAAT;AACAtI,uBAAmBsI,OAAnB;AACD;;AAED;;;;;AAKA,WAASzI,UAAT,CAAoByI,OAApB,EAA6B;AAC3B,QAAI+Y,0BAA0BlhB,SAASC,gBAAT,CAC5B,6BAD4B,CAA9B;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIghB,wBAAwB/gB,MAA5C,EAAoDD,GAApD,EAAyD;AACvD,UAAIE,WAAW8gB,wBAAwBhhB,CAAxB,EAA2BG,YAA3B,CACb,yBADa,CAAf;AAGA,UAAIiD,YAAY4d,wBAAwBhhB,CAAxB,EAA2BG,YAA3B,CACd,2BADc,CAAhB;;AAIA,UAAID,YAAY,IAAZ,IAAoBkD,aAAa,IAArC,EAA2C;AACzC4d,gCAAwBhhB,CAAxB,EAA2ByE,SAA3B,GAAuCwD,OAAvC;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASxI,YAAT,CAAsBwI,OAAtB,EAA+B;AAC7B,QAAI+Y,0BAA0BlhB,SAASC,gBAAT,CAC5B,0DACE1C,iBAAO7C,eADT,GAEE,IAH0B,CAA9B;;AAMA,SAAK,IAAIwF,IAAI,CAAb,EAAgBA,IAAIghB,wBAAwB/gB,MAA5C,EAAoDD,GAApD,EAAyD;AACvD,UAAIoD,YAAY4d,wBAAwBhhB,CAAxB,EAA2BG,YAA3B,CACd,2BADc,CAAhB;;AAIA,UAAIiD,aAAa,IAAjB,EAAuB;AACrB4d,gCAAwBhhB,CAAxB,EAA2ByE,SAA3B,GAAuCwD,OAAvC;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASvI,QAAT,CAAkBuI,OAAlB,EAA2B;AACzB,QAAI5K,iBAAO7C,eAAP,IAA0B,IAA9B,EAAoC;AAClC,UAAIwmB,0BAA0BlhB,SAASC,gBAAT,CAC5B,4DACE1C,iBAAO9C,YADT,GAEE,IAH0B,CAA9B;;AAMA,WAAK,IAAIyF,IAAI,CAAb,EAAgBA,IAAIghB,wBAAwB/gB,MAA5C,EAAoDD,GAApD,EAAyD;AACvD,YAAIE,WAAW8gB,wBAAwBhhB,CAAxB,EAA2BG,YAA3B,CACb,yBADa,CAAf;;AAIA,YAAID,YAAY,IAAhB,EAAsB;AACpB8gB,kCAAwBhhB,CAAxB,EAA2ByE,SAA3B,GAAuCwD,OAAvC;AACD;AACF;AACF;AACF;;AAED;;;;;AAKA,WAAStI,kBAAT,CAA4BsI,OAA5B,EAAqC;AACnC,QAAIxH,sBACFpD,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA1D,GACI6C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAD7C,GAEI,IAHN;;AAKA,QAAIymB,0BAA0BlhB,SAASC,gBAAT,CAC5B,0DACE1C,iBAAO7C,eADT,GAEE,gCAFF,GAGEiG,mBAHF,GAIE,IAL0B,CAA9B;;AAQA,SAAK,IAAIT,IAAI,CAAb,EAAgBA,IAAIghB,wBAAwB/gB,MAA5C,EAAoDD,GAApD,EAAyD;AACvDghB,8BAAwBhhB,CAAxB,EAA2ByE,SAA3B,GAAuCwD,OAAvC;AACD;AACF;;AAED;;;AAGA,WAASN,UAAT,GAAsB;AACpB,QAAIqZ,0BAA0BlhB,SAASC,gBAAT,CAC5B,6BAD4B,CAA9B;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIghB,wBAAwB/gB,MAA5C,EAAoDD,GAApD,EAAyD;AACvDghB,8BAAwBhhB,CAAxB,EAA2ByE,SAA3B,GAAuC,IAAvC;AACD;AACF;;AAED;;;AAGA,SAAO;AACLlF,UAAMA,IADD;AAELoI,gBAAYA;AAFP,GAAP;AAID,CAjI4B,EAA7B,C,CAXA;;;;kBA8IeS,sB;;;;;;;;;;;;;;AC1If;;;;;;AAEA;;;;;AAKA,IAAIC,yBAA0B,YAAW;AACvC;;;;;AAKA,WAAS9I,IAAT,CAAcX,OAAd,EAAuB;AACrBY,eAAWZ,OAAX;AACAa,iBAAab,OAAb;AACAc,aAASd,OAAT;AACAe,uBAAmBf,OAAnB;AACD;;AAED;;;;;AAKA,WAASY,UAAT,CAAoBZ,OAApB,EAA6B;AAC3B,QAAIqiB,0BAA0BnhB,SAASC,gBAAT,CAC5B,6BAD4B,CAA9B;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIihB,wBAAwBhhB,MAA5C,EAAoDD,GAApD,EAAyD;AACvD,UAAIE,WAAW+gB,wBAAwBjhB,CAAxB,EAA2BG,YAA3B,CACb,yBADa,CAAf;AAGA,UAAIiD,YAAY6d,wBAAwBjhB,CAAxB,EAA2BG,YAA3B,CACd,2BADc,CAAhB;;AAIA,UAAID,YAAY,IAAZ,IAAoBkD,aAAa,IAArC,EAA2C;AACzC6d,gCAAwBjhB,CAAxB,EAA2ByE,SAA3B,GAAuC7F,OAAvC;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASa,YAAT,CAAsBb,OAAtB,EAA+B;AAC7B,QAAIqiB,0BAA0BnhB,SAASC,gBAAT,CAC5B,0DACE1C,iBAAO7C,eADT,GAEE,IAH0B,CAA9B;;AAMA,SAAK,IAAIwF,IAAI,CAAb,EAAgBA,IAAIihB,wBAAwBhhB,MAA5C,EAAoDD,GAApD,EAAyD;AACvD,UAAIoD,YAAY6d,wBAAwBjhB,CAAxB,EAA2BG,YAA3B,CACd,2BADc,CAAhB;;AAIA,UAAIiD,aAAa,IAAjB,EAAuB;AACrB6d,gCAAwBjhB,CAAxB,EAA2ByE,SAA3B,GAAuC7F,OAAvC;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASc,QAAT,CAAkBd,OAAlB,EAA2B;AACzB,QAAIvB,iBAAO7C,eAAP,IAA0B,IAA9B,EAAoC;AAClC,UAAIymB,0BAA0BnhB,SAASC,gBAAT,CAC5B,4DACE1C,iBAAO9C,YADT,GAEE,IAH0B,CAA9B;;AAMA,WAAK,IAAIyF,IAAI,CAAb,EAAgBA,IAAIihB,wBAAwBhhB,MAA5C,EAAoDD,GAApD,EAAyD;AACvD,YAAIE,WAAW+gB,wBAAwBjhB,CAAxB,EAA2BG,YAA3B,CACb,0BADa,CAAf;;AAIA,YAAID,YAAY,IAAhB,EAAsB;AACpB+gB,kCAAwBjhB,CAAxB,EAA2ByE,SAA3B,GAAuC7F,OAAvC;AACD;AACF;AACF;AACF;;AAED;;;;;AAKA,WAASe,kBAAT,CAA4Bf,OAA5B,EAAqC;AACnC,QAAI6B,sBACFpD,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA1D,GACI6C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAD7C,GAEI,IAHN;;AAKA,QAAI0mB,0BAA0BnhB,SAASC,gBAAT,CAC5B,0DACE1C,iBAAO7C,eADT,GAEE,gCAFF,GAGEiG,mBAHF,GAIE,IAL0B,CAA9B;;AAQA,SAAK,IAAIT,IAAI,CAAb,EAAgBA,IAAIihB,wBAAwBhhB,MAA5C,EAAoDD,GAApD,EAAyD;AACvDihB,8BAAwBjhB,CAAxB,EAA2ByE,SAA3B,GAAuC7F,OAAvC;AACD;AACF;;AAED;;;AAGA,WAAS+I,UAAT,GAAsB;AACpB,QAAIsZ,0BAA0BnhB,SAASC,gBAAT,CAC5B,6BAD4B,CAA9B;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIihB,wBAAwBhhB,MAA5C,EAAoDD,GAApD,EAAyD;AACvDihB,8BAAwBjhB,CAAxB,EAA2ByE,SAA3B,GAAuC,IAAvC;AACD;AACF;;AAED;;;AAGA,SAAO;AACLlF,UAAMA,IADD;AAELoI,gBAAYA;AAFP,GAAP;AAID,CAjI4B,EAA7B,C,CAXA;;;;kBA8IeU,sB;;;;;;;;;;;;;;AC1If;;;;;;AAEA;;;;;AAKA,IAAIC,uBAAwB,YAAW;AACrC;;;;;AAKA,WAAS/I,IAAT,CAAc2hB,YAAd,EAA4B;AAC1B,QAAIC,eAAeC,oBAAoBF,YAApB,CAAnB;;AAEA1hB,eAAW2hB,YAAX;AACA1hB,iBAAa0hB,YAAb;AACAzhB,aAASyhB,YAAT;AACAxhB,uBAAmBwhB,YAAnB;AACD;;AAED;;;;;AAKA,WAAS3hB,UAAT,CAAoB2hB,YAApB,EAAkC;AAChC,QAAIE,wBAAwBvhB,SAASC,gBAAT,CAC1B,0BAD0B,CAA5B;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIqhB,sBAAsBphB,MAA1C,EAAkDD,GAAlD,EAAuD;AACrD,UAAIE,WAAWmhB,sBAAsBrhB,CAAtB,EAAyBG,YAAzB,CACb,yBADa,CAAf;AAGA,UAAIiD,YAAYie,sBAAsBrhB,CAAtB,EAAyBG,YAAzB,CACd,2BADc,CAAhB;;AAIA,UAAID,YAAY,IAAZ,IAAoBkD,aAAa,IAArC,EAA2C;AACzCie,8BAAsBrhB,CAAtB,EAAyByE,SAAzB,GAAqC0c,YAArC;AACD;AACF;AACF;;AAED;;;;;AAKA,WAAS1hB,YAAT,CAAsB0hB,YAAtB,EAAoC;AAClC,QAAIE,wBAAwBvhB,SAASC,gBAAT,CAC1B,uDACE1C,iBAAO7C,eADT,GAEE,IAHwB,CAA5B;;AAMA,SAAK,IAAIwF,IAAI,CAAb,EAAgBA,IAAIqhB,sBAAsBphB,MAA1C,EAAkDD,GAAlD,EAAuD;AACrD,UAAIoD,YAAYie,sBAAsBrhB,CAAtB,EAAyBG,YAAzB,CACd,2BADc,CAAhB;;AAIA,UAAIiD,aAAa,IAAjB,EAAuB;AACrBie,8BAAsBrhB,CAAtB,EAAyByE,SAAzB,GAAqC0c,YAArC;AACD;AACF;AACF;;AAED;;;;;AAKA,WAASzhB,QAAT,CAAkByhB,YAAlB,EAAgC;AAC9B,QAAI9jB,iBAAO7C,eAAP,IAA0B,IAA9B,EAAoC;AAClC,UAAI6mB,wBAAwBvhB,SAASC,gBAAT,CAC1B,yDACE1C,iBAAO9C,YADT,GAEE,IAHwB,CAA5B;;AAMA,WAAK,IAAIyF,IAAI,CAAb,EAAgBA,IAAIqhB,sBAAsBphB,MAA1C,EAAkDD,GAAlD,EAAuD;AACrD,YAAIE,WAAWmhB,sBAAsBrhB,CAAtB,EAAyBG,YAAzB,CACb,yBADa,CAAf;;AAIA,YAAID,YAAY,IAAhB,EAAsB;AACpBmhB,gCAAsBrhB,CAAtB,EAAyByE,SAAzB,GAAqC0c,YAArC;AACD;AACF;AACF;AACF;;AAED;;;;;AAKA,WAASxhB,kBAAT,CAA4BwhB,YAA5B,EAA0C;AACxC,QAAI1gB,sBACFpD,iBAAO7C,eAAP,IAA0B,EAA1B,IAAgC6C,iBAAO7C,eAAP,IAA0B,IAA1D,GACI6C,iBAAOzC,SAAP,CAAiByC,iBAAO7C,eAAxB,EAAyCD,YAD7C,GAEI,IAHN;;AAKA,QAAI8mB,wBAAwBvhB,SAASC,gBAAT,CAC1B,uDACE1C,iBAAO7C,eADT,GAEE,gCAFF,GAGEiG,mBAHF,GAIE,IALwB,CAA5B;;AAQA,SAAK,IAAIT,IAAI,CAAb,EAAgBA,IAAIqhB,sBAAsBphB,MAA1C,EAAkDD,GAAlD,EAAuD;AACrDqhB,4BAAsBrhB,CAAtB,EAAyByE,SAAzB,GAAqC0c,YAArC;AACD;AACF;;AAED;;;AAGA,WAASxZ,UAAT,GAAsB;AACpB,QAAI0Z,wBAAwBvhB,SAASC,gBAAT,CAC1B,0BAD0B,CAA5B;;AAIA,SAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIqhB,sBAAsBphB,MAA1C,EAAkDD,GAAlD,EAAuD;AACrDqhB,4BAAsBrhB,CAAtB,EAAyByE,SAAzB,GAAqC,OAArC;AACD;AACF;;AAED;;;;;AAKA,WAAS2c,mBAAT,CAA6BF,YAA7B,EAA2C;AACzC,QAAIC,eAAe,OAAnB;;AAEA,QAAI,CAACzd,MAAMwd,aAAajZ,OAAnB,CAAD,IAAgC,CAACvE,MAAMwd,aAAatiB,OAAnB,CAArC,EAAkE;AAChEuiB,qBAAeD,aAAajZ,OAAb,GAAuB,GAAvB,GAA6BiZ,aAAatiB,OAAzD;AACA,UAAI,CAAC8E,MAAMwd,aAAalZ,KAAnB,CAAD,IAA8BkZ,aAAalZ,KAAb,GAAqB,CAAvD,EAA0D;AACxDmZ,uBAAeD,aAAalZ,KAAb,GAAqB,GAArB,GAA2BmZ,YAA1C;AACD;AACF;;AAED,WAAOA,YAAP;AACD;;AAED;;;AAGA,SAAO;AACL5hB,UAAMA,IADD;AAELoI,gBAAYA;AAFP,GAAP;AAID,CArJ0B,EAA3B,C,CAXA;;;;kBAkKeW,oB;;;;;;;AClKf,kBAAkB,mTAAmT,uQAAuQ,gBAAgB,aAAa,6BAA6B,iIAAiI,eAAe,0EAA0E,2IAA2I,4DAA4D,kE", "file": "amplitude.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"Amplitude\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Amplitude\"] = factory();\n\telse\n\t\troot[\"Amplitude\"] = factory();\n})(this, function() {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// identity function for calling harmony imports with the correct context\n \t__webpack_require__.i = function(value) { return value; };\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 47);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap c928b168697f944252b1", "/**\n * These variables make Amplitude run. The config is the most important\n * containing active settings and parameters.\n *\n * The config JSON is the global settings for ALL of Amplitude functions.\n * This is global and contains all of the user preferences. The default\n * settings are set, and the user overwrites them when they initialize\n * Amplitude.\n *\n * @module config\n * @type {object}\n * @property {string}  \tconfig.version          \t\t\t\t- The current version of AmplitudeJS.\n * @property {object} \tconfig.audio \t\t \t\t\t\t\t\t\t\t-\tHandles all of the audio.\n * @property {object} \tconfig.active_metadata\t\t\t\t\t- Contains the active metadata for the song.\n * @property {string} \tconfig.active_album\t\t\t\t\t\t\t- Holds the active album name. Used to check and see if the album changed and run the album changed callback.\n * @property {number} \tconfig.active_index\t\t\t\t\t\t\t- Contains the index of the actively playing song.\n * @property {string} \tconfig.active_playlist\t\t\t\t\t- Contains the key to the active playlist index.\n * @property {number} \tconfig.playback_speed\t\t\t\t\t\t- Sets the initial playback speed of the song. The values for this can be 1.0, 1.5, 2.0\n * @property {object} \tconfig.callbacks\t\t\t\t\t\t\t\t- The user can pass a JSON object with a key => value store of callbacks to be run at certain events.\n * @property {array} \t\tconfig.songs\t\t\t\t\t\t\t\t\t\t- Contains all of the songs the user has passed to Amplitude to use.\n * @property {object} \tconfig.playlists\t\t\t\t\t\t\t\t- Contains all of the playlists the user created.\n * @property {object} \tconfig.start_song \t\t\t\t\t\t\t- The index of the song that AmplitudeJS should start with.\n * @property {string} \tconfig.starting_playlist \t\t\t\t- The starting playlist the player will intiialize to.\n * @property {string} \tconfig.starting_playlist_song \t- The index of the song in the playlist that should be started.\n * @property {boolean} \tconfig.repeat \t\t\t\t\t\t\t\t\t- When repeat is on, when the song ends the song will replay itself.\n * @property {object} \tconfig.shuffle_list\t\t\t\t\t\t\t- When shuffled, gets populated with the songs the user provided in a random order.\n * @property {boolean} \tconfig.shuffle_on\t\t\t\t\t\t\t\t- When on, gets set to true so when traversing through songs, AmplitudeJS knows whether or not to use the songs object or the shuffle_list\n * @property {string}\t\tconfig.default_album_art \t\t\t\t- The user can set default album art to be displayed if the song they set doesn't contain album art.\n * @property {string} \tconfig.default_playlist_art \t\t- The user can set default playlist art to be displayed if the playlist they are setting meta data for doesn't contain an art picture.\n * @property {boolean} \tconfig.debug\t\t\t\t\t\t\t\t\t\t- When set to true, AmplitudeJS will print to the console any errors providing helpful feedback to the user.\n * @property {number} \tconfig.volume \t\t\t\t\t\t\t\t\t- The user can set the initial volume to a number between 0 and 1 over-riding the default of .5\n * @property {number} \tconfig.pre_mute_volume \t\t\t\t\t- This is set on mute so that when a user un-mutes AmplitudeJS knows what to restore the volume to.\n * @property {number}\t\tconfig.volume_increment \t\t\t\t- The default values are an integer between 1 and 100 for how much the volume should increase when the user presses the volume up button.\n * @property {number}\t\tconfig.volume_decrement \t\t\t\t- The default values are an integer between 1 and 100 for how much the volume should decrease when the user presses the volume down button.\n * @property {string} \tconfig.soundcloud_client \t\t\t\t- When using SoundCloud, the user will have to provide their API Client ID\n * @property {boolean} \tconfig.soundcloud_use_art \t\t\t- The user can set this to true and AmplitudeJS will use the album art for the song returned from the Soundcloud API\n * @property {number} \tconfig.soundcloud_song_count \t\t- Used on config to count how many songs are from Soundcloud and compare it to how many are ready for when to move to the rest of the configuration\n * @property {number} \tconfig.soundcloud_songs_ready \t- Used on config to count how many songs are ready so when we get all of the data from the SoundCloud API that we need this should match the SoundCloud song count meaning we can move to the rest of the config.\n * @property {integer}\tconfig.is_touch_moving \t\t\t\t\t- Flag for if the user is moving the screen.\n * @property {boolean}\tconfig.buffered\t\t\t\t\t\t\t\t\t- How much of the song is buffered.\n * @property {object} \tconfig.bindings\t\t\t\t\t\t\t\t\t- Array of bindings to certain key events.\n * @property {boolean} \tconfig.continue_next \t\t\t\t\t\t- Determines when a song ends, we should continue to the next song.\n * @property {number}   config.delay \t\t\t\t\t\t\t\t\t\t- Sets the delay between songs in MS.\n * @property {boolean}  config.use_web_audio_api \t\t\t\t- Flag that determines if the user wants to use Web Audio API Components.\n * @property {boolean}  config.web_audio_api_available  - Flag that determines if the Web Audio API is available.\n * @property {object}  \tconfig.context \t\t\t\t\t\t\t\t\t- Web Audio API Context\n * @property {object}\t\tconfig.source \t\t\t\t\t\t\t\t\t- Web Audio API Source\n * @property {object} \tconfig.analyser \t\t\t\t\t\t\t\t- Web Audio API Analyser\n * @property {string}\t\tconfig.player_state \t\t\t\t\t\t- The current state of the player.\n */\nimport { version } from \"../package.json\";\n\nmodule.exports = {\n  version: version,\n\n  audio: new Audio(),\n\n  active_metadata: {},\n\n  active_album: \"\",\n\n  active_index: 0,\n\n  active_playlist: null,\n\n  playback_speed: 1.0,\n\n  callbacks: {},\n\n  songs: [],\n\n  playlists: {},\n\n  start_song: \"\",\n\n  starting_playlist: \"\",\n\n  starting_playlist_song: \"\",\n\n  repeat: false,\n\n  repeat_song: false,\n\n  shuffle_list: {},\n\n  shuffle_on: false,\n\n  default_album_art: \"\",\n\n  default_playlist_art: \"\",\n\n  debug: false,\n\n  volume: 0.5,\n\n  pre_mute_volume: 0.5,\n\n  volume_increment: 5,\n\n  volume_decrement: 5,\n\n  soundcloud_client: \"\",\n\n  soundcloud_use_art: false,\n\n  soundcloud_song_count: 0,\n\n  soundcloud_songs_ready: 0,\n\n  is_touch_moving: false,\n\n  buffered: 0,\n\n  bindings: {},\n\n  continue_next: true,\n\n  delay: 0,\n\n  player_state: \"stopped\",\n\n  web_audio_api_available: false,\n\n  context: null,\n\n  source: null,\n\n  analyser: null,\n\n  visualizations: {\n    available: [],\n\n    active: [],\n\n    backup: \"\"\n  },\n\n  waveforms: {\n    sample_rate: 100,\n\n    built: []\n  }\n};\n\n\n\n// WEBPACK FOOTER //\n// ./src/config.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the Checks module.\n * @module utilities/checks\n */\nimport Checks from \"../utilities/checks.js\";\n\n/**\n * Imports the Audio Navigation module.\n * @module utilities/audioNavigation\n */\nimport AudioNavigation from \"../utilities/audioNavigation.js\";\n\n/**\n * Imports the Play/Pause Visual Elements module.\n * @module visual/playPauseElements\n */\nimport PlayPauseElements from \"../visual/playPauseElements.js\";\n\n/**\n * Imports the Meta Data Visual Elements module.\n * @module visual/metaDataElements\n */\nimport MetaDataElements from \"../visual/metaDataElements.js\";\n\n/**\n * Imports AmplitudeJS Callback Utility\n * @module utilities/callbacks\n */\nimport Callbacks from \"../utilities/callbacks.js\";\n\n/**\n * Imports AmplitudeJS Debug Utility\n * @module utilities/debug\n */\nimport Debug from \"../utilities/debug.js\";\n\n/**\n * Import the Visualizations from the FX module.\n * @module fx/visualizations\n */\nimport Visualizations from \"../fx/visualizations.js\";\n\n/**\n * Interacts directly with native functions of the Audio element. Logic\n * leading up to these methods are handled by click handlers which call\n * helpers and visual synchronizers. These are the core functions of AmplitudeJS.\n * Every other function that leads to these prepare the information to be\n * acted upon by these functions.\n *\n * @module core/Core\n */\nlet Core = (function() {\n  /**\n   * Plays the active song. If the current song is live, it reconnects\n   * the stream before playing.\n   *\n   * Public Accessor: Amplitude.play()\n   *\n   * @access public\n   */\n  function play() {\n    Visualizations.stop();\n    Visualizations.run();\n\n    /*\n\t\t\tIf the audio is live we re-conenct the stream.\n\t\t*/\n    if (config.active_metadata.live) {\n      reconnectStream();\n    }\n\n    /*\n\t\t\tMobile remote sources need to be reconnected on play. I think this is\n\t\t\tbecause mobile browsers are optimized not to load all resources\n\t\t\tfor speed reasons. We only do this if mobile and the paused button\n\t\t\tis not clicked. If the pause button was clicked then we don't reconnect\n\t\t\tor the user will lose their place in the stream.\n\t\t*/\n    if (\n      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n        navigator.userAgent\n      ) &&\n      !config.paused\n    ) {\n      reconnectStream();\n    }\n\n    /*\n\t\t\tPlay the song and set the playback rate to the playback\n\t\t\tspeed.\n    */\n    let playPromise = config.audio.play();\n\n    if( playPromise !== undefined ){\n      playPromise.then( _ => {\n\n      })\n      .catch( error => {\n\n      });\n    }\n    config.audio.play();\n    config.audio.playbackRate = config.playback_speed;\n  }\n\n  /**\n   * Pauses the active song. If it's live, it disconnects the stream.\n   *\n   * Public Accessor: Amplitude.pause()\n   *\n   * @access public\n   */\n  function pause() {\n    Visualizations.stop();\n\n    /*\n\t\t\tPause the active song.\n\t\t*/\n    config.audio.pause();\n\n    /*\n\t\t\tFlag that pause button was clicked.\n\t\t*/\n    config.paused = true;\n\n    /*\n\t\t\tIf the song is live, we disconnect the stream so we aren't\n\t\t\tsaving it to memory.\n\t\t*/\n    if (config.active_metadata.live) {\n      disconnectStream();\n    }\n  }\n\n  /**\n   * Stops the active song by setting the current song time to 0.\n   * When the user resumes, it will be from the beginning.\n   * If it's a live stream it disconnects.\n   *\n   * Public Accessor: Amplitude.stop()\n   *\n   * @access public\n   */\n  function stop() {\n    Visualizations.stop();\n\n    /*\n\t\t\tSet the current time of the song to 0 which will reset the song.\n\t\t*/\n    if (config.audio.currentTime != 0) {\n      config.audio.currentTime = 0;\n    }\n\n    /*\n\t\t\tRun pause so the song will stop\n\t\t*/\n    config.audio.pause();\n\n    /*\n\t\t\tIf the song is live, disconnect the stream.\n\t\t*/\n    if (config.active_metadata.live) {\n      disconnectStream();\n    }\n\n    /*\n\t\t\tRun the stop callback\n\t\t*/\n    Callbacks.run(\"stop\");\n  }\n\n  /**\n   * Sets the song volume.\n   *\n   * Public Accessor: Amplitude.setVolume( volumeLevel )\n   *\n   * @access public\n   * @param {number} volumeLevel - A number between 1 and 100 as a percentage of\n   * min to max for a volume level.\n   */\n  function setVolume(volumeLevel) {\n    /*\n\t\t\tIf the volume is set to mute somewhere else, we sync the display.\n\t\t*/\n    if (volumeLevel == 0) {\n      config.audio.muted = true;\n    } else {\n      config.audio.muted = false;\n    }\n\n    /*\n\t\t\tSets the volume in the config so we can reference it later on.\n\t\t*/\n    config.volume = volumeLevel;\n\n    /*\n\t\t\tSet the volume of the active song.\n\t\t*/\n    config.audio.volume = volumeLevel / 100;\n  }\n\n  /**\n   * Sets the song percentage. If it's a live song, we ignore this because\n   * we can't skip ahead. This is an issue if you have a playlist with\n   * a live source.\n   *\n   * Public Accessor: Amplitude.setSongLocation( songPercentage )\n   *\n   * @access public\n   * @param {number} songPercentage - A number between 1 and 100 as a percentage of song completion.\n   */\n  function setSongLocation(songPercentage) {\n    /*\n\t\t\tAs long as the song is not live, we can set the current time of the\n\t\t\tsong to the percentage the user passed in.\n\t\t*/\n    if (!config.active_metadata.live) {\n      config.audio.currentTime = config.audio.duration * (songPercentage / 100);\n    }\n  }\n\n  /**\n   * Skips to a location in a song\n   *\n   * Public Accessor: Amplitude.skipToLocation( seconds )\n   *\n   * @access public\n   * @param {number} seconds - An integer containing the seconds to skip to\n   */\n  function skipToLocation(seconds) {\n    /*\n\t\t\tWhen the active song can be played through, we can check to\n\t\t\tsee if the seconds will work. We only bind the event handler\n\t\t\tonce and remove it once it's fired.\n\t\t*/\n    config.audio.addEventListener(\n      \"canplaythrough\",\n      function() {\n        /*\n\t\t\t\tIf the active song duration is greater than or equal to the\n\t\t\t\tamount of seconds the user wants to skip to and the seconds\n\t\t\t\tis greater than 0, we skip to the seconds defined.\n\t\t\t*/\n        if (config.audio.duration >= seconds && seconds > 0) {\n          config.audio.currentTime = seconds;\n        } else {\n          Debug.writeMessage(\n            \"Amplitude can't skip to a location greater than the duration of the audio or less than 0\"\n          );\n        }\n      },\n      { once: true }\n    );\n  }\n\n  /**\n   * Disconnects the live stream\n   *\n   * Public Accessor: Amplitude.disconnectStream()\n   *\n   * @access public\n   */\n  function disconnectStream() {\n    config.audio.src = \"\";\n    config.audio.load();\n  }\n\n  /**\n   * Reconnects the live stream\n   *\n   * Public Accessor: Amplitude.reconnectStream()\n   *\n   * @access public\\\n   */\n  function reconnectStream() {\n    config.audio.src = config.active_metadata.url;\n    config.audio.load();\n  }\n\n  /**\n   * Sets the playback speed for the song.\n   *\n   * @param {number} playbackSpeed The speed we want the song to play back at.\n   */\n  function setPlaybackSpeed(playbackSpeed) {\n    /*\n\t\t\tSet the config playback speed.\n\t\t*/\n    config.playback_speed = playbackSpeed;\n\n    /*\n\t\t\tSet the active song playback rate.\n\t\t*/\n    config.audio.playbackRate = config.playback_speed;\n  }\n\n  /*\n\t\tReturn publically facing functions\n\t*/\n  return {\n    play: play,\n    pause: pause,\n    stop: stop,\n    setVolume: setVolume,\n    setSongLocation: setSongLocation,\n    skipToLocation: skipToLocation,\n    disconnectStream: disconnectStream,\n    reconnectStream: reconnectStream,\n    setPlaybackSpeed: setPlaybackSpeed\n  };\n})();\n\nexport default Core;\n\n\n\n// WEBPACK FOOTER //\n// ./src/core/core.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Defines the visual representation of AmplitudeJS play pause elements.\n * @module visual/PlayPauseElements\n */\nlet PlayPauseElements = (function() {\n  /**\n   * Syncs all play pause elements.\n   *\n   * @access public\n   */\n  function sync() {\n    syncGlobal();\n    syncPlaylist();\n    syncSong();\n    syncSongInPlaylist();\n  }\n\n  /**\n   * Syncs the global play pause buttons to the state of the active song.\n   *\n   * @access public\n   */\n  function syncGlobal() {\n    /*\n      Get the active song state.\n    */\n    let state = config.audio.paused ? \"paused\" : \"playing\";\n\n    /*\n      Get all play pause buttons.\n    */\n    const playPauseElements = document.querySelectorAll(\n      \".amplitude-play-pause\"\n    );\n\n    /*\n      Iterate over all of the play pause elements syncing the\n      display visually.\n    */\n    for (let i = 0; i < playPauseElements.length; i++) {\n      /*\n        Grab the playlist and song attributes from the element.\n      */\n      let playlist = playPauseElements[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n      let song = playPauseElements[i].getAttribute(\"data-amplitude-song-index\");\n\n      /*\n        This method is responsible for only the global elements,\n        so we make sure there are no playlist or songs defined on\n        the element.\n      */\n      if (playlist == null && song == null) {\n        /*\n          Determines what classes we should add and remove\n          from the elements.\n        */\n        switch (state) {\n          case \"playing\":\n            setElementPlay(playPauseElements[i]);\n            break;\n          case \"paused\":\n            setElementPause(playPauseElements[i]);\n            break;\n        }\n      }\n    }\n  }\n\n  /**\n   * Syncs the main playlist play pause buttons to the state of the active song.\n   *\n   * @access public\n   */\n  function syncPlaylist() {\n    let state = config.audio.paused ? \"paused\" : \"playing\";\n\n    /*\n      Get all of the main playlist play pause elements\n    */\n    const playlistPlayPauseElements = document.querySelectorAll(\n      '.amplitude-play-pause[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"]'\n    );\n\n    /*\n      Iterate over the play pause elements, syncing the state accordingly.\n    */\n    for (let i = 0; i < playlistPlayPauseElements.length; i++) {\n      /*\n        Grab the song attributes from the element.\n      */\n      let song = playlistPlayPauseElements[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      /*\n        We want only the play pause elements for the main on a\n        playlist nothing else. We have another method for the\n        song in playlist play pause method.\n      */\n      if (song == null) {\n        /*\n          Determines what classes we should add and remove\n          from the elements.\n        */\n        switch (state) {\n          case \"playing\":\n            setElementPlay(playlistPlayPauseElements[i]);\n            break;\n          case \"paused\":\n            setElementPause(playlistPlayPauseElements[i]);\n            break;\n        }\n      }\n    }\n  }\n\n  /**\n   * Syncs the song play pause buttons to the state of the active song.\n   *\n   * @access public\n   */\n  function syncSong() {\n    let state = config.audio.paused ? \"paused\" : \"playing\";\n\n    /*\n      Get all of the individual song play pause buttons. These have an\n      amplitude-song-index that matches the active index attribute.\n    */\n    let songPlayPauseElements = document.querySelectorAll(\n      '.amplitude-play-pause[data-amplitude-song-index=\"' +\n        config.active_index +\n        '\"]'\n    );\n\n    /*\n      Iterate over all of the song play pause elements\n    */\n    for (let i = 0; i < songPlayPauseElements.length; i++) {\n      /*\n        Grab the playlist attributes from the element.\n      */\n      let playlist = songPlayPauseElements[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n\n      /*\n        We want only the song play pause buttons, not ones scoped in a playlist.\n      */\n      if (playlist == null) {\n        /*\n          Determines what classes we should add and remove\n          from the elements.\n        */\n        switch (state) {\n          case \"playing\":\n            setElementPlay(songPlayPauseElements[i]);\n            break;\n          case \"paused\":\n            setElementPause(songPlayPauseElements[i]);\n            break;\n        }\n      }\n    }\n  }\n\n  /**\n   * Syncs the song in playlist play pause buttons to the state of\n   * the active song.\n   *\n   * @access public\n   */\n  function syncSongInPlaylist() {\n    let state = config.audio.paused ? \"paused\" : \"playing\";\n\n    let activePlaylistIndex =\n      config.active_playlist != \"\" && config.active_playlist != null\n        ? config.playlists[config.active_playlist].active_index\n        : null;\n\n    /*\n      Get all of the individual song play pause buttons. These have an\n      amplitude-song-index attribute. Some have amplitude-playlist which\n      means they are individual songs within a playlist.\n    */\n    let songInPlaylistPlayPauseElements = document.querySelectorAll(\n      '.amplitude-play-pause[data-amplitude-song-index=\"' +\n        activePlaylistIndex +\n        '\"][data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"]'\n    );\n\n    /*\n      Iterate over all of the individual play pause elements for songs inspect\n      a playlist.\n    */\n    for (let i = 0; i < songInPlaylistPlayPauseElements.length; i++) {\n      /*\n        Determines what classes we should add and remove\n        from the elements.\n      */\n      switch (state) {\n        case \"playing\":\n          setElementPlay(songInPlaylistPlayPauseElements[i]);\n          break;\n        case \"paused\":\n          setElementPause(songInPlaylistPlayPauseElements[i]);\n          break;\n      }\n    }\n  }\n\n  /**\n   * Sets all of the play pause buttons to paused.\n   *\n   * @access public\n   */\n  function syncToPause() {\n    /*\n      Gets all of the play pause elements\n    */\n    let playPauseElements = document.querySelectorAll(\".amplitude-play-pause\");\n\n    /*\n      Sets all of the elements to pause\n    */\n    for (let i = 0; i < playPauseElements.length; i++) {\n      setElementPause(playPauseElements[i]);\n    }\n  }\n\n  /**\n   * Sets an element to be playing by removing the 'amplitude-paused' class\n   * and adding the 'amplitude-playing' class\n   *\n   * @access public\n   * @param {element} element \t- The element getting the playing class added.\n   */\n  function setElementPlay(element) {\n    element.classList.add(\"amplitude-playing\");\n    element.classList.remove(\"amplitude-paused\");\n  }\n\n  /**\n   * Sets an element to be paused by adding the 'amplitude-paused' class\n   * and removing the 'amplitude-playing' class\n   *\n   * @access public\n   * @param {element} element \t- The element getting the paused class added.\n   */\n  function setElementPause(element) {\n    element.classList.remove(\"amplitude-playing\");\n    element.classList.add(\"amplitude-paused\");\n  }\n\n  /**\n   * Returns the public facing methods\n   */\n  return {\n    sync: sync,\n    syncGlobal: syncGlobal,\n    syncPlaylist: syncPlaylist,\n    syncSong: syncSong,\n    syncSongInPlaylist: syncSongInPlaylist,\n    syncToPause: syncToPause\n  };\n})();\n\nexport default PlayPauseElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/playPauseElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the Core Module\n *\n * @module core/Core\n */\nimport Core from \"../core/core.js\";\n\n/**\n * Imports the Callbacks Module\n *\n * @module utilities/Callbacks\n */\nimport Callbacks from \"../utilities/callbacks.js\";\n\n/**\n * Imports the Checks Module\n *\n * @module utilities/Checks\n */\nimport Checks from \"../utilities/checks.js\";\n\n/**\n * Imports the Play Pause Elements Module\n *\n * @module visual/PlayPauseElements\n */\nimport PlayPauseElements from \"../visual/playPauseElements.js\";\n\n/**\n * Imports the Song Slider Elements Module\n *\n * @module visual/SongSliderElements\n */\nimport SongSliderElements from \"../visual/songSliderElements.js\";\n\n/**\n * Imports the Song Played Progress Elements Module\n *\n * @module visual/SongPlayedProgressElements\n */\nimport SongPlayedProgressElements from \"../visual/songPlayedProgressElements.js\";\n\n/**\n * Imports the Time Elements Module\n *\n * @module visual/TimeElements\n */\nimport TimeElements from \"../visual/timeElements.js\";\n\n/**\n * Meta Data Elements Module\n *\n * @module visual/MetaDataElements\n */\nimport MetaDataElements from \"../visual/metaDataElements.js\";\n\n/**\n * Container Elements Module\n *\n * @module visual/ContainerElements\n */\nimport ContainerElements from \"../visual/containerElements.js\";\n\n/**\n * AmplitudeJS Audio Navigation Utility.\n *\n * @module utilities/AudioNavigation\n */\nlet AudioNavigation = (function() {\n  /**\n   * Sets the next song\n   *\n   * @access public\n   * @param {boolean} [songEnded=false] If the song ended, this is set to true\n   * so we take into effect the repeat setting.\n   */\n  function setNext(songEnded = false) {\n    /*\n      Initializes the next index variable. This will be the\n      index of the song that is next.\n    */\n    let nextIndex = null;\n    let nextSong = {};\n\n    /*\n      Ensure we don't loop in the playlist if config.repeat is not true\n    */\n    let endOfList = false;\n\n    /*\n      Determines if we are repeating the song or not. If we are repeating,\n      the next song will be the same song index.\n    */\n    if (config.repeat_song) {\n      /*\n        If the playlist is shuffled, get the now playing index.\n      */\n      if (config.shuffle_on) {\n        nextIndex = config.shuffle_list[ config.active_index ].index;\n        nextSong = config.shuffle_list[nextIndex];\n      } else {\n        nextIndex = config.active_index;\n        nextSong = config.songs[nextIndex];\n      }\n    } else {\n      /*\n        If the shuffle is on, we use the shuffled list of\n        songs to determine our next song.\n      */\n      if (config.shuffle_on) {\n        /*\n          If the active shuffle index + 1 is less than the length, then\n          we use the next shuffle otherwise we go to the beginning\n          of the shuffle list.\n        */\n        if (parseInt(config.active_index) + 1 < config.shuffle_list.length) {\n          /*\n            Set the next index to be the index of the song in the shuffle list.\n          */\n          nextIndex = parseInt( config.active_index ) + 1;\n        } else {\n          nextIndex = 0\n          endOfList = true;\n        }\n\n        nextSong = config.shuffle_list[ nextIndex ];\n      } else {\n        /*\n          If the active index + 1 is less than the length of the songs, then\n          we use the next song otherwise we go to the beginning of the\n          song list.\n        */\n        if (parseInt(config.active_index) + 1 < config.songs.length) {\n          nextIndex = parseInt(config.active_index) + 1;\n        } else {\n          nextIndex = 0;\n          endOfList = true;\n        }\n\n        /*\n          Sets the next index.\n        */\n        nextSong = config.songs[nextIndex];\n      }\n    }\n\n    /*\n      Change the song after the next button has been pressed.\n    */\n    changeSong(nextSong, nextIndex);\n\n    /*\n   \t\tIf it's the end of the list and repeat is not on, do nothing.\n   \t*/\n    if (endOfList && !config.repeat) {\n    } else {\n      /*\n   \t\t\tIf the song has ended and repeat is on, play the song.\n   \t\t*/\n      if (!(songEnded && !config.repeat && endOfList)) {\n        Core.play();\n      }\n    }\n\n    /*\n      Sync the play pause elements and run the\n      after next callback.\n    */\n    PlayPauseElements.sync();\n    Callbacks.run(\"next\");\n\n    /*\n      If we repeated the song, run the repeat song callback.\n    */\n    if (config.repeat_song) {\n      Callbacks.run(\"song_repeated\");\n    }\n  }\n\n  /**\n   * Sets the next song in a playlist\n   *\n   * @param {string} playlist - The playlist being shuffled\n   * @param {boolean} [songEnded=false] - If the song ended, this is set to true\n   * so we take into effect the repeat setting.\n   */\n  function setNextPlaylist(playlist, songEnded = false) {\n    /*\n      Initializes the next index\n    */\n    let nextIndex = null;\n    let nextSong = {};\n\n    /*\n      Ensure we don't loop in the playlist if config.repeat is not true\n    */\n    let endOfList = false;\n\n    /*\n      If we are repeating the song, then we just start the song over.\n    */\n    if (config.repeat_song) {\n      /*\n        If the playlist is shuffled, get the now playing index.\n      */\n      if (config.playlists[playlist].shuffle) {\n        nextIndex = config.playlists[playlist].active_index;\n        nextSong = config.playlists[playlist].shuffle_list[nextIndex];\n      } else {\n        nextIndex = config.playlists[playlist].active_index;\n        nextSong = config.playlists[playlist].songs[nextIndex];\n      }\n    } else {\n      /*\n        If the playlist is shuffled we get the next index of the playlist.\n      */\n      if (config.playlists[playlist].shuffle) {\n        /*\n          If the active shuffle index + 1 is less than the length of the shuffle list,\n          then we use the next shuffle otherwise we go to the beginning of the shuffle list.\n        */\n        if (\n          parseInt(config.playlists[playlist].active_index) + 1 <\n          config.playlists[playlist].shuffle_list.length\n        ) {\n          /*\n            Set the next index to be the index of the song in the shuffle list.\n          */\n          nextIndex = config.playlists[playlist].active_index + 1;\n        } else {\n          nextIndex = 0;\n          endOfList = true;\n        }\n\n        nextSong = config.playlists[playlist].shuffle_list[nextIndex];\n      } else {\n        /*\n          If the active index +1 is less than the length of the songs in the playlist,\n          then we use the next song otherwise we go to the beginning of the playlist.\n        */\n        if (\n          parseInt(config.playlists[playlist].active_index) + 1 <\n          config.playlists[playlist].songs.length\n        ) {\n          nextIndex = parseInt(config.playlists[playlist].active_index) + 1;\n        } else {\n          nextIndex = 0;\n          endOfList = true;\n        }\n\n        /*\n          Sets the next song.\n        */\n        nextSong = config.playlists[playlist].songs[nextIndex];\n      }\n    }\n\n    /*\n      Sets the active playlist to the playlist we are on.\n    */\n    setActivePlaylist(playlist);\n\n    /*\n      Change the song within the playlist.\n    */\n    changeSongPlaylist(playlist, nextSong, nextIndex);\n\n    /*\n      If it's the end of the playlist and we aren't repeating, do nothing.\n    */\n    if (endOfList && !config.repeat) {\n    } else {\n      if (!(songEnded && !config.repeat && endOfList)) {\n        Core.play();\n      }\n    }\n\n    /*\n      Sync the play pause buttons.\n    */\n    PlayPauseElements.sync();\n    Callbacks.run(\"next\");\n\n    /*\n      Repeat the song.\n    */\n    if (config.repeat_song) {\n      Callbacks.run(\"song_repeated\");\n    }\n  }\n\n  /**\n   * Sets the previous song on the global songs array.\n   *\n   * @access private\n   */\n  function setPrevious() {\n    /*\n      Initializes the previous index\n    */\n    let previousIndex = null;\n    let previousSong = {};\n\n    /*\n      If we are repeating the song, then we just start the song over.\n    */\n    if (config.repeat_song) {\n      /*\n        If the config is shuffled, get the now playing index.\n      */\n      if (config.shuffle_on) {\n        previousIndex = config.active_index;\n        previousSong = config.shuffle_list[previousIndex];\n      } else {\n        previousIndex = config.active_index;\n        previousSong = config.songs[previousIndex];\n      }\n    } else {\n      /*\n        Get the previous index. If the previous index will be less than 0, get the\n        last song of the array and continue.\n      */\n      if (parseInt(config.active_index) - 1 >= 0) {\n        previousIndex = parseInt(config.active_index - 1);\n      } else {\n        previousIndex = parseInt(config.songs.length - 1);\n      }\n\n      /*\n        If the config is shuffled, we grab the song from the shuffle list\n      */\n      if (config.shuffle_on) {\n        /*\n          Grab song from the shuffle list\n        */\n        previousSong = config.shuffle_list[previousIndex];\n      } else {\n        /*\n          Grab song from the songs array\n        */\n        previousSong = config.songs[previousIndex];\n      }\n    }\n    /*\n      Change the song after the next button has been pressed.\n    */\n    changeSong(previousSong, previousIndex);\n\n    /*\n      Play the newest song.\n    */\n    Core.play();\n\n    /*\n      Sync the play pause elements and run the\n      after next callback.\n    */\n    PlayPauseElements.sync();\n    Callbacks.run(\"prev\");\n\n    /*\n      If we repeated the song, run the repeat song callback.\n    */\n    if (config.repeat_song) {\n      Callbacks.run(\"song_repeated\");\n    }\n  }\n\n  /**\n   * Sets the previous playlist song.\n   *\n   * @access private\n   *\n   * @prop {string} playlist  - The playlist we are navigating in.\n   */\n  function setPreviousPlaylist(playlist) {\n    /*\n      Initializes the previous index\n    */\n    let previousIndex = null;\n    let previousSong = {};\n\n    /*\n      If we are repeating the song, then we just start the song over.\n    */\n    if (config.repeat_song) {\n      /*\n        If the playlist is shuffled, get the now playing index.\n      */\n      if (config.playlists[playlist].shuffle) {\n        previousIndex = config.playlists[playlist].active_index;\n        previousSong = config.playlists[playlist].shuffle_list[previousIndex];\n      } else {\n        previousIndex = config.playlists[playlist].active_index;\n        previousSong = config.playlists[playlist].songs[previousIndex];\n      }\n    } else {\n      /*\n        Get the previous index. If the previous index will be less than 0, get the\n        last song of the array and continue.\n      */\n      if (parseInt(config.playlists[playlist].active_index) - 1 >= 0) {\n        previousIndex = parseInt(config.playlists[playlist].active_index - 1);\n      } else {\n        previousIndex = parseInt(config.playlists[playlist].songs.length - 1);\n      }\n\n      /*\n        If the playlist is shuffled, we grab the song from the shuffle list\n      */\n      if (config.playlists[playlist].shuffle) {\n        /*\n          Grab song from the shuffle list\n        */\n        previousSong = config.playlists[playlist].shuffle_list[previousIndex];\n      } else {\n        /*\n          Grab song from the songs array\n        */\n        previousSong = config.playlists[playlist].songs[previousIndex];\n      }\n    }\n\n    /*\n      Sets the active playlist to the playlist we are on.\n    */\n    setActivePlaylist(playlist);\n\n    /*\n      Change the song within the playlist.\n    */\n    changeSongPlaylist(playlist, previousSong, previousIndex);\n\n    /*\n      Plays the song\n    */\n    Core.play();\n\n    /*\n      Sync the play pause buttons.\n    */\n    PlayPauseElements.sync();\n    Callbacks.run(\"prev\");\n\n    /*\n      Repeat the song.\n    */\n    if (config.repeat_song) {\n      Callbacks.run(\"song_repeated\");\n    }\n  }\n\n  /**\n   * Change song in the songs array.\n   *\n   * @access private\n   * @prop {object} song  - The song we are changing to.\n   * @prop {number} index - The index we are changing to.\n   */\n  function changeSong(song, index) {\n    /*\n      Prepare the song change.\n    */\n    prepareSongChange(song);\n\n    /*\n      Change the song.\n    */\n    config.audio.src = song.url;\n    config.active_metadata = song;\n    config.active_album = song.album;\n    \n    config.active_index = parseInt(index);\n\n    /*\n      Set new information now that the song has changed.\n    */\n    afterSongChange();\n  }\n\n  /**\n   * Handles a song change in the playlist\n   *\n   * @access private\n   * @prop {string} playlist - The playlist we are changing the song on.\n   * @prop {object} song     - The song we are changing to in the playlist.\n   * @prop {number} index    - The inded of the song we are changing to in the playlist.\n   */\n  function changeSongPlaylist(playlist, song, index) {\n    /*\n      Prepare the song change.\n    */\n    prepareSongChange(song);\n\n    /*\n      Change the song.\n    */\n    config.audio.src = song.url;\n    config.active_metadata = song;\n    config.active_album = song.album;\n    config.active_index = null;\n\n    config.playlists[playlist].active_index = parseInt(index);\n\n    /*\n      Set new information now that the song has changed.\n    */\n    afterSongChange();\n  }\n\n  /**\n   *  Prepares a song change\n   *\n   * @access private\n   * @prop {object} song  - The song we change to.\n   */\n  function prepareSongChange(song) {\n    /*\n      Stop the current song.\n    */\n    Core.stop();\n\n    /*\n      Sync all of the elements to a stopped song.\n    */\n    PlayPauseElements.syncToPause();\n    SongSliderElements.resetElements();\n    SongPlayedProgressElements.resetElements();\n    TimeElements.resetCurrentTimes();\n\n    /*\n      If an album changes, fire an album change.\n    */\n    if (Checks.newAlbum(song)) {\n      Callbacks.run(\"album_change\");\n    }\n  }\n\n  /**\n   * Updates data on the display after a song has changed.\n   *\n   * @access private\n   */\n  function afterSongChange() {\n    MetaDataElements.displayMetaData();\n    ContainerElements.setActive();\n    TimeElements.resetDurationTimes();\n\n    /*\n      Run the song change callback.\n    */\n    Callbacks.run(\"song_change\");\n  }\n\n  /**\n   * Sets the active playlist\n   *\n   * @access public\n   * @param {string} playlist - The string of the playlist being set to active.\n   */\n  function setActivePlaylist(playlist) {\n    /*\n      If the active playlist is different than the playlist being set,\n      we run the `playlist_changed` callback.\n    */\n    if (config.active_playlist != playlist) {\n      Callbacks.run(\"playlist_changed\");\n      /*\n        Set the active playlist to the playlist parameter. Only need to\n        set if it's different.\n      */\n      config.active_playlist = playlist;\n\n      if (playlist != null) {\n        config.playlists[playlist].active_index = 0;\n      }\n    }\n  }\n\n  /*\n    Return the publically facing methods\n  */\n  return {\n    setNext: setNext,\n    setNextPlaylist: setNextPlaylist,\n    setPrevious: setPrevious,\n    setPreviousPlaylist: setPreviousPlaylist,\n    changeSong: changeSong,\n    changeSongPlaylist: changeSongPlaylist,\n    setActivePlaylist: setActivePlaylist\n  };\n})();\n\nexport default AudioNavigation;\n\n\n\n// WEBPACK FOOTER //\n// ./src/utilities/audioNavigation.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Handles the debugging of AmplitudeJS\n * @module utilities/Debug\n */\nlet Debug = (function() {\n  /**\n   * Writes out debug message to the console if enabled.\n   *\n   * Public Accessor: Debug.writeMessage( message )\n   *\n   * @access public\n   * @param {string} message - The string that gets printed to alert the user of a debugging error.\n   */\n  function writeMessage(message) {\n    /*\n      If the user has flagged AmplitudeJS to debug, we print out a message\n      to the console.\n    */\n    if (config.debug) {\n      console.log(message);\n    }\n  }\n\n  /*\n    Returns the public facing methods\n  */\n  return {\n    writeMessage: writeMessage\n  };\n})();\n\nexport default Debug;\n\n\n\n// WEBPACK FOOTER //\n// ./src/utilities/debug.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * AmplitudeJS Checks Module. Checks for new songs, albums, and playlists\n *\n * @module utilities/Checks\n */\nlet Checks = (function() {\n  /**\n   * Checks to see if the new song to be played is different than the song\n   * that is currently playing. To be true, the user would have selected\n   * play on a new song with a new index. To be false, the user would have\n   * clicked play/pause on the song that was playing.\n   *\n   * Public Accessor: Checks.newSong( playlist, songIndex )\n   * @access public\n   * @param {string} playlist - The playlist we are checking the new song for. Could be null\n   * @param {number} songIndex - The index of the new song to be played.\n   * @returns {boolean} True if we are setting a new song, false if we are not setting a new song.\n   */\n  function newSong(playlist, songIndex) {\n    /*\n      If the playlists don't match, then it's definitely a new song.\n    */\n    if (config.active_playlist != playlist) {\n      return true;\n    } else {\n      /*\n        If we aren't in a playlist, we check the active index.\n      */\n      if (config.active_playlist == null && playlist == null) {\n        /*\n          If the active indexes don't match, then it's a new song.\n        */\n        if (config.active_index != songIndex) {\n          return true;\n        } else {\n          return false;\n        }\n      } else {\n        /*\n          If we are in a playlist, then we check to see if the\n          new song index matches the active index.\n        */\n        if (\n          config.active_playlist == playlist &&\n          config.playlists[playlist].active_index != songIndex\n        ) {\n          return true;\n        } else {\n          return false;\n        }\n      }\n    }\n  }\n\n  /**\n   * Checks to see if there is a new album\n   *\n   * Public Accessor: Checks.newAlbum( album )\n   *\n   * @access public\n   * @param {string} album - Checks to see if the new song will have a new album.\n   * @returns {boolean} True if there is a new album, false if there is not a new ablum.\n   */\n  function newAlbum(album) {\n    if (config.active_album != album) {\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  /**\n   * Checks to see if there is a new playlist\n   *\n   * Public Accessor: Checks.newPlaylist( playlist )\n   *\n   * @access public\n   * @param {string} playlist - The playlist passed in to check against the active playlist.\n   * @returns {boolean} True if there is a new playlist, false if there is not a new playlist.\n   */\n  function newPlaylist(playlist) {\n    if (config.active_playlist != playlist) {\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  /**\n   * Determines if the string passed in is a URL or not\n   *\n   * Public Accessor: AmplitudeHelpers.isURL( url )\n   *\n   * @access public\n   * @param {string} url - The string we are testing to see if it's a URL.\n   * @returns {boolean} True if the string is a url, false if it is not.\n   */\n  function isURL(url) {\n    /*\n\t\t\tTest the string against the URL pattern and return if it matches\n\t\t*/\n    let pattern = /(ftp|http|https):\\/\\/(\\w+:{0,1}\\w*@)?(\\S+)(:[0-9]+)?(\\/|\\/([\\w#!:.?+=&%@!\\-\\/]))?/;\n\n    return pattern.test(url);\n  }\n\n  /**\n   * Determines if what is passed in is an integer or not.\n   *\n   * Public Accessor: AmplitudeHelpers.isInt( int )\n   *\n   * @access public\n   * @param {string|number} int - The variable we are testing to see is an integer or not.\n   * @returns {boolean} If the variable is an integer or not.\n   */\n  function isInt(int) {\n    return (\n      !isNaN(int) && parseInt(Number(int)) == int && !isNaN(parseInt(int, 10))\n    );\n  }\n\n  /**\n   * Returns public facing methods\n   */\n  return {\n    newSong: newSong,\n    newAlbum: newAlbum,\n    newPlaylist: newPlaylist,\n    isURL: isURL,\n    isInt: isInt\n  };\n})();\n\nexport default Checks;\n\n\n\n// WEBPACK FOOTER //\n// ./src/utilities/checks.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Handles the state of the config object.\n *\n * @module utilities/ConfigState\n */\nlet ConfigState = (function() {\n  /**\n   * Resets the config to the default state. This is called on initialize\n   * to ensure the user's config is what matters.\n   *\n   * Public Accessor: AmplitudeHelpers.resetConfig()\n   *\n   * @access public\n   */\n  function resetConfig() {\n    config.audio = new Audio();\n    config.active_metadata = {};\n    config.active_album = \"\";\n    config.active_index = 0;\n    config.active_playlist = null;\n    config.playback_speed = 1.0;\n    config.callbacks = {};\n    config.songs = [];\n    config.playlists = {};\n    config.start_song = \"\";\n    config.starting_playlist = \"\";\n    config.starting_playlist_song = \"\";\n    config.repeat = false;\n    config.shuffle_list = {};\n    config.shuffle_on = false;\n    config.default_album_art = \"\";\n    config.default_playlist_art = \"\";\n    config.debug = false;\n    config.volume = 0.5;\n    config.pre_mute_volume = 0.5;\n    config.volume_increment = 5;\n    config.volume_decrement = 5;\n    config.soundcloud_client = \"\";\n    config.soundcloud_use_art = false;\n    config.soundcloud_song_count = 0;\n    config.soundcloud_songs_ready = 0;\n    config.continue_next = true;\n  }\n\n  /**\n   * Sets the state of the player.\n   */\n  function setPlayerState() {\n    /*\n      If paused and the current time is 0 the player is stopped.\n    */\n    if (config.audio.paused && config.audio.currentTime == 0) {\n      config.player_state = \"stopped\";\n    }\n\n    /*\n      If paused and the current time is greater than 0 the player is\n      paused.\n    */\n    if (config.audio.paused && config.audio.currentTime > 0) {\n      config.player_state = \"paused\";\n    }\n\n    /*\n      If playing, the current state is playing.\n    */\n    if (!config.audio.paused) {\n      config.player_state = \"playing\";\n    }\n  }\n\n  /*\n\t\tReturns the public facing methods\n\t*/\n  return {\n    resetConfig: resetConfig,\n    setPlayerState: setPlayerState\n  };\n})();\n\nexport default ConfigState;\n\n\n\n// WEBPACK FOOTER //\n// ./src/utilities/configState.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the debug module\n * @module utilities/debug\n */\nimport Debug from \"./debug.js\";\n\n/**\n * AmplitudeJS Callback Utility\n *\n * @module utilities/callbacks\n */\nlet Callbacks = (function() {\n  /**\n   * Initializes the callbacks for the player.\n   */\n  function initialize() {\n    /*\n      Event: abort\n      https://www.w3schools.com/tags/av_event_abort.asp\n    */\n    config.audio.addEventListener(\"abort\", function() {\n      run(\"abort\");\n    });\n\n    /*\n      Event: error\n      https://www.w3schools.com/tags/av_event_error.asp\n    */\n    config.audio.addEventListener(\"error\", function() {\n      run(\"error\");\n    });\n\n    /*\n      Event: loadeddata\n      https://www.w3schools.com/tags/av_event_loadeddata.asp\n    */\n    config.audio.addEventListener(\"loadeddata\", function() {\n      run(\"loadeddata\");\n    });\n\n    /*\n      Event: loadedmetadata\n      https://www.w3schools.com/tags/av_event_loadedmetadata.asp\n    */\n    config.audio.addEventListener(\"loadedmetadata\", function() {\n      run(\"loadedmetadata\");\n    });\n\n    /*\n      Event: loadstart\n      https://www.w3schools.com/tags/av_event_loadstart.asp\n    */\n    config.audio.addEventListener(\"loadstart\", function() {\n      run(\"loadstart\");\n    });\n\n    /*\n      Event: pause\n      https://www.w3schools.com/tags/av_event_pause.asp\n    */\n    config.audio.addEventListener(\"pause\", function() {\n      run(\"pause\");\n    });\n\n    /*\n      Event: playing\n      https://www.w3schools.com/tags/av_event_playing.asp\n    */\n    config.audio.addEventListener(\"playing\", function() {\n      run(\"playing\");\n    });\n\n    /*\n      Event: play\n      https://www.w3schools.com/tags/av_event_play.asp\n    */\n    config.audio.addEventListener(\"play\", function() {\n      run(\"play\");\n    });\n\n    /*\n      Event: progress\n      https://www.w3schools.com/tags/av_event_progress.asp\n    */\n    config.audio.addEventListener(\"progress\", function() {\n      run(\"progress\");\n    });\n\n    /*\n      Event: ratechange\n      https://www.w3schools.com/tags/av_event_ratechange.asp\n    */\n    config.audio.addEventListener(\"ratechange\", function() {\n      run(\"ratechange\");\n    });\n\n    /*\n      Event: seeked\n      https://www.w3schools.com/tags/av_event_seeked.asp\n    */\n    config.audio.addEventListener(\"seeked\", function() {\n      run(\"seeked\");\n    });\n\n    /*\n      Event: seeking\n      https://www.w3schools.com/tags/av_event_seeking.asp\n    */\n    config.audio.addEventListener(\"seeking\", function() {\n      run(\"seeking\");\n    });\n\n    /*\n      Event: stalled\n      https://www.w3schools.com/tags/av_event_stalled.asp\n    */\n    config.audio.addEventListener(\"stalled\", function() {\n      run(\"stalled\");\n    });\n\n    /*\n      Event: suspend\n      https://www.w3schools.com/tags/av_event_suspend.asp\n    */\n    config.audio.addEventListener(\"suspend\", function() {\n      run(\"suspend\");\n    });\n\n    /*\n      Event: timeupdate\n      https://www.w3schools.com/tags/av_event_timeupdate.asp\n    */\n    config.audio.addEventListener(\"timeupdate\", function() {\n      run(\"timeupdate\");\n    });\n\n    /*\n      Event: volumechange\n      https://www.w3schools.com/tags/av_event_volumechange.asp\n    */\n    config.audio.addEventListener(\"volumechange\", function() {\n      run(\"volumechange\");\n    });\n\n    /*\n      Event: waiting\n      https://www.w3schools.com/tags/av_event_waiting.asp\n    */\n    config.audio.addEventListener(\"waiting\", function() {\n      run(\"waiting\");\n    });\n\n    /*\n      Event: canplay\n      https://www.w3schools.com/tags/av_event_canplay.asp\n    */\n    config.audio.addEventListener(\"canplay\", function() {\n      run(\"canplay\");\n    });\n\n    /*\n      Event: canplaythrough\n      https://www.w3schools.com/tags/av_event_canplaythrough.asp\n    */\n    config.audio.addEventListener(\"canplaythrough\", function() {\n      run(\"canplaythrough\");\n    });\n\n    /*\n      Event: durationchange\n      https://www.w3schools.com/tags/av_event_durationchange.asp\n    */\n    config.audio.addEventListener(\"durationchange\", function() {\n      run(\"durationchange\");\n    });\n\n    /*\n      Event: ended\n      https://www.w3schools.com/tags/av_event_ended.asp\n    */\n    config.audio.addEventListener(\"ended\", function() {\n      run(\"ended\");\n    });\n  }\n\n  /**\n   * Runs a user defined callback method\n   *\n   * Public Accessor: Callbacks.run( callbackName )\n   *\n   * @access public\n   * @param {string} callbackName - The name of the callback we are going to run.\n   */\n  function run(callbackName) {\n    /*\n      Checks to see if a user defined a callback method for the\n      callback we are running.\n    */\n    if (config.callbacks[callbackName]) {\n      /*\n        Build the callback function\n      */\n      let callbackFunction = config.callbacks[callbackName];\n\n      /*\n        Write a debug message stating the callback we are running\n      */\n      Debug.writeMessage(\"Running Callback: \" + callbackName);\n\n      /*\n        Run the callback function and catch any errors\n      */\n      try {\n        callbackFunction();\n      } catch (error) {\n        if (error.message == \"CANCEL EVENT\") {\n          throw error;\n        } else {\n          Debug.writeMessage(\"Callback error: \" + error.message);\n        }\n      }\n    }\n  }\n\n  return {\n    initialize: initialize,\n    run: run\n  };\n})();\n\nexport default Callbacks;\n\n\n\n// WEBPACK FOOTER //\n// ./src/utilities/callbacks.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * These methods help display the audio's meta data\n *\n * @module visual/MetaDataElements\n */\nlet MetaDataElements = (function() {\n  /**\n   * Displays the active song's metadata. This is called after a song has\n   * been changed. This method takes the active song and displays the\n   * metadata. So once the new active song is set, we update all of the\n   * screen elements.\n   *\n   * @access public\n   */\n  function displayMetaData() {\n    /*\n\t\t\tDefine the image meta data keys. These are managed separately\n\t\t\tsince we aren't actually changing the inner HTML of these elements.\n\t\t*/\n    let imageMetaDataKeys = [\n      \"cover_art_url\",\n      \"station_art_url\",\n      \"podcast_episode_cover_art_url\"\n    ];\n\n    /*\n\t\t\tGet all of the song info elements\n\t\t*/\n    let songInfoElements = document.querySelectorAll(\n      \"[data-amplitude-song-info]\"\n    );\n\n    /*\n\t\t\tIterate over all of the song info elements. We will either\n\t\t\tset these to the new values, or clear them if the active song\n\t\t\tdoesn't have the info set.\n\t\t*/\n    for (let i = 0; i < songInfoElements.length; i++) {\n      /*\n\t\t\t\tGet the info so we can check if the active meta data has the\n\t\t\t\tkey.\n\t\t\t*/\n      let info = songInfoElements[i].getAttribute(\"data-amplitude-song-info\");\n\n      /*\n\t\t\t\tGrab the playlist and song index.\n\t\t\t*/\n      let playlist = songInfoElements[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n      let songIndex = songInfoElements[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      /*\n\t\t\t\tEnsure that we don't set any individual elements now. We set this with the\n\t\t\t\tsync meta data method. The reason we don't set them here is because\n\t\t\t\tall individual songs would get the now playing artwork. If the playlists\n\t\t\t\tmatch or the element is a main element meaning it doesn't\n\t\t\t\tbelong to a playlist or a song, then we set the song info.\n\t\t\t*/\n      if (\n        songIndex == null &&\n        (config.active_playlist == playlist ||\n          (playlist == null && songIndex == null))\n      ) {\n        /*\n\t\t\t\t\tIf the active metadata has the key, then we set it,\n\t\t\t\t\totherwise we clear it. If it's an image element then\n\t\t\t\t\twe default it to the default info if needed.\n\t\t\t\t*/\n        let val = (config.active_metadata[info] != undefined) ? config.active_metadata[info] : null;\n        if (imageMetaDataKeys.indexOf(info) >= 0) {\n          val = val || config.default_album_art\n          songInfoElements[i].setAttribute(\n            \"src\",\n            val\n          );\n        } else {\n          val = val || \"\"\n          songInfoElements[i].innerHTML = val;\n        }\n      }\n    }\n  }\n\n  /**\n   * Displays the playlist meta data.\n   */\n  function displayPlaylistMetaData() {\n    /*\n\t\t\tDefine the image meta data keys. These are managed separately\n\t\t\tsince we aren't actually changing the inner HTML of these elements.\n\t\t*/\n    let imageMetaDataKeys = [\"image_url\"];\n\n    /*\n\t\t\tGet all of the playlist info elements\n\t\t*/\n    let playlistInfoElements = document.querySelectorAll(\n      \"[data-amplitude-playlist-info]\"\n    );\n\n    /*\n\t\t\tIterate over all of the playlist info elements. We will either\n\t\t\tset these to the new values, or clear them if the active song\n\t\t\tdoesn't have the info set.\n\t\t*/\n    for (let i = 0; i < playlistInfoElements.length; i++) {\n      /*\n\t\t\t\tGet the info so we can check if the active meta data has the\n\t\t\t\tkey.\n\t\t\t*/\n      let info = playlistInfoElements[i].getAttribute(\n        \"data-amplitude-playlist-info\"\n      );\n      let playlist = playlistInfoElements[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n\n      if (config.playlists[playlist][info] != undefined) {\n        if (imageMetaDataKeys.indexOf(info) >= 0) {\n          playlistInfoElements[i].setAttribute(\n            \"src\",\n            config.playlists[playlist][info]\n          );\n        } else {\n          playlistInfoElements[i].innerHTML = config.playlists[playlist][info];\n        }\n      } else {\n        /*\n\t\t\t\t\tWe look for the default album art because\n\t\t\t\t\tthe actual key didn't exist. If the default album\n\t\t\t\t\tart doesn't exist then we set the src attribute\n\t\t\t\t\tto null.\n\t\t\t\t*/\n        if (imageMetaDataKeys.indexOf(info) >= 0) {\n          if (config.default_playlist_art != \"\") {\n            playlistInfoElements[i].setAttribute(\n              \"src\",\n              config.default_playlist_art\n            );\n          } else {\n            playlistInfoElements[i].setAttribute(\"src\", \"\");\n          }\n        } else {\n          playlistInfoElements[i].innerHTML = \"\";\n        }\n      }\n    }\n  }\n\n  /**\n   * Sets the first song in the playlist. This is used to fill in the meta\n   * data in the playlist\n   *\n   * @param {object} song \t\t\t- The song we are setting to be the first song in the playlist\n   * @param {string} playlist \t- Key of the playlist we are setting the first song in\n   */\n  function setFirstSongInPlaylist(song, playlist) {\n    /*\n      Define the image meta data keys. These are managed separately\n      since we aren't actually changing the inner HTML of these elements.\n    */\n    let imageMetaDataKeys = [\n      \"cover_art_url\",\n      \"station_art_url\",\n      \"podcast_episode_cover_art_url\"\n    ];\n\n    /*\n      Get all of the song info elements\n    */\n    let songInfoElements = document.querySelectorAll(\n      '[data-amplitude-song-info][data-amplitude-playlist=\"' + playlist + '\"]'\n    );\n\n    /*\n      Iterate over all of the song info elements. We will either\n      set these to the new values, or clear them if the active song\n      doesn't have the info set.\n    */\n    for (let i = 0; i < songInfoElements.length; i++) {\n      /*\n        Get the info so we can check if the active meta data has the\n        key.\n      */\n      let info = songInfoElements[i].getAttribute(\"data-amplitude-song-info\");\n\n      /*\n        Get the song info element playlist.\n      */\n      let elementPlaylist = songInfoElements[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n\n      /*\n        If the playlists match or the element is a main element, then\n        we set the song info.\n      */\n      if (elementPlaylist == playlist) {\n        /*\n          If the active metadata has the key, then we set it,\n          otherwise we clear it. If it's an image element then\n          we default it to the default info if needed.\n        */\n        if (song[info] != undefined) {\n          if (imageMetaDataKeys.indexOf(info) >= 0) {\n            songInfoElements[i].setAttribute(\"src\", song[info]);\n          } else {\n            songInfoElements[i].innerHTML = song[info];\n          }\n        } else {\n          /*\n            We look for the default album art because\n            the actual key didn't exist. If the default album\n            art doesn't exist then we set the src attribute\n            to null.\n          */\n          if (imageMetaDataKeys.indexOf(info) >= 0) {\n            if (song.default_album_art != \"\") {\n              songInfoElements[i].setAttribute(\"src\", song.default_album_art);\n            } else {\n              songInfoElements[i].setAttribute(\"src\", \"\");\n            }\n          } else {\n            songInfoElements[i].innerHTML = \"\";\n          }\n        }\n      }\n    }\n  }\n\n  /**\n   * Sets the meta data for songs loaded in the songs array\n   */\n  function syncMetaData() {\n    /*\n\t\t Define the image meta data keys. These are managed separately\n\t\t since we aren't actually changing the inner HTML of these elements.\n\t */\n    let imageMetaDataKeys = [\n      \"cover_art_url\",\n      \"station_art_url\",\n      \"podcast_episode_cover_art_url\"\n    ];\n\n    /*\n\t\t Get all of the song info elements\n\t */\n    let songInfoElements = document.querySelectorAll(\n      \"[data-amplitude-song-info]\"\n    );\n\n    /*\n\t\t Iterate over all of the song info elements. We will either\n\t\t set these to the new values, or clear them if the active song\n\t\t doesn't have the info set.\n\t */\n    for (let i = 0; i < songInfoElements.length; i++) {\n      let songIndex = songInfoElements[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n      let playlist = songInfoElements[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n\n      if (songIndex != null && playlist == null) {\n        let info = songInfoElements[i].getAttribute(\"data-amplitude-song-info\");\n\n        /*\n         Get the song info value referenced on the element.  Depending on the type of\n         element, we may need to fallback to another value when the direct value\n         we want isn't found.\n         i.e.\n            data-amplitude-song-info=\"cover_art_url\" defaults to using the value\n            of \"default_album_art\" when \"cover_art_url\" is missing on the song.\n        */\n        let val = config.songs[songIndex][info] != undefined ? config.songs[songIndex][info] : null;\n        /*\n         If it's an image meta data key, then we set the src attribute of\n         the element. Otherwise we set the inner HTML of the element.\n        */\n        if (imageMetaDataKeys.indexOf(info) >= 0) {\n          /*\n           If this is an image meta data key and the individual song doesn't\n           have the key, use the default_album_art\n           */\n          val = val || config.default_album_art\n          songInfoElements[i].setAttribute(\n            \"src\",\n            val\n          );\n        } else {\n          songInfoElements[i].innerHTML = val;\n        }\n      }\n\n      /*\n        If the song index and playlist are not null, continue.\n      */\n      if (songIndex != null && playlist != null) {\n        /*\n          Get the info we are displaying.\n        */\n        let info = songInfoElements[i].getAttribute(\"data-amplitude-song-info\");\n\n        /*\n          Set the meta data accordingly.\n        */\n        if (config.playlists[playlist].songs[songIndex][info] != undefined) {\n          if (imageMetaDataKeys.indexOf(info) >= 0) {\n            songInfoElements[i].setAttribute(\n              \"src\",\n              config.playlists[playlist].songs[songIndex][info]\n            );\n          } else {\n            songInfoElements[i].innerHTML =\n              config.playlists[playlist].songs[songIndex][info];\n          }\n        }\n      }\n    }\n\n    /*\n      Display the playlist meta data.\n    */\n    displayPlaylistMetaData();\n  }\n\n  /**\n   * Returns publically facing methods\n   */\n  return {\n    displayMetaData: displayMetaData,\n    setFirstSongInPlaylist: setFirstSongInPlaylist,\n    syncMetaData: syncMetaData,\n    displayPlaylistMetaData: displayPlaylistMetaData\n  };\n})();\n\nexport default MetaDataElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/metaDataElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Handles all of the visual syncing to the state of the config for the repeat\n * elements.\n *\n * @module visual/RepeatElements\n */\nlet RepeatElements = (function() {\n  /**\n   * Syncs repeat for all of the repeat buttons. Users\n   * can apply styles to the 'amplitude-repeat-on' and\n   * 'amplitude-repeat-off' classes. They represent the state\n   * of the player.\n   */\n  function syncRepeat() {\n    /*\n\t\t\tGets all of the repeat classes\n\t\t*/\n    let repeatClasses = document.getElementsByClassName(\"amplitude-repeat\");\n\n    /*\n\t\t\tIterate over all of the repeat classes. If repeat is on,\n\t\t\tthen add the 'amplitude-repeat-on' class and remove the\n\t\t\t'amplitude-repeat-off' class. If it's off, then do the\n\t\t\topposite.\n\t\t*/\n    for (let i = 0; i < repeatClasses.length; i++) {\n      if (config.repeat) {\n        repeatClasses[i].classList.add(\"amplitude-repeat-on\");\n        repeatClasses[i].classList.remove(\"amplitude-repeat-off\");\n      } else {\n        repeatClasses[i].classList.remove(\"amplitude-repeat-on\");\n        repeatClasses[i].classList.add(\"amplitude-repeat-off\");\n      }\n    }\n  }\n\n  /**\n   * Syncs repeat for all of the playlist repeat buttons. Users\n   * can apply styles to the `amplitude-repeat-on` and `amplitude-repeat-off`\n   * classes. They repreent the state of the playlist in the player.\n   */\n  function syncRepeatPlaylist(playlist) {\n    /*\n\t\t\t Gets all of the repeat buttons.\n\t\t */\n    let repeatButtons = document.getElementsByClassName(\"amplitude-repeat\");\n\n    /*\n\t\t\t Iterate over all of the repeat buttons\n\t\t */\n    for (let i = 0; i < repeatButtons.length; i++) {\n      /*\n\t\t\t\t Ensure that the repeat button belongs to matches the\n\t\t\t\t playlist we are syncing the state for.\n\t\t\t */\n      if (\n        repeatButtons[i].getAttribute(\"data-amplitude-playlist\") == playlist\n      ) {\n        /*\n\t\t\t\t\t If the state of the playlist is shuffled on, true, then\n\t\t\t\t\t we add the 'amplitude-repeat-on' class and remove the\n\t\t\t\t\t 'amplitude-repeat-off' class. If the player is not shuffled\n\t\t\t\t\t then we do the opposite.\n\t\t\t\t */\n        if (config.playlists[playlist].repeat) {\n          repeatButtons[i].classList.add(\"amplitude-repeat-on\");\n          repeatButtons[i].classList.remove(\"amplitude-repeat-off\");\n        } else {\n          repeatButtons[i].classList.add(\"amplitude-repeat-off\");\n          repeatButtons[i].classList.remove(\"amplitude-repeat-on\");\n        }\n      }\n    }\n  }\n\n  /**\n   * Syncs repeat for all of the repeat song buttons. Users\n   * can apply styles to the 'amplitude-repeat-song-on' and\n   * 'amplitude-repeat-song-off' classes. They represent the state\n   * of the player.\n   */\n  function syncRepeatSong() {\n    /*\n\t\t\tGets all of the repeat song classes\n\t\t*/\n    let repeatSongClasses = document.getElementsByClassName(\n      \"amplitude-repeat-song\"\n    );\n\n    /*\n\t\t\tIterate over all of the repeat song classes. If repeat is on,\n\t\t\tthen add the 'amplitude-repeat-song-on' class and remove the\n\t\t\t'amplitude-repeat-song-off' class. If it's off, then do the\n\t\t\topposite.\n\t\t*/\n    for (let i = 0; i < repeatSongClasses.length; i++) {\n      if (config.repeat_song) {\n        repeatSongClasses[i].classList.add(\"amplitude-repeat-song-on\");\n        repeatSongClasses[i].classList.remove(\"amplitude-repeat-song-off\");\n      } else {\n        repeatSongClasses[i].classList.remove(\"amplitude-repeat-song-on\");\n        repeatSongClasses[i].classList.add(\"amplitude-repeat-song-off\");\n      }\n    }\n  }\n\n  /*\n    Returns the publically available methods.\n  */\n  return {\n    syncRepeat: syncRepeat,\n    syncRepeatPlaylist: syncRepeatPlaylist,\n    syncRepeatSong: syncRepeatSong\n  };\n})();\n\nexport default RepeatElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/repeatElements.js", "/**\n * Handles the visual state for all of the mute elements.\n *\n * @module visual/MuteElements\n */\nlet MuteElements = (function() {\n  /**\n   * Syncs mute for all of the mute buttons. This represents the\n   * state of the player if it's muted or not.\n   *\n   * @access public\n   * @param {string} state \t- The muted state of the player.\n   */\n  function setMuted(state) {\n    /*\n\t\t\tGet all of the mute buttons.\n\t\t*/\n    let muteClasses = document.getElementsByClassName(\"amplitude-mute\");\n\n    /*\n\t\t\tIterate over all of the mute classes. If the state of the player\n\t\t\tis not-muted then we add the amplitude-not-muted classe and remove\n\t\t\tthe amplitude muted class otherwise we do the opposite.\n\t\t*/\n    for (let i = 0; i < muteClasses.length; i++) {\n      if (!state) {\n        muteClasses[i].classList.add(\"amplitude-not-muted\");\n        muteClasses[i].classList.remove(\"amplitude-muted\");\n      } else {\n        muteClasses[i].classList.remove(\"amplitude-not-muted\");\n        muteClasses[i].classList.add(\"amplitude-muted\");\n      }\n    }\n  }\n\n  return {\n    setMuted: setMuted\n  };\n})();\n\nexport default MuteElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/muteElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Keeps the volume slider elements in sync.\n * @module visual/VolumeSliderElements\n */\nlet VolumeSliderElements = (function() {\n  /**\n   * Visually syncs the volume sliders so they are all the same if there\n   * are more than one.\n   *\n   * @access public\n   */\n  function sync() {\n    let volumeSliders = document.getElementsByClassName(\n      \"amplitude-volume-slider\"\n    );\n\n    /*\n\t\t\tIterates over all of the volume sliders for the song, setting the value\n\t\t\tto the config value.\n\t\t*/\n    for (let i = 0; i < volumeSliders.length; i++) {\n      volumeSliders[i].value = config.audio.volume * 100;\n    }\n  }\n\n  /**\n   * Returns the public facing methods\n   */\n  return {\n    sync: sync\n  };\n})();\n\nexport default VolumeSliderElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/volumeSliderElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * AmplitudeJS Repeater utility. Handles setting the repeat for all scenarios.\n *\n * @module utilities/Repeater\n */\nlet Repeater = (function() {\n  /**\n   * Sets the state of the repeat for a song.\n   *\n   * @access public\n   * @param {boolean} repeat - A boolean representing whether the repeat should be on or off\n   */\n  function setRepeat(repeat) {\n    /*\n      Set the global repeat to be toggled\n    */\n    config.repeat = repeat;\n  }\n\n  /**\n   * Sets the state of the repeat for a playlist.\n   *\n   * @access public\n   * @param {boolean} repeat - A boolean representing whether the repeat should be on or off\n   * @param {string} playlist - The key of the playlist for repeating\n   */\n  function setRepeatPlaylist(repeat, playlist) {\n    /*\n      Set the playlist repeat to be toggled.\n    */\n    config.playlists[playlist].repeat = repeat;\n  }\n\n  /**\n   * Sets the state of the repeat song\n   *\n   * @access public\n   * @param {boolean} repeat - A boolean representing whether the repeat shoudl be on or off for the song.\n   */\n  function setRepeatSong(repeat) {\n    config.repeat_song = repeat;\n  }\n\n  /*\n    Returns the public facing methods\n  */\n  return {\n    setRepeat: setRepeat,\n    setRepeatPlaylist: setRepeatPlaylist,\n    setRepeatSong: setRepeatSong\n  };\n})();\n\nexport default Repeater;\n\n\n\n// WEBPACK FOOTER //\n// ./src/utilities/repeater.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * AmplitudeJS Shuffle Module. Handles all of the shuffling functionality for\n * AmplitudeJS\n *\n * @module utilities/Shuffler\n */\nlet Shuffler = (function() {\n  /**\n   * Sets the shuffle state globally\n   *\n   * @access public\n   * @param {boolean} shuffle   - True when we are shuffling, false when we turn it off.\n   */\n  function setShuffle(shuffle) {\n    config.shuffle_on = shuffle;\n\n    if (shuffle) {\n      shuffleSongs();\n    } else {\n      config.shuffle_list = [];\n    }\n  }\n\n  /**\n   * Toggles the shuffle status globally.\n   *\n   * @access public\n   */\n  function toggleShuffle() {\n    /*\n      If shuffle is on, we toggle it off. If shuffle is off, we\n      toggle on.\n    */\n    if (config.shuffle_on) {\n      config.shuffle_on = false;\n      config.shuffle_list = [];\n    } else {\n      config.shuffle_on = true;\n      shuffleSongs();\n    }\n  }\n\n  /**\n   * Sets the shuffle state for a playlist\n   *\n   * @access public\n   * @param {string} playlist   The key of the playlist we are shuffling.\n   * @param {boolean} shuffle   True when we are shuffling the playlist, false when we turn off shuffle.\n   */\n  function setShufflePlaylist(playlist, shuffle) {\n    config.playlists[playlist].shuffle = shuffle;\n\n    if (config.playlists[playlist].shuffle) {\n      shufflePlaylistSongs(playlist);\n    } else {\n      config.playlists[playlist].shuffle_list = [];\n    }\n  }\n\n  /**\n   * Sets the shuffle state for a playlist\n   *\n   * @access public\n   * @param {string} playlist   The key of the playlist we are shuffling.\n   */\n  function toggleShufflePlaylist(playlist) {\n    /*\n      If the playlist shuffled is on, we toggle it off. If the\n      playlist shuffled is off, we toggle it on.\n    */\n    if (config.playlists[playlist].shuffle) {\n      config.playlists[playlist].shuffle = false;\n      config.playlists[playlist].shuffle_list = [];\n    } else {\n      config.playlists[playlist].shuffle = true;\n      shufflePlaylistSongs(playlist);\n    }\n  }\n\n  /**\n   * Shuffles individual songs in the config\n   * Based off of: http://www.codinghorror.com/blog/2007/12/the-danger-of-naivete.html\n   *\n   * Public Accessor: Shuffle.shuffleSongs()\n   *\n   * @access public\n   */\n  function shuffleSongs() {\n    /*\n\t\t\tBuilds a temporary array with the length of the config.\n\t\t*/\n    let shuffleTemp = new Array(config.songs.length);\n\n    /*\n\t\t\tSet the temporary array equal to the songs array.\n\t\t*/\n    for (let i = 0; i < config.songs.length; i++) {\n      shuffleTemp[i] = config.songs[i];\n    }\n\n    /*\n\t\t\tIterate ove rthe songs and generate random numbers to\n\t\t\tswap the indexes of the shuffle array.\n\t\t*/\n    for (let i = config.songs.length - 1; i > 0; i--) {\n      let randNum = Math.floor(Math.random() * config.songs.length + 1);\n      shuffleSwap(shuffleTemp, i, randNum - 1);\n    }\n\n    /*\n\t\t\tSet the shuffle list to the shuffle temp.\n\t\t*/\n    config.shuffle_list = shuffleTemp;\n  }\n\n  /**\n   * Shuffle songs in a playlist\n   *\n   * Public Accessor: Shuffle.shufflePlaylistSongs( playlist )\n   *\n   * @access public\n   * @param {string} playlist - The playlist we are shuffling.\n   */\n  function shufflePlaylistSongs(playlist) {\n    /*\n      Builds a temporary array with the length of the playlist songs.\n    */\n    let shuffleTemp = new Array(config.playlists[playlist].songs.length);\n\n    /*\n      Set the temporary array equal to the playlist array.\n    */\n    for (let i = 0; i < config.playlists[playlist].songs.length; i++) {\n      shuffleTemp[i] = config.playlists[playlist].songs[i];\n    }\n\n    /*\n      Iterate ove rthe songs and generate random numbers to\n      swap the indexes of the shuffle array.\n    */\n    for (let i = config.playlists[playlist].songs.length - 1; i > 0; i--) {\n      let randNum = Math.floor(\n        Math.random() * config.playlists[playlist].songs.length + 1\n      );\n      shuffleSwap(shuffleTemp, i, randNum - 1);\n    }\n\n    /*\n      Set the shuffle list to the shuffle temp.\n    */\n    config.playlists[playlist].shuffle_list = shuffleTemp;\n  }\n\n  /**\n   * Swaps and randomizes the song shuffle.\n   *\n   * @access private\n   * @param {object} shuffleList \t- The list of songs that is going to be shuffled\n   * @param {number} original \t\t- The original index of he song in the songs array\n   * @param {number} random \t\t\t- The randomized index that will be the new index of the song in the shuffle array.\n   */\n  function shuffleSwap(shuffleList, original, random) {\n    let temp = shuffleList[original];\n    shuffleList[original] = shuffleList[random];\n    shuffleList[random] = temp;\n  }\n\n  /**\n   * Returns public facing methods\n   */\n  return {\n    setShuffle: setShuffle,\n    toggleShuffle: toggleShuffle,\n    setShufflePlaylist: setShufflePlaylist,\n    toggleShufflePlaylist: toggleShufflePlaylist,\n    shuffleSongs: shuffleSongs,\n    shufflePlaylistSongs: shufflePlaylistSongs\n  };\n})();\n\nexport default Shuffler;\n\n\n\n// WEBPACK FOOTER //\n// ./src/utilities/shuffler.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Defines the visual representation of AmplitudeJS song slider elements.\n * @module visual/SongSliderElements\n */\nlet SongSliderElements = (function() {\n  /**\n   * Syncs all of the song slider elements.\n   *\n   * @access public\n   * @param {number} location \t- The location of the song as a percentage.\n   * @param {string} playlist \t- The playlist we are setting the song slider for.\n   * @param {number} songIndex \t- The index of the song we are adjusting the song slider for.\n   */\n  function sync(location, playlist, songIndex) {\n    syncMain(location);\n    syncPlaylist(location, playlist);\n    syncSong(location, songIndex);\n    syncSongInPlaylist(location, playlist);\n  }\n\n  /**\n   * Syncs the main slider location\n   *\n   * @access public\n   * @param {number} location \t- The location of the song as a percentage.\n   */\n  function syncMain(location) {\n    /*\n\t\t\tEnsure we have a location that's a number\n\t\t*/\n    location = !isNaN(location) ? location : 0;\n\n    /*\n\t\t\tGets the main song sliders\n\t\t*/\n    const mainSongSliders = document.querySelectorAll(\".amplitude-song-slider\");\n\n    /*\n\t\t\tIterates over all of the main sliders and sets the value to the\n\t\t\tpercentage of the song played.\n\t\t*/\n    for (let i = 0; i < mainSongSliders.length; i++) {\n      /*\n        Grab the playlist and song attributes from the element.\n      */\n      let playlist = mainSongSliders[i].getAttribute(\"data-amplitude-playlist\");\n      let song = mainSongSliders[i].getAttribute(\"data-amplitude-song-index\");\n\n      /*\n        This method is responsible for only the global elements,\n        so we make sure there are no playlist or songs defined on\n        the element.\n      */\n      if (playlist == null && song == null) {\n        mainSongSliders[i].value = location;\n      }\n    }\n  }\n\n  /**\n   * Syncs playlist song slider locations\n   *\n   * @access public\n   * @param {number} location \t- The location of the song as a percentage.\n   * @param {string} playlist \t- The playlist we are setting the song slider for.\n   */\n  function syncPlaylist(location, playlist) {\n    /*\n\t\t\tEnsure we have a location that's a number\n\t\t*/\n    location = !isNaN(location) ? location : 0;\n\n    /*\n\t\t\tGets the playlist song sliders\n\t\t*/\n    const playlistSongSliders = document.querySelectorAll(\n      '.amplitude-song-slider[data-amplitude-playlist=\"' + playlist + '\"]'\n    );\n\n    /*\n\t\t\tIterates over all of the playlist sliders and sets the value to the\n\t\t\tpercentage of the song played.\n\t\t*/\n    for (let i = 0; i < playlistSongSliders.length; i++) {\n      /*\n        Grab the playlist and song attributes from the element.\n      */\n      let playlistAttribute = playlistSongSliders[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n      let songAttribute = playlistSongSliders[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      /*\n\t\t\t\tThis method is responsible for only the playlist elements,\n\t\t\t\tso we make sure the playlist attribute matches what is passed\n\t\t\t\tin.\n\t\t\t*/\n      if (playlistAttribute == playlist && songAttribute == null) {\n        playlistSongSliders[i].value = location;\n      }\n    }\n  }\n\n  /**\n   * Syncs individual song slider locations\n   *\n   * @access public\n   * @param {number} location \t- The location of the song as a percentage.\n   * @param {number} songIndex \t- The index of the song we are adjusting the song slider for.\n   */\n  function syncSong(location, songIndex) {\n    /*\n\t\t\tWe only want to sync song sliders if the playlist is null.\n\t\t*/\n    if (config.active_playlist == null) {\n      /*\n\t\t\t\tEnsure we have a location that's a number\n\t\t\t*/\n      location = !isNaN(location) ? location : 0;\n\n      /*\n\t\t\t\tGets the individual song sliders\n\t\t\t*/\n      const songSliders = document.querySelectorAll(\n        '.amplitude-song-slider[data-amplitude-song-index=\"' + songIndex + '\"]'\n      );\n\n      /*\n\t\t\t\tIterates over all of the individual song sliders and sets the value\n\t\t\t\tto the percentage of the song played.\n\t\t\t*/\n      for (let i = 0; i < songSliders.length; i++) {\n        /*\n\t        Grab the playlist and song attributes from the element.\n\t      */\n        let playlistAttribute = songSliders[i].getAttribute(\n          \"data-amplitude-playlist\"\n        );\n        let songAttribute = songSliders[i].getAttribute(\n          \"data-amplitude-song-index\"\n        );\n\n        /*\n\t\t\t\t\tThis method is responsible for only the playlist elements,\n\t\t\t\t\tso we make sure the playlist attribute matches what is passed\n\t\t\t\t\tin.\n\t\t\t\t*/\n        if (playlistAttribute == null && songAttribute == songIndex) {\n          songSliders[i].value = location;\n        }\n      }\n    }\n  }\n\n  /**\n   * Syncs individual song slider locations\n   *\n   * @access public\n   * @param {number} location \t- The location of the song as a percentage.\n   * @param {string} playlist \t- The playlist we are setting the song slider for.\n   */\n  function syncSongInPlaylist(location, playlist) {\n    /*\n\t\t\tEnsure we have a location that's a number\n\t\t*/\n    location = !isNaN(location) ? location : 0;\n\n    let activePlaylistIndex =\n      config.active_playlist != \"\" && config.active_playlist != null\n        ? config.playlists[config.active_playlist].active_index\n        : null;\n\n    /*\n\t\t\tGets the song in playlist sliders\n\t\t*/\n    const songInPlaylistSliders = document.querySelectorAll(\n      '.amplitude-song-slider[data-amplitude-playlist=\"' +\n        playlist +\n        '\"][data-amplitude-song-index=\"' +\n        activePlaylistIndex +\n        '\"]'\n    );\n\n    /*\n\t\t\tIterates over all of the song in playlist sliders and sets the value\n\t\t\tto the percentage of the song played.\n\t\t*/\n    for (let i = 0; i < songInPlaylistSliders.length; i++) {\n      songInPlaylistSliders[i].value = location;\n    }\n  }\n\n  /**\n   * Visually syncs the song sliders back to 0. This usually happens when\n   * a song has changed, we ensure that all song sliders get reset.\n   *\n   * @access public\n   */\n  function resetElements() {\n    let songSliders = document.getElementsByClassName(\"amplitude-song-slider\");\n\n    /*\n\t\t\tIterate over all of the song sliders and set them to\n\t\t\t0 essentially resetting them.\n\t\t*/\n    for (let i = 0; i < songSliders.length; i++) {\n      songSliders[i].value = 0;\n    }\n  }\n\n  /**\n   * Returns the public facing methods\n   */\n  return {\n    sync: sync,\n    syncMain: syncMain,\n    syncPlaylist: syncPlaylist,\n    syncSong: syncSong,\n    syncSongInPlaylist: syncSongInPlaylist,\n    resetElements: resetElements\n  };\n})();\n\nexport default SongSliderElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/songSliderElements.js", "/**\n * Imports the AmplitudeJS Current Time\n * @module visual/time/CurrentTimeElements\n */\nimport CurrentTimeElements from \"./time/currentTimeElements.js\";\n\n/**\n * Imports the AmplitudeJS Current Hour Elements\n * @module visual/time/CurrentHourElements\n */\nimport CurrentHourElements from \"./time/currentHourElements.js\";\n\n/**\n * Imports the AmplitudeJS Current Minute Elements\n * @module visual/time/CurrentMinuteElements\n */\nimport CurrentMinuteElements from \"./time/currentMinuteElements.js\";\n\n/**\n * Imports the AmplitudeJS Current Second Elements\n * @module visual/time/CurrentTimeElements\n */\nimport CurrentSecondElements from \"./time/currentSecondElements.js\";\n\n/**\n * Imports the AmplitudeJS Duration Count Down Time Elements\n * @module visual/time/DurationCountDownTimeElements\n */\nimport DurationCountDownTimeElements from \"./time/durationCountDownTimeElements.js\";\n\n/**\n * Imports the AmplitudeJS Duration Hour Elements\n * @module visual/time/DurationHourElements\n */\nimport DurationHourElements from \"./time/durationHourElements.js\";\n\n/**\n * Imports the AmplitudeJS Duration Minute Elements\n * @module visual/time/DurationMinuteElements\n */\nimport DurationMinuteElements from \"./time/durationMinuteElements.js\";\n\n/**\n * Imports the AmplitudeJS Duration Second Elements\n * @module visual/time/DurationSecondElements\n */\nimport DurationSecondElements from \"./time/durationSecondElements.js\";\n\n/**\n * Imports the AmplitudeJS Duration Time Elements\n * @module visual/time/DurationTimeElements\n */\nimport DurationTimeElements from \"./time/durationTimeElements.js\";\n\n/**\n * Time Elements Interface. This allows us to update all of the sub time elements\n * through one central point.\n * @module visual/TimeElements\n */\nlet TimeElements = (function() {\n  /**\n   * Resets the current times.\n   */\n  function resetCurrentTimes() {\n    CurrentTimeElements.resetTimes();\n    CurrentHourElements.resetTimes();\n    CurrentMinuteElements.resetTimes();\n    CurrentSecondElements.resetTimes();\n  }\n\n  /**\n   * Syncs the current time elements to the time provided.\n   *\n   * @param {Object} currentTime - An object representing the current time of the audio.\n   */\n  function syncCurrentTimes(currentTime) {\n    CurrentTimeElements.sync(currentTime);\n    CurrentHourElements.sync(currentTime.hours);\n    CurrentMinuteElements.sync(currentTime.minutes);\n    CurrentSecondElements.sync(currentTime.seconds);\n  }\n\n  /**\n   * Resets the duration times.\n   */\n  function resetDurationTimes() {\n    DurationCountDownTimeElements.resetTimes();\n    DurationHourElements.resetTimes();\n    DurationMinuteElements.resetTimes();\n    DurationSecondElements.resetTimes();\n    DurationTimeElements.resetTimes();\n  }\n\n  /**\n   * Syncs the duration times to the times provided.\n   *\n   * @param {Object} currentTime - An object representing the current time of the audio.\n   * @param {Object} songDuration - An object representing the duration of the audio\n   */\n  function syncDurationTimes(currentTime, songDuration) {\n    DurationCountDownTimeElements.sync(currentTime, songDuration);\n    DurationTimeElements.sync(songDuration);\n    DurationHourElements.sync(songDuration.hours);\n    DurationMinuteElements.sync(songDuration.minutes);\n    DurationSecondElements.sync(songDuration.seconds);\n  }\n\n  /**\n   * Returns the publically accessible methods.\n   */\n  return {\n    resetCurrentTimes: resetCurrentTimes,\n    syncCurrentTimes: syncCurrentTimes,\n    resetDurationTimes: resetDurationTimes,\n    syncDurationTimes: syncDurationTimes\n  };\n})();\n\nexport default TimeElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/timeElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the debug module\n * @module utilities/Debug\n */\nimport Debug from \"../utilities/debug.js\";\n\n/**\n * Handles the visualizations elements.\n *\n * @module Visualizations\n */\nlet Visualizations = (function() {\n  /**\n   * Runs all of the visualizations on the screen.\n   */\n  function run() {\n    /*\n      Get all of the visualization elements on the page\n    */\n    let visualizationElements = document.querySelectorAll(\n      \".amplitude-visualization\"\n    );\n\n    /*\n      If the web audio API is available, we display the visualizations.\n    */\n    if (config.web_audio_api_available) {\n      /*\n        If the visualization has not started, there are visualizations available,\n        and we have at least one visualization element, then we continue.\n      */\n      if (\n        Object.keys(config.visualizations.available).length > 0 &&\n        visualizationElements.length > 0\n      ) {\n        /*\n            Iterate over all of the visualizations on the page and activate the\n            ones we need.\n          */\n        for (let i = 0; i < visualizationElements.length; i++) {\n          /*\n              Grab the playlist and song attributes from the visualization to\n              determine which one we run.\n            */\n          let playlist = visualizationElements[i].getAttribute(\n            \"data-amplitude-playlist\"\n          );\n          let song = visualizationElements[i].getAttribute(\n            \"data-amplitude-song-index\"\n          );\n\n          /*\n              If the playlist and song are null, it's a global visualization element.\n            */\n          if (playlist == null && song == null) {\n            runGlobalVisualization(visualizationElements[i]);\n          }\n\n          /*\n              if the playlist is not null and the song is null it's a playlist visualization\n              element.\n            */\n          if (playlist != null && song == null) {\n            runPlaylistVisualization(visualizationElements[i], playlist);\n          }\n\n          /*\n              If the playlist is null and the song is not null it's a song visualization element.\n            */\n          if (playlist == null && song != null) {\n            runSongVisualization(visualizationElements[i], song);\n          }\n\n          /*\n              If the playlist and song are not null then it's a song in playlist visualization\n              element.\n            */\n          if (playlist != null && song != null) {\n            runSongInPlaylistVisualization(\n              visualizationElements[i],\n              playlist,\n              song\n            );\n          }\n        }\n      }\n    } else {\n      displayBackups();\n    }\n  }\n\n  /**\n   * Runs a global visualization\n   *\n   * @param {Node} element  The container element that handles the visualization.\n   */\n  function runGlobalVisualization(element) {\n    /*\n      Gets the global visualization index and the active song visualization indexes\n      so we know which visualization to use. The song will override the global\n    */\n    let globalVisualizationIndex = config.visualization;\n    let activeSongVisualizationIndex =\n      config.active_index != null\n        ? config.songs[config.active_index].visualization\n        : config.playlists[config.active_playlist].songs[\n            config.playlists[config.active_playlist].active_index\n          ].visualization;\n\n    /*\n      If the active song visualization is defined and the visualization exists,\n      use that visualization.\n    */\n    if (\n      activeSongVisualizationIndex != undefined &&\n      config.visualizations.available[activeSongVisualizationIndex] != undefined\n    ) {\n      addToActiveVisualizations(activeSongVisualizationIndex, element);\n\n      /*\n      If the user defined a global visualization, use that one.\n    */\n    } else if (\n      globalVisualizationIndex != undefined &&\n      config.visualizations.available[globalVisualizationIndex] != undefined\n    ) {\n      addToActiveVisualizations(globalVisualizationIndex, element);\n\n      /*\n      If the user didn't define a global visualization, use the first visualization\n      registered if there is one.\n    */\n    } else {\n      /*\n        Grab the first registered visualization. If it exists, use that one.\n      */\n      let firstVisualization =\n        Object.keys(config.visualizations.available).length > 0\n          ? Object.keys(config.visualizations.available)[0]\n          : null;\n\n      if (firstVisualization != null) {\n        addToActiveVisualizations(firstVisualization, element);\n      }\n    }\n  }\n\n  /**\n   * Run a specific playlist visualization.\n   *\n   * @param {Node} element  The container element that handles the visualization.\n   * @param {string} playlist The key of the playlist we are running the visualization for.\n   */\n  function runPlaylistVisualization(element, playlist) {\n    /*\n      If the playlist is equal to the active playlist, then we continue.\n    */\n    if (playlist == config.active_playlist) {\n      /*\n        Checks if the song has a visualization and that visualization exists,\n        run that visualization.\n      */\n      let activeSongVisualizationIndex =\n        config.playlists[config.active_playlist].songs[\n          config.playlists[config.active_playlist].active_index\n        ].visualization;\n      let activePlaylistVisualizationIndex =\n        config.playlists[config.active_playlist].visualization;\n      let globalVisualizationIndex = config.visualization;\n\n      /*\n        If the actual song has a visualization, we run that.\n      */\n      if (\n        activeSongVisualizationIndex != undefined &&\n        config.visualizations.available[activeSongVisualizationIndex] !=\n          undefined\n      ) {\n        addToActiveVisualizations(activeSongVisualizationIndex, element);\n\n        /*\n        If the actual playlist has a visualization, run that.\n      */\n      } else if (\n        activePlaylistVisualizationIndex != undefined &&\n        config.visualizations.available[activePlaylistVisualizationIndex] !=\n          undefined\n      ) {\n        addToActiveVisualizations(activePlaylistVisualizationIndex, element);\n\n        /*\n        If a global visualization is defined, run that.\n      */\n      } else if (\n        globalVisualizationIndex != undefined &&\n        config.visualizations.available[globalVisualizationIndex] != undefined\n      ) {\n        addToActiveVisualizations(globalVisualizationIndex, element);\n      } else {\n        /*\n          Grab the first registered visualization. If it exists, use that one.\n        */\n        let firstVisualization =\n          Object.keys(config.visualizations.available).length > 0\n            ? Object.keys(config.visualizations.available)[0]\n            : null;\n\n        if (firstVisualization != null) {\n          addToActiveVisualizations(firstVisualization, element);\n        }\n      }\n    }\n  }\n\n  /**\n   * Run a song specific visualization.\n   *\n   * @param {Node} element The container element that handles the visualization.\n   * @param {string} song The song index that we are running the visualization for.\n   */\n  function runSongVisualization(element, song) {\n    /*\n      If the song is equal to the active song, then we continue.\n    */\n    if (song == config.active_index) {\n      /*\n        Get the indexes of the song\n      */\n      let activeSongVisualizationIndex =\n        config.songs[config.active_index].visualization;\n      let globalVisualizationIndex = config.visualization;\n\n      /*\n        If the song has a visualization, run that.\n      */\n      if (\n        activeSongVisualizationIndex != undefined &&\n        config.visualizations.available[activeSongVisualizationIndex] !=\n          undefined\n      ) {\n        addToActiveVisualizations(activeSongVisualizationIndex, element);\n\n        /*\n        If the global visualization is set, use that.\n      */\n      } else if (\n        globalVisualizationIndex != undefined &&\n        config.visualizations.available[globalVisualizationIndex] != undefined\n      ) {\n        addToActiveVisualizations(globalVisualizationIndex, element);\n      } else {\n        /*\n          Grab the first registered visualization. If it exists, use that one.\n        */\n        let firstVisualization =\n          Object.keys(config.visualizations.available).length > 0\n            ? Object.keys(config.visualizations.available)[0]\n            : null;\n\n        if (firstVisualization != null) {\n          addToActiveVisualizations(firstVisualization, element);\n        }\n      }\n    }\n  }\n\n  /**\n   * Run a song in playlist visualization.\n   *\n   * @param {Node} element - The element containing the visualization.\n   * @param {string} playlist - The string of the playlist key.\n   * @param {index} song - The index of the song in the playlist.\n   */\n  function runSongInPlaylistVisualization(element, playlist, song) {\n    /*\n      If the playlist is the same as the active playlist and the active\n      index of the song is the same as the song, then we continue.\n    */\n    if (\n      playlist == config.active_playlist &&\n      config.playlists[playlist].active_index == song\n    ) {\n      /*\n        Checks if the song has a visualization and that visualization exists,\n        run that visualization.\n      */\n      let activeSongVisualizationIndex =\n        config.playlists[config.active_playlist].songs[\n          config.playlists[config.active_playlist].active_index\n        ].visualization;\n      let activePlaylistVisualizationIndex =\n        config.playlists[config.active_playlist].visualization;\n      let globalVisualizationIndex = config.visualization;\n\n      /*\n        If the active song has a visualization, we use that.\n      */\n      if (\n        activeSongVisualizationIndex != undefined &&\n        config.visualizations.available[activeSongVisualizationIndex] !=\n          undefined\n      ) {\n        addToActiveVisualizations(activeSongVisualizationIndex, element);\n\n        /*\n        If the active playlist has a visualization, we use that.\n      */\n      } else if (\n        activePlaylistVisualizationIndex != undefined &&\n        config.visualizations.available[activePlaylistVisualizationIndex] !=\n          undefined\n      ) {\n        addToActiveVisualizations(activePlaylistVisualizationIndex, element);\n\n        /*\n        If the global visualization has been set, we use that.\n      */\n      } else if (\n        globalVisualizationIndex != undefined &&\n        config.visualizations.available[globalVisualizationIndex] != undefined\n      ) {\n        addToActiveVisualizations(globalVisualizationIndex, element);\n      } else {\n        /*\n          Grab the first registered visualization. If it exists, use that one.\n        */\n        let firstVisualization =\n          Object.keys(config.visualizations.available).length > 0\n            ? Object.keys(config.visualizations.available)[0]\n            : null;\n\n        if (firstVisualization != null) {\n          addToActiveVisualizations(firstVisualization, element);\n        }\n      }\n    }\n  }\n\n  /**\n   * Add a visualization to the array of active visualizations.\n   *\n   * @param {string} key - The key of the active visualization.\n   * @param {Node} element - The element that the visualization will be applied to.\n   */\n  function addToActiveVisualizations(key, element) {\n    let visualization = new config.visualizations.available[key][\"object\"]();\n    visualization.setPreferences(\n      config.visualizations.available[key][\"preferences\"]\n    );\n    visualization.startVisualization(element);\n    config.visualizations.active.push(visualization);\n  }\n\n  /**\n   * Stops all active visualizations.\n   */\n  function stop() {\n    /*\n      Iterates over all of the visualizations and stop the visualization.\n    */\n    for (let i = 0; i < config.visualizations.active.length; i++) {\n      config.visualizations.active[i].stopVisualization();\n    }\n\n    /*\n      Clear the active visualizations.\n    */\n    config.visualizations.active = [];\n  }\n\n  /**\n   * Registers any visualization we can use.\n   *\n   * @param {object} visualization The visualization object itself\n   * @param {object} preferences User preferences overrides.\n   */\n  function register(visualization, preferences) {\n    /*\n      Initialize the new visualization.\n    */\n    let newVisualization = new visualization();\n\n    /*\n\t    Adds the visualization to the global config so it knows\n\t    it can be used when playing songs.\n\n\t    getID is a public function for getting a visualization's id.\n\t    It becomes the key to access the visualization.\n\t  */\n    config.visualizations.available[newVisualization.getID()] = new Array();\n    config.visualizations.available[newVisualization.getID()][\n      \"object\"\n    ] = visualization;\n    config.visualizations.available[newVisualization.getID()][\n      \"preferences\"\n    ] = preferences;\n  }\n\n  /**\n   * Displays the backups for the visualizations.\n   */\n  function displayBackups() {\n    /*\n      Get all of the visualization elements on the page\n    */\n    let visualizationElements = document.querySelectorAll(\n      \".amplitude-visualization\"\n    );\n\n    if (visualizationElements.length > 0) {\n      for (let x = 0; x < visualizationElements.length; x++) {\n        /*\n          Grab the playlist and song attributes from the visualization to\n          determine which one we run.\n        */\n        let playlist = visualizationElements[x].getAttribute(\n          \"data-amplitude-playlist\"\n        );\n        let song = visualizationElements[x].getAttribute(\n          \"data-amplitude-song-index\"\n        );\n\n        /*\n          If the playlist and song are null, it's a global visualization element.\n        */\n        if (playlist == null && song == null) {\n          displayGlobalBackup(visualizationElements[x]);\n        }\n\n        /*\n          if the playlist is not null and the song is null it's a playlist visualization\n          element.\n        */\n        if (playlist != null && song == null) {\n          displayPlaylistBackup(visualizationElements[x], playlist);\n        }\n\n        /*\n          If the playlist is null and the song is not null it's a song visualization element.\n        */\n        if (playlist == null && song != null) {\n          displaySongBackup(visualizationElements[x], song);\n        }\n\n        /*\n          If the playlist and song are not null then it's a song in playlist visualization\n          element.\n        */\n        if (playlist != null && song != null) {\n          displaySongInPlaylistBackup(visualizationElements[x], playlist, song);\n        }\n      }\n    }\n  }\n\n  /**\n   * Displays the global backup which is the cover art of the image in the\n   * visualization container.\n   *\n   * @param {node} element  - The element we are adding the background image to.\n   */\n  function displayGlobalBackup(element) {\n    element.style.backgroundImage =\n      \"url(\" + config.active_metadata.cover_art_url + \")\";\n  }\n\n  /**\n   * Displays the playlist backup which is the cover art of the image in the\n   * visualization container.\n   *\n   * @param {node} element  - The element we are adding the background image to.\n   */\n  function displayPlaylistBackup(element, playlist) {\n    if (config.active_playlist == playlist) {\n      element.style.backgroundImage =\n        \"url(\" + config.active_metadata.cover_art_url + \")\";\n    }\n  }\n\n  /**\n   * Displays the song backup which is the cover art of the image in the\n   * visualization container.\n   *\n   * @param {node} element  - The element we are adding the background image to.\n   */\n  function displaySongBackup(element, song) {\n    if (config.active_index == song) {\n      element.style.backgroundImage =\n        \"url(\" + config.active_metadata.cover_art_url + \")\";\n    }\n  }\n\n  /**\n   * Displays the song in playlist backup which is the cover art of the image in the\n   * visualization container.\n   *\n   * @param {node} element  - The element we are adding the background image to.\n   */\n  function displaySongInPlaylistBackup(element, playlist, song) {\n    if (\n      config.active_playlist == playlist &&\n      config.playlists[active_playlist].active_index == song\n    ) {\n      element.style.backgroundImage =\n        \"url(\" + config.active_metadata.cover_art_url + \")\";\n    }\n  }\n\n  /*\n    Returns the public facing methods\n  */\n  return {\n    run: run,\n    stop: stop,\n    register: register\n  };\n})();\n\nexport default Visualizations;\n\n\n\n// WEBPACK FOOTER //\n// ./src/fx/visualizations.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the initializer\n * @module init/AmplitudeInitializer\n */\nimport AmplitudeInitializer from \"../init/init.js\";\n\n/**\n * These helpers wrap around the basic methods of the Soundcloud API\n * and get the information we need from SoundCloud to make the songs\n * streamable through Amplitude\n *\n * @module soundcloud/SoundCloud\n */\nlet SoundCloud = (function() {\n  /**\n   * Defines the temporary user config used while we configure soundcloud\n   * @type {object}\n   */\n  let tempUserConfig = {};\n\n  /**\n   * Loads the soundcloud SDK for use with Amplitude so the user doesn't have\n   * to load it themselves.\n   * With help from: http://stackoverflow.com/questions/950087/include-a-javascript-file-in-another-javascript-file\n   *\n   * @access public\n   * @param {object} userConfig \t- The config defined by the user for AmplitudeJS\n   */\n  function loadSoundCloud(userConfig) {\n    /*\n\t\t\tSets the temporary config to the config passed by the user so we can make changes\n\t\t\tand not break the actual config.\n\t\t*/\n    tempUserConfig = userConfig;\n\n    /*\n\t\t\tGets the head tag for the document and create a script element.\n\t\t*/\n    let head = document.getElementsByTagName(\"head\")[0];\n    let script = document.createElement(\"script\");\n\n    script.type = \"text/javascript\";\n\n    /*\n\t\t\tURL to the remote soundcloud SDK\n\t\t*/\n    script.src = \"https://connect.soundcloud.com/sdk.js\";\n    script.onreadystatechange = initSoundcloud;\n    script.onload = initSoundcloud;\n\n    /*\n\t\t\tAdd the script to the head of the document.\n\t\t*/\n    head.appendChild(script);\n  }\n\n  /**\n   * Initializes soundcloud with the key provided.\n   *\n   * @access private\n   */\n  function initSoundcloud() {\n    /*\n\t\t\tCalls the SoundCloud initialize function\n\t\t\tfrom their API and sends it the client_id\n\t\t\tthat the user passed in.\n\t\t*/\n    SC.initialize({\n      client_id: config.soundcloud_client\n    });\n\n    /*\n\t\t\tGets the streamable URLs to run through Amplitue. This is\n\t\t\tVERY important since Amplitude can't stream the copy and pasted\n\t\t\tlink from the SoundCloud page, but can resolve the streaming\n\t\t\tURLs from the link.\n\t\t*/\n    getStreamableURLs();\n  }\n\n  /**\n   * Gets the streamable URL from the URL provided for\n   * all of the soundcloud links.  This will loop through\n   * and set all of the information for the soundcloud\n   * urls.\n   *\n   * @access private\n   */\n  function getStreamableURLs() {\n    /*\n\t\t\tDefine the regex to find the soundcloud URLs\n\t\t*/\n    let soundcloud_regex = /^https?:\\/\\/(soundcloud.com|snd.sc)\\/(.*)$/;\n\n    for (let i = 0; i < config.songs.length; i++) {\n      /*\n\t\t\t\tIf the URL matches soundcloud, we grab\n\t\t\t\tthat url and get the streamable link\n\t\t\t\tif there is one.\n\t\t\t*/\n      if (config.songs[i].url.match(soundcloud_regex)) {\n        config.soundcloud_song_count++;\n        resolveStreamable(config.songs[i].url, i);\n      }\n    }\n  }\n\n  /**\n   * Resolves an individual streamable URL.\n   *\n   * @param {string} url - The URL of the SoundCloud song to get the streamable URL from.\n   * @param {string} playlist - The playlist we are getting the streamable URL for.\n   * @param {Integer} index - The index of the song in the playlist or the songs array.\n   * @param {boolean} addToShuffleList - Whether we add to the shuffle list for the songs or playlist.\n   *\n   */\n  function resolveIndividualStreamableURL(\n    url,\n    playlist,\n    index,\n    addToShuffleList = false\n  ) {\n    SC.get(\"/resolve/?url=\" + url, function(sound) {\n      /*\n        If streamable we get the url and bind the client ID to the end\n        so Amplitude can just stream the song normally. We then overwrite\n        the url the user provided with the streamable URL.\n      */\n      if (sound.streamable) {\n        if (playlist != null) {\n          config.playlists[playlist].songs[index].url =\n            sound.stream_url + \"?client_id=\" + config.soundcloud_client;\n\n          if (addToShuffleList) {\n            config.playlists[playlist].shuffle_list[index].url =\n              sound.stream_url + \"?client_id=\" + config.soundcloud_client;\n          }\n          /*\n            If the user want's to use soundcloud art, we overwrite the\n            cover_art_url with the soundcloud artwork url.\n          */\n          if (config.soundcloud_use_art) {\n            config.playlists[playlist].songs[index].cover_art_url =\n              sound.artwork_url;\n\n            if (addToShuffleList) {\n              config.playlists[playlist].shuffle_list[index].cover_art_url =\n                sound.artwork_url;\n            }\n          }\n\n          /*\n            Grab the extra metadata from soundcloud and bind it to the\n            song.  The user can get this through the public function:\n            getActiveSongMetadata\n          */\n          config.playlists[playlist].songs[index].soundcloud_data = sound;\n\n          if (addToShuffleList) {\n            config.playlists[playlist].shuffle_list[\n              index\n            ].soundcloud_data = sound;\n          }\n        } else {\n          config.songs[index].url =\n            sound.stream_url + \"?client_id=\" + config.soundcloud_client;\n\n          if (addToShuffleList) {\n            config.shuffle_list[index].stream_url +\n              \"?client_id=\" +\n              config.soundcloud_client;\n          }\n\n          /*\n            If the user want's to use soundcloud art, we overwrite the\n            cover_art_url with the soundcloud artwork url.\n          */\n          if (config.soundcloud_use_art) {\n            config.songs[index].cover_art_url = sound.artwork_url;\n\n            if (addToShuffleList) {\n              config.shuffle_list[index].cover_art_url = sound.artwork_url;\n            }\n          }\n\n          /*\n            Grab the extra metadata from soundcloud and bind it to the\n            song.  The user can get this through the public function:\n            getActiveSongMetadata\n          */\n          config.songs[index].soundcloud_data = sound;\n\n          if (addToShuffleList) {\n            config.shuffle_list[index].soundcloud_data = sound;\n          }\n        }\n      } else {\n        if (playlist != null) {\n          AmplitudeHelpers.writeDebugMessage(\n            config.playlists[playlist].songs[index].name +\n              \" by \" +\n              config.playlists[playlist].songs[index].artist +\n              \" is not streamable by the Soundcloud API\"\n          );\n        } else {\n          /*\n            If not streamable, then we print a message to the user stating\n            that the song with name X and artist X is not streamable. This\n            gets printed ONLY if they have debug turned on.\n          */\n          AmplitudeHelpers.writeDebugMessage(\n            config.songs[index].name +\n              \" by \" +\n              config.songs[index].artist +\n              \" is not streamable by the Soundcloud API\"\n          );\n        }\n      }\n    });\n  }\n\n  /**\n   * Due to Soundcloud SDK being asynchronous, we need to scope the\n   * index of the song in another function. The privateGetSoundcloudStreamableURLs\n   * function does the actual iteration and scoping.\n   *\n   * @access private\n   * @param {string} url \t\t- URL of the soundcloud song\n   * @param {number} index \t- The index of the soundcloud song in the songs array.\n   */\n  function resolveStreamable(url, index) {\n    SC.get(\"/resolve/?url=\" + url, function(sound) {\n      /*\n\t\t\t\tIf streamable we get the url and bind the client ID to the end\n\t\t\t\tso Amplitude can just stream the song normally. We then overwrite\n\t\t\t\tthe url the user provided with the streamable URL.\n\t\t\t*/\n      if (sound.streamable) {\n        config.songs[index].url =\n          sound.stream_url + \"?client_id=\" + config.soundcloud_client;\n\n        /*\n\t\t\t\t\tIf the user want's to use soundcloud art, we overwrite the\n\t\t\t\t\tcover_art_url with the soundcloud artwork url.\n\t\t\t\t*/\n        if (config.soundcloud_use_art) {\n          config.songs[index].cover_art_url = sound.artwork_url;\n        }\n\n        /*\n\t\t\t\t\tGrab the extra metadata from soundcloud and bind it to the\n\t\t\t\t\tsong.  The user can get this through the public function:\n\t\t\t\t\tgetActiveSongMetadata\n\t\t\t\t*/\n        config.songs[index].soundcloud_data = sound;\n      } else {\n        /*\n\t\t\t\t\tIf not streamable, then we print a message to the user stating\n\t\t\t\t\tthat the song with name X and artist X is not streamable. This\n\t\t\t\t\tgets printed ONLY if they have debug turned on.\n\t\t\t\t*/\n        AmplitudeHelpers.writeDebugMessage(\n          config.songs[index].name +\n            \" by \" +\n            config.songs[index].artist +\n            \" is not streamable by the Soundcloud API\"\n        );\n      }\n      /*\n\t\t\t\tIncrements the song ready counter.\n\t\t\t*/\n      config.soundcloud_songs_ready++;\n\n      /*\n\t\t\t\tWhen all songs are accounted for, then amplitude is ready\n\t\t\t\tto rock and we set the rest of the config.\n\t\t\t*/\n      if (config.soundcloud_songs_ready == config.soundcloud_song_count) {\n        AmplitudeInitializer.setConfig(tempUserConfig);\n      }\n    });\n  }\n\n  /**\n   * Determines if a given URL is a SoundCloud URL.\n   *\n   * @param {string} url - The URL to test if it's a SoundCloud URL.\n   */\n  function isSoundCloudURL(url) {\n    let soundcloud_regex = /^https?:\\/\\/(soundcloud.com|snd.sc)\\/(.*)$/;\n\n    return url.match(soundcloud_regex);\n  }\n\n  /*\n\t\tReturns the publically accessible methods\n\t*/\n  return {\n    loadSoundCloud: loadSoundCloud,\n    resolveIndividualStreamableURL: resolveIndividualStreamableURL,\n    isSoundCloudURL: isSoundCloudURL\n  };\n})();\n\nexport default SoundCloud;\n\n\n\n// WEBPACK FOOTER //\n// ./src/soundcloud/soundcloud.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Defines the Playback Speed Visual Elements Handler\n * @module visual/PlaybackSpeedElements\n */\nlet PlaybackSpeedElements = (function() {\n  /**\n   * Sets all of the visual playback speed buttons to have the right class\n   * to display the background image that represents the current playback\n   * speed.\n   *\n   * @access public\n   */\n  function sync() {\n    /*\n\t\t\tGets all of the playback speed classes.\n\t\t*/\n    let playbackSpeedClasses = document.getElementsByClassName(\n      \"amplitude-playback-speed\"\n    );\n\n    /*\n\t\t\tIterates over all of the playback speed classes\n\t\t\tapplying the right speed class for visual purposes.\n\t\t*/\n    for (let i = 0; i < playbackSpeedClasses.length; i++) {\n      /*\n\t\t\t\tRemoves all of the old playback speed classes.\n\t\t\t*/\n      playbackSpeedClasses[i].classList.remove(\"amplitude-playback-speed-10\");\n      playbackSpeedClasses[i].classList.remove(\"amplitude-playback-speed-15\");\n      playbackSpeedClasses[i].classList.remove(\"amplitude-playback-speed-20\");\n\n      /*\n\t\t\t\tSwitch the current playback speed and apply the appropriate\n\t\t\t\tspeed class.\n\t\t\t*/\n      switch (config.playback_speed) {\n        case 1:\n          playbackSpeedClasses[i].classList.add(\"amplitude-playback-speed-10\");\n          break;\n        case 1.5:\n          playbackSpeedClasses[i].classList.add(\"amplitude-playback-speed-15\");\n          break;\n        case 2:\n          playbackSpeedClasses[i].classList.add(\"amplitude-playback-speed-20\");\n          break;\n      }\n    }\n  }\n\n  /**\n   * Returns the public facing methods\n   */\n  return {\n    sync: sync\n  };\n})();\n\nexport default PlaybackSpeedElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/playbackSpeedElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Handles all of the shuffle elements\n * @module visual/ShuffleElements\n */\nlet ShuffleElements = (function() {\n  /**\n   * Syncs the global shuffle button visual state.\n   *\n   * @access public\n   */\n  function syncMain() {\n    /*\n\t\t\tGets the shuffle buttons.\n\t\t*/\n    let shuffleButtons = document.getElementsByClassName(\"amplitude-shuffle\");\n\n    /*\n\t\t\tIterate over all of the shuffle buttons.\n\t\t*/\n    for (let i = 0; i < shuffleButtons.length; i++) {\n      /*\n\t\t\t\tEnsure the shuffle button doesn't belong to a playlist. We have\n\t\t\t\ta separate method for that.\n\t\t\t*/\n      if (shuffleButtons[i].getAttribute(\"data-amplitude-playlist\") == null) {\n        /*\n\t\t\t\t\tIf the state of the player is shuffled on, true, then\n\t\t\t\t\twe add the 'amplitude-shuffle-on' class and remove the\n\t\t\t\t\t'amplitude-shuffle-off' class. If the player is not shuffled\n\t\t\t\t\tthen we do the opposite.\n\t\t\t\t*/\n        if (config.shuffle_on) {\n          shuffleButtons[i].classList.add(\"amplitude-shuffle-on\");\n          shuffleButtons[i].classList.remove(\"amplitude-shuffle-off\");\n        } else {\n          shuffleButtons[i].classList.add(\"amplitude-shuffle-off\");\n          shuffleButtons[i].classList.remove(\"amplitude-shuffle-on\");\n        }\n      }\n    }\n  }\n\n  /**\n   * Syncs the playlist shuffle button visual state.\n   *\n   * @access public\n   * @param {string} playlist - The playlist string the shuffle button belongs to.\n   */\n  function syncPlaylist(playlist) {\n    /*\n\t\t\tGets all of the shuffle buttons.\n\t\t*/\n    let shuffleButtons = document.querySelectorAll(\n      '.amplitude-shuffle[data-amplitude-playlist=\"' + playlist + '\"]'\n    );\n\n    /*\n\t\t\tIterate over all of the shuffle buttons\n\t\t*/\n    for (let i = 0; i < shuffleButtons.length; i++) {\n      /*\n\t\t\t\tIf the state of the playlist is shuffled on, true, then\n\t\t\t\twe add the 'amplitude-shuffle-on' class and remove the\n\t\t\t\t'amplitude-shuffle-off' class. If the player is not shuffled\n\t\t\t\tthen we do the opposite.\n\t\t\t*/\n      if (config.playlists[playlist].shuffle) {\n        shuffleButtons[i].classList.add(\"amplitude-shuffle-on\");\n        shuffleButtons[i].classList.remove(\"amplitude-shuffle-off\");\n      } else {\n        shuffleButtons[i].classList.add(\"amplitude-shuffle-off\");\n        shuffleButtons[i].classList.remove(\"amplitude-shuffle-on\");\n      }\n    }\n  }\n\n  /**\n   * Returns public facing methods\n   */\n  return {\n    syncMain: syncMain,\n    syncPlaylist: syncPlaylist\n  };\n})();\n\nexport default ShuffleElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/shuffleElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Handles the syncing of the song played progress elements.\n *\n * @module visual/SongPlayedProgressElements\n */\nlet SongPlayedProgressElements = (function() {\n  /**\n   * Syncs the song played progress bars. These are HTML5 progress elements.\n   *\n   * @access private\n   * @param {number} songPlayedPercentage  \t- The percentage of the song that has been played.\n   */\n  function sync(songPlayedPercentage) {\n    syncGlobal(songPlayedPercentage);\n    syncPlaylist(songPlayedPercentage);\n    syncSong(songPlayedPercentage);\n    syncSongInPlaylist(songPlayedPercentage);\n  }\n\n  /**\n   * Sync how much has been played with a progress bar. This is the global progress bar.\n   *\n   * @access private\n   * @param {number} songPlayedPercentage \t- The percent of the song completed.\n   */\n  function syncGlobal(percentage) {\n    /*\n\t\t\tEnsure that the song completion percentage is a number\n\t\t*/\n    if (!isNaN(percentage)) {\n      /*\n\t\t\t\tGet all of the song progress bars\n\t\t\t*/\n      let songPlayedProgressBars = document.querySelectorAll(\n        \".amplitude-song-played-progress\"\n      );\n\n      for (let i = 0; i < songPlayedProgressBars.length; i++) {\n        let playlist = songPlayedProgressBars[i].getAttribute(\n          \"data-amplitude-playlist\"\n        );\n        let songIndex = songPlayedProgressBars[i].getAttribute(\n          \"data-amplitude-song-index\"\n        );\n\n        if (playlist == null && songIndex == null) {\n          let max = songPlayedProgressBars[i].max;\n\n          songPlayedProgressBars[i].value = (percentage / 100) * max;\n        }\n      }\n    }\n  }\n\n  /**\n   * Sync how much has been played with a progress bar. This is the playlist progress bar.\n   *\n   * @access public\n   * @param {number} songPlayedPercentage \t- The percent of the song completed.\n   */\n  function syncPlaylist(percentage) {\n    /*\n\t\t\tEnsure that the song completion percentage is a number\n\t\t*/\n    if (!isNaN(percentage)) {\n      /*\n\t\t\t\tGet all of the song progress bars\n\t\t\t*/\n      let songPlayedProgressBars = document.querySelectorAll(\n        '.amplitude-song-played-progress[data-amplitude-playlist=\"' +\n          config.active_playlist +\n          '\"]'\n      );\n\n      for (let i = 0; i < songPlayedProgressBars.length; i++) {\n        let song = songPlayedProgressBars[i].getAttribute(\n          \"data-amplitude-song-index\"\n        );\n\n        if (song == null) {\n          let max = songPlayedProgressBars[i].max;\n\n          songPlayedProgressBars[i].value = (percentage / 100) * max;\n        }\n      }\n    }\n  }\n\n  /**\n   * Sync how much has been played with a progress bar. This is for an individual song.\n   *\n   * @access private\n   * @param {number} songPlayedPercentage \t- The percent of the song completed.\n   */\n  function syncSong(percentage) {\n    if (config.active_playlist == null) {\n      /*\n\t\t\t\tEnsure that the song completion percentage is a number\n\t\t\t*/\n      if (!isNaN(percentage)) {\n        /*\n\t\t\t\t\tGet all of the song progress bars\n\t\t\t\t*/\n        let songPlayedProgressBars = document.querySelectorAll(\n          '.amplitude-song-played-progress[data-amplitude-song-index=\"' +\n            config.active_index +\n            '\"]'\n        );\n\n        for (let i = 0; i < songPlayedProgressBars.length; i++) {\n          let playlist = songPlayedProgressBars[i].getAttribute(\n            \"data-amplitude-playlist\"\n          );\n\n          if (playlist == null) {\n            let max = songPlayedProgressBars[i].max;\n\n            songPlayedProgressBars[i].value = (percentage / 100) * max;\n          }\n        }\n      }\n    }\n  }\n\n  /**\n   * Sync how much has been played with a progress bar. This is for an individual song in playlist.\n   *\n   * @access private\n   * @param {number} songPlayedPercentage \t- The percent of the song completed.\n   */\n  function syncSongInPlaylist(percentage) {\n    /*\n\t\t\tEnsure that the song completion percentage is a number\n\t\t*/\n    if (!isNaN(percentage)) {\n      let activePlaylistIndex =\n        config.active_playlist != \"\" && config.active_playlist != null\n          ? config.playlists[config.active_playlist].active_index\n          : null;\n\n      /*\n\t\t\t\tGet all of the song progress bars\n\t\t\t*/\n      let songPlayedProgressBars = document.querySelectorAll(\n        '.amplitude-song-played-progress[data-amplitude-playlist=\"' +\n          config.active_playlist +\n          '\"][data-amplitude-song-index=\"' +\n          activePlaylistIndex +\n          '\"]'\n      );\n\n      /*\n        Iterates over all of the song played progress elements\n        and sets them accordingly.\n      */\n      for (let i = 0; i < songPlayedProgressBars.length; i++) {\n        let playlist = songPlayedProgressBars[i].getAttribute(\n          \"data-amplitude-playlist\"\n        );\n        let songIndex = songPlayedProgressBars[i].getAttribute(\n          \"data-amplitude-song-index\"\n        );\n\n        if (playlist != null && songIndex != null) {\n          let max = songPlayedProgressBars[i].max;\n\n          songPlayedProgressBars[i].value = (percentage / 100) * max;\n        }\n      }\n    }\n  }\n\n  /**\n   * Sets all of the song played progress bars to 0\n   *\n   * @access public\n   */\n  function resetElements() {\n    let songPlayedProgressBars = document.getElementsByClassName(\n      \"amplitude-song-played-progress\"\n    );\n\n    for (let i = 0; i < songPlayedProgressBars.length; i++) {\n      songPlayedProgressBars[i].value = 0;\n    }\n  }\n\n  return {\n    sync: sync,\n    resetElements: resetElements\n  };\n})();\n\nexport default SongPlayedProgressElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/songPlayedProgressElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * AmplitudeJS Core Module\n * @module core/Core\n */\nimport Core from \"../core/core.js\";\n\n/**\n * AmplitudeJS SoundCloud Module\n * @module soundcloud/SoundCloud\n */\nimport SoundCloud from \"../soundcloud/soundcloud.js\";\n\n/**\n * Imports the utilities used by the main module.\n */\n/**\n * AmplitudeJS Config State Module\n * @module utilities/ConfigState\n */\nimport ConfigState from \"../utilities/configState.js\";\n\n/**\n * AmplitudeJS Debug Module\n * @module utilities/Debug\n */\nimport Debug from \"../utilities/debug.js\";\n\n/**\n * AmplitudeJS Checks Module\n * @module utilities/Checks\n */\nimport Checks from \"../utilities/checks.js\";\n\n/**\n * AmplitudeJS Shuffler Module\n * @module utilities/Shuffler\n */\nimport Shuffler from \"../utilities/shuffler.js\";\n\n/**\n * AmplitudeJS Events Module\n * @module events/Events\n */\nimport Events from \"../events/events.js\";\n\n/**\n * AmplitudeJS FX Module\n * @module fx/Fx\n */\nimport Fx from \"../fx/fx.js\";\n\n/**\n * AmplitudeJS Visualizations Module\n * @module fx/Visualizations\n */\nimport Visualizations from \"../fx/visualizations.js\";\n\n/**\n * AmplitudeJS WaveForm Module\n * @module fx/WaveForm\n */\nimport WaveForm from \"../fx/waveform.js\";\n\n/**\n * AmplitudeJS Audio Navigation Module.\n * @module utilities/AudioNavigation\n */\nimport AudioNavigation from \"../utilities/audioNavigation.js\";\n\n/**\n * AmplitudeJS Callbacks Module\n * @module utilities/Callbacks\n */\nimport Callbacks from \"../utilities/callbacks.js\";\n\n/**\n * AmplitudeJS Playlists Initializer Module\n * @module init/Playlists\n */\nimport PlaylistsInitializer from \"./playlists.js\";\n\n/**\n * Imports the AmplitudeJS Shuffle Elements\n * @module visual/ShuffleElements\n */\nimport ShuffleElements from \"../visual/shuffleElements.js\";\n\n/**\n * Imports the AmplitudeJS Mute Elements\n * @module visual/MuteElements\n */\nimport MuteElements from \"../visual/muteElements.js\";\n\n/**\n * Imports the AmplitudeJS Volume Slider\n * @module visual/VolumeSliderElements\n */\nimport VolumeSliderElements from \"../visual/volumeSliderElements.js\";\n\n/**\n * Imports the AmplitudeJS Time Elements\n * @module visual/TimeElements\n */\nimport TimeElements from \"../visual/timeElements.js\";\n\n/**\n * Imports the AmplitudeJS Play/Pause Elements Module.\n * @module visual/PlayPauseElements\n */\nimport PlayPauseElements from \"../visual/playPauseElements.js\";\n\n/**\n * Imports the AmplitudeJS MetaData Elements Module.\n * @module visual/MetaDataElements\n */\nimport MetaDataElements from \"../visual/metaDataElements.js\";\n\n/**\n * Imports the AmplitudeJS PlaybackSpeedElements Module.\n * @module visual/PlayBackSpeedElements\n */\nimport PlaybackSpeedElements from \"../visual/playbackSpeedElements.js\";\n\n/**\n * Imports the AmplitudeJS Repeat Element\n * @module visual/RepeatElements\n */\nimport RepeatElements from \"../visual/repeatElements.js\";\n\n/**\n * AmplitudeJS Initializer Module. Helps with the handling of all of the\n * initialization for AmplitudeJS.\n *\n * @module init/Initializer\n */\nlet Initializer = (function() {\n  /**\n   * The main init function.  The user will call this through\n   * Amplitude.init({}) and pass in their settings.\n   *\n   * Public Accessor: Amplitude.init( user_config_json )\n   * @access public\n   * @param {object} userConfig - A JSON object of user defined values that help configure and initialize AmplitudeJS.\n   */\n  function initialize(userConfig) {\n    let ready = false;\n\n    /*\n\t\t\tReset the config on init so we have a clean slate. This is if the\n\t\t\tuser has to re-init.\n\t\t*/\n    ConfigState.resetConfig();\n\n    /*\n\t\t\tInitialize event handlers on init. This will clear any old\n\t\t\tevent handlers on the amplitude element and re-bind what is\n\t\t\tnecessary.\n\t\t*/\n    Events.initialize();\n\n    /*\n      Initialize the callbacks we listen to for the audio object.\n    */\n    Callbacks.initialize();\n\n    /*\n\t\t\tInitializes debugging right away so we can use it for the rest\n\t\t\tof the configuration.\n\t\t*/\n    config.debug = userConfig.debug != undefined ? userConfig.debug : false;\n\n    /*\n      Set default artwork, if specified.\n    */\n    setArt(userConfig)\n\n    /*\n\t\t\tChecks to see if the user has songs defined.\n\t\t*/\n    if (userConfig.songs) {\n      /*\n\t\t\t\tChecks to see if the user has some songs in the songs array.\n\t\t\t*/\n      if (userConfig.songs.length != 0) {\n        /*\n\t\t\t\t\tCopies over the user defined songs. and prepares\n\t\t\t\t\tAmplitude for the rest of the configuration.\n\t\t\t\t*/\n        config.songs = userConfig.songs;\n        /*\n\t\t\t\t\tFlag amplitude as ready.\n\t\t\t\t*/\n        ready = true;\n      } else {\n        Debug.writeMessage(\"Please add some songs, to your songs object!\");\n      }\n    } else {\n      Debug.writeMessage(\n        \"Please provide a songs object for AmplitudeJS to run!\"\n      );\n    }\n\n    /*\n\t\t\tInitializes the audio context. In this method it checks to see if the\n\t\t\tuser wants to use visualizations or not before proceeding.\n\t\t*/\n    if (Fx.webAudioAPIAvailable()) {\n      if( Fx.determineUsingAnyFX() ){\n        /*\n          Configure the Web Audio API If It's available.\n        */\n        Fx.configureWebAudioAPI();\n\n        /*\n            Activates the audio context after an event for the user.\n        */\n        document.documentElement.addEventListener(\n          \"mousedown\", function(){\n            if (config.context.state !== 'running') {\n              config.context.resume();\n            }\n          });\n\n          document.documentElement.addEventListener(\n          \"keydown\", function(){\n            if (config.context.state !== 'running') {\n              config.context.resume();\n            }\n          });\n\n          document.documentElement.addEventListener(\n            \"keyup\", function(){\n              if (config.context.state !== 'running') {\n                config.context.resume();\n              }\n          });\n  \n      \n\n          /*\n            Set the user waveform settings if provided.\n          */\n          if (\n            userConfig.waveforms != undefined &&\n            userConfig.waveforms.sample_rate != undefined\n          ) {\n            config.waveforms.sample_rate = userConfig.waveforms.sample_rate;\n          }\n\n          /*\n            Initialize the waveform.\n          */\n          WaveForm.init();\n\n          /*\n            If the user is registering visualizations on init,\n            we set them right away.\n          */\n          if (\n            userConfig.visualizations != undefined &&\n            userConfig.visualizations.length > 0\n          ) {\n            /*\n                  Iterate over all of the visualizations and\n                  register them in our player.\n                */\n            for (let i = 0; i < userConfig.visualizations.length; i++) {\n              Visualizations.register(\n                userConfig.visualizations[i].object,\n                userConfig.visualizations[i].params\n              );\n            }\n          }\n      }\n    } else {\n      Debug.writeMessage(\n        \"The Web Audio API is not available on this platform. We are using your defined backups!\"\n      );\n    }\n\n    /*\n      Initialize default live settings\n    */\n    initializeDefaultLiveSettings();\n\n    /*\n      Initialize default song indexes\n    */\n    initializeDefaultSongIndexes();\n\n    /*\n\t\t\tWhen the preliminary config is ready, we are ready to proceed.\n\t\t*/\n    if (ready) {\n      /*\n\t\t\t\tCopies over the soundcloud information to the global config\n\t\t\t\twhich will determine where we go from there.\n\t\t\t*/\n      config.soundcloud_client =\n        userConfig.soundcloud_client != undefined\n          ? userConfig.soundcloud_client\n          : \"\";\n\n      /*\n\t\t\t\tChecks if we want to use the art loaded from soundcloud.\n\t\t\t*/\n      config.soundcloud_use_art =\n        userConfig.soundcloud_use_art != undefined\n          ? userConfig.soundcloud_use_art\n          : \"\";\n\n      /*\n\t\t\t\tIf the user provides a soundcloud client then we assume that\n\t\t\t\tthere are URLs in their songs that will reference SoundCloud.\n\t\t\t\tWe then copy over the user config they provided to the\n\t\t\t\ttemp_user_config so we don't mess up the global or their configs\n\t\t\t\tand load the soundcloud information.\n\t\t\t*/\n      let tempUserConfig = {};\n\n      /*\n        If there's a soundcloud_client key set, we load the SoundCloud data\n        for all of the songs in the array.\n      */\n      if (config.soundcloud_client != \"\") {\n        tempUserConfig = userConfig;\n\n        /*\n\t\t\t\t\tLoad up SoundCloud for use with AmplitudeJS.\n\t\t\t\t*/\n        SoundCloud.loadSoundCloud(tempUserConfig);\n      } else {\n        /*\n\t\t\t\t\tThe user is not using Soundcloud with Amplitude at this point\n\t\t\t\t\tso we just finish the configuration with the users's preferences.\n\t\t\t\t*/\n        setConfig(userConfig);\n      }\n    }\n\n    /*\n\t\t\tDebug out what was initialized with AmplitudeJS.\n\t\t*/\n    Debug.writeMessage(\"Initialized With: \");\n    Debug.writeMessage(config);\n  }\n\n  /**\n   * Rebinds all of the elements in the display.\n   *\n   * Public Accessor: Amplitude.rebindDisplay()\n   * @access public\n   */\n  function rebindDisplay() {\n    Events.initialize();\n    MetaDataElements.displayMetaData();\n  }\n\n  /**\n   * Finishes the initalization of the config. Takes all of the user defined\n   * parameters and makes sure they override the defaults. The important\n   * config information is assigned in the publicInit() function.\n   *\n   * This function can be called from 2 different locations:\n   * \t1. Right away on init after the important settings are defined.\n   *\n   * \t2. After all of the Soundcloud URLs are resolved properly and\n   *\t \tsoundcloud is configured.  We will need the proper URLs from Soundcloud\n   * \t\tto stream through Amplitude so we get those right away before we\n   * \t\tset the information and the active song\n   *\n   * @access public\n   * @param {object} userConfig - A JSON object of user defined values that help configure and initialize AmplitudeJS.\n   */\n  function setConfig(userConfig) {\n    /*\n      Checks if the user has any playlists defined. If they do\n      we have to initialize the functionality for the playlists.\n    */\n    if (userConfig.playlists && countPlaylists(userConfig.playlists) > 0) {\n      PlaylistsInitializer.initialize(userConfig.playlists);\n    }\n\n    /*\n\t\t\tCheck to see if the user entered a start song\n\t\t*/\n    if (userConfig.start_song != undefined && userConfig.starting_playlist) {\n      /*\n\t\t\t\tEnsure what has been entered is an integer.\n\t\t\t*/\n      if (Checks.isInt(userConfig.start_song)) {\n        AudioNavigation.changeSong(\n          config.songs[userConfig.start_song],\n          userConfig.start_song\n        );\n      } else {\n        Debug.writeMessage(\n          \"You must enter an integer index for the start song.\"\n        );\n      }\n    } else {\n      AudioNavigation.changeSong(config.songs[0], 0);\n    }\n\n    /*\n      If the shuffle is on by default, shuffle the songs and\n      switch to the shuffled song.\n    */\n    if (userConfig.shuffle_on != undefined && userConfig.shuffle_on) {\n      config.shuffle_on = true;\n      Shuffler.shuffleSongs();\n\n      AudioNavigation.changeSong(config.shuffle_list[0], 0);\n    }\n\n    /*\n\t\t\tAllows the user to set whether they want to continue to the next song\n\t\t\twhen the current song finishes or not. In any scenario that's not a playlist,\n\t\t\tcontining to the next song may not be desired.\n\t\t*/\n    config.continue_next =\n      userConfig.continue_next != undefined ? userConfig.continue_next : true;\n\n    /*\n\t\t\tIf the user defined a playback speed, we copy over their\n\t\t\tpreference here, otherwise we default to normal playback\n\t\t\tspeed of 1.0.\n\t\t*/\n    config.playback_speed =\n      userConfig.playback_speed != undefined ? userConfig.playback_speed : 1.0;\n\n    /*\n\t\t\tSets the audio playback speed.\n\t\t*/\n    Core.setPlaybackSpeed(config.playback_speed);\n\n    /*\n\t\t\tIf the user wants the song to be pre-loaded for instant\n\t\t\tplayback, they set it to true. By default it's set to just\n\t\t\tload the metadata.\n\t\t*/\n    config.audio.preload =\n      userConfig.preload != undefined ? userConfig.preload : \"auto\";\n\n    /*\n\t\t\tInitializes the user defined callbacks. This should be a JSON\n\t\t\tobject that contains a key->value store of the callback name\n\t\t\tand the name of the function the user needs to call.\n\t\t*/\n    config.callbacks =\n      userConfig.callbacks != undefined ? userConfig.callbacks : {};\n\n    /*\n\t\t\tInitializes the user defined key bindings. This should be a JSON\n\t\t\tobject that contains a key->value store of the key event number\n\t\t\tpressed and the method to be run.\n\t\t*/\n    config.bindings =\n      userConfig.bindings != undefined ? userConfig.bindings : {};\n\n    /*\n\t\t\tThe user can define a starting volume in a range of 0-100 with\n\t\t\t0 being muted and 100 being the loudest. After the config is set\n\t\t\tAmplitude sets the active song's volume to the volume defined\n\t\t\tby the user.\n\t\t*/\n    config.volume = userConfig.volume != undefined ? userConfig.volume : 50;\n\n    /*\n\t\t\tSets the delay between songs if the user has it set. This should be in MS.\n\t\t*/\n    config.delay = userConfig.delay != undefined ? userConfig.delay : 0;\n\n    /*\n\t\t\tThe user can set the volume increment and decrement values between 1 and 100\n\t\t\tfor when the volume up or down button is pressed.  The default is an increase\n\t\t\tor decrease of 5.\n\t\t*/\n    config.volume_increment =\n      userConfig.volume_increment != undefined\n        ? userConfig.volume_increment\n        : 5;\n\n    config.volume_decrement =\n      userConfig.volume_decrement != undefined\n        ? userConfig.volume_decrement\n        : 5;\n\n    /*\n\t\t\tSet the volume to what is defined in the config. The user can define this,\n\t\t\tso we should set it up that way.\n\t\t*/\n    Core.setVolume(config.volume);\n\n    /*\n     Set default artwork, if specified\n     */\n    setArt(userConfig)\n\n    /*\n      Initialize the visual elements\n    */\n    initializeElements();\n\n    /*\n\t\t\tIf the user has selected a starting playlist, we need to set the starting playlist\n\t\t\tand sync the visuals\n\t\t*/\n    if (\n      userConfig.starting_playlist != undefined &&\n      userConfig.starting_playlist != \"\"\n    ) {\n      /*\n\t\t\t\tSet the active playlist to the starting playlist by the user\n\t\t\t*/\n      config.active_playlist = userConfig.starting_playlist;\n\n      /*\n\t\t\t\tCheck if the user defined a song to start with in the playlist.\n\t\t\t*/\n      if (\n        userConfig.starting_playlist_song != undefined &&\n        userConfig.starting_playlist_song != \"\"\n      ) {\n        /*\n\t\t\t\t\tEnsure the song is a valid index.\n\t\t\t\t*/\n        if (\n          typeof userConfig.playlists[userConfig.starting_playlist].songs[\n            parseInt(userConfig.starting_playlist_song)\n          ] != undefined\n        ) {\n          /*\n\t\t\t\t\t\tSet the player to the song defined by the user.\n\t\t\t\t\t*/\n          AudioNavigation.changeSongPlaylist(\n            config.active_playlist,\n            userConfig.playlists[userConfig.starting_playlist].songs[\n              parseInt(userConfig.starting_playlist_song)\n            ],\n            parseInt(userConfig.starting_playlist_song)\n          );\n        } else {\n          /*\n\t\t\t\t\t\tSet the player to the first song in the playlist\n\t\t\t\t\t*/\n          AudioNavigation.changeSongPlaylist(\n            config.active_playlist,\n            userConfig.playlists[userConfig.starting_playlist].songs[0],\n            0\n          );\n          /*\n\t\t\t\t\t\tDebug that the song index doesn't exist\n\t\t\t\t\t*/\n          Debug.writeMessage(\n            \"The index of \" +\n              userConfig.starting_playlist_song +\n              \" does not exist in the playlist \" +\n              userConfig.starting_playlist\n          );\n        }\n      } else {\n        /*\n\t\t\t\t\tSet the player to the first song in the playlist\n\t\t\t\t*/\n        AudioNavigation.changeSong(\n          config.active_playlist,\n          userConfig.playlists[userConfig.starting_playlist].songs[0],\n          0\n        );\n      }\n\n      /*\n\t\t\t\tSync the main and song play pause buttons.\n\t\t\t*/\n      PlayPauseElements.sync();\n    }\n\n    /*\n\t\t\tRun after init callback\n\t\t*/\n    Callbacks.run(\"initialized\");\n  }\n\n  /**\n   * Sets the default_album_art and default_playlist_art from the\n   * user supplied configuration.\n   *\n   * @access public\n   * @param {object} userConfig - A JSON object of user defined values that help configure and initialize AmplitudeJS.\n   */\n  function setArt(userConfig){\n    /*\n      If the user defines default album art, this image will display if the active\n      song doesn't have album art defined.\n    */\n    if (userConfig.default_album_art != undefined) {\n      config.default_album_art = userConfig.default_album_art;\n    } else {\n      config.default_album_art = \"\";\n    }\n\n    /*\n\t\t\tIf the user defines default playlist art, this image will display if the user\n\t\t\ttries to set up a playlist meta data image tag but doesn't have one defined.\n\t\t*/\n    if (userConfig.default_playlist_art != undefined) {\n      config.default_playlist_art = userConfig.default_playlist_art;\n    } else {\n      config.default_playlist_art = \"\";\n    }\n  }\n\n  /**\n   * Initializes all of the elements on the page to the default starting point\n   * to build from there.\n   *\n   * @access private\n   */\n  function initializeElements() {\n    /*\n\t\t\tVisually sync the shuffle statuses\n\t\t*/\n    ShuffleElements.syncMain();\n\n    /*\n\t\t\tSync Mute Elements.\n\t\t*/\n    MuteElements.setMuted(config.volume == 0 ? true : false);\n\n    /*\n\t\t\tSync Volume Slider Elements\n\t\t*/\n    VolumeSliderElements.sync();\n\n    /*\n\t\t\tSyncs all of the playback speed elements.\n\t\t*/\n    PlaybackSpeedElements.sync();\n\n    /*\n\t\t\tSyncs all of the visual time elements to 00.\n\t\t*/\n    TimeElements.resetCurrentTimes();\n\n    /*\n\t\t\tSets all of the play pause buttons to pause.\n\t\t*/\n    PlayPauseElements.syncToPause();\n\n    /*\n\t\t\tSets the meta data for the songs automatically.\n\t\t*/\n    MetaDataElements.syncMetaData();\n\n    /*\n\t\t\tSets the repeat buttons automatically.\n\t\t*/\n    RepeatElements.syncRepeatSong();\n  }\n\n  /**\n   * Counts the number of playlists the user has configured. This ensures\n   * that the user has at least 1 playlist so we can validate the songs\n   * defined in the playlist are correct and they didn't enter an invalid\n   * ID.\n   *\n   * @access private\n   * @param {object} playlists \t-\n   */\n  function countPlaylists(playlists) {\n    /*\n\t\t\tInitialize the placeholders to iterate through the playlists\n\t\t\tand find out how many we have to account for.\n\t\t*/\n    let size = 0,\n      key;\n\n    /*\n\t\t\tIterate over playlists and if the user has the playlist defined,\n\t\t\tincrement the size of playlists.\n\t\t*/\n    for (key in playlists) {\n      if (playlists.hasOwnProperty(key)) {\n        size++;\n      }\n    }\n\n    /*\n\t\t\tDebug how many playlists are in the config.\n\t\t*/\n    Debug.writeMessage(\"You have \" + size + \" playlist(s) in your config\");\n\n    /*\n\t\t\tReturn the number of playlists in the config.\n\t\t*/\n    return size;\n  }\n\n  /**\n   * Intializes the default live settings for all of the songs.\n   *\n   * @access private\n   */\n  function initializeDefaultLiveSettings() {\n    for (let i = 0; i < config.songs.length; i++) {\n      if (config.songs[i].live == undefined) {\n        config.songs[i].live = false;\n      }\n    }\n  }\n\n  /** \n   * Initializes the index of the song in the songs array so\n   * we can reference it if needed\n   * \n   * @access private\n   */\n  function initializeDefaultSongIndexes(){\n    for (let i = 0; i < config.songs.length; i++) {\n      config.songs[i].index = i;\n    }\n  }\n\n\n  /*\n\t\tReturns the publicly accessible methods\n\t*/\n  return {\n    initialize: initialize,\n    setConfig: setConfig,\n    rebindDisplay: rebindDisplay\n  };\n})();\n\nexport default Initializer;\n\n\n\n// WEBPACK FOOTER //\n// ./src/init/init.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Builds a waveform for the current audio.\n * Help from: https://robots.thoughtbot.com/javascript-audio-api\n * https://stackoverflow.com/questions/21347833/html-svg-not-drawing-works-in-other-pages\n */\nlet WaveForm = (function() {\n  /*\n    Initialize the local variables used in the Waveform.\n  */\n  let buffer = \"\";\n  let sampleRate = config.waveforms.sample_rate;\n  let peaks = \"\";\n\n  function init() {\n    /*\n      Grabs all of the waveform elements on the page.\n    */\n    let waveforms = document.querySelectorAll(\".amplitude-wave-form\");\n\n    /*\n      If there are waveforms, we iterate over them and set them up to display\n      properly.\n    */\n    if (waveforms.length > 0) {\n      /*\n        Iterate over all of the waveforms and build the SVG parts.\n      */\n      for (let i = 0; i < waveforms.length; i++) {\n        /*\n          Clear the inner HTML of the element if we are replacing the waveform.\n        */\n        waveforms[i].innerHTML = \"\";\n\n        /*\n          Inserts an SVG into the element.\n        */\n        let svg = document.createElementNS(\"http://www.w3.org/2000/svg\", \"svg\");\n        svg.setAttribute(\"viewBox\", \"0 -1 \" + sampleRate + \" 2\");\n        svg.setAttribute(\"preserveAspectRatio\", \"none\");\n\n        /*\n          Add a g component to the SVG\n        */\n        let g = document.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n        svg.appendChild(g);\n\n        /*\n          Add a path component to the g\n        */\n        let path = document.createElementNS(\n          \"http://www.w3.org/2000/svg\",\n          \"path\"\n        );\n        path.setAttribute(\"d\", \"\");\n        path.setAttribute(\"id\", \"waveform\");\n\n        g.appendChild(path);\n\n        /*\n          Append the SVG to the waveform.\n        */\n        waveforms[i].appendChild(svg);\n      }\n    }\n  }\n\n  /**\n   * Builds each waveform for the page.\n   */\n  function build() {\n    if (config.web_audio_api_available) {\n      /*\n        If we don't have the wave form built, we need to build the waveform by loading\n        the src with an array buffer.\n      */\n      if (\n        config.waveforms.built[\n          Math.abs(\n            config.audio.src.split(\"\").reduce(function(a, b) {\n              a = (a << 5) - a + b.charCodeAt(0);\n              return a & a;\n            }, 0)\n          )\n        ] == undefined\n      ) {\n        /*\n          Initializes a new XML Http Request.\n        */\n        var req = new XMLHttpRequest();\n\n        /*\n          Opens the src parameter for the audio file to read in.\n        */\n        req.open(\"GET\", config.audio.src, true);\n        req.responseType = \"arraybuffer\";\n\n        /*\n          When the ready state changes, check to see if we can render the\n          wave form.\n        */\n        req.onreadystatechange = function(e) {\n          /*\n            When the request is complete, then we begin decoding the\n            audio to build the waveform.\n          */\n          if (req.readyState == 4) {\n            /*\n              If the status is 200 means the response is a success and\n              we decode the audio data.\n            */\n            if (req.status == 200) {\n              /*\n                Decode the audio data and process the waveform.\n              */\n              config.context.decodeAudioData(req.response, function(\n                bufferedAudio\n              ) {\n                /*\n                  Set the buffer to the audio returned.\n                */\n                buffer = bufferedAudio;\n\n                /*\n                  Get the peaks in the audio.\n                */\n                peaks = getPeaks(sampleRate, buffer);\n\n                /*\n                  Build the SVG\n                */\n                process(sampleRate, buffer, peaks);\n              });\n            }\n          }\n        };\n        req.send();\n      } else {\n        /*\n          If we already have a waveform, we grab the waveform that\n          was created for the song and display it. We do a simple hash\n          of the song URL so it's already unique.\n        */\n        displayWaveForms(\n          config.waveforms.built[\n            Math.abs(\n              config.audio.src.split(\"\").reduce(function(a, b) {\n                a = (a << 5) - a + b.charCodeAt(0);\n                return a & a;\n              }, 0)\n            )\n          ]\n        );\n      }\n    }\n  }\n\n  /**\n   * Processes the audio and generates the waveform.\n   *\n   * @param {sampleRate} sampleRate - The rate we should sample the audio.\n   * @param {arraybuffer} buffer - The Web Audio API\n   * @param {array} peaks - The peaks in the audio.\n   */\n  function process(sampleRate, buffer, peaks) {\n    /*\n      If we have a buffer, we find the peaks in the audio.\n    */\n    if (buffer) {\n      /*\n        Get the total peaks in the song.\n      */\n      let totalPeaks = peaks.length;\n\n      /*\n        Figure out the depth of the peak.\n      */\n      let d = \"\";\n      for (let peakNumber = 0; peakNumber < totalPeaks; peakNumber++) {\n        if (peakNumber % 2 === 0) {\n          d += ` M${~~(peakNumber / 2)}, ${peaks.shift()}`;\n        } else {\n          d += ` L${~~(peakNumber / 2)}, ${peaks.shift()}`;\n        }\n      }\n\n      /*\n        Add the waveform to the built waveforms array.\n      */\n      config.waveforms.built[\n        Math.abs(\n          config.audio.src.split(\"\").reduce(function(a, b) {\n            a = (a << 5) - a + b.charCodeAt(0);\n            return a & a;\n          }, 0)\n        )\n      ] = d;\n\n      /*\n        Display the waveform.\n      */\n      displayWaveForms(\n        config.waveforms.built[\n          Math.abs(\n            config.audio.src.split(\"\").reduce(function(a, b) {\n              a = (a << 5) - a + b.charCodeAt(0);\n              return a & a;\n            }, 0)\n          )\n        ]\n      );\n    }\n  }\n\n  /**\n   * Get the peaks of the audio for the waveform.\n   *\n   * @param {number} length - The sample size of the audio.\n   * @param {array} buffer - The array buffer used to find the peaks in the audio.\n   */\n  function getPeaks(length, buffer) {\n    /*\n      Set the parameters needed to build the SVG.\n    */\n    const sampleSize = buffer.length / length;\n    const sampleStep = ~~(sampleSize / 10) || 1;\n    const numberOfChannels = buffer.numberOfChannels;\n    const mergedPeaks = [];\n\n    /*\n      Iterate over the channels and find the peaks.\n    */\n    for (\n      let channelNumber = 0;\n      channelNumber < numberOfChannels;\n      channelNumber++\n    ) {\n      /*\n        Initialize the peaks array and set the channel data to what\n        the buffer has in its channel data.\n      */\n      const peaks = [];\n      const channelData = buffer.getChannelData(channelNumber);\n\n      /*\n        Iterate over peaks with respect to the sample size.\n      */\n      for (let peakNumber = 0; peakNumber < length; peakNumber++) {\n        /*\n          Gt the start and end peak.\n        */\n        const start = ~~(peakNumber * sampleSize);\n        const end = ~~(start + sampleSize);\n\n        /*\n          Set min and max to the channel data first peak.\n        */\n        let min = channelData[0];\n        let max = channelData[0];\n\n        /*\n          Iterate over the parts of the song starting to the\n          ending to display the waveform.\n        */\n        for (\n          let sampleIndex = start;\n          sampleIndex < end;\n          sampleIndex += sampleStep\n        ) {\n          const value = channelData[sampleIndex];\n\n          if (value > max) {\n            max = value;\n          }\n          if (value < min) {\n            min = value;\n          }\n        }\n\n        /*\n          Set the max and min for the peak.\n        */\n        peaks[2 * peakNumber] = max;\n        peaks[2 * peakNumber + 1] = min;\n\n        /*\n          Merge the peaks\n        */\n        if (channelNumber === 0 || max > mergedPeaks[2 * peakNumber]) {\n          mergedPeaks[2 * peakNumber] = max;\n        }\n\n        if (channelNumber === 0 || min < mergedPeaks[2 * peakNumber + 1]) {\n          mergedPeaks[2 * peakNumber + 1] = min;\n        }\n      }\n    }\n\n    /*\n      Returns the merged peaks.\n    */\n    return mergedPeaks;\n  }\n\n  /**\n   * Displays all of the waveforms necessary.\n   *\n   * @param {path} svg - The drawing of the waveform.\n   */\n  function displayWaveForms(svg) {\n    let waveformElements = document.querySelectorAll(\".amplitude-wave-form\");\n\n    /*\n      Iterate over all of the waveform elements and\n      display the waveform.\n    */\n    for (let i = 0; i < waveformElements.length; i++) {\n      /*\n        Get the playlist attribute of the waveform element.\n      */\n      let playlist = waveformElements[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n\n      /*\n        Get the song index attribute of the waveform element.\n      */\n      let song = waveformElements[i].getAttribute(\"data-amplitude-song-index\");\n\n      /*\n        If the playlist is null and the song is null it's a global element.\n      */\n      if (playlist == null && song == null) {\n        displayGlobalWaveform(waveformElements[i], svg);\n      }\n\n      /*\n        If the playlist is defined but the song is null it's a playlist element.\n      */\n      if (playlist != null && song == null) {\n        displayPlaylistWaveform(waveformElements[i], svg, playlist);\n      }\n\n      /*\n        If the playlist is not defined and the song is not null it's a song\n        element.\n      */\n      if (playlist == null && song != null) {\n        displaySongWaveform(waveformElements[i], svg, song);\n      }\n\n      /*\n        If the playlist and song are defined it's a song in the playlist element.\n      */\n      if (playlist != null && song != null) {\n        displaySongInPlaylistWaveform(waveformElements[i], svg, playlist, song);\n      }\n    }\n  }\n\n  /**\n   * Displays a global wave form.\n   *\n   * @param {Node} element - Element to display the waveform in.\n   * @param {SVG} svg - The waveform path.\n   */\n  function displayGlobalWaveform(element, svg) {\n    let waveformPath = element.querySelector(\"svg g path\");\n\n    waveformPath.setAttribute(\"d\", svg);\n  }\n\n  /**\n   * Displays a playlist wave form.\n   *\n   * @param {Node} element - Element to display the waveform in.\n   * @param {SVG} svg - The waveform path.\n   * @param {string} playlist - The playlist we are displaying the waveform for.\n   */\n  function displayPlaylistWaveform(element, svg, playlist) {\n    /*\n      Ensure the playlist is the active playlist.\n    */\n    if (config.active_playlist == playlist) {\n      let waveformPath = element.querySelector(\"svg g path\");\n\n      waveformPath.setAttribute(\"d\", svg);\n    }\n  }\n\n  /**\n   * Displays a song wave form.\n   *\n   * @param {Node} element - Element to display the waveform in.\n   * @param {SVG} svg - The waveform path.\n   * @param {Integer} song - The index of the song we are displaying the\n   * waveform for.\n   */\n  function displaySongWaveform(element, svg, song) {\n    /*\n      Ensure it's the active song being displayed.\n    */\n    if (config.active_index == song) {\n      let waveformPath = element.querySelector(\"svg g path\");\n\n      waveformPath.setAttribute(\"d\", svg);\n    }\n  }\n\n  /**\n   * Displays a song in playlist waveform.\n   *\n   * @param {Node} element - Element to display the waveform in.\n   * @param {SVG} svg - The waveform path.\n   * @param {String} playlist - The playlist the waveform is in.\n   * @param {Integer} song - The index of the song we are displaying the waveform for.\n   */\n  function displaySongInPlaylistWaveform(element, svg, playlist, song) {\n    /*\n      Ensure it's the active song in the active playlist.\n    */\n    if (\n      config.active_playlist == playlist &&\n      config.playlists[config.active_playlist].active_index == song\n    ) {\n      let waveformPath = element.querySelector(\"svg g path\");\n\n      waveformPath.setAttribute(\"d\", svg);\n    }\n  }\n\n  /**\n   * Determines if the user is using waveforms\n   */\n  function determineIfUsingWaveforms(){\n    let waveforms = document.querySelectorAll(\".amplitude-wave-form\");\n\n    if( waveforms.length > 0 ){\n      return true;\n    }else{\n      return false;\n    }\n  }\n\n  /*\n    Return the public methods.\n  */\n  return {\n    init: init,\n    build: build,\n    determineIfUsingWaveforms: determineIfUsingWaveforms\n  };\n})();\n\nexport default WaveForm;\n\n\n\n// WEBPACK FOOTER //\n// ./src/fx/waveform.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * The utility to handle the computation of time in AmplitudeJS.\n * @module utilities/Time\n */\nlet Time = (function() {\n  /**\n   * Computes the current song time. Breaks down where the song is into\n   * hours, minutes, seconds and formats it to be displayed to the user.\n   *\n   * @access public\n   */\n  function computeCurrentTimes() {\n    /*\n\t\t\tInitialize the current time object that will be returned.\n\t\t*/\n    let currentTime = {};\n\n    /*\n\t\t\tComputes the current seconds for the song.\n\t\t*/\n    let currentSeconds =\n      (Math.floor(config.audio.currentTime % 60) < 10 ? \"0\" : \"\") +\n      Math.floor(config.audio.currentTime % 60);\n\n    /*\n\t\t\tComputes the current minutes for the song.\n\t\t*/\n    let currentMinutes = Math.floor(config.audio.currentTime / 60);\n\n    /*\n\t\t\tInitialize the current hours variable.\n\t\t*/\n    let currentHours = \"00\";\n\n    /*\n\t\t\tIf the current minutes is less than 10, we add a leading 0.\n\t\t*/\n    if (currentMinutes < 10) {\n      currentMinutes = \"0\" + currentMinutes;\n    }\n\n    /*\n\t\t\tIf the user is more than 60 minutes into the song, then\n\t\t\twe extract the hours.\n\t\t*/\n    if (currentMinutes >= 60) {\n      currentHours = Math.floor(currentMinutes / 60);\n      currentMinutes = currentMinutes % 60;\n\n      /*\n\t\t\t\tIf the user is less than 10 minutes in, we append the\n\t\t\t\tadditional 0 to the minutes.\n\t\t\t*/\n      if (currentMinutes < 10) {\n        currentMinutes = \"0\" + currentMinutes;\n      }\n    }\n\n    /*\n\t\t\tBuild a clean current time object and send back the appropriate information.\n\t\t*/\n    currentTime.seconds = currentSeconds;\n    currentTime.minutes = currentMinutes;\n    currentTime.hours = currentHours;\n\n    return currentTime;\n  }\n\n  /**\n   * Computes the current song duration. Breaks down where the song is into\n   * hours, minutes, seconds and formats it to be displayed to the user.\n   *\n   * @access public\n   */\n  function computeSongDuration() {\n    /*\n\t\t\tInitialize the song duration object that will be returned.\n\t\t*/\n    let songDuration = {};\n\n    /*\n\t\t\tComputes the duration of the song's seconds.\n\t\t*/\n    let songDurationSeconds =\n      (Math.floor(config.audio.duration % 60) < 10 ? \"0\" : \"\") +\n      Math.floor(config.audio.duration % 60);\n\n    /*\n\t\t\tComputes the duration of the song's minutes.\n\t\t*/\n    let songDurationMinutes = Math.floor(config.audio.duration / 60);\n\n    /*\n\t\t\tInitialize the hours duration variable.\n\t\t*/\n    var songDurationHours = \"00\";\n\n    /*\n\t\t\tIf the song duration minutes is less than 10, we add a leading 0.\n\t\t*/\n    if (songDurationMinutes < 10) {\n      songDurationMinutes = \"0\" + songDurationMinutes;\n    }\n\n    /*\n\t\t\tIf there is more than 60 minutes in the song, then we\n\t\t\textract the hours.\n\t\t*/\n    if (songDurationMinutes >= 60) {\n      songDurationHours = Math.floor(songDurationMinutes / 60);\n      songDurationMinutes = songDurationMinutes % 60;\n\n      /*\n\t\t\t\tIf the song duration minutes is less than 10 we append\n\t\t\t\tthe additional 0.\n\t\t\t*/\n      if (songDurationMinutes < 10) {\n        songDurationMinutes = \"0\" + songDurationMinutes;\n      }\n    }\n\n    /*\n\t\t\tBuild a clean song duration object and send back the appropriate information.\n\t\t*/\n    songDuration.seconds = isNaN(songDurationSeconds)\n      ? \"00\"\n      : songDurationSeconds;\n    songDuration.minutes = isNaN(songDurationMinutes)\n      ? \"00\"\n      : songDurationMinutes;\n    songDuration.hours = isNaN(songDurationHours)\n      ? \"00\"\n      : songDurationHours.toString();\n\n    return songDuration;\n  }\n\n  /**\n   * Computes the song completion percentage.\n   *\n   * @access public\n   */\n  function computeSongCompletionPercentage() {\n    return (config.audio.currentTime / config.audio.duration) * 100;\n  }\n\n  /**\n   * Sets the current time for the audio.\n   *\n   * @access public\n   */\n  function setCurrentTime(time) {\n    /*\n      If the song is not live, we can set the current time.\n    */\n    if (!config.active_metadata.live) {\n      /*\n        Makes sure the number is finite to set the time.\n      */\n      if (isFinite(time)) {\n        config.audio.currentTime = time;\n      }\n    }\n  }\n\n  /**\n   * Defines what is returned by the module\n   */\n  return {\n    computeCurrentTimes: computeCurrentTimes,\n    computeSongDuration: computeSongDuration,\n    computeSongCompletionPercentage: computeSongCompletionPercentage,\n    setCurrentTime: setCurrentTime\n  };\n})();\n\nexport default Time;\n\n\n\n// WEBPACK FOOTER //\n// ./src/utilities/time.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * AmplitudeJS Visual Handler for Buffered Progress Elements\n *\n * @module visual/BufferedProgressElements\n */\nlet BufferedProgressElements = (function() {\n  /**\n   * Syncs the buffered progress bars to the current percentage in the config\n   *\n   * @access public\n   */\n  function sync() {\n    syncGlobal();\n    syncPlaylist();\n    syncSong();\n    syncSongInPlaylist();\n  }\n\n  /**\n   * Sync the global song buffered progress elements.\n   */\n  function syncGlobal() {\n    /*\n\t\t\tGets all of the song buffered progress bars.\n\t\t*/\n    const songBufferedProgressBars = document.getElementsByClassName(\n      \"amplitude-buffered-progress\"\n    );\n\n    /*\n\t\t\tIterate over all of the song buffered progress bar and\n\t\t\tset them to 0 which is like re-setting them.\n\t\t*/\n    for (let i = 0; i < songBufferedProgressBars.length; i++) {\n      let playlist = songBufferedProgressBars[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n      let song = songBufferedProgressBars[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      if (playlist == null && song == null) {\n        songBufferedProgressBars[i].value = parseFloat(\n          parseFloat(config.buffered) / 100\n        );\n      }\n    }\n  }\n\n  /**\n   * Sync the playlist song buffered progress elements.\n   */\n  function syncPlaylist() {\n    /*\n\t\t\tGets all of the song buffered progress bars.\n\t\t*/\n    const songBufferedProgressBarsPlaylist = document.querySelectorAll(\n      '.amplitude-buffered-progress[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"]'\n    );\n\n    /*\n\t\t\tIterate over all of the song buffered progress bar and\n\t\t\tset them to 0 which is like re-setting them.\n\t\t*/\n    for (let i = 0; i < songBufferedProgressBarsPlaylist.length; i++) {\n      let song = songBufferedProgressBarsPlaylist[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      if (song == null) {\n        songBufferedProgressBarsPlaylist[i].value = parseFloat(\n          parseFloat(config.buffered) / 100\n        );\n      }\n    }\n  }\n\n  /**\n   * Sync the song song buffered progress elements.\n   */\n  function syncSong() {\n    /*\n\t\t\tGets all of the song buffered progress bars.\n\t\t*/\n    const songBufferedProgressBarsSongs = document.querySelectorAll(\n      '.amplitude-buffered-progress[data-amplitude-song-index=\"' +\n        config.active_index +\n        '\"]'\n    );\n\n    /*\n\t\t\tIterate over all of the song buffered progress bar and\n\t\t\tset them to 0 which is like re-setting them.\n\t\t*/\n    for (let i = 0; i < songBufferedProgressBarsSongs.length; i++) {\n      let playlist = songBufferedProgressBarsSongs[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n\n      if (playlist == null) {\n        songBufferedProgressBarsSongs[i].value = parseFloat(\n          parseFloat(config.buffered) / 100\n        );\n      }\n    }\n  }\n\n  /**\n   * Sync the song in playlist song buffered progress elements.\n   */\n  function syncSongInPlaylist() {\n    let activePlaylistIndex =\n      config.active_playlist != null && config.active_playlist != \"\"\n        ? config.playlists[config.active_playlist].active_index\n        : null;\n\n    /*\n\t\t\tGets all of the song buffered progress bars.\n\t\t*/\n    const songBufferedProgressBarsSongsInPlaylist = document.querySelectorAll(\n      '.amplitude-buffered-progress[data-amplitude-song-index=\"' +\n        activePlaylistIndex +\n        '\"][data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"]'\n    );\n\n    /*\n\t\t\tIterate over all of the song buffered progress bar and\n\t\t\tset them to 0 which is like re-setting them.\n\t\t*/\n    for (let i = 0; i < songBufferedProgressBarsSongsInPlaylist.length; i++) {\n      songBufferedProgressBarsSongsInPlaylist[i].value = parseFloat(\n        parseFloat(config.buffered) / 100\n      );\n    }\n  }\n\n  /**\n   * Sets all of the song buffered progress bars to 0\n   *\n   * @access public\n   */\n  function reset() {\n    /*\n\t\t\tGets all of the song buffered progress bars.\n\t\t*/\n    let songBufferedProgressBars = document.getElementsByClassName(\n      \"amplitude-buffered-progress\"\n    );\n\n    /*\n\t\t\tIterate over all of the song buffered progress bar and\n\t\t\tset them to 0 which is like re-setting them.\n\t\t*/\n    for (let i = 0; i < songBufferedProgressBars.length; i++) {\n      songBufferedProgressBars[i].value = 0;\n    }\n  }\n\n  /**\n   * Returns the public facing methods\n   */\n  return {\n    sync: sync,\n    reset: reset\n  };\n})();\n\nexport default BufferedProgressElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/bufferedProgressElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the Audio Navigation Utility\n * @module utilities/AudioNavigation\n */\nimport AudioNavigation from \"../utilities/audioNavigation.js\";\n\n/**\n * Imports the Callback Utility\n * @module utilities/callbacks\n */\nimport Callbacks from \"../utilities/callbacks.js\";\n\n/**\n * Imports the AmplitudeJS Core Methods\n * @module core/Core\n */\nimport Core from \"../core/core.js\";\n\n/**\n * Imports the AmplitudeJS Play Pause Elements\n * @module visual/PlayPauseElements\n */\nimport PlayPauseElements from \"../visual/playPauseElements.js\";\n\n/**\n * AmplitudeJS Ended Module. Handles the ended event on the audio.\n *\n * @module events/Ended\n */\nlet Ended = (function() {\n  /**\n   * When the song has ended, handles what to do next\n   *\n   * HANDLER FOR: ended\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n      Sets the time out for song ended. This determines if\n      we should go to the next song or delay between songs.\n    */\n    setTimeout(function() {\n      /*\n        If we continue next, we should move to the next song in the playlist.\n      */\n      if (config.continue_next) {\n        /*\n\t\t\t\t\tIf the active playlist is not set, we set the\n\t\t\t\t\tnext song that's in the songs array.\n\t\t\t\t*/\n        if (config.active_playlist == \"\" || config.active_playlist == null) {\n          AudioNavigation.setNext(true);\n        } else {\n          AudioNavigation.setNextPlaylist(config.active_playlist, true);\n        }\n      } else {\n        if (!config.is_touch_moving) {\n          /*\n\t\t\t\t\t\tStops the active song.\n\t\t\t\t\t*/\n          AmplitudeCore.stop();\n\n          /*\n            Sync the play pause elements.\n          */\n          PlayPauseElements.sync();\n        }\n      }\n    }, config.delay);\n  }\n\n  /*\n    Returns the public facing methods.\n  */\n  return {\n    handle: handle\n  };\n})();\n\nexport default Ended;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/ended.js", "/*\n\tImport the necessary classes and config to use\n\twith the events.\n*/\nimport config from \"../config.js\";\n\n/**\n * Imports all of the handler objects used by the events.\n */\nimport KeyDown from \"./keydown.js\";\nimport TimeUpdate from \"./timeUpdate.js\";\nimport Ended from \"./ended.js\";\nimport Progress from \"./progress.js\";\nimport Play from \"./play.js\";\nimport Pause from \"./pause.js\";\nimport PlayPause from \"./playPause.js\";\nimport Stop from \"./stop.js\";\nimport Mute from \"./mute.js\";\nimport VolumeUp from \"./volumeUp.js\";\nimport VolumeDown from \"./volumeDown.js\";\nimport SongSlider from \"./songSlider.js\";\nimport VolumeSlider from \"./volumeSlider.js\";\nimport Next from \"./next.js\";\nimport Prev from \"./prev.js\";\nimport Repeat from \"./repeat.js\";\nimport RepeatSong from \"./repeatSong.js\";\nimport PlaybackSpeed from \"./playbackSpeed.js\";\nimport Shuffle from \"./shuffle.js\";\nimport SkipTo from \"./skipTo.js\";\nimport WaveForm from \"../fx/waveform.js\";\n\n/**\n * Imports the utility classes used by the evnets.\n */\nimport Debug from \"../utilities/debug.js\";\n\n/**\n * AmplitudeJS Events Module. Handles all of the events we listen to in\n * AmplitudeJS.\n *\n * @module events/Events\n */\nvar Events = (function() {\n  /**\n   * Initializes the handlers for the events listened to by Amplitude\n   *\n   * @access public\n   */\n  function initialize() {\n    /*\n\t\t\tWrite out debug message\n\t\t*/\n    Debug.writeMessage(\"Beginning initialization of event handlers..\");\n\n    /*\n\t\t\tSets flag that the screen is moving and not a tap\n\t\t*/\n    document.addEventListener(\"touchmove\", function() {\n      config.is_touch_moving = true;\n    });\n\n    /*\n\t\t\tOn touch end if it was a touch move event, set moving to\n\t\t\tfalse\n\t\t*/\n    document.addEventListener(\"touchend\", function() {\n      if (config.is_touch_moving) {\n        config.is_touch_moving = false;\n      }\n    });\n\n    /*\n\t\t\tOn time update for the audio element, update visual displays that\n\t\t\trepresent the time on either a visualized element or time display.\n\t\t*/\n    bindTimeUpdate();\n\n    /*\n\t\t\tBinds key down event handlers for matching key codes to functions.\n\t\t*/\n    bindKeyDownEventHandlers();\n\n    /*\n\t\t\tWhen the audio element has ended playing, we handle the song\n\t\t\tending. In a single song or multiple modular song instance,\n\t\t\tthis just synchronizes the visuals for time and song time\n\t\t\tvisualization, but for a playlist it determines whether\n\t\t\tit should play the next song or not.\n\t\t*/\n    bindSongEnded();\n\n    /*\n\t\t\tBinds progress event so we can see how much of the song is loaded.\n\t\t*/\n    bindProgress();\n\n    /*\n\t\t\tBinds 'amplitude-play' event handlers\n\t\t*/\n    bindPlay();\n\n    /*\n\t\t\tBinds 'amplitude-pause' event handlers.\n\t\t*/\n    bindPause();\n\n    /*\n\t\t\tBinds 'amplitude-play-pause' event handlers.\n\t\t*/\n    bindPlayPause();\n\n    /*\n\t\t\tBinds 'amplitude-stop' event handlers.\n\t\t*/\n    bindStop();\n\n    /*\n\t\t\tBinds 'amplitude-mute' event handlers.\n\t\t*/\n    bindMute();\n\n    /*\n\t\t\tBinds 'amplitude-volume-up' event handlers\n\t\t*/\n    bindVolumeUp();\n\n    /*\n\t\t\tBinds 'amplitude-volume-down' event handlers\n\t\t*/\n    bindVolumeDown();\n\n    /*\n\t\t\tBinds 'amplitude-song-slider' event handlers\n\t\t*/\n    bindSongSlider();\n\n    /*\n\t\t\tBinds 'amplitude-volume-slider' event handlers.\n\t\t*/\n    bindVolumeSlider();\n\n    /*\n\t\t\tBinds 'amplitude-next' event handlers.\n\t\t*/\n    bindNext();\n\n    /*\n\t\t\tBinds 'amplitude-prev' event handlers.\n\t\t*/\n    bindPrev();\n\n    /*\n\t\t\tBinds 'amplitude-shuffle' event handlers.\n\t\t*/\n    bindShuffle();\n\n    /*\n\t\t\tBinds 'amplitude-repeat' event handlers.\n\t\t*/\n    bindRepeat();\n\n    /*\n\t\t\tBinds 'amplitude-repeat-song' event handlers.\n\t\t*/\n    bindRepeatSong();\n\n    /*\n\t\t\tBinds 'amplitude-playback-speed' event handlers.\n\t\t*/\n    bindPlaybackSpeed();\n\n    /*\n\t\t\tBinds 'amplitude-skip-to' event handlers.\n\t\t*/\n    bindSkipTo();\n\n    /*\n\t\t\tBinds `canplaythrough` event to build the waveform.\n\t\t*/\n    bindCanPlayThrough();\n  }\n\n  /**\n   * On time update for the audio element, update visual displays that\n   * represent the time on either a visualized element or time display.\n   *\n   * @access private\n   */\n  function bindTimeUpdate() {\n    /*\n\t\t\tBind for time update\n\t\t*/\n    config.audio.removeEventListener(\"timeupdate\", TimeUpdate.handle);\n    config.audio.addEventListener(\"timeupdate\", TimeUpdate.handle);\n\n    /*\n\t\t\tBind for duration change\n\t\t*/\n    config.audio.removeEventListener(\"durationchange\", TimeUpdate.handle);\n    config.audio.addEventListener(\"durationchange\", TimeUpdate.handle);\n  }\n\n  /**\n   * On keydown, we listen to what key got pressed so we can map the key to\n   * a function. This allows the user to map pause and play, next, etc. to key\n   * presses.\n   *\n   * @access private\n   */\n  function bindKeyDownEventHandlers() {\n    document.removeEventListener(\"keydown\", KeyDown.handle);\n    document.addEventListener(\"keydown\", KeyDown.handle);\n  }\n\n  /**\n   * When the audio element has ended playing, we handle the song\n   * ending. In a single song or multiple modular song instance,\n   * this just synchronizes the visuals for time and song time\n   * visualization, but for a playlist it determines whether\n   * it should play the next song or not.\n   *\n   * @access private\n   */\n  function bindSongEnded() {\n    config.audio.removeEventListener(\"ended\", Ended.handle);\n    config.audio.addEventListener(\"ended\", Ended.handle);\n  }\n\n  /**\n   * As the audio is loaded, the progress event gets fired. We bind into this\n   * to grab the buffered percentage of the song. We can then add more elements\n   * to show the buffered amount.\n   *\n   * @access private\n   */\n  function bindProgress() {\n    config.audio.removeEventListener(\"progress\", Progress.handle);\n    config.audio.addEventListener(\"progress\", Progress.handle);\n  }\n\n  /**\n   * Binds click and touchend events for AmplitudeJS play buttons\n   *\n   * @access private\n   */\n  function bindPlay() {\n    /*\n\t\t\tGets all of the elements with the class amplitude-play\n\t\t*/\n    var play_classes = document.getElementsByClassName(\"amplitude-play\");\n\n    /*\n\t\t\tIterates over all of the play classes and binds the event interaction\n\t\t\tmethod to the element. If the browser is mobile, then the event is touchend\n\t\t\totherwise it is click.\n\t\t*/\n    for (var i = 0; i < play_classes.length; i++) {\n      if (\n        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n          navigator.userAgent\n        )\n      ) {\n        play_classes[i].removeEventListener(\"touchend\", Play.handle);\n        play_classes[i].addEventListener(\"touchend\", Play.handle);\n      } else {\n        play_classes[i].removeEventListener(\"click\", Play.handle);\n        play_classes[i].addEventListener(\"click\", Play.handle);\n      }\n    }\n  }\n\n  /**\n   * Binds click and touchend events for AmplitudeJS pause buttons.\n   *\n   * @access private\n   */\n  function bindPause() {\n    /*\n\t\t\tGets all of the elements with the class amplitude-pause\n\t\t*/\n    var pause_classes = document.getElementsByClassName(\"amplitude-pause\");\n\n    /*\n\t\t\tIterates over all of the pause classes and binds the event interaction\n\t\t\tmethod to the element. If the browser is mobile, then the event is touchend\n\t\t\totherwise it is click.\n\t\t*/\n    for (var i = 0; i < pause_classes.length; i++) {\n      if (\n        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n          navigator.userAgent\n        )\n      ) {\n        pause_classes[i].removeEventListener(\"touchend\", Pause.handle);\n        pause_classes[i].addEventListener(\"touchend\", Pause.handle);\n      } else {\n        pause_classes[i].removeEventListener(\"click\", Pause.handle);\n        pause_classes[i].addEventListener(\"click\", Pause.handle);\n      }\n    }\n  }\n\n  /**\n   * Binds click and touchend events for AmplitudeJS play pause buttons\n   *\n   * @access private\n   */\n  function bindPlayPause() {\n    /*\n\t\t\tGets all of the elements with the class amplitude-play-pause\n\t\t*/\n    var play_pause_classes = document.getElementsByClassName(\n      \"amplitude-play-pause\"\n    );\n\n    /*\n\t\t\tIterates over all of the play/pause classes and binds the event interaction\n\t\t\tmethod to the element. If the browser is mobile, then the event is touchend\n\t\t\totherwise it is click.\n\t\t*/\n    for (var i = 0; i < play_pause_classes.length; i++) {\n      if (\n        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n          navigator.userAgent\n        )\n      ) {\n        play_pause_classes[i].removeEventListener(\"touchend\", PlayPause.handle);\n        play_pause_classes[i].addEventListener(\"touchend\", PlayPause.handle);\n      } else {\n        play_pause_classes[i].removeEventListener(\"click\", PlayPause.handle);\n        play_pause_classes[i].addEventListener(\"click\", PlayPause.handle);\n      }\n    }\n  }\n\n  /**\n   * Binds click and touchend events for AmplitudeJS stop buttons\n   *\n   * @access private\n   */\n  function bindStop() {\n    /*\n\t\t\tGets all of the elements with the class amplitude-stop\n\t\t*/\n    var stop_classes = document.getElementsByClassName(\"amplitude-stop\");\n\n    /*\n\t\t\tIterates over all of the stop classes and binds the event interaction\n\t\t\tmethod to the element.  If the browser is mobile, then the event is touchend\n\t\t\totherwise it is click.\n\t\t*/\n    for (var i = 0; i < stop_classes.length; i++) {\n      if (\n        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n          navigator.userAgent\n        )\n      ) {\n        stop_classes[i].removeEventListener(\"touchend\", Stop.handle);\n        stop_classes[i].addEventListener(\"touchend\", Stop.handle);\n      } else {\n        stop_classes[i].removeEventListener(\"click\", Stop.handle);\n        stop_classes[i].addEventListener(\"click\", Stop.handle);\n      }\n    }\n  }\n\n  /**\n   * Binds click and touchend events for AmplitudeJS mute buttons\n   *\n   * @access private\n   */\n  function bindMute() {\n    /*\n\t\t\tGets all of the elements with the class amplitue-mute\n\t\t*/\n    var mute_classes = document.getElementsByClassName(\"amplitude-mute\");\n\n    /*\n\t\t\tIterates over all of the mute classes and binds the event interaction\n\t\t\tmethod to the element. If the browser is mobile, then the event is touchend\n\t\t\totherwise it is click.\n\t\t*/\n    for (var i = 0; i < mute_classes.length; i++) {\n      /*\n\t\t\t\tWARNING: If iOS, we don't do anything because iOS does not allow the\n\t\t\t\tvolume to be adjusted through anything except the buttons on the side of\n\t\t\t\tthe device.\n\t\t\t*/\n      if (\n        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n          navigator.userAgent\n        )\n      ) {\n        /*\n\t\t\t\t\tChecks for an iOS device and displays an error message if debugging\n\t\t\t\t\tis turned on.\n\t\t\t\t*/\n        if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {\n          Debug.writeMessage(\n            \"iOS does NOT allow volume to be set through javascript: https://developer.apple.com/library/safari/documentation/AudioVideo/Conceptual/Using_HTML5_Audio_Video/Device-SpecificConsiderations/Device-SpecificConsiderations.html#//apple_ref/doc/uid/**********-CH5-SW4\"\n          );\n        } else {\n          mute_classes[i].removeEventListener(\"touchend\", Mute.handle);\n          mute_classes[i].addEventListener(\"touchend\", Mute.handle);\n        }\n      } else {\n        mute_classes[i].removeEventListener(\"click\", Mute.handle);\n        mute_classes[i].addEventListener(\"click\", Mute.handle);\n      }\n    }\n  }\n\n  /**\n   * Binds click and touchend events for AmplitudeJS Volume Up Buttons\n   *\n   * @access private\n   */\n  function bindVolumeUp() {\n    /*\n\t\t\tGets all of the elements with the class amplitude-volume-up\n\t\t*/\n    var volume_up_classes = document.getElementsByClassName(\n      \"amplitude-volume-up\"\n    );\n\n    /*\n\t\t\tIterates over all of the volume up classes and binds the event interaction\n\t\t\tmethods to the element. If the browser is mobile, then the event is touchend\n\t\t\totherwise it is click.\n\t\t*/\n    for (var i = 0; i < volume_up_classes.length; i++) {\n      /*\n\t\t\t\tWARNING: If iOS, we don't do anything because iOS does not allow the\n\t\t\t\tvolume to be adjusted through anything except the buttons on the side of\n\t\t\t\tthe device.\n\t\t\t*/\n      if (\n        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n          navigator.userAgent\n        )\n      ) {\n        /*\n\t\t\t\t\tChecks for an iOS device and displays an error message if debugging\n\t\t\t\t\tis turned on.\n\t\t\t\t*/\n        if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {\n          Debug.writeMessage(\n            \"iOS does NOT allow volume to be set through javascript: https://developer.apple.com/library/safari/documentation/AudioVideo/Conceptual/Using_HTML5_Audio_Video/Device-SpecificConsiderations/Device-SpecificConsiderations.html#//apple_ref/doc/uid/**********-CH5-SW4\"\n          );\n        } else {\n          volume_up_classes[i].removeEventListener(\"touchend\", VolumeUp.handle);\n          volume_up_classes[i].addEventListener(\"touchend\", VolumeUp.handle);\n        }\n      } else {\n        volume_up_classes[i].removeEventListener(\"click\", VolumeUp.handle);\n        volume_up_classes[i].addEventListener(\"click\", VolumeUp.handle);\n      }\n    }\n  }\n\n  /**\n   * Binds click and touchend events for AmplitudeJS Volume Down Buttons\n   *\n   * @access private\n   */\n  function bindVolumeDown() {\n    /*\n\t\t\tGets all of the elements with the class amplitude-volume-down\n\t\t*/\n    var volume_down_classes = document.getElementsByClassName(\n      \"amplitude-volume-down\"\n    );\n\n    /*\n\t\t\tIterates over all of the volume down classes and binds the event interaction\n\t\t\tmethods to the element. If the browser is mobile, then the event is touchend\n\t\t\totherwise it is click.\n\t\t*/\n    for (var i = 0; i < volume_down_classes.length; i++) {\n      /*\n\t\t\t\tWARNING: If iOS, we don't do anything because iOS does not allow the\n\t\t\t\tvolume to be adjusted through anything except the buttons on the side of\n\t\t\t\tthe device.\n\t\t\t*/\n      if (\n        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n          navigator.userAgent\n        )\n      ) {\n        /*\n\t\t\t\t\tChecks for an iOS device and displays an error message if debugging\n\t\t\t\t\tis turned on.\n\t\t\t\t*/\n        if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {\n          Debug.writeMessage(\n            \"iOS does NOT allow volume to be set through javascript: https://developer.apple.com/library/safari/documentation/AudioVideo/Conceptual/Using_HTML5_Audio_Video/Device-SpecificConsiderations/Device-SpecificConsiderations.html#//apple_ref/doc/uid/**********-CH5-SW4\"\n          );\n        } else {\n          volume_down_classes[i].removeEventListener(\n            \"touchend\",\n            VolumeDown.handle\n          );\n          volume_down_classes[i].addEventListener(\n            \"touchend\",\n            VolumeDown.handle\n          );\n        }\n      } else {\n        volume_down_classes[i].removeEventListener(\"click\", VolumeDown.handle);\n        volume_down_classes[i].addEventListener(\"click\", VolumeDown.handle);\n      }\n    }\n  }\n\n  /**\n   * Binds change and input events for AmplitudeJS Song Slider Inputs\n   *\n   * @access private\n   */\n  function bindSongSlider() {\n    /*\n\t\t\tGets browser so if we need to apply overrides, like we usually\n\t\t\thave to do for anything cool in IE, we can do that.\n\t\t*/\n    var ua = window.navigator.userAgent;\n    var msie = ua.indexOf(\"MSIE \");\n\n    /*\n\t\t\tGets all of the elements with the class amplitude-song-slider\n\t\t*/\n    var song_sliders = document.getElementsByClassName(\"amplitude-song-slider\");\n\n    /*\n\t\t\tIterates over all of the song slider classes and binds the event interaction\n\t\t\tmethods to the element. If the browser is IE we listen to the change event\n\t\t\twhere if it is anything else, it's the input method.\n\t\t*/\n    for (var i = 0; i < song_sliders.length; i++) {\n      if (msie > 0 || !!navigator.userAgent.match(/Trident.*rv\\:11\\./)) {\n        song_sliders[i].removeEventListener(\"change\", SongSlider.handle);\n        song_sliders[i].addEventListener(\"change\", SongSlider.handle);\n      } else {\n        song_sliders[i].removeEventListener(\"input\", SongSlider.handle);\n        song_sliders[i].addEventListener(\"input\", SongSlider.handle);\n      }\n    }\n  }\n\n  /**\n   * Binds change and input events fro AmplitudeJS Volume Slider inputs\n   *\n   * @access private\n   */\n  function bindVolumeSlider() {\n    /*\n\t\t\tGets browser so if we need to apply overrides, like we usually\n\t\t\thave to do for anything cool in IE, we can do that.\n\t\t*/\n    var ua = window.navigator.userAgent;\n    var msie = ua.indexOf(\"MSIE \");\n\n    /*\n\t\t\tGets all of the elements with the class amplitude-volume-slider\n        */\n    var volume_sliders = document.getElementsByClassName(\n      \"amplitude-volume-slider\"\n    );\n\n    /*\n\t\t\tIterates over all of the volume slider classes and binds the event interaction\n\t\t\tmethods to the element. If the browser is IE we listen to the change event\n\t\t\twhere if it is anything else, it's the input method.\n\t\t*/\n    for (var i = 0; i < volume_sliders.length; i++) {\n      /*\n\t\t\t\tWARNING: If iOS, we don't do anything because iOS does not allow the\n\t\t\t\tvolume to be adjusted through anything except the buttons on the side of\n\t\t\t\tthe device.\n\t\t\t*/\n      if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {\n        Debug.writeMessage(\n          \"iOS does NOT allow volume to be set through javascript: https://developer.apple.com/library/safari/documentation/AudioVideo/Conceptual/Using_HTML5_Audio_Video/Device-SpecificConsiderations/Device-SpecificConsiderations.html#//apple_ref/doc/uid/**********-CH5-SW4\"\n        );\n      } else {\n        if (msie > 0 || !!navigator.userAgent.match(/Trident.*rv\\:11\\./)) {\n          volume_sliders[i].removeEventListener(\"change\", VolumeSlider.handle);\n          volume_sliders[i].addEventListener(\"change\", VolumeSlider.handle);\n        } else {\n          volume_sliders[i].removeEventListener(\"input\", VolumeSlider.handle);\n          volume_sliders[i].addEventListener(\"input\", VolumeSlider.handle);\n        }\n      }\n    }\n  }\n\n  /**\n   * Binds click and touchend events fro AmplitudeJS Next buttons\n   *\n   * @access private\n   */\n  function bindNext() {\n    /*\n\t\t\tGets all of the elements with the class amplitude-next\n        */\n    var next_classes = document.getElementsByClassName(\"amplitude-next\");\n\n    /*\n\t\t\tIterates over all of the next classes and binds the event interaction\n\t\t\tmethods to the element. If the browser is mobile, then the event is touchend\n\t\t\totherwise it is click.\n\t\t*/\n    for (var i = 0; i < next_classes.length; i++) {\n      if (\n        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n          navigator.userAgent\n        )\n      ) {\n        next_classes[i].removeEventListener(\"touchend\", Next.handle);\n        next_classes[i].addEventListener(\"touchend\", Next.handle);\n      } else {\n        next_classes[i].removeEventListener(\"click\", Next.handle);\n        next_classes[i].addEventListener(\"click\", Next.handle);\n      }\n    }\n  }\n\n  /**\n   * Binds click and touchend events for AmplitudeJS prev buttons.\n   *\n   * @access private\n   */\n  function bindPrev() {\n    /*\n\t\t\tGets all of the elements with the class amplitude-prev\n\t\t*/\n    var prev_classes = document.getElementsByClassName(\"amplitude-prev\");\n\n    /*\n\t\t\tIterates over all of the prev classes and binds the event interaction\n\t\t\tmethods to the element. If the browser is mobile, then the event is touchend\n\t\t\totherwise it is click.\n\t\t*/\n    for (var i = 0; i < prev_classes.length; i++) {\n      if (\n        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n          navigator.userAgent\n        )\n      ) {\n        prev_classes[i].removeEventListener(\"touchend\", Prev.handle);\n        prev_classes[i].addEventListener(\"touchend\", Prev.handle);\n      } else {\n        prev_classes[i].removeEventListener(\"click\", Prev.handle);\n        prev_classes[i].addEventListener(\"click\", Prev.handle);\n      }\n    }\n  }\n\n  /**\n   * Binds click and touchend events for AmplitudeJS shuffle buttons.\n   *\n   * @access private\n   */\n  function bindShuffle() {\n    /*\n\t\t\tGets all of the elements with the class amplitude-shuffle\n\t\t*/\n    var shuffle_classes = document.getElementsByClassName(\"amplitude-shuffle\");\n\n    /*\n\t\t\tIterates over all of the shuffle classes and binds the event interaction\n\t\t\tmethods to the element. If the browser is mobile, then the event is touchend\n\t\t\totherwise it is click.\n\t\t*/\n    for (var i = 0; i < shuffle_classes.length; i++) {\n      /*\n\t\t\t\tSince we are re-binding everything we remove any classes that signify\n\t\t\t\ta state of the shuffle control.\n\t\t\t*/\n      shuffle_classes[i].classList.remove(\"amplitude-shuffle-on\");\n      shuffle_classes[i].classList.add(\"amplitude-shuffle-off\");\n\n      if (\n        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n          navigator.userAgent\n        )\n      ) {\n        shuffle_classes[i].removeEventListener(\"touchend\", Shuffle.handle);\n        shuffle_classes[i].addEventListener(\"touchend\", Shuffle.handle);\n      } else {\n        shuffle_classes[i].removeEventListener(\"click\", Shuffle.handle);\n        shuffle_classes[i].addEventListener(\"click\", Shuffle.handle);\n      }\n    }\n  }\n\n  /**\n   * Binds click and touchend events for AmplitudeJS repeat buttons.\n   *\n   * @access private\n   */\n  function bindRepeat() {\n    /*\n\t\t\tGets all of the elements with the class amplitude-repeat\n\t\t*/\n    var repeat_classes = document.getElementsByClassName(\"amplitude-repeat\");\n\n    /*\n\t\t\tIterates over all of the repeat classes and binds the event interaction\n\t\t\tmethods to the element. If the browser is mobile, then the event is touchend\n\t\t\totherwise it is click.\n\t\t*/\n    for (var i = 0; i < repeat_classes.length; i++) {\n      /*\n\t\t\t\tSince we are re-binding everything we remove any classes that signify\n\t\t\t\ta state of the repeat control.\n\t\t\t*/\n      repeat_classes[i].classList.remove(\"amplitude-repeat-on\");\n      repeat_classes[i].classList.add(\"amplitude-repeat-off\");\n\n      if (\n        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n          navigator.userAgent\n        )\n      ) {\n        repeat_classes[i].removeEventListener(\"touchend\", Repeat.handle);\n        repeat_classes[i].addEventListener(\"touchend\", Repeat.handle);\n      } else {\n        repeat_classes[i].removeEventListener(\"click\", Repeat.handle);\n        repeat_classes[i].addEventListener(\"click\", Repeat.handle);\n      }\n    }\n  }\n\n  /**\n   * Binds click and touchend events for AmplitudeJS repeat song buttons.\n   *\n   * @access private\n   */\n  function bindRepeatSong() {\n    /*\n\t\t\tGets all of the elements with the class amplitude-repeat-song\n\t\t*/\n    var repeat_song_classes = document.getElementsByClassName(\n      \"amplitude-repeat-song\"\n    );\n\n    /*\n\t\t\tIterates over all of the repeat song classes and binds the event interaction\n\t\t\tmethods to the element. If the browser is mobile, then the event is touchend\n\t\t\totherwise it is click.\n\t\t*/\n    for (var i = 0; i < repeat_song_classes.length; i++) {\n      /*\n\t\t\t\tSince we are re-binding everything we remove any classes that signify\n\t\t\t\ta state of the repeat control.\n\t\t\t*/\n      repeat_song_classes[i].classList.remove(\"amplitude-repeat-on\");\n      repeat_song_classes[i].classList.add(\"amplitude-repeat-off\");\n\n      if (\n        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n          navigator.userAgent\n        )\n      ) {\n        repeat_song_classes[i].removeEventListener(\n          \"touchend\",\n          RepeatSong.handle\n        );\n        repeat_song_classes[i].addEventListener(\"touchend\", RepeatSong.handle);\n      } else {\n        repeat_song_classes[i].removeEventListener(\"click\", RepeatSong.handle);\n        repeat_song_classes[i].addEventListener(\"click\", RepeatSong.handle);\n      }\n    }\n  }\n\n  /**\n   * Binds click and touchend events for AmplitudeJS playback speed buttons\n   *\n   * @access private\n   */\n  function bindPlaybackSpeed() {\n    /*\n\t\t\tGets all of the elements with the class amplitude-playback-speed\n\t\t*/\n    var playback_speed_classes = document.getElementsByClassName(\n      \"amplitude-playback-speed\"\n    );\n\n    /*\n\t\t\tIterates over all of the playback speed classes and binds the event interaction\n\t\t\tmethods to the element. If the browser is mobile, then the event is touchend\n\t\t\totherwise it is click.\n\t\t*/\n    for (var i = 0; i < playback_speed_classes.length; i++) {\n      if (\n        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n          navigator.userAgent\n        )\n      ) {\n        playback_speed_classes[i].removeEventListener(\n          \"touchend\",\n          PlaybackSpeed.handle\n        );\n        playback_speed_classes[i].addEventListener(\n          \"touchend\",\n          PlaybackSpeed.handle\n        );\n      } else {\n        playback_speed_classes[i].removeEventListener(\n          \"click\",\n          PlaybackSpeed.handle\n        );\n        playback_speed_classes[i].addEventListener(\n          \"click\",\n          PlaybackSpeed.handle\n        );\n      }\n    }\n  }\n\n  /**\n   * Binds click and touchend events for AmplitudeJS skip to buttons.\n   *\n   * @access private\n   */\n  function bindSkipTo() {\n    /*\n\t\t\tGets all of the skip to elements with the class 'amplitude-skip-to'\n\t\t*/\n    var skipToClasses = document.getElementsByClassName(\"amplitude-skip-to\");\n\n    /*\n\t\t\tIterates over all of the skip to classes and binds the event interaction\n\t\t\tmethods to the element. If the browser is mobile, then the event is touchend\n\t\t\totherwise it's a click.\n\t\t*/\n    for (var i = 0; i < skipToClasses.length; i++) {\n      if (\n        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n          navigator.userAgent\n        )\n      ) {\n        skipToClasses[i].removeEventListener(\"touchend\", SkipTo.handle);\n        skipToClasses[i].addEventListener(\"touchend\", SkipTo.handle);\n      } else {\n        skipToClasses[i].removeEventListener(\"click\", SkipTo.handle);\n        skipToClasses[i].addEventListener(\"click\", SkipTo.handle);\n      }\n    }\n  }\n\n  /**\n   * Binds can play through to a song.\n   *\n   * @access private\n   */\n  function bindCanPlayThrough() {\n    if( WaveForm.determineIfUsingWaveforms() ){\n      config.audio.removeEventListener(\"canplaythrough\", WaveForm.build);\n      config.audio.addEventListener(\"canplaythrough\", WaveForm.build);\n    }\n  }\n\n  /*\n\t\tReturns the public facing functions.\n\t*/\n  return {\n    initialize: initialize\n  };\n})();\n\nexport default Events;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/events.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the core of AmplitudeJS\n * @module core/Core\n */\nimport Core from \"../core/core.js\";\n\n/**\n * Imports the Shuffle Utility\n * @module utilities/Shuffle\n */\nimport Shuffle from \"../utilities/shuffler.js\";\n\n/**\n * Imports the Repeater Utility\n * @module utilities/Repeater\n */\nimport Repeater from \"../utilities/repeater.js\";\n\n/**\n * Imports the Audio Navigation Utility\n * @module utilities/AudioNavigation\n */\nimport AudioNavigation from \"../utilities/audioNavigation.js\";\n\n/**\n * Imports the Repeat Elements Visual Handler\n * @module visual/RepeatElements\n */\nimport RepeatElements from \"../visual/repeatElements.js\";\n\n/**\n * Imports the Play Pause Elements Visual Handler\n * @module visual/PlayPauseElements\n */\nimport PlayPauseElements from \"../visual/playPauseElements.js\";\n\n/**\n * AmplitudeJS Key Down event handler\n *\n * @module events/KeyDown\n */\nlet KeyDown = (function() {\n  /**\n   * When the keydown event is fired, we determine which function should be run\n   * based on what was passed in.\n   *\n   * HANDLER FOR: keydown\n   *\n   * @access public\n   * @prop {object} event The event object being passed in.\n   */\n  function handle(event) {\n    runKeyEvent(event.which);\n  }\n\n  /**\n   * Runs an event on key down\n   *\n   * @access public\n   * @param {number} key \t- The key code the event is bound to.\n   */\n  function runKeyEvent(key) {\n    /*\n\t\t\tChecks to see if the user bound an event to the code pressed.\n\t\t*/\n    if (config.bindings[key] != undefined) {\n      /*\n\t\t\t\tDetermine which event should be run if bound.\n\t\t\t*/\n      switch (config.bindings[key]) {\n        /*\n\t\t\t\t\tFires a play pause event.\n\t\t\t\t*/\n        case \"play_pause\":\n          runPlayPauseKeyDownEvent();\n          break;\n\n        /*\n\t\t\t\t\tFires a next event.\n\t\t\t\t*/\n        case \"next\":\n          runNextKeyDownEvent();\n          break;\n\n        /*\n\t\t\t\t\tFires a previous event.\n\t\t\t\t*/\n        case \"prev\":\n          runPrevKeyDownEvent();\n          break;\n\n        /*\n\t\t\t\t\tFires a stop event.\n\t\t\t\t*/\n        case \"stop\":\n          runStopKeyDownEvent();\n          break;\n\n        /*\n\t\t\t\t\tFires a shuffle event.\n\t\t\t\t*/\n        case \"shuffle\":\n          runShuffleKeyDownEvent();\n          break;\n\n        /*\n\t\t\t\t\tFires a repeat event.\n\t\t\t\t*/\n        case \"repeat\":\n          runRepeatKeyDownEvent();\n          break;\n      }\n    }\n  }\n\n  /**\n   * Runs the play pause method for key down.\n   */\n  function runPlayPauseKeyDownEvent() {\n    /*\n      If the song is paused, we play the song. If the song is playing,\n      we pause the song.\n    */\n    if (config.audio.paused) {\n      Core.play();\n    } else {\n      Core.pause();\n    }\n\n    /*\n      Now we sync all the elements to match the state of the audio.\n      We don't need to do any checks on new songs or changed playlists\n      in the global since it's whatever song is playing.\n    */\n    PlayPauseElements.sync();\n  }\n\n  /**\n   * Runs the next method for key down.\n   */\n  function runNextKeyDownEvent() {\n    /*\n      Check to see if the current state of the player\n      is in playlist mode or not playlist mode.\n    */\n    if (config.active_playlist == \"\" || config.active_playlist == null) {\n      AudioNavigation.setNext();\n    } else {\n      AudioNavigation.setNextPlaylist(config.active_playlist);\n    }\n  }\n\n  /**\n   * Runs the previous method for key down.\n   */\n  function runPrevKeyDownEvent() {\n    /*\n      Check to see if the current playlist has been set\n      or null and set the previous song.\n    */\n    if (config.active_playlist == \"\" || config.active_playlist == null) {\n      AudioNavigation.setPrevious();\n    } else {\n      AudioNavigation.setPreviousPlaylist(config.active_playlist);\n    }\n  }\n\n  /**\n   * Runs the stop method for key down.\n   */\n  function runStopKeyDownEvent() {\n    /*\n      Syncs all of the play pause elements to pause.\n    */\n    PlayPauseElements.syncToPause();\n\n    /*\n      Stops the active song.\n    */\n    Core.stop();\n  }\n\n  /**\n   * Runs the shuffle method for key down.\n   */\n  function runShuffleKeyDownEvent() {\n    /*\n      Check to see if the current playlist has been set\n      or null and set the previous song.\n    */\n    if (config.active_playlist == \"\" || config.active_playlist == null) {\n      Shuffle.toggleShuffle();\n    } else {\n      Shuffle.toggleShufflePlaylist(config.active_playlist);\n    }\n  }\n\n  /**\n   * Run the repeat method for key down.\n   */\n  function runRepeatKeyDownEvent() {\n    /*\n      Toggles the repeat\n    */\n    Repeater.setRepeat(!config.repeat);\n\n    /*\n      Visually sync repeat\n    */\n    RepeatElements.syncRepeat();\n  }\n\n  /**\n   * Returns the public methods for the handler.\n   */\n  return {\n    handle: handle\n  };\n})();\n\nexport default KeyDown;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/keydown.js", "/**\n * Imports the config to use the values\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the AmplitudeJS Core Methods\n * @module core/core\n */\nimport Core from \"../core/core.js\";\n\n/**\n * Imports the AmplitudeJS Visual Mute Elements\n * @module visual/MuteElements\n */\nimport MuteElements from \"../visual/muteElements.js\";\n\n/**\n * Imports the AmplitudeJS Visual Volume Slider Elements\n * @module visual/VolumeSliderElements\n */\nimport VolumeSliderElements from \"../visual/volumeSliderElements.js\";\n\n/**\n * Handles all events for a mute event.\n * @module events/Mute\n */\nlet Mute = (function() {\n  /**\n   * Handles an event for a mute element\n   *\n   * HANDLER FOR:       class=\"amplitude-mute\"\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n      We don't fire this if the user is touching the screen and it's moving.\n      This could lead to a mis-fire\n    */\n    if (!config.is_touch_moving) {\n      /*\n\t\t\t\tIf the current volume in the config is 0, we set the volume to the\n\t\t\t\tpre_mute level.  This means that the audio is already muted and\n\t\t\t\tneeds to be restored to the pre_mute level.\n\n\t\t\t\tOtherwise, we set pre_mute volume to the current volume\n\t\t\t\tand set the config volume to 0, muting the audio.\n\t\t\t*/\n      if (config.volume == 0) {\n        Core.setVolume(config.pre_mute_volume);\n      } else {\n        config.pre_mute_volume = config.volume;\n        Core.setVolume(0);\n      }\n\n      /*\n        Sync Mute Elements.\n      */\n      MuteElements.setMuted(config.volume == 0 ? true : false);\n\n      /*\n\t\t\t\tSyncs the volume sliders so the visuals align up with the functionality.\n\t\t\t\tIf the volume is at 0, then the sliders should represent that so the user\n\t\t\t\thas the right starting point.\n\t\t\t*/\n      VolumeSliderElements.sync();\n    }\n  }\n\n  /**\n   * Returns the public facing methods\n   */\n  return {\n    handle: handle\n  };\n})();\n\nexport default Mute;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/mute.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the AmplitudeJS Core module.\n * @module core/core\n */\nimport Core from \"../core/core.js\";\n\n/**\n * Imports the Play Pause Elements Module.\n * @module visual/PlayPauseElements\n */\nimport PlayPauseElements from \"../visual/playPauseElements.js\";\n\n/**\n * Imports the Callbacks Module\n * @module utilities/Callbacks\n */\nimport Callbacks from \"../utilities/callbacks.js\";\n\n/**\n * Imports the Amplitude Audio Navigation Utility\n * @module utilities/AudioNavigation\n */\nimport AudioNavigation from \"../utilities/audioNavigation.js\";\n\n/**\n * AmplitudeJS Debug Module\n * @module utilities/Debug\n */\nimport Debug from \"../utilities/debug.js\";\n\n/**\n * AmplitudeJS Next Event Handler\n *\n * @module events/Next\n */\nlet Next = (function() {\n  /**\n   * Handles an event on the next button\n   *\n   * HANDLER FOR:       class=\"amplitude-next\"\n   *\n   * GLOBAL:            class=\"amplitude-next\"\n   * PLAYLIST:          class=\"amplitude-next\" amplitude-playlist=\"playlist_key\"\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n      We don't fire this if the user is touching the screen and it's moving.\n      This could lead to a mis-fire\n    */\n    if (!config.is_touch_moving) {\n      /*\n        Gets the playlist attribute from the element.\n      */\n      let playlist = this.getAttribute(\"data-amplitude-playlist\");\n\n      /*\n        If the playlist is null, we handle the global next.\n      */\n      if (playlist == null) {\n        handleGlobalNext();\n      }\n\n      /*\n        If the playlist is set, we handle the playlist next.\n      */\n      if (playlist != null) {\n        handlePlaylistNext(playlist);\n      }\n    }\n  }\n\n  /**\n   * Handles an event on a global enxt button.\n   *\n   * @access private\n   */\n  function handleGlobalNext() {\n    /*\n      Check to see if the current state of the player\n      is in playlist mode or not playlist mode. If we are in playlist mode,\n      we set next on the playlist.\n    */\n    if (config.active_playlist == \"\" || config.active_playlist == null) {\n      AudioNavigation.setNext();\n    } else {\n      AudioNavigation.setNextPlaylist(config.active_playlist);\n    }\n  }\n\n  /**\n   * Handles an event on a next playlist button.\n   *\n   * @access private\n   * @prop {string} playlist  - The playlist we are handling the next for.\n   */\n  function handlePlaylistNext(playlist) {\n    /*\n      Ensure the playlist is the same as the active playlist. To get to change\n      the scope to a new playlist, you need to play that playlist.\n    */\n    if (playlist == config.active_playlist) {\n      AudioNavigation.setNextPlaylist(playlist);\n    } else {\n      Debug.writeMessage(\n        \"You can not go to the next song on a playlist that is not being played!\"\n      );\n    }\n  }\n\n  /*\n    Returns the public facing methods.\n  */\n  return {\n    handle: handle\n  };\n})();\n\nexport default Next;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/next.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the ConfigState module.\n * @module utilities/ConfigState\n */\nimport ConfigState from \"../utilities/configState.js\";\n\n/**\n * Imports the AmplitudeJS Core Methods\n * @module core/Core\n */\nimport Core from \"../core/core.js\";\n\n/**\n * Imports the AmplitudeJS Play Pause Elements\n * @module visual/PlayPauseElements\n */\nimport PlayPauseElements from \"../visual/playPauseElements.js\";\n\n/**\n * Handles all of the pause events\n * @module events/Pause\n */\nlet Pause = (function() {\n  /**\n   * Handles an event on a pause button\n   *\n   * HANDLER FOR:       class=\"amplitude-pause\"\n   *\n   * GLOBAL:            class=\"amplitude-pause\"\n   * PLAYLIST:          class=\"amplitude-pause\" amplitude-playlist=\"playlist_key\"\n   * SONG:              class=\"amplitude-pause\" amplitude-song-index=\"song_index\"\n   * SONG IN PLAYLIST:  class=\"amplitude-pause\" amplitude-playlist=\"playlist-key\" amplitude-song-index=\"playlist_index\"\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n      If the touch is moving, we do not want to accidentally touch the play\n      pause element and fire an event.\n    */\n    if (!config.is_touch_moving) {\n      /*\n        Gets the attribute for song index so we can check if\n        there is a need to change the song.  In some scenarios\n        there might be multiple pause classes on the page. In that\n        case it is possible the user could click a different pause\n        class.\n      */\n      let songIndexAttribute = this.getAttribute(\"data-amplitude-song-index\");\n      let playlistAttribute = this.getAttribute(\"data-amplitude-playlist\");\n\n      /*\n        Handle a global pause button.\n      */\n      if (playlistAttribute == null && songIndexAttribute == null) {\n        handleGlobalPause();\n      }\n\n      /*\n        Handle a playlist pause button.\n      */\n      if (playlistAttribute != null && songIndexAttribute == null) {\n        handlePlaylistPause(playlistAttribute);\n      }\n\n      /*\n        Handle a song pause button.\n      */\n      if (playlistAttribute == null && songIndexAttribute != null) {\n        handleSongPause(songIndexAttribute);\n      }\n\n      /*\n        Handle a song in playlist pause button.\n      */\n      if (playlistAttribute != null && songIndexAttribute != null) {\n        handleSongInPlaylistPause(playlistAttribute, songIndexAttribute);\n      }\n\n      ConfigState.setPlayerState();\n    }\n  }\n\n  /**\n   * Handles global pause button which pauses whatever song is\n   * active.\n   *\n   * @access private\n   */\n  function handleGlobalPause() {\n    /*\n      Pauses the song.\n    */\n    Core.pause();\n\n    /*\n      Sync the play pause elements.\n    */\n    PlayPauseElements.sync();\n  }\n\n  /**\n   * Handles the playlist pause.\n   *\n   * @access private\n   * @param {string} playlist The playlist the pause button belongs to.\n   */\n  function handlePlaylistPause(playlist) {\n    /*\n      Checks to see if the active playlist is the same\n    */\n    if (config.active_playlist == playlist) {\n      /*\n        Pauses the song.\n      */\n      Core.pause();\n\n      /*\n        Sync the play pause elements.\n      */\n      PlayPauseElements.sync();\n    }\n  }\n\n  /**\n   * Handles the song pause.\n   *\n   * @access private\n   * @param {integer} song The song the pause button belongs to.\n   */\n  function handleSongPause(song) {\n    /*\n      Checks to see if the active playlist is null and the song matches\n      the active index.\n    */\n    if (\n      (config.active_playlist == \"\" || config.active_playlist == null) &&\n      config.active_index == song\n    ) {\n      /*\n        Pauses the song.\n      */\n      Core.pause();\n\n      /*\n        Sync the play pause elements.\n      */\n      PlayPauseElements.sync();\n    }\n  }\n\n  /**\n   * Handles the song in playlist pause.\n   *\n   * @access private\n   * @param {string} playlist The playlist the pause button belongs to.\n   * @param {integer} song The song the pause button belongs to.\n   */\n  function handleSongInPlaylistPause(playlist, song) {\n    /*\n      Checks to see if the active song matches the song and the\n      active playlist matches the playlist. This means the pause button is\n      for the song in the playlist.\n    */\n    if (\n      config.active_playlist == playlist &&\n      config.playlists[playlist].active_index == song\n    ) {\n      /*\n        Pauses the song.\n      */\n      Core.pause();\n\n      /*\n        Sync the play pause elements.\n      */\n      PlayPauseElements.sync();\n    }\n  }\n\n  /*\n    Returns the public facing elements\n  */\n  return {\n    handle: handle\n  };\n})();\n\nexport default Pause;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/pause.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the config state utility.\n * @module utilities/ConfigState\n */\nimport ConfigState from \"../utilities/configState.js\";\n\n/**\n * Imports the AmplitudeJS Core Methods\n * @module core/Core\n */\nimport Core from \"../core/core.js\";\n\n/**\n * Imports the AmplitudeJS Checks Utility\n * @module utilities/Checks\n */\nimport Checks from \"../utilities/checks.js\";\n\n/**\n * Imports the AmplitudeJS Audio Navigation Utility\n * @module utilities/AudioNavigation\n */\nimport AudioNavigation from \"../utilities/audioNavigation.js\";\n\n/**\n * Imports the AmplitudeJS Play Pause Elements\n * @module visual/PlayPauseElements\n */\nimport PlayPauseElements from \"../visual/playPauseElements.js\";\n\n/**\n * Handles all of the play events\n * @module events/Play\n */\nlet Play = (function() {\n  /**\n   * Handles an event on a play button in Amplitude.\n   *\n   * HANDLER FOR:       class=\"amplitude-play\"\n   *\n   * GLOBAL:            class=\"amplitude-play\"\n   * PLAYLIST:          class=\"amplitude-play\" amplitude-playlist=\"playlist_key\"\n   * SONG:              class=\"amplitude-play\" amplitude-song-index=\"song_index\"\n   * SONG IN PLAYLIST:  class=\"amplitude-play\" amplitude-playlist=\"playlist-key\" amplitude-song-index=\"playlist_index\"\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n      If the touch is moving, we do not want to accidentally touch the play\n      pause element and fire an event.\n    */\n    if (!config.is_touch_moving) {\n      /*\n        Gets the attribute for song index so we can check if\n        there is a need to change the song.  In some scenarios\n        there might be multiple play classes on the page. In that\n        case it is possible the user could click a different play\n        class and change the song.\n      */\n      let songIndexAttribute = this.getAttribute(\"data-amplitude-song-index\");\n      let playlistAttribute = this.getAttribute(\"data-amplitude-playlist\");\n\n      /*\n        Handle a global play button.\n      */\n      if (playlistAttribute == null && songIndexAttribute == null) {\n        handleGlobalPlay();\n      }\n\n      /*\n        Handle a playlist play button.\n      */\n      if (playlistAttribute != null && songIndexAttribute == null) {\n        handlePlaylistPlay(playlistAttribute);\n      }\n\n      /*\n        Handle a song play button.\n      */\n      if (playlistAttribute == null && songIndexAttribute != null) {\n        handleSongPlay(songIndexAttribute);\n      }\n\n      /*\n        Handle a song in playlist play button.\n      */\n      if (playlistAttribute != null && songIndexAttribute != null) {\n        handleSongInPlaylistPlay(playlistAttribute, songIndexAttribute);\n      }\n\n      ConfigState.setPlayerState();\n    }\n  }\n\n  /**\n   * Handles global play button which plays whatever song is\n   * active.\n   *\n   * @access private\n   */\n  function handleGlobalPlay() {\n    /*\n      Plays the song\n    */\n    Core.play();\n\n    /*\n      Sync the play pause elements.\n    */\n    PlayPauseElements.sync();\n  }\n\n  /**\n   * Handle the playlist play.\n   *\n   * @access private\n   * @param {string} playlist The playlist the play button belongs to.\n   */\n  function handlePlaylistPlay(playlist) {\n    /*\n      Checks if we have a new playlist.\n    */\n    if (Checks.newPlaylist(playlist)) {\n      /*\n        Sets the active playlist to what belongs to the playlist.\n      */\n      AudioNavigation.setActivePlaylist(playlist);\n\n      /*\n        Play first song in the playlist since we just\n        switched playlists, we start from the first song.\n\n        If the user has shuffle on for the playlist, then\n        we go from the first song in the shuffle playlist array.\n      */\n      if (config.playlists[playlist].shuffle) {\n        AudioNavigation.changeSongPlaylist(\n          playlist,\n          config.playlists[playlist].shuffle_list[0],\n          0\n        );\n      } else {\n        AudioNavigation.changeSongPlaylist(\n          playlist,\n          config.playlists[playlist].songs[0],\n          0\n        );\n      }\n    }\n\n    /*\n      Plays the song.\n    */\n    Core.play();\n\n    /*\n      Syncs the play pause elements since they are dependent upon this state\n      of the player.\n    */\n    PlayPauseElements.sync();\n  }\n\n  /**\n   * Handles the song play button.\n   *\n   * @access private\n   * @param {integer} song The index of the song we are playing.\n   */\n  function handleSongPlay(song) {\n    /*\n      There can be multiple playlists on the page and there can be\n      multiple songs on the page AND there can be songs in multiple\n      playlists, so we have some checking to do.\n    */\n\n    /*\n      Check to see if the playlist has changed. Essentially, if we are moving\n      out of a playlist context.\n    */\n    if (Checks.newPlaylist(null)) {\n      /*\n        We've moved out of the playlist context, so we set the active playlist\n        to null\n      */\n      AudioNavigation.setActivePlaylist(null);\n\n      /*\n        We then change the song to the index selected.\n      */\n      AudioNavigation.changeSong(config.songs[song], song);\n    }\n\n    /*\n      Check to see if the song has changed. If it has,\n      set the active song. If it was in a playlist, the\n      song wouldn't change here, since we already set the\n      song when we checked for a playlist.\n    */\n    if (Checks.newSong(null, song)) {\n      /*\n        The song selected is different, so we change the\n        song.\n      */\n      AudioNavigation.changeSong(config.songs[song], song);\n    }\n\n    /*\n      Plays the song\n    */\n    Core.play();\n\n    /*\n      Syncs the play pause elements since they are dependent upon this state\n      of the player.\n    */\n    PlayPauseElements.sync();\n  }\n\n  /**\n   * Handles the song in playlist play.\n   *\n   * @access private\n   * @param {string} playlist The playlist the play button belongs to.\n   * @param {integer} song The song the play button belongs to.\n   */\n  function handleSongInPlaylistPlay(playlist, song) {\n    /*\n\t\t\tThere can be multiple playlists on the page and there can be\n\t\t\tmultiple songs on the page AND there can be songs in multiple\n\t\t\tplaylists, so we have some checking to do.\n\t\t*/\n\n    /*\n\t\t\tCheck to see if the playlist has changed. Essentially, if we are moving\n      out of a playlist context.\n\t\t*/\n    if (Checks.newPlaylist(playlist)) {\n      /*\n        We've moved out of the playlist context, so we set the active playlist\n        to null\n      */\n      AudioNavigation.setActivePlaylist(playlist);\n\n      /*\n\t\t\t\tWe then change the song to the index selected.\n\t\t\t*/\n      AudioNavigation.changeSongPlaylist(\n        playlist,\n        config.playlists[playlist].songs[song],\n        song\n      );\n    }\n\n    /*\n\t\t\tCheck to see if the song has changed. If it has,\n\t\t\tset the active song. If it was in a playlist, the\n\t\t\tsong wouldn't change here, since we already set the\n\t\t\tsong when we checked for a playlist.\n\t\t*/\n    if (Checks.newSong(playlist, song)) {\n      /*\n\t\t\t\tThe song selected is different, so we change the\n\t\t\t\tsong.\n\t\t\t*/\n      AudioNavigation.changeSongPlaylist(\n        playlist,\n        config.playlists[playlist].songs[song],\n        song\n      );\n    }\n\n    /*\n      Plays the song\n    */\n    Core.play();\n\n    /*\n      Now we sync all the elements to match the state of the audio.\n      We don't need to do any checks on new songs or changed playlists\n      in the global since it's whatever song is playing.\n    */\n    PlayPauseElements.sync();\n  }\n\n  /*\n    Returns the public facing elements\n  */\n  return {\n    handle: handle\n  };\n})();\n\nexport default Play;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/play.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Import the config state utility.\n * @module utilities/configState\n */\nimport ConfigState from \"../utilities/configState.js\";\n\n/**\n * Imports the AmplitudeJS Core Methods\n * @module core/Core\n */\nimport Core from \"../core/core.js\";\n\n/**\n * Imports the AmplitudeJS Checks Utility\n * @module utilities/Checks\n */\nimport Checks from \"../utilities/checks.js\";\n\n/**\n * Imports the AmplitudeJS Audio Navigation Utility\n * @module utilities/AudioNavigation\n */\nimport AudioNavigation from \"../utilities/audioNavigation.js\";\n\n/**\n * Imports the AmplitudeJS Play Pause Elements\n * @module visual/PlayPauseElements\n */\nimport PlayPauseElements from \"../visual/playPauseElements.js\";\n\n/**\n * Handles all of the play pause events\n * @module events/PlayPause\n */\nlet PlayPause = (function() {\n  /**\n   * Handles an event on a play/pause button\n   *\n   * HANDLER FOR:       class=\"amplitude-play-pause\"\n   *\n   * GLOBAL:            class=\"amplitude-play-pause\"\n   * PLAYLIST:          class=\"amplitude-play-pause\" amplitude-playlist=\"playlist_key\"\n   * SONG:              class=\"amplitude-play-pause\" amplitude-song-index=\"song_index\"\n   * SONG IN PLAYLIST:  class=\"amplitude-play-pause\" amplitude-playlist=\"playlist-key\" amplitude-song-index=\"playlist_index\"\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n      If the touch is moving, we do not want to accidentally touch the play\n      pause element and fire an event.\n    */\n    if (!config.is_touch_moving) {\n      /*\n        Get the playlist and song from the element. It's alright if these\n        are null.\n      */\n      let playlist = this.getAttribute(\"data-amplitude-playlist\");\n      let song = this.getAttribute(\"data-amplitude-song-index\");\n\n      /*\n        Handle a global play pause button\n      */\n      if (playlist == null && song == null) {\n        handleGlobalPlayPause();\n      }\n\n      /*\n        Handle a playlist play pause button\n      */\n      if (playlist != null && song == null) {\n        handlePlaylistPlayPause(playlist);\n      }\n\n      /*\n        Handle a song play pause button\n      */\n      if (playlist == null && song != null) {\n        handleSongPlayPause(song);\n      }\n\n      /*\n        Handle a song in playlist play pause button\n      */\n      if (playlist != null && song != null) {\n        handleSongInPlaylistPlayPause(playlist, song);\n      }\n\n      ConfigState.setPlayerState();\n    }\n  }\n\n  /**\n   * Sets the main play pause buttons to the current state of the song.\n   * @access private\n   */\n  function handleGlobalPlayPause() {\n    /*\n      If the song is paused, we play the song. If the song is playing,\n      we pause the song.\n    */\n    if (config.audio.paused) {\n      Core.play();\n    } else {\n      Core.pause();\n    }\n\n    /*\n      Now we sync all the elements to match the state of the audio.\n      We don't need to do any checks on new songs or changed playlists\n      in the global since it's whatever song is playing.\n    */\n    PlayPauseElements.sync();\n  }\n\n  /**\n   * Sets the playlist main play pause buttons to the current state of the song.\n   * @access private\n   * @param {string} playlist The playlist the main play pause button controls\n   */\n  function handlePlaylistPlayPause(playlist) {\n    /*\n      The only thing that can change when you click a playlist\n      play pause is the playlist. Main play pauses have no change\n      in song, song play pauses can change playlist and song.\n    */\n    if (Checks.newPlaylist(playlist)) {\n      /*\n        If there's a new playlist, then we set the new playlist.\n      */\n      AudioNavigation.setActivePlaylist(playlist);\n\n      /*\n        Play first song in the playlist since we just\n        switched playlists, we start from the first song.\n\n        If the user has shuffle on for the playlist, then\n        we go from the first song in the shuffle playlist array.\n      */\n      if (config.playlists[playlist].shuffle) {\n        AudioNavigation.changeSongPlaylist(\n          playlist,\n          config.playlists[playlist].shuffle_list[0],\n          0\n        );\n      } else {\n        AudioNavigation.changeSongPlaylist(\n          playlist,\n          config.playlists[playlist].songs[0],\n          0\n        );\n      }\n    }\n\n    /*\n      If the song is paused, we play the song. If the song is playing,\n      we pause the song.\n    */\n    if (config.audio.paused) {\n      Core.play();\n    } else {\n      Core.pause();\n    }\n\n    /*\n      Now we sync all the elements to match the state of the audio.\n      We don't need to do any checks on new songs or changed playlists\n      in the global since it's whatever song is playing.\n    */\n    PlayPauseElements.sync();\n  }\n\n  /**\n   * Sets the playlist main play pause buttons to the current state of the song.\n   * @access private\n   * @param {string} song The index of the song being played/paused\n   */\n  function handleSongPlayPause(song) {\n    /*\n\t\t\tThere can be multiple playlists on the page and there can be\n\t\t\tmultiple songs on the page AND there can be songs in multiple\n\t\t\tplaylists, so we have some checking to do.\n\t\t*/\n\n    /*\n\t\t\tCheck to see if the playlist has changed. Essentially, if we are moving\n      out of a playlist context.\n\t\t*/\n    if (Checks.newPlaylist(null)) {\n      /*\n        We've moved out of the playlist context, so we set the active playlist\n        to null\n      */\n      AudioNavigation.setActivePlaylist(null);\n\n      /*\n\t\t\t\tWe then change the song to the index selected.\n\t\t\t*/\n      AudioNavigation.changeSong(config.songs[song], song);\n    }\n\n    /*\n\t\t\tCheck to see if the song has changed. If it has,\n\t\t\tset the active song. If it was in a playlist, the\n\t\t\tsong wouldn't change here, since we already set the\n\t\t\tsong when we checked for a playlist.\n\t\t*/\n    if (Checks.newSong(null, song)) {\n      /*\n\t\t\t\tThe song selected is different, so we change the\n\t\t\t\tsong.\n\t\t\t*/\n      AudioNavigation.changeSong(config.songs[song], song);\n    }\n\n    /*\n      If the song is paused, we play the song. If the song is playing,\n      we pause the song.\n    */\n    if (config.audio.paused) {\n      Core.play();\n    } else {\n      Core.pause();\n    }\n\n    /*\n      Now we sync all the elements to match the state of the audio.\n      We don't need to do any checks on new songs or changed playlists\n      in the global since it's whatever song is playing.\n    */\n    PlayPauseElements.sync();\n  }\n\n  /**\n   * Sets the song in playlist play pause buttons to the current\n   * state of the song.\n   * @access private\n   * @param {string} playlist The playlist the song is a part of\n   * @param {number} song The index of the song being played/paused\n   */\n  function handleSongInPlaylistPlayPause(playlist, song) {\n    /*\n\t\t\tThere can be multiple playlists on the page and there can be\n\t\t\tmultiple songs on the page AND there can be songs in multiple\n\t\t\tplaylists, so we have some checking to do.\n\t\t*/\n\n    /*\n\t\t\tCheck to see if the playlist has changed. Essentially, if we are moving\n      out of a playlist context.\n\t\t*/\n    if (Checks.newPlaylist(playlist)) {\n      /*\n        We've moved out of the playlist context, so we set the active playlist\n        to null\n      */\n      AudioNavigation.setActivePlaylist(playlist);\n\n      /*\n\t\t\t\tWe then change the song to the index selected.\n\t\t\t*/\n      AudioNavigation.changeSongPlaylist(\n        playlist,\n        config.playlists[playlist].songs[song],\n        song\n      );\n    }\n\n    /*\n\t\t\tCheck to see if the song has changed. If it has,\n\t\t\tset the active song. If it was in a playlist, the\n\t\t\tsong wouldn't change here, since we already set the\n\t\t\tsong when we checked for a playlist.\n\t\t*/\n    if (Checks.newSong(playlist, song)) {\n      /*\n\t\t\t\tThe song selected is different, so we change the\n\t\t\t\tsong.\n\t\t\t*/\n      AudioNavigation.changeSongPlaylist(\n        playlist,\n        config.playlists[playlist].songs[song],\n        song\n      );\n    }\n\n    /*\n      If the song is paused, we play the song. If the song is playing,\n      we pause the song.\n    */\n    if (config.audio.paused) {\n      Core.play();\n    } else {\n      Core.pause();\n    }\n\n    /*\n      Now we sync all the elements to match the state of the audio.\n      We don't need to do any checks on new songs or changed playlists\n      in the global since it's whatever song is playing.\n    */\n    PlayPauseElements.sync();\n  }\n\n  /**\n   * Returns the public facing methods\n   */\n  return {\n    handle: handle\n  };\n})();\n\nexport default PlayPause;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/playPause.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the Amplitude Core module\n * @module core/Core\n */\nimport Core from \"../core/core.js\";\n\n/**\n * Imports the Playback Speed Visual Elements\n * @module visual/PlaybackSpeedElements\n */\nimport PlaybackSpeedElements from \"../visual/playbackSpeedElements.js\";\n\n/**\n * AmplitudeJS Playback Speed Event Handler\n *\n * @module events/PlaybackSpeed\n */\nlet PlaybackSpeed = (function() {\n  /**\n   * Handles an event on the playback speed button\n   *\n   * HANDLER FOR:       class=\"amplitude-playback-speed\"\n   *\n   * @access public\n   */\n  function handle() {\n    if (!config.is_touch_moving) {\n      /*\n\t\t\t\tWe increment the speed by .5 everytime we click\n\t\t\t\tthe button to change the playback speed. Once we are\n\t\t\t\tactively playing back at 2, we start back at 1 which\n\t\t\t\tis normal speed.\n\t\t\t*/\n      switch (config.playback_speed) {\n        case 1:\n          Core.setPlaybackSpeed(1.5);\n          break;\n        case 1.5:\n          Core.setPlaybackSpeed(2);\n          break;\n        case 2:\n          Core.setPlaybackSpeed(1);\n          break;\n      }\n\n      /*\n\t\t\t\tVisually sync the playback speed.\n\t\t\t*/\n      PlaybackSpeedElements.sync();\n    }\n  }\n\n  /*\n    Returns public facing methods\n  */\n  return {\n    handle: handle\n  };\n})();\n\nexport default PlaybackSpeed;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/playbackSpeed.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the Amplitude Audio Navigation Utility\n * @module utilities/AudioNavigation\n */\nimport AudioNavigation from \"../utilities/audioNavigation.js\";\n\n/**\n * AmplitudeJS Debug Module\n * @module utilities/Debug\n */\nimport Debug from \"../utilities/debug.js\";\n\n/**\n * AmplitudeJS Prev Event Handler\n *\n * @module events/Prev\n */\nlet Prev = (function() {\n  /**\n   * Handles an event on the previous button\n   *\n   * HANDLER FOR:       class=\"amplitude-prev\"\n   *\n   * GLOBAL:            class=\"amplitude-prev\"\n   * PLAYLIST:          class=\"amplitude-prev\" amplitude-playlist=\"playlist_key\"\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n      We don't fire this if the user is touching the screen and it's moving.\n      This could lead to a mis-fire\n    */\n    if (!config.is_touch_moving) {\n      /*\n        Gets the playlist attribute from the element.\n      */\n      let playlist = this.getAttribute(\"data-amplitude-playlist\");\n\n      /*\n        If the playlist is null, we handle the global prev.\n      */\n      if (playlist == null) {\n        handleGlobalPrev();\n      }\n\n      /*\n        If the playlist is set, we handle the playlist prev.\n      */\n      if (playlist != null) {\n        handlePlaylistPrev(playlist);\n      }\n    }\n  }\n\n  /**\n   * Handles an event on a global previous button.\n   *\n   * @access private\n   */\n  function handleGlobalPrev() {\n    /*\n      Check to see if the current state of the player\n      is in playlist mode or not playlist mode. If we are in playlist mode,\n      we set prev on the playlist.\n    */\n    if (config.active_playlist == \"\" || config.active_playlist == null) {\n      AudioNavigation.setPrevious();\n    } else {\n      AudioNavigation.setPreviousPlaylist(config.active_playlist);\n    }\n  }\n\n  /**\n   * Handles an event on a previous playlist button.\n   *\n   * @access private\n   * @prop {string} playlist  - The playlist we are handling the previous for.\n   */\n  function handlePlaylistPrev(playlist) {\n    /*\n      Ensure the playlist is the same as the active playlist. To get to change\n      the scope to a new playlist, you need to play that playlist.\n    */\n    if (playlist == config.active_playlist) {\n      AudioNavigation.setPreviousPlaylist(config.active_playlist);\n    } else {\n      Debug.writeMessage(\n        \"You can not go to the previous song on a playlist that is not being played!\"\n      );\n    }\n  }\n\n  /*\n    Returns the public facing methods.\n  */\n  return {\n    handle: handle\n  };\n})();\n\nexport default Prev;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/prev.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the BufferedProgressElements visual handler\n * @module visual/bufferedProgressElements.js\n */\nimport BufferedProgressElements from \"../visual/bufferedProgressElements.js\";\n\n/**\n * AmplitudeJS Event Handler for progress\n *\n * @module events/Progress\n */\nlet Progress = (function() {\n  /**\n   * As the song is buffered, we can display the buffered percentage in\n   * a progress bar.\n   *\n   * HANDLER FOR: progress\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n      Help from: http://jsbin.com/badimipi/1/edit?html,js,output\n    */\n    if (config.audio.buffered.length - 1 >= 0) {\n      let bufferedEnd = config.audio.buffered.end(\n        config.audio.buffered.length - 1\n      );\n      let duration = config.audio.duration;\n\n      /*\n        Set the computed song buffered value to the config.\n      */\n      config.buffered = (bufferedEnd / duration) * 100;\n    }\n\n    /*\n      Sync the buffered progress bars.\n    */\n    BufferedProgressElements.sync();\n  }\n\n  /**\n   * Returns the public facing methods\n   */\n  return {\n    handle: handle\n  };\n})();\n\nexport default Progress;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/progress.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the repeater utility module.\n * @module utilities/Repeater\n */\nimport Repeater from \"../utilities/repeater.js\";\n\n/**\n * Imports the visual repeat elements module\n * @module visual/RepeatElements\n */\nimport RepeatElements from \"../visual/repeatElements.js\";\n\n/**\n * AmplitudeJS Repeat Event Handler\n *\n * @module events/Repeat\n */\nlet Repeat = (function() {\n  /**\n   * Handles an event on the repeat button\n   *\n   * HANDLER FOR:       class=\"amplitude-repeat\"\n   *\n   * GLOBAL:            class=\"amplitude-repeat\"\n   * PLAYLIST:          class=\"amplitude-repeat\" amplitude-playlist=\"playlist_key\"\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n      We don't fire this if the user is touching the screen and it's moving.\n      This could lead to a mis-fire\n    */\n    if (!config.is_touch_moving) {\n      /*\n        Gets the playlist attribute from the element.\n      */\n      let playlist = this.getAttribute(\"data-amplitude-playlist\");\n\n      /*\n        If the playlist is null, we handle the global repeat.\n      */\n      if (playlist == null) {\n        handleGlobalRepeat();\n      }\n\n      /*\n        If the playlist is set, we handle the playlist repeat.\n      */\n      if (playlist != null) {\n        handlePlaylistRepeat(playlist);\n      }\n    }\n  }\n\n  /**\n   * Handles an event on a global repeat button.\n   *\n   * @access private\n   */\n  function handleGlobalRepeat() {\n    /*\n      Sets repeat to the opposite of what it was set to\n    */\n    Repeater.setRepeat(!config.repeat);\n\n    /*\n      Visually sync repeat\n    */\n    RepeatElements.syncRepeat();\n  }\n\n  /**\n   * Handles an event on a playlist repeat button.\n   *\n   * @access private\n   * @prop {string} playlist - The playlist we are handling the repeat store.\n   */\n  function handlePlaylistRepeat(playlist) {\n    /*\n      Sets repeat to the opposite of what it was set to for the playlist.\n    */\n    Repeater.setRepeatPlaylist(!config.playlists[playlist].repeat, playlist);\n\n    /*\n      Visually sync playlist repeat\n    */\n    RepeatElements.syncRepeatPlaylist(playlist);\n  }\n\n  /*\n    Returns the public facing methods.\n  */\n  return {\n    handle: handle\n  };\n})();\n\nexport default Repeat;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/repeat.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the repeat utility\n * @module utilities/Repeater\n */\nimport Repeater from \"../utilities/repeater.js\";\n\n/**\n * Imports the AmplitudeJS Repeat Element\n * @module visual/RepeatElements\n */\nimport RepeatElements from \"../visual/repeatElements.js\";\n\n/**\n * Handles an event on the Amplitude Repeat Song.\n *\n * @module events/RepeatSong\n */\nlet RepeatSong = (function() {\n  /**\n   * Handles an event on the repeat song button\n   *\n   * HANDLER FOR: 'amplitude-repeat-song'\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n      If the touch is moving, we do not want to accidentally touch the play\n      pause element and fire an event.\n    */\n    if (!config.is_touch_moving) {\n      /*\n\t\t\t\tSets repeat song to the opposite of what it was set to\n\t\t\t*/\n      Repeater.setRepeatSong(!config.repeat_song);\n\n      /*\n\t\t\t\tVisually sync repeat song\n\t\t\t*/\n      RepeatElements.syncRepeatSong();\n    }\n  }\n\n  /**\n   * Returns the public facing methods\n   */\n  return {\n    handle: handle\n  };\n})();\n\nexport default RepeatSong;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/repeatSong.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the shuffler utility\n * @module utilities/Shuffler\n */\nimport Shuffler from \"../utilities/shuffler.js\";\n\n/**\n * Imports the visual shuffle elements\n * @module visual/ShuffleElements\n */\nimport ShuffleElements from \"../visual/shuffleElements.js\";\n\n/**\n * Handles all of the shuffle events\n * @module events/Shuffle\n */\nlet Shuffle = (function() {\n  /**\n   * Handles an event on the shuffle button\n   *\n   * HANDLER FOR:       class=\"amplitude-shuffle\"\n   *\n   * GLOBAL:            class=\"amplitude-shuffle\"\n   * PLAYLIST:          class=\"amplitude-shuffle\" amplitude-playlist=\"playlist_key\"\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n      If the touch is moving, we do not want to accidentally touch the play\n      pause element and fire an event.\n    */\n    if (!config.is_touch_moving) {\n      /*\n        Get the playlist attribute\n      */\n      let playlist = this.getAttribute(\"data-amplitude-playlist\");\n\n      /*\n\t\t\t\tCheck to see if the shuffle button belongs to a playlist\n\t\t\t*/\n      if (playlist == null) {\n        handleGlobalShuffle();\n      } else {\n        handlePlaylistShuffle(playlist);\n      }\n    }\n  }\n\n  /**\n   * Handles the event on the global shuffle button.\n   */\n  function handleGlobalShuffle() {\n    /*\n      Either shuffles or removes shuffle on the global state.\n    */\n    Shuffler.toggleShuffle();\n\n    /*\n      Visualize the shuffle state change.\n    */\n    ShuffleElements.syncMain(config.shuffle_on);\n  }\n\n  /**\n   * Handles the event on the playlist shuffle button.\n   *\n   * @param {string} playlist - The playlist string the shuffle button belongs to.\n   */\n  function handlePlaylistShuffle(playlist) {\n    /*\n      Either shuffles or removes shuffle on the playlist state.\n    */\n    Shuffler.toggleShufflePlaylist(playlist);\n\n    /*\n      Visually sync the playlist shuffle statuses.\n    */\n    ShuffleElements.syncPlaylist(playlist);\n  }\n\n  /**\n   * Returns public facing methods\n   */\n  return {\n    handle: handle\n  };\n})();\n\nexport default Shuffle;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/shuffle.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports AmplitudeJS Debug Utility\n * @module utilities/debug\n */\nimport Debug from \"../utilities/debug.js\";\n\n/**\n * Imports the AmplitudeJS Audio Navigation Utility\n * @module utilities/AudioNavigation\n */\nimport AudioNavigation from \"../utilities/audioNavigation.js\";\n\n/**\n * Imports the AmplitudeJS Checks Utility\n * @module utilities/Checks\n */\nimport Checks from \"../utilities/checks.js\";\n\n/**\n * Imports the AmplitudeJS Core Methods\n * @module core/Core\n */\nimport Core from \"../core/core.js\";\n\n/**\n * Imports the AmplitudeJS play pause elements.\n * @module visual/PlayPauseElements\n */\nimport PlayPauseElements from \"../visual/playPauseElements.js\";\n\n/**\n * Handles the skip to event.\n *\n * @module events/SkipTo\n */\nlet SkipTo = (function() {\n  /**\n   * Handles an event on a skip to button.\n   *\n   * HANDLER FOR:       class=\"amplitude-skip-to\"\n   *\n   * GLOBAL:            class=\"amplitude-skip-to\" amplitude-song-index=\"song_index\" amplitude-location=\"seconds\"\n   * PLAYLIST:          class=\"amplitude-skip-to\" amplitude-playlist=\"playlist_key\" amplitude-song-index=\"song_index\" amplitude-location=\"seconds\"\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n      If the touch is moving, we do not want to accidentally touch the play\n      pause element and fire an event.\n    */\n    if (!config.is_touch_moving) {\n      /*\n        Extracts the needed attributes from the element.\n      */\n      let playlist = this.getAttribute(\"data-amplitude-playlist\");\n      let songIndex = this.getAttribute(\"data-amplitude-song-index\");\n      let location = this.getAttribute(\"data-amplitude-location\");\n\n      /*\n        If the location is null, write a message. We can't skip to a location\n        that is null\n      */\n      if (location == null) {\n        Debug.writeMessage(\n          \"You must add an 'data-amplitude-location' attribute in seconds to your 'amplitude-skip-to' element.\"\n        );\n      }\n\n      /*\n        If the song index is null, write a debug message. We can't skip to a location\n        of a null song.\n      */\n      if (songIndex == null) {\n        Debug.writeMessage(\n          \"You must add an 'data-amplitude-song-index' attribute to your 'amplitude-skip-to' element.\"\n        );\n      }\n\n      /*\n        If the location and song index are set, continue.\n      */\n      if (location != null && songIndex != null) {\n        /*\n  \t\t\t\tDetermines if the skip to button is in the scope of a playlist.\n  \t\t\t*/\n        if (playlist == null) {\n          handleSkipToSong(parseInt(songIndex), parseInt(location));\n        } else {\n          handleSkipToPlaylist(\n            playlist,\n            parseInt(songIndex),\n            parseInt(location)\n          );\n        }\n      }\n    }\n  }\n\n  /**\n   * Handles the skipping to a specific song\n   *\n   * @access private\n   * @param {string} songIndex  - The index of the song being skipped to\n   * @param {number} location   - The seconds location of the song in the playlist.\n   */\n  function handleSkipToSong(songIndex, location) {\n    /*\n      Changes the song to where it's being skipped and then\n      play the song.\n    */\n    AudioNavigation.changeSong(config.songs[songIndex], songIndex);\n    Core.play();\n\n    /*\n      Syncs all of the play pause buttons now that we've skipped.\n    */\n    PlayPauseElements.syncGlobal();\n    PlayPauseElements.syncSong();\n\n    /*\n      Skip to the location in the song.\n    */\n    Core.skipToLocation(location);\n  }\n\n  /**\n   * Handles the skipping to a song that's in a playlist.\n   *\n   * @access private\n   * @param {string} playlist   - The playlist being skipped to\n   * @param {string} songIndex  - The index of the song in the playlist\n   * @param {number} location   - The seconds location of the song in the playlist.\n   */\n  function handleSkipToPlaylist(playlist, songIndex, location) {\n    /*\n      Checks if we are skipping to a new playlist\n    */\n    if (Checks.newPlaylist(playlist)) {\n      AudioNavigation.setActivePlaylist(playlist);\n    }\n\n    /*\n      Changes the song to where it's being skipped and then\n      play the song.\n    */\n    AudioNavigation.changeSongPlaylist(\n      playlist,\n      config.playlists[playlist].songs[songIndex],\n      songIndex\n    );\n    Core.play();\n\n    /*\n      Sync all of the play pause elements.\n    */\n    PlayPauseElements.syncGlobal();\n    PlayPauseElements.syncPlaylist();\n    PlayPauseElements.syncSong();\n\n    /*\n      Skip to the location in the song.\n    */\n    Core.skipToLocation(location);\n  }\n\n  /**\n   * Return public facing methods\n   */\n  return {\n    handle: handle\n  };\n})();\n\nexport default SkipTo;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/skipTo.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the time utility\n * @module utilities/Time\n */\nimport Time from \"../utilities/time.js\";\n\n/**\n * Imports the song slider elements.\n * @module visual/SongSliderElements\n */\nimport SongSliderElements from \"../visual/songSliderElements.js\";\n\n/**\n * Handles the song slider to event.\n *\n * @module events/SongSlider\n */\nlet SongSlider = (function() {\n  /**\n   * Handles a song slider element.\n   *\n   * HANDLER FOR:       class=\"amplitude-song-slider\"\n   *\n   * GLOBAL:            class=\"amplitude-song-slider\"\n   * PLAYLIST:          class=\"amplitude-song-slider\" amplitude-playlist=\"playlist_key\"\n   * SONG:              class=\"amplitude-song-slider\" amplitude-song-index=\"song_index\"\n   * SONG IN PLAYLIST:  class=\"amplitude-song-slider\" amplitude-playlist=\"playlist_key\" amplitude-song-index=\"song_index\"\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n\t\t\tGets the percentage of the song we will be setting the location for.\n\t\t*/\n    let locationPercentage = this.value;\n\n    /*\n      Computes the time in seconds for the current song.\n    */\n    let computedTime = config.audio.duration * (locationPercentage / 100);\n\n    /*\n      Gets the attributes for playlist and index for the element.\n    */\n    let playlist = this.getAttribute(\"data-amplitude-playlist\");\n    let song = this.getAttribute(\"data-amplitude-song-index\");\n\n    /*\n      If no playlist or song is defined, then it's a global song slider.\n    */\n    if (playlist == null && song == null) {\n      handleGlobalSongSlider(computedTime, locationPercentage);\n    }\n\n    /*\n      If a playlist but no song is defined, then it's playlist slider.\n    */\n    if (playlist != null && song == null) {\n      handlePlaylistSongSlider(computedTime, locationPercentage, playlist);\n    }\n\n    /*\n      If no playlist but a song is defined, then it's a song slider.\n    */\n    if (playlist == null && song != null) {\n      handleSongSongSlider(computedTime, locationPercentage, song);\n    }\n\n    /*\n      If playlist and song are defined then it's a song in a playlist\n      slider.\n    */\n    if (playlist != null && song != null) {\n      handleSongInPlaylistSongSlider(\n        computedTime,\n        locationPercentage,\n        playlist,\n        song\n      );\n    }\n  }\n\n  /**\n   * Handles a change on a global audio slider\n   *\n   * @access private\n   * @param {integer} computedTime  - The time we will set the audio to.\n   * @param {float}   locationPercentage - The percent through the song.\n   */\n  function handleGlobalSongSlider(computedTime, locationPercentage) {\n    /*\n\t\t\tIf the active song is not live, set the current time and adjust the slider.\n\t\t*/\n    if (!config.active_metadata.live) {\n      Time.setCurrentTime(computedTime);\n\n      /*\n        Sync song slider elements.\n      */\n      SongSliderElements.sync(\n        locationPercentage,\n        config.active_playlist,\n        config.active_index\n      );\n    }\n  }\n\n  /**\n   * Handles a change on a playlist audio slider\n   *\n   * @access private\n   * @param {integer} computedTime  - The time we will set the audio to.\n   * @param {float}   locationPercentage - The percent through the song.\n   * @param {string}  playlist = The playlist the song slider belongs to.\n   */\n  function handlePlaylistSongSlider(\n    computedTime,\n    locationPercentage,\n    playlist\n  ) {\n    /*\n\t\t\tWe don't want to song slide a playlist that's not the\n\t\t\tactive placylist.\n\t\t*/\n    if (config.active_playlist == playlist) {\n      /*\n  \t\t\tIf the active song is not live, set the current time\n  \t\t*/\n      if (!config.active_metadata.live) {\n        Time.setCurrentTime(computedTime);\n\n        /*\n          Sync song slider elements.\n        */\n        SongSliderElements.sync(\n          locationPercentage,\n          playlist,\n          config.active_index\n        );\n      }\n    }\n  }\n\n  /**\n   * Handles a change on a song audio slider\n   *\n   * @access private\n   * @param {integer} computedTime  - The time we will set the audio to.\n   * @param {float}   locationPercentage - The percent through the song.\n   * @param {integer} songIndex = The song being navigated.\n   */\n  function handleSongSongSlider(computedTime, locationPercentage, songIndex) {\n    /*\n      We only want to move the slider if the active song is the\n      same as the song being selected.\n    */\n    if (config.active_index == songIndex && config.active_playlist == null) {\n      /*\n    \t\tIf the active song is not live, set the current time\n    \t*/\n      if (!config.active_metadata.live) {\n        Time.setCurrentTime(computedTime);\n\n        /*\n          Sync song slider elements.\n        */\n        SongSliderElements.sync(\n          locationPercentage,\n          config.active_playlist,\n          songIndex\n        );\n      }\n    }\n  }\n\n  /**\n   * Handles a change on a song audio slider\n   *\n   * @access private\n   * @param {integer} computedTime  - The time we will set the audio to.\n   * @param {float}   locationPercentage - The percent through the song.\n   * @param {integer} playlist = The playlist the song belongs to.\n   * @param {integer} songIndex = The song being navigated.\n   */\n  function handleSongInPlaylistSongSlider(\n    computedTime,\n    locationPercentage,\n    playlist,\n    songIndex\n  ) {\n    /*\n      We only want to move the slider if the active song is the\n      same as the song being selected and the active playlist is the same\n      as the playlist selected.\n    */\n    if (\n      config.playlists[playlist].active_index == songIndex &&\n      config.active_playlist == playlist\n    ) {\n      /*\n    \t\tIf the active song is not live, set the current time\n    \t*/\n      if (!config.active_metadata.live) {\n        Time.setCurrentTime(computedTime);\n\n        /*\n          Sync song slider elements.\n        */\n        SongSliderElements.sync(locationPercentage, playlist, songIndex);\n      }\n    }\n  }\n\n  /*\n    Return public facing methods\n  */\n  return {\n    handle: handle\n  };\n})();\n\nexport default SongSlider;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/songSlider.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the config state module.\n * @module utilities/configState\n */\nimport ConfigState from \"../utilities/configState.js\";\n\n/**\n * Imports the AmplitudeJS Play Pause Elements\n * @module visual/PlayPauseElements\n */\nimport PlayPauseElements from \"../visual/playPauseElements.js\";\n\n/**\n * Imports the AmplitudeJS Core Methods\n * @module core/Core\n */\nimport Core from \"../core/core.js\";\n\n/**\n * Handles all of the stop events\n * @module events/Stop\n */\nlet Stop = (function() {\n  /**\n   * Handles an event on a stop element.\n   *\n   * HANDLER FOR:       class=\"amplitude-stop\"\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n      If touch is not moving, we run. We don't want to accidentally press\n      stop if touch is moving.\n    */\n    if (!config.is_touch_moving) {\n      /*\n\t\t\t\tSets all of the play/pause buttons to pause\n\t\t\t*/\n      PlayPauseElements.syncToPause();\n\n      /*\n\t\t\t\tStops the active song.\n\t\t\t*/\n      Core.stop();\n\n      /*\n        Set the state of the player.\n      */\n      ConfigState.setPlayerState();\n    }\n  }\n\n  /**\n   * Returns public facing methods\n   */\n  return {\n    handle: handle\n  };\n})();\n\nexport default Stop;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/stop.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the Buffered Progress Elements visual class\n * @module visual/bufferedProgressElements\n */\nimport BufferedProgressElements from \"../visual/bufferedProgressElements.js\";\n\n/**\n * Imports the Time Elements visual class.\n * @module visual/timeElements\n */\nimport TimeElements from \"../visual/timeElements.js\";\n\n/**\n * Imports the Song Slider Elements visual class.\n * @module visual/songSliderElements\n */\nimport SongSliderElements from \"../visual/songSliderElements.js\";\n\n/**\n * Imports the Song Played Progress Elements visual class.\n * @module visual/songPlayedProgressElements\n */\nimport SongPlayedProgressElements from \"../visual/songPlayedProgressElements.js\";\n\n/**\n * Imports the Time utility class\n * @module utilities/Time\n */\nimport Time from \"../utilities/time.js\";\n\n/**\n * Imports the Callback utility class\n * @module utilities/Callbacks\n */\nimport Callbacks from \"../utilities/callbacks.js\";\n\n/**\n * AmplitudeJS Event Handler for Time Update\n *\n * @module events/TimeUpdate\n */\nlet TimeUpdate = (function() {\n  /**\n   * When the time updates on the active song, we sync the current time displays\n   *\n   * HANDLER FOR: timeupdate\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n      Computes the buffered time.\n    */\n    computeBufferedTime();\n\n    /*\n      Sync the buffered progress elements.\n    */\n    BufferedProgressElements.sync();\n\n    /*\n      Updates the current time information.\n    */\n    updateTimeInformation();\n\n    /*\n      Run time callbacks\n    */\n    runTimeCallbacks();\n  }\n\n  /**\n   * Computes the buffered time\n   */\n  function computeBufferedTime() {\n    /*\n      Help from: http://jsbin.com/badimipi/1/edit?html,js,output\n    */\n    if (config.audio.buffered.length - 1 >= 0) {\n      let bufferedEnd = config.audio.buffered.end(\n        config.audio.buffered.length - 1\n      );\n      let duration = config.audio.duration;\n\n      config.buffered = (bufferedEnd / duration) * 100;\n    }\n  }\n\n  /**\n   * Updates the current time information.\n   * @access private\n   */\n  function updateTimeInformation() {\n    /*\n      If the current song is not live, then\n      we can update the time information. Otherwise the\n      current time updates wouldn't mean much since the time\n      is infinite.\n    */\n    if (!config.active_metadata.live) {\n      /*\n        Compute the current time\n      */\n      let currentTime = Time.computeCurrentTimes();\n\n      /*\n        Compute the song completion percentage\n      */\n      let songCompletionPercentage = Time.computeSongCompletionPercentage();\n\n      /*\n        Computes the song duration\n      */\n      let songDuration = Time.computeSongDuration();\n\n      /*\n        Sync the current time elements with the current\n        location of the song and the song duration elements with\n        the duration of the song.\n      */\n      TimeElements.syncCurrentTimes(currentTime);\n\n      /*\n        Sync the song slider elements.\n      */\n      SongSliderElements.sync(\n        songCompletionPercentage,\n        config.active_playlist,\n        config.active_index\n      );\n\n      /*\n        Sync the song played progress elements.\n      */\n      SongPlayedProgressElements.sync(songCompletionPercentage);\n\n      /*\n        Sync the duration time elements.\n      */\n      TimeElements.syncDurationTimes(currentTime, songDuration);\n    }\n  }\n\n  /**\n   * Runs a callback at a certain time in the song.\n   */\n  function runTimeCallbacks() {\n    /*\n      Gets the current seconds into the song.\n    */\n    let currentSeconds = Math.floor(config.audio.currentTime);\n\n    /*\n      Checks to see if there is a callback at the certain seconds into the song.\n    */\n    if (\n      config.active_metadata.time_callbacks != undefined &&\n      config.active_metadata.time_callbacks[currentSeconds] != undefined\n    ) {\n      /*\n        Checks to see if the callback has been run. Since the time updates more than\n        one second, we don't want the callback to run X times.\n      */\n      if (!config.active_metadata.time_callbacks[currentSeconds].run) {\n        config.active_metadata.time_callbacks[currentSeconds].run = true;\n        config.active_metadata.time_callbacks[currentSeconds]();\n      }\n    } else {\n      /*\n        Iterate over all of the callbacks for a song. If the song has one, we flag\n        the run as false. This occurs because we have passed the active second for\n        the callback, so we flag it as not run. It will either run again if the user\n        seeks back or not run in the future.\n      */\n      for (var seconds in config.active_metadata.time_callbacks) {\n        if (config.active_metadata.time_callbacks.hasOwnProperty(seconds)) {\n          config.active_metadata.time_callbacks[seconds].run = false;\n        }\n      }\n    }\n  }\n  /**\n   * Returns public functions\n   */\n  return {\n    handle: handle\n  };\n})();\n\nexport default TimeUpdate;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/timeUpdate.js", "/**\n * Imports the config to use the values\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the AmplitudeJS Core Methods\n * @module core/core\n */\nimport Core from \"../core/core.js\";\n\n/**\n * Imports the AmplitudeJS Visual Mute Elements\n * @module visual/MuteElements\n */\nimport MuteElements from \"../visual/muteElements.js\";\n\n/**\n * Imports the AmplitudeJS Visual Volume Slider Elements\n * @module visual/VolumeSliderElements\n */\nimport VolumeSliderElements from \"../visual/volumeSliderElements.js\";\n\n/**\n * Handles all events for a volume down event.\n * @module events/VolumeDown\n */\nlet VolumeDown = (function() {\n  /**\n   * Handles a click on a volume down element.\n   *\n   * HANDLER FOR:       class=\"amplitude-volume-down\"\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n      We don't fire this if the user is touching the screen and it's moving.\n      This could lead to a mis-fire\n    */\n    if (!config.is_touch_moving) {\n      /*\n\t\t\t\tThe volume range is from 0 to 1 for an audio element. We make this\n\t\t\t\ta base of 100 for ease of working with.\n\n\t\t\t\tIf the new value is less than 100, we use the new calculated\n\t\t\t\tvalue which gets converted to the proper unit for the audio element.\n\n\t\t\t\tIf the new value is greater than 100, we set the volume to 1 which\n\t\t\t\tis the max for the audio element.\n\t\t\t*/\n      let volume = null;\n\n      if (config.volume - config.volume_increment > 0) {\n        volume = config.volume - config.volume_increment;\n      } else {\n        volume = 0;\n      }\n\n      /*\n\t\t\t\tCalls the core function to set the volume to the computed value\n\t\t\t\tbased on the user's intent.\n\t\t\t*/\n      Core.setVolume(volume);\n\n      /*\n        Sync Mute Elements.\n      */\n      MuteElements.setMuted(config.volume == 0 ? true : false);\n\n      /*\n        Sync Volume Slider Elements\n      */\n      VolumeSliderElements.sync();\n    }\n  }\n\n  /**\n   * Returns the public facing methods\n   */\n  return {\n    handle: handle\n  };\n})();\n\nexport default VolumeDown;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/volumeDown.js", "/**\n * Imports the config to use the values\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the AmplitudeJS Core Methods\n * @module core/core\n */\nimport Core from \"../core/core.js\";\n\n/**\n * Imports the AmplitudeJS Visual Mute Elements\n * @module visual/MuteElements\n */\nimport MuteElements from \"../visual/muteElements.js\";\n\n/**\n * Imports the AmplitudeJS Visual Volume Slider Elements\n * @module visual/VolumeSliderElements\n */\nimport VolumeSliderElements from \"../visual/volumeSliderElements.js\";\n\n/**\n * Handles all events for a volume up event.\n * @module events/VolumeSlider\n */\nlet VolumeSlider = (function() {\n  /**\n   * Handles a change on the volume slider\n   *\n   * HANDLER FOR:       class=\"amplitude-volume-slider\"\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n\t\t\tCalls the core function to set the volume to the computed value\n\t\t\tbased on the user's intent.\n\t\t*/\n    Core.setVolume(this.value);\n\n    /*\n      Sync Mute Elements.\n    */\n    MuteElements.setMuted(config.volume == 0 ? true : false);\n\n    /*\n\t\t\tSync the volume slider locations\n\t\t*/\n    VolumeSliderElements.sync();\n  }\n\n  /**\n   * Returns the public facing methods\n   */\n  return {\n    handle: handle\n  };\n})();\n\nexport default VolumeSlider;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/volumeSlider.js", "/**\n * Imports the config to use the values\n */\nimport config from \"../config.js\";\n\n/**\n * Imports the AmplitudeJS Core Methods\n * @module core/core\n */\nimport Core from \"../core/core.js\";\n\n/**\n * Imports the AmplitudeJS Visual Mute Elements\n * @module visual/MuteElements\n */\nimport MuteElements from \"../visual/muteElements.js\";\n\n/**\n * Imports the AmplitudeJS Visual Volume Slider Elements\n * @module visual/VolumeSliderElements\n */\nimport VolumeSliderElements from \"../visual/volumeSliderElements.js\";\n\n/**\n * Handles all events for a volume up event.\n * @module events/VolumeUp\n */\nlet VolumeUp = (function() {\n  /**\n   * Handles a click on a volume up element.\n   *\n   * HANDLER FOR:       class=\"amplitude-volume-up\"\n   *\n   * @access public\n   */\n  function handle() {\n    /*\n      We don't fire this if the user is touching the screen and it's moving.\n      This could lead to a mis-fire\n    */\n    if (!config.is_touch_moving) {\n      /*\n\t\t\t\tThe volume range is from 0 to 1 for an audio element. We make this\n\t\t\t\ta base of 100 for ease of working with.\n\n\t\t\t\tIf the new value is less than 100, we use the new calculated\n\t\t\t\tvalue which gets converted to the proper unit for the audio element.\n\n\t\t\t\tIf the new value is greater than 100, we set the volume to 1 which\n\t\t\t\tis the max for the audio element.\n\t\t\t*/\n      let volume = null;\n\n      if (config.volume + config.volume_increment <= 100) {\n        volume = config.volume + config.volume_increment;\n      } else {\n        volume = 100;\n      }\n\n      /*\n\t\t\t\tCalls the core function to set the volume to the computed value\n\t\t\t\tbased on the user's intent.\n\t\t\t*/\n      Core.setVolume(volume);\n\n      /*\n        Sync Mute Elements.\n      */\n      MuteElements.setMuted(config.volume == 0 ? true : false);\n\n      /*\n        Sync Volume Slider Elements\n      */\n      VolumeSliderElements.sync();\n    }\n  }\n\n  /**\n   * Returns the public facing methods\n   */\n  return {\n    handle: handle\n  };\n})();\n\nexport default VolumeUp;\n\n\n\n// WEBPACK FOOTER //\n// ./src/events/volumeUp.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * AmplitudeJS FX Module. Helps with configuring and setting up visualizations\n * and any other features of the Web Audio API that Amplitude takes advantage\n * of.\n *\n * @module fx/FX\n */\nlet Fx = (function() {\n  /**\n   * Configures the Web Audio API to work with AmplitudeJS\n   */\n  function configureWebAudioAPI() {\n    /*\n\t\t\tGets the context for the browser. If this is null, the Web Audio\n\t\t\tAPI is unavailable.\n\t\t*/\n    let browserContext =\n      window.AudioContext ||\n      window.webkitAudioContext ||\n      window.mozAudioContext ||\n      window.oAudioContext ||\n      window.msAudioContext;\n\n    /*\n\t\t\tIf we have a context, the Web Audio API is available and we can continue,\n\t\t\totherwise, we alert the user if they have debug turned on.\n\t\t*/\n    if (browserContext) {\n      /*\n\t\t\t\tWeb Audio API is available, set the context in our config.\n\t\t\t*/\n      config.context = new browserContext();\n      \n      /*\n\t\t\t\tCreate an analyzer that we will use in the context.\n\t\t\t*/\n      config.analyser = config.context.createAnalyser();\n\n      /*\n\t\t\t\tSet cross origin to anonymous so we have a better chance of being able\n\t\t\t\tto use the power of the Web Audio API.\n\t\t\t*/\n      config.audio.crossOrigin = \"anonymous\";\n      \n      /*\n\t\t\t\tBind the source to the Javascript Audio Element.\n\t\t\t*/\n      config.source = config.context.createMediaElementSource(config.audio);\n\n      /*\n\t\t\t\tConnect the analyser to the source\n\t\t\t*/\n      config.source.connect(config.analyser);\n\n      /*\n\t\t\t\tConnect the context destination to the analyser.\n\t\t\t*/\n      config.analyser.connect(config.context.destination);\n    } else {\n      AmplitudeHelpers.writeDebugMessage(\n        \"Web Audio API is unavailable! We will set any of your visualizations with your back up definition!\"\n      );\n    }\n  }\n\n  /**\n   * Determines if the web audio API is available or not.\n   */\n  function webAudioAPIAvailable() {\n    /*\n\t\t\tGets the context for the browser. If this is null, the Web Audio\n\t\t\tAPI is unavailable.\n\t\t*/\n    let browserContext =\n      window.AudioContext ||\n      window.webkitAudioContext ||\n      window.mozAudioContext ||\n      window.oAudioContext ||\n      window.msAudioContext;\n    config.web_audio_api_available = false;\n\n    /*\n\t\t\tDetermines if the Web Audio API is available or not.\n\t\t*/\n    if (browserContext) {\n      /*\n\t\t\t\tSet the flag in the config that the Web Audio API is available\n\t\t\t*/\n      config.web_audio_api_available = true;\n      return true;\n    } else {\n      /*\n\t\t\t\tSet the flag in the config that the Web Audio API is not available\n\t\t\t*/\n      config.web_audio_api_available = false;\n      return false;\n    }\n  }\n\n  /**\n   * Determines if the user is using any of the web audio API features.\n   */\n  function determineUsingAnyFX(){\n    let waveforms = document.querySelectorAll(\".amplitude-wave-form\");\n    let visualizationElements = document.querySelectorAll(\n      \".amplitude-visualization\"\n    );\n\n    if( waveforms.length > 0 || visualizationElements.length > 0 ){\n      return true;\n    }else{\n      return false;\n    }\n  }\n\n  /*\n\t\tReturns the publicly accessible methods\n\t*/\n  return {\n    configureWebAudioAPI: configureWebAudioAPI,\n    webAudioAPIAvailable: webAudioAPIAvailable,\n    determineUsingAnyFX: determineUsingAnyFX\n  };\n})();\n\nexport default Fx;\n\n\n\n// WEBPACK FOOTER //\n// ./src/fx/fx.js", "/**\n * @name \t\tAmplitude.js\n * <AUTHOR> (521 Dimensions) <<EMAIL>>\n */\n/**\n * AmplitudeJS Initializer Module\n *\n * @module init/AmplitudeInitializer\n */\nimport Initializer from \"./init/init.js\";\n\n/****************************************************\n * Config\n ****************************************************/\n/**\n * Imports the config module\n * @module config\n */\nimport config from \"./config.js\";\n\n/****************************************************\n * Core\n ****************************************************/\n/**\n * AmplitudeJS Core Module\n *\n * @module core/Core\n */\nimport Core from \"./core/core.js\";\n\n/****************************************************\n * Utilities\n ****************************************************/\n/**\n * Shuffler Module\n * @module utilities/Shuffler\n */\nimport Shuffler from \"./utilities/shuffler.js\";\n\n/**\n * Imports the config state module.\n * @module ConfigState\n */\nimport ConfigState from \"./utilities/configState.js\";\n\n/**\n * Imports the audio navigation\n * @module utilities/AudioNavigation\n */\nimport AudioNavigation from \"./utilities/audioNavigation.js\";\n\n/**\n * Repeater Module\n *\n * @module utilities/Repeater\n */\nimport Repeater from \"./utilities/repeater.js\";\n\n/**\n * Imports the checks\n * @module utilities/Checks\n */\nimport Checks from \"./utilities/checks.js\";\n\n/****************************************************\n * FX Modules\n ****************************************************/\n/**\n * Imports the visualizations module\n * @module fx/Visualizations\n */\nimport Visualizations from \"./fx/visualizations.js\";\n\n/****************************************************\n * Elements\n ****************************************************/\n/**\n * Visual Shuffle Elements\n * @module visual/ShuffleElements\n */\nimport ShuffleElements from \"./visual/shuffleElements.js\";\n\n/**\n * Visual Repeat Elements\n * @module visual/RepeatElements\n */\nimport RepeatElements from \"./visual/repeatElements.js\";\n\n/**\n * Song Slider Elements\n * @module visual/SongSliderElements\n */\nimport SongSliderElements from \"./visual/songSliderElements.js\";\n\n/**\n * Song Played Progress Elements\n * @module visual/SongPlayedProgressElements\n */\nimport SongPlayedProgressElements from \"./visual/songPlayedProgressElements.js\";\n\n/**\n * Time Elements\n * @module visual/TimeElements\n */\nimport TimeElements from \"./visual/timeElements.js\";\n\n/**\n * Play Pause Elements\n * @module visual/PlayPauseElements\n */\nimport PlayPauseElements from \"./visual/playPauseElements.js\";\n\n/**\n * Meta Data Elements\n * @module visual/MetaDataElements\n */\nimport MetaDataElements from \"./visual/metaDataElements.js\";\n\n/** \n * Playback Speed Elements\n * @module visual/PlaybackSpeedElements\n */\nimport PlaybackSpeedElements from \"./visual/playbackSpeedElements.js\";\n\nimport Debug from \"./utilities/debug.js\";\n\nimport SoundCloud from \"./soundcloud/soundcloud.js\";\n\n/**\n * Amplitude should just be an interface to the public functions.\n * Everything else should be handled by other objects\n *\n * @module Amplitude\n */\nlet Amplitude = (function() {\n  /**\n   * The main init function.  The user will call this through\n   * Amplitude.init({}) and pass in their settings.\n   *\n   * Public Accessor: Amplitude.init( user_config_json );\n   *\n   * @access public\n   * @param {object} userConfig \t- A JSON object of user defined values that helps configure and initialize AmplitudeJS.\n   */\n  function init(userConfig) {\n    Initializer.initialize(userConfig);\n  }\n\n  /**\n   * Returns the current config for AmplitudeJS\n   */\n  function getConfig() {\n    return config;\n  }\n\n  /**\n   * Binds new elements that were added to the page.\n   *\n   * Public Accessor: Amplitude.bindNewElements()\n   *\n   * @access public\n   */\n  function bindNewElements() {\n    Initializer.rebindDisplay();\n  }\n\n  /**\n   * Returns the active playlist.\n   *\n   * Public Accessor: Amplitude.getActivePlaylist()\n   *\n   * @access public\n   */\n  function getActivePlaylist() {\n    return config.active_playlist;\n  }\n\n  /**\n   * Returns the current playback speed.\n   *\n   * Public Accessor: Amplitude.getPlaybackSpeed()\n   *\n   * @access public\n   */\n  function getPlaybackSpeed() {\n    return config.playback_speed;\n  }\n\n  /**\n   * Sets the playback speed\n   * \n   * Public Accessor: Amplitude.setPlaybackSpeed( speed )\n   * \n   * @access public\n   */\n  function setPlaybackSpeed( speed ) {\n    /*\n      Increments are set in .5 We only accept values\n      1, 1.5, 2\n\n      1 -> Regular Speed\n      1.5 -> 50% faster\n      2 -> Twice as fast\n    */\n    Core.setPlaybackSpeed( speed );\n\n    /*\n      Visually sync the playback speed.\n    */\n    PlaybackSpeedElements.sync();\n  }\n\n  /**\n   * Gets the repeat state of the player.\n   *\n   * Public Accessor: Amplitude.getRepeat()\n   *\n   * @access public\n   */\n  function getRepeat() {\n    return config.repeat;\n  }\n\n  /**\n   * Gets the repeat state for a playlist\n   *\n   * Public Accessor: Amplitude.getRepeatPlaylist()\n   *\n   * @access public\n   */\n  function getRepeatPlaylist(playlistKey) {\n    return config.playlists[playlistKey].repeat;\n  }\n\n  /**\n   * Returns the shuffle state of the player.\n   *\n   * Public Accessor: Amplitude.getShuffle()\n   *\n   * @access public\n   */\n  function getShuffle() {\n    return config.shuffle_on;\n  }\n\n  /**\n   * Returns the shuffle state of the playlist.\n   *\n   * Public Accessor: Amplitude.getShufflePlaylist( playlist )\n   *\n   * @access public\n   * @param {string} playlist \t- The key representing the playlist ID to see if it's shuffled or not.\n   */\n  function getShufflePlaylist(playlist) {\n    return config.playlists[playlist].shuffle;\n  }\n\n  /**\n   * Sets the shuffle state for the player.\n   *\n   * Public Accessor: Amplitude.setShuffle()\n   *\n   * @param {boolean} shuffle  \t- True when we are shuffling the songs, false when we turn off shuffle.\n   *\n   * @access public\n   */\n  function setShuffle(shuffle) {\n    Shuffler.setShuffle(shuffle);\n\n    ShuffleElements.syncMain();\n  }\n\n  /**\n   * Sets the shuffle state for the playlist\n   *\n   * Public Accessor: Amplitude.setShufflePlaylist( playlist )\n   *\n   * @access public\n   * @param {string} playlist \t- The key representing the playlist ID to to shuffle the playlist.\n   * @param {boolean} shuffle \t- True when we are shuffling the playlist, false when we turn off shuffle.\n   */\n  function setShufflePlaylist(playlist, shuffle) {\n    Shuffler.setShufflePlaylist(playlist, shuffle);\n\n    ShuffleElements.syncMain();\n    ShuffleElements.syncPlaylist(playlist);\n  }\n\n  /**\n   * Sets the repeat state for the player.\n   *\n   * Public Accessor: Amplitude.setRepeat()\n   *\n   * @access public\n   * @param {boolean} repeatState \t- The state you want the repeat song to be in.\n   */\n  function setRepeat(repeatState) {\n    Repeater.setRepeat(repeatState);\n    RepeatElements.syncRepeat();\n  }\n\n  /**\n   * Sets the repeat state for a playlist.\n   *\n   * Public Accessor: Amplitude.setRepeatPlaylist( playlistKey )\n   *\n   * @access public\n   * @param {string} playlist \t- The key representing the playlist ID to to shuffle the playlist.\n   * @param {boolean} repeatState - The state you want the repeat playlist to be in.\n   */\n  function setRepeatPlaylist(playlist, repeatState) {\n    Repeater.setRepeatPlaylist(repeatState, playlist);\n    RepeatElements.syncRepeatPlaylist(playlist);\n  }\n\n  /**\n   * Sets the repeat state for the song.\n   *\n   * Public Accessor: Amplitude.setRepeatSong()\n   *\n   * @access public\n   * @param {boolean} repeatState \t- The state you want the repeat song status to be in.\n   */\n  function setRepeatSong(repeatState) {\n    if (!config.is_touch_moving) {\n      /*\n\t\t\t Sets repeat to the opposite of what it was set to\n\t\t\t*/\n      Repeater.setRepeatSong(!config.repeat_song);\n\n      /*\n\t\t\t\tVisually sync repeat song\n\t\t\t*/\n      RepeatElements.syncRepeatSong();\n    }\n  }\n\n  /**\n   * Gets the default album art for the player\n   *\n   * Public Accessor: Amplitude.getDefaultAlbumArt()\n   *\n   * @access public\n   */\n  function getDefaultAlbumArt() {\n    return config.default_album_art;\n  }\n\n  /**\n   * Gets the default playlist art for the playlists\n   *\n   * Public Accessor: Amplitude.getDefaultPlaylistArt()\n   *\n   * @access public\n   */\n  function getDefaultPlaylistArt(){\n    return config.default_playlist_art;\n  }\n\n  /**\n   * Sets the default album art for the player\n   *\n   * Public Accessor: Amplitude.setDefaultAlbumArt( url )\n   *\n   * @access public\n   * @param {string} url \t- A string representing the URL of the new default album art.\n   */\n  function setDefaultAlbumArt(url) {\n    config.default_album_art = url;\n  }\n\n  /**\n   * Sets the default playlist art for the player\n   *\n   * Public Accessor: Amplitude.setDefaultPlaylistArt( url )\n   *\n   * @access public\n   * @param {string} url - A string representing the URL of the new default playlist art.\n   */\n   function setDefaultPlaylistArt(url){\n     config.default_plalist_art = url;\n   }\n\n  /**\n   * Allows the user to get the percentage of the song played.\n   *\n   * Public Accessor: Amplitude.getSongPlayedPercentage();\n   *\n   * @access public\n   */\n  function getSongPlayedPercentage() {\n    /*\n\t\t\tReturns the percentage of the song played.\n\t\t*/\n    return (config.audio.currentTime / config.audio.duration) * 100;\n  }\n\n  /**\n   * Allows the user to get the amount of seconds the song has played.\n   *\n   * Public Accessor: Amplitude.getSongPlayed();\n   *\n   * @access public\n   */\n  function getSongPlayedSeconds() {\n    /*\n\t\t\tReturns the amount of seconds the song has played.\n\t\t*/\n    return config.audio.currentTime;\n  }\n\n  /**\n   * Allows the user to get the duration of the current song\n   *\n   * Public Accessor: Amplitude.getSongPlayed();\n   *\n   * @access public\n   */\n  function getSongDuration() {\n    /*\n\t\t\tReturns the duration of the current song\n\t\t*/\n    return config.audio.duration;\n  }\n\n  /**\n   * Allows the user to set how far into the song they want to be. This is\n   * helpful for implementing custom range sliders. Only works on the current song.\n   *\n   * Public Accessor: Amplitude.setSongPlayedPercentage( float );\n   *\n   * @access public\n   * @param {number} percentage \t- The percentage of the song played\n   */\n  function setSongPlayedPercentage(percentage) {\n    /*\n\t\t\tEnsures the percentage is a number and is between 0 and 100.\n\t\t*/\n    if (typeof percentage == \"number\" && (percentage > 0 && percentage < 100)) {\n      /*\n\t\t\t\t\tSets the current time of the song to the percentage.\n\t\t\t\t*/\n      config.audio.currentTime = config.audio.duration * (percentage / 100);\n    }\n  }\n\n  /**\n   * Allows the user to turn on debugging.\n   *\n   * Public Accessor: Amplitude.setDebug( bool );\n   *\n   * @access public\n   * @param {boolean} state \t\t- Turns debugging on and off.\n   */\n  function setDebug(state) {\n    /*\n\t\t\tSets the global config debug on or off.\n\t\t*/\n    config.debug = state;\n  }\n\n  /**\n   * Returns the active song meta data for the user to do what is\n   * needed.\n   *\n   * Public Accessor: Amplitude.getActiveSongMetadata();\n   *\n   * @access public\n   * @returns {object} JSON Object with the active song information\n   */\n  function getActiveSongMetadata() {\n    return config.active_metadata;\n  }\n\n  /**\n   * Returns the active playlist meta data for the for the user to use.\n   *\n   * Public Accessor: Amplitude.getActivePlaylistMetadata();\n   *\n   * @access public\n   * @returns {object} JSON representation for the active playlist\n   */\n  function getActivePlaylistMetadata() {\n    return config.playlists[config.active_playlist];\n  }\n\n  /**\n   * Returns a song in the songs array at that index\n   *\n   * Public Accessor: Amplitude.getSongAtIndex( song_index )\n   *\n   * @access public\n   * @param {number} index \t- The integer for the index of the song in the songs array.\n   * @returns {object} JSON representation for the song at a specific index.\n   */\n  function getSongAtIndex(index) {\n    return config.songs[index];\n  }\n\n  /**\n   * Returns a song at a playlist index\n   *\n   * Public Accessor: Amplitude.getSongAtPlaylistIndex( playlist, index\n   *\n   * @access public\n   * @param {number} index \t\t\t- The integer for the index of the song in the playlist.\n   * @param {string} playlist\t\t- The key of the playlist we are getting the song at the index for\n   * @returns {object} JSON representation for the song at a specific index.\n   */\n  function getSongAtPlaylistIndex(playlist, index) {\n    let song = config.playlists[playlist].songs[index];\n\n    return song;\n  }\n\n  /**\n   * Adds a song to the end of the config array.  This will allow Amplitude\n   * to play the song in a playlist type setting.\n   *\n   * Public Accessor: Amplitude.addSong( song_json )\n   *\n   * @access public\n   * @param {object} song \t- JSON representation of a song.\n   * @returns {number} New index of the song.\n   */\n  function addSong(song) {\n    /*\n\t\t\tEnsures we have a songs array to push to.\n\t\t*/\n    if (config.songs == undefined) {\n      config.songs = [];\n    }\n\n    config.songs.push(song);\n\n    if (config.shuffle_on) {\n      config.shuffle_list.push(song);\n    }\n\n    if (SoundCloud.isSoundCloudURL(song.url)) {\n      SoundCloud.resolveIndividualStreamableURL(\n        song.url,\n        null,\n        config.songs.length - 1,\n        config.shuffle_on\n      );\n    }\n\n    return config.songs.length - 1;\n  }\n\n  /**\n   * Adds a song to a playlist. This will allow Amplitude to play the song in the\n   * playlist\n   *\n   * Public Accessor: Amplitude.addSongToPlaylist( song_json, playlist_key )\n   *\n   * @access public\n   * @param {object} song \t\t\t- JSON representation of a song.\n   * @param {string} playlist\t\t- Playlist we are adding the song to.\n   * @returns {mixed} New index of song in playlist or null if no playlist exists\n   */\n  function addSongToPlaylist(song, playlist) {\n    if (config.playlists[playlist] != undefined) {\n      config.playlists[playlist].songs.push(song);\n\n      if (config.playlists[playlist].shuffle) {\n        config.playlists[playlist].shuffle_list.push(song);\n      }\n\n      if (SoundCloud.isSoundCloudURL(song.url)) {\n        SoundCloud.resolveIndividualStreamableURL(\n          song.url,\n          playlist,\n          config.playlists[playlist].songs.length - 1,\n          config.playlists[playlist].shuffle\n        );\n      }\n\n      return config.playlists[playlist].songs.length - 1;\n    } else {\n      Debug.writeMessage(\"Playlist doesn't exist!\");\n      return null;\n    }\n  }\n\n  /**\n   * Adds a playlist to Amplitude.\n   *\n   * @param {string} key  - The key of the playlist we are adding.\n   * @param {object} data - The data relating to the playlist\n   * @param {array} songs - The songs to add to the playlist\n   */\n  function addPlaylist(key, data, songs) {\n    /*\n      Ensures the playlist is not already defined.\n    */\n    if (config.playlists[key] == undefined) {\n      /*\n        Initialize the new playlist object.\n      */\n      config.playlists[key] = {};\n\n      /*\n        Define the ignored keys that we don't want to copy over.\n      */\n      let ignoredKeys = [\"repeat\", \"shuffle\", \"shuffle_list\", \"songs\", \"src\"];\n\n      /*\n        Iterate over all of the keys defined by the user and\n        set them on the playlist.\n      */\n      for (let dataKey in data) {\n        if (ignoredKeys.indexOf(dataKey) < 0) {\n          config.playlists[key][dataKey] = data[dataKey];\n        }\n      }\n\n      /*\n        Initialize the default parameters for the playlist and set the songs.\n      */\n      config.playlists[key].songs = songs;\n      config.playlists[key].active_index = null;\n      config.playlists[key].repeat = false;\n      config.playlists[key].shuffle = false;\n      config.playlists[key].shuffle_list = [];\n\n      return config.playlists[key];\n    } else {\n      Debug.writeMessage(\"A playlist already exists with that key!\");\n      return null;\n    }\n  }\n\n  /**\n   * Removes a song from the song array\n   *\n   * Public Accessor: Amplitude.removeSong( index )\n   *\n   * @access public\n   * @param {integer} index - Index of the song being removed\n   * @returns {boolean} True if removed false if not.\n   */\n  function removeSong(index) {\n    config.songs.splice(index, 1);\n  }\n\n  /**\n   * Removes a song from the playlist\n   *\n   * Public Accessor: Amplitude.removeSongFromPlaylist( index, playlist )\n   *\n   * @access public\n   * @param {integer} index \t\t\t- Index of the song being removed from the playlist.\n   * @param {string} playlist\t\t\t- Playlist we are removing the song from.\n   * @returns {boolean} True if removed false if not.\n   */\n  function removeSongFromPlaylist(index, playlist) {\n    if (config.playlists[playlist] != undefined) {\n      config.playlists[playlist].songs.splice(index, 1);\n    }\n  }\n\n  /**\n   * When you pass a song object it plays that song right awawy.  It sets\n   * the active song in the config to the song you pass in and synchronizes\n   * the visuals.\n   *\n   * Public Accessor: Amplitude.playNow( song )\n   *\n   * @access public\n   * @param {object} song \t- JSON representation of a song.\n   */\n  function playNow(song) {\n    /*\n\t\t\tMakes sure the song object has a URL associated with it\n\t\t\tor there will be nothing to play.\n\t\t*/\n    if (song.url) {\n      config.audio.src = song.url;\n      config.active_metadata = song;\n      config.active_album = song.album;\n    } else {\n      /*\n\t\t\t\tWrite error message since the song passed in doesn't\n\t\t\t\thave a URL.\n\t\t\t*/\n      Debug.writeMessage(\"The song needs to have a URL!\");\n    }\n\n    /*\n\t\t\tPlays the song.\n\t\t*/\n    Core.play();\n\n    /*\n\t\t\tSets the main song control status visual\n\t\t*/\n    PlayPauseElements.sync();\n\n    /*\n\t\t\tUpdate the song meta data\n\t\t*/\n    MetaDataElements.displayMetaData();\n\n    /*\n\t\t\tReset the song sliders, song progress bar info, and\n\t\t\treset times. This ensures everything stays in sync.\n\t\t*/\n    SongSliderElements.resetElements();\n\n    /*\n\t\t\tReset the song played progress elements.\n\t\t*/\n    SongPlayedProgressElements.resetElements();\n\n    /*\n\t\t\tReset all of the current time elements.\n\t\t*/\n    TimeElements.resetCurrentTimes();\n\n    /*\n\t\t\tReset all of the duration time elements.\n\t\t*/\n    TimeElements.resetDurationTimes();\n\n    /*\n      Sets the state of the player.\n    */\n    ConfigState.setPlayerState();\n  }\n\n  /**\n   * Plays a song at the index passed in from the songs array.\n   *\n   * Public Accessor: Amplitude.playSongAtIndex( index )\n   *\n   * @access public\n   * @param {number} index \t- The number representing the song in the songs array.\n   */\n  function playSongAtIndex(index) {\n    /*\n\t\t\t Stop the current song.\n\t\t*/\n    Core.stop();\n\n    /*\n\t\t\t Determine if there is a new playlist, if so set the active playlist and change the song.\n\t\t*/\n    if (Checks.newPlaylist(null)) {\n      AudioNavigation.setActivePlaylist(null);\n\n      AudioNavigation.changeSong(config.songs[index], index);\n    }\n\n    /*\n\t\t\t Check if the song is new. If so, change the song.\n\t\t*/\n    if (Checks.newSong(null, index)) {\n      AudioNavigation.changeSong(config.songs[index], index);\n    }\n\n    /*\n\t\t\tPlay the song\n\t\t*/\n    Core.play();\n\n    /*\n      Sets the state of the player.\n    */\n    ConfigState.setPlayerState();\n\n    /*\n\t\t\tSync all of the play pause buttons.\n\t\t*/\n    PlayPauseElements.sync();\n  }\n\n  /**\n   * Plays a song at the index passed in for the playlist provided. The index passed\n   * in should be the index of the song in the playlist and not the songs array.\n   *\n   * @access public\n   * @param {number} index \t\t- The number representing the song in the playlist array.\n   * @param {string} playlist - The key string representing the playlist we are playing the song from.\n   *\n   */\n  function playPlaylistSongAtIndex(index, playlist) {\n    Core.stop();\n\n    /*\n\t\t\tDetermine if there is a new playlist, if so set the active playlist and change the song.\n\t\t*/\n    if (Checks.newPlaylist(playlist)) {\n      AudioNavigation.setActivePlaylist(playlist);\n\n      AudioNavigation.changeSongPlaylist(\n        playlist,\n        config.playlists[playlist].songs[index],\n        index\n      );\n    }\n\n    /*\n\t\t\tCheck if the song is new. If so, change the song.\n\t\t*/\n    if (Checks.newSong(playlist, index)) {\n      AudioNavigation.changeSongPlaylist(\n        playlist,\n        config.playlists[playlist].songs[index],\n        index\n      );\n    }\n\n    /*\n\t\t\tSync all of the play pause buttons.\n\t\t*/\n    PlayPauseElements.sync();\n\n    /*\n\t\t\tPlay the song\n\t\t*/\n    Core.play();\n\n    /*\n\t\t\tSet the state of the player\n\t\t*/\n    ConfigState.setPlayerState();\n  }\n\n  /**\n   * Allows the user to play whatever the active song is directly\n   * through Javascript. Normally ALL of Amplitude functions that access\n   * the core features are called through event handlers.\n   *\n   * Public Accessor: Amplitude.play();\n   *\n   * @access public\n   */\n  function play() {\n    Core.play();\n\n    ConfigState.setPlayerState();\n  }\n\n  /**\n   * Allows the user to pause whatever the active song is directly\n   * through Javascript. Normally ALL of Amplitude functions that access\n   * the core features are called through event handlers.\n   *\n   * Public Accessor: Amplitude.pause();\n   *\n   * @access public\n   */\n  function pause() {\n    Core.pause();\n\n    ConfigState.setPlayerState();\n  }\n\n  /**\n   * Returns the audio object used to play the audio\n   *\n   * Public Accessor: Amplitude.getAudio();\n   *\n   * @access public\n   */\n  function getAudio() {\n    return config.audio;\n  }\n\n  /**\n   * Returns the Web Audio API ANalyser used for visualizations.\n   *\n   * Public Accessor: Amplitude.getAnalyser()\n   *\n   * @access public\n   */\n  function getAnalyser() {\n    return config.analyser;\n  }\n\n  /**\n   * Plays the next song either in the playlist or globally.\n   *\n   * Public Accessor: Amplitude.next( playlist );\n   *\n   * @access public\n   * @param {string} [playlist = null \t- The playlist key\n   */\n  function next(playlist = null) {\n    let nextData = {};\n    /*\n\t\t\tIf the playlist is empty or null, then we check the active\n\t\t\tplaylist\n\t\t*/\n    if (playlist == \"\" || playlist == null) {\n      /*\n\t\t\t\tIf the active playlist is null, then we set the next global\n\t\t\t\tsong or we set the next in the playlist.\n\t\t\t*/\n      if (config.active_playlist == null || config.active_playlist == \"\") {\n        AudioNavigation.setNext();\n      } else {\n        AudioNavigation.setNextPlaylist(config.active_playlist);\n      }\n    } else {\n      AudioNavigation.setNextPlaylist(playlist);\n    }\n  }\n\n  /**\n   * Plays the prev song either in the playlist or globally.\n   *\n   * Public Accessor: Amplitude.prev( playlist );\n   *\n   * @access public\n   * @param {string} [playlist = null] \t- The playlist key\n   */\n  function prev(playlist = null) {\n    let prevData = {};\n\n    /*\n\t\t\tIf the playlist is empty or null, then we check the active\n\t\t\tplaylist\n\t\t*/\n    if (playlist == \"\" || playlist == null) {\n      /*\n\t\t\t\tIf the active playlist is null, then we set the prev global\n\t\t\t\tsong or we set the prev in the playlist.\n\t\t\t*/\n      if (config.active_playlist == null || config.active_playlist == \"\") {\n        AudioNavigation.setPrevious();\n      } else {\n        AudioNavigation.setPreviousPlaylist(config.active_playlist);\n      }\n    } else {\n      AudioNavigation.setPreviousPlaylist(playlist);\n    }\n  }\n\n  /**\n   * Gets all of the songs in the songs array\n   *\n   * Public Accessor: Amplitude.getSongs( );\n   *\n   * @access public\n   */\n  function getSongs() {\n    return config.songs;\n  }\n\n  /**\n   * Gets all of the songs in a playlist\n   *\n   * Public Accessor: Amplitude.getSongsInPlaylist( playlist );\n   *\n   * @access public\n   * @param {string} playlist \t- The playlist key\n   */\n  function getSongsInPlaylist(playlist) {\n    return config.playlists[playlist].songs;\n  }\n\n  /**\n   * Get current state of songs. If shuffled, this will return the shuffled\n   * songs.\n   *\n   * Public Accessor: Amplitude.getSongsState();\n   *\n   * @access public\n   */\n  function getSongsState() {\n    if (config.shuffle_on) {\n      return config.shuffle_list;\n    } else {\n      return config.songs;\n    }\n  }\n\n  /**\n   * Get current state of songs in playlist. If shuffled, this will return the\n   * shuffled songs.\n   *\n   * Public Accessor: Amplitude.getSongsStatePlaylist( playlist );\n   *\n   * @access public\n   * @param {string} playlist \t- The playlist key\n   */\n  function getSongsStatePlaylist(playlist) {\n    if (config.playlists[playlist].shuffle) {\n      return config.playlists[playlist].shuffle_list;\n    } else {\n      return config.playlists[playlist].songs;\n    }\n  }\n\n  /**\n   * Gets the active index of the player\n   *\n   * Public Accessor: Amplitude.getActiveIndex()\n   *\n   * @access public\n   */\n  function getActiveIndex() {\n    return parseInt(config.active_index);\n  }\n\n  /**\n   * Get the version of AmplitudeJS\n   *\n   * Public Accessor: Amplitude.getVersion()\n   *\n   * @access public\n   */\n  function getVersion() {\n    return config.version;\n  }\n\n  /**\n   * Get the buffered amount for the current song\n   *\n   * Public Accessor: Amplitude.getBuffered()\n   *\n   * @access public\n   */\n  function getBuffered() {\n    return config.buffered;\n  }\n\n  /**\n   * Skip to a certain location in a selected song.\n   *\n   * Public Accessor: Amplitude.getBuffered()\n   *\n   * @access public\n   * @param {number} seconds \t\t\t\t\t\t- The amount of seconds we should skip to in the song.\n   * @param {number} songIndex \t\t\t\t\t- The index of the song in the songs array.\n   * @param {string} [playlist = null]\t- The playlist the song we are skipping to belogns to.\n   */\n  function skipTo(seconds, songIndex, playlist = null) {\n    seconds = parseInt(seconds);\n\n    if (playlist != null) {\n      /*\n        Checks if we are skipping to a new playlist\n      */\n      if (Checks.newPlaylist(playlist)) {\n        AudioNavigation.setActivePlaylist(playlist);\n      }\n\n      /*\n        Changes the song to where it's being skipped and then\n        play the song.\n      */\n      AudioNavigation.changeSongPlaylist(\n        playlist,\n        config.playlists[playlist].songs[songIndex],\n        songIndex\n      );\n      Core.play();\n\n      /*\n        Sync all of the play pause elements.\n      */\n      PlayPauseElements.syncGlobal();\n      PlayPauseElements.syncPlaylist();\n      PlayPauseElements.syncSong();\n\n      /*\n        Skip to the location in the song.\n      */\n      Core.skipToLocation(seconds);\n    } else {\n      /*\n        Changes the song to where it's being skipped and then\n        play the song.\n      */\n      AudioNavigation.changeSong(config.songs[songIndex], songIndex);\n      Core.play();\n\n      /*\n        Syncs all of the play pause buttons now that we've skipped.\n      */\n      PlayPauseElements.syncGlobal();\n      PlayPauseElements.syncSong();\n\n      /*\n        Skip to the location in the song.\n      */\n      Core.skipToLocation(seconds);\n    }\n  }\n\n  /**\n   * Sets the meta data for a song in the songs array. This will set any\n   * meta data for a song besides the URL. The URL could cause issues if the\n   * song was playing.\n   *\n   * Public Accessor: Amplitude.setSongMetaData()\n   *\n   * @access public\n   * @param {number} index\t\t\t\t\t- The index of the song in the songs array.\n   * @param {object} metaData \t\t\t- The object containing the meta data we are updating.\n   * @param {string} playlist       - The playlist we are updating the song meta data for.\n   */\n  function setSongMetaData(index, metaData, playlist = null) {\n    /*\n      Update the meta data for a song in a playlist.\n    */\n    if (\n      playlist != \"\" &&\n      playlist != null &&\n      config.playlists[playlist] != undefined\n    ) {\n      /*\n        Set all of the defined meta data properties\n      */\n      for (var key in metaData) {\n        if (metaData.hasOwnProperty(key)) {\n          if (key != \"url\" && key != \"URL\" && key != \"live\" && key != \"LIVE\") {\n            config.playlists[playlist].songs[index][key] = metaData[key];\n          }\n        }\n      }\n    } else {\n      /*\n        Update the meta data for a song.\n      */\n      for (var key in metaData) {\n        if (metaData.hasOwnProperty(key)) {\n          if (key != \"url\" && key != \"URL\" && key != \"live\" && key != \"LIVE\") {\n            config.songs[index][key] = metaData[key];\n          }\n        }\n      }\n    }\n\n    /*\n      Display the updates on the screen.\n    */\n    MetaDataElements.displayMetaData();\n    MetaDataElements.syncMetaData();\n  }\n\n  function setPlaylistMetaData(playlist, metaData) {\n    if (config.playlists[playlist] != undefined) {\n      /*\n  \t\t\tThese are the ignored keys that we won't be worrying about displaying.\n  \t\t\tEvery other key in the playlist object can be displayed.\n  \t\t*/\n      let ignoredKeys = [\"repeat\", \"shuffle\", \"shuffle_list\", \"songs\", \"src\"];\n\n      for (var key in metaData) {\n        if (metaData.hasOwnProperty(key)) {\n          if (ignoredKeys.indexOf(key) < 0) {\n            config.playlists[playlist][key] = metaData[key];\n          }\n        }\n      }\n\n      MetaDataElements.displayPlaylistMetaData();\n    } else {\n      Debug.writeMessage(\"You must provide a valid playlist key!\");\n    }\n  }\n\n  /**\n   * Sets the delay between the songs when they are finished.\n   *\n   * Public Accessor: Amplitude.setDelay()\n   *\n   * @access public\n   * @param {number} delay \t- The millisecond delay time between songs\n   */\n  function setDelay(time) {\n    config.delay = time;\n  }\n\n  /**\n   * Returns the current delay between songs.\n   *\n   * Public Accessor: Amplitude.getDelay()\n   *\n   * @access public\n   */\n  function getDelay() {\n    return config.delay;\n  }\n\n  /**\n   * Returns the state of the player.\n   *\n   * Public Accessor: Amplitude.getPlayerState();\n   */\n  function getPlayerState() {\n    return config.player_state;\n  }\n\n  /**\n   * Registers a visualization and sets that visualization's\n   * preferences. When creating a visualization, you can set certain\n   * preferences that the user can overwrite similar to Amplitude.\n   * Public Accessor: Amplitude.registerVisualization( visualization, preferences )\n   *\n   * @param {object} visualzation A visualization object that gets registered\n   * with Amplitude\n   *\n   * @param {object} preferences A JSON object of preferences relating to the\n   * visualization\n   */\n  function registerVisualization(visualization, preferences) {\n    Visualizations.register(visualization, preferences);\n  }\n\n  /**\n   * Set the visualization for the playlist\n   *\n   * @param {string} playlist - The playlist we are setting the visualization for.\n   * @param {string} visualizationKey - The key of the visualization we are adding to the playlist.\n   */\n  function setPlaylistVisualization(playlist, visualizationKey) {\n    if (config.playlists[playlist] != undefined) {\n      if (config.visualizations.available[visualizationKey] != undefined) {\n        config.playlists[playlist].visualization = visualizationKey;\n      } else {\n        Debug.writeMessage(\n          \"A visualization does not exist for the key provided.\"\n        );\n      }\n    } else {\n      Debug.writeMessage(\"The playlist for the key provided does not exist\");\n    }\n  }\n\n  /**\n   * Set a visualization for the song.\n   *\n   * @param {number} songIndex - The index of the song in the songs array we are setting the visualization for.\n   * @param {string} visualizationKey - The key of the visualization we are adding to the playlist.\n   */\n  function setSongVisualization(songIndex, visualizationKey) {\n    if (config.songs[songIndex]) {\n      if (config.visualizations.available[visualizationKey] != undefined) {\n        config.songs[songIndex].visualization = visualizationKey;\n      } else {\n        Debug.writeMessage(\n          \"A visualization does not exist for the key provided.\"\n        );\n      }\n    } else {\n      Debug.writeMessage(\"A song at that index is undefined\");\n    }\n  }\n\n  /**\n   * Set song in playlist visualization.\n   *\n   * @param {string} playlist - The playlist we are setting the song visualization for.\n   * @param {number} songIndex - The index we are setting the visualization for.\n   * @param {strong} visualizationKey - The key of the visualization we are adding to the song in the playlist.\n   */\n  function setSongInPlaylistVisualization(\n    playlist,\n    songIndex,\n    visualizationKey\n  ) {\n    if (config.playlists[playlist].songs[songIndex] != undefined) {\n      if (config.visualizations.available[visualizationKey] != undefined) {\n        config.playlists[playlist].songs[\n          songIndex\n        ].visualization = visualizationKey;\n      } else {\n        Debug.writeMessage(\n          \"A visualization does not exist for the key provided.\"\n        );\n      }\n    } else {\n      Debug.writeMessage(\"The song in the playlist at that key is not defined\");\n    }\n  }\n\n  /**\n   * Sets the global visualization default.\n   */\n  function setGlobalVisualization(visualizationKey) {\n    if (config.visualizations.available[visualizationKey] != undefined) {\n      config.visualization = visualizationKey;\n    } else {\n      Debug.writeMessage(\n        \"A visualization does not exist for the key provided.\"\n      );\n    }\n  }\n\n  /**\n   * Sets the active volume.\n   * @param {number} volumeLevel - A number between 1 and 100 as a percentage of\n   * min to max for a volume level.\n   */\n  function setVolume( volumeLevel ){\n    Core.setVolume( volumeLevel );\n  }\n\n  /**\n   * Gets the active volume.\n   */\n  function getVolume(){\n    return config.volume;\n  }\n\n  /*\n\t\tReturns all of the publically accesible methods.\n\t*/\n  return {\n    init: init,\n    getConfig: getConfig,\n    bindNewElements: bindNewElements,\n    getActivePlaylist: getActivePlaylist,\n    getPlaybackSpeed: getPlaybackSpeed,\n    setPlaybackSpeed: setPlaybackSpeed,\n    getRepeat: getRepeat,\n    getRepeatPlaylist: getRepeatPlaylist,\n    getShuffle: getShuffle,\n    getShufflePlaylist: getShufflePlaylist,\n    setShuffle: setShuffle,\n    setShufflePlaylist: setShufflePlaylist,\n    setRepeat: setRepeat,\n    setRepeatSong: setRepeatSong,\n    setRepeatPlaylist: setRepeatPlaylist,\n    getDefaultAlbumArt: getDefaultAlbumArt,\n    setDefaultAlbumArt: setDefaultAlbumArt,\n    getDefaultPlaylistArt: getDefaultPlaylistArt,\n    setDefaultPlaylistArt: setDefaultPlaylistArt,\n    getSongPlayedPercentage: getSongPlayedPercentage,\n    setSongPlayedPercentage: setSongPlayedPercentage,\n    getSongPlayedSeconds: getSongPlayedSeconds,\n    getSongDuration: getSongDuration,\n    setDebug: setDebug,\n    getActiveSongMetadata: getActiveSongMetadata,\n    getActivePlaylistMetadata: getActivePlaylistMetadata,\n    getSongAtIndex: getSongAtIndex,\n    getSongAtPlaylistIndex: getSongAtPlaylistIndex,\n    addSong: addSong,\n    addSongToPlaylist: addSongToPlaylist,\n    removeSong: removeSong,\n    removeSongFromPlaylist: removeSongFromPlaylist,\n    playNow: playNow,\n    playSongAtIndex: playSongAtIndex,\n    playPlaylistSongAtIndex: playPlaylistSongAtIndex,\n    play: play,\n    pause: pause,\n    getAudio: getAudio,\n    getAnalyser: getAnalyser,\n    next: next,\n    prev: prev,\n    getSongs: getSongs,\n    getSongsInPlaylist: getSongsInPlaylist,\n    getSongsState: getSongsState,\n    getSongsStatePlaylist: getSongsStatePlaylist,\n    getActiveIndex: getActiveIndex,\n    getVersion: getVersion,\n    getBuffered: getBuffered,\n    skipTo: skipTo,\n    setSongMetaData: setSongMetaData,\n    setPlaylistMetaData: setPlaylistMetaData,\n    setDelay: setDelay,\n    getDelay: getDelay,\n    getPlayerState: getPlayerState,\n    addPlaylist: addPlaylist,\n    registerVisualization: registerVisualization,\n    setPlaylistVisualization: setPlaylistVisualization,\n    setSongVisualization: setSongVisualization,\n    setSongInPlaylistVisualization: setSongInPlaylistVisualization,\n    setGlobalVisualization: setGlobalVisualization,\n    getVolume: getVolume,\n    setVolume: setVolume\n  };\n})();\n\nexport default Amplitude;\n\n\n\n// WEBPACK FOOTER //\n// ./src/index.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * AmplitudeJS Debug Module\n * @module utilities/Debug\n */\nimport Debug from \"../utilities/debug.js\";\n\n/**\n * AmplitudeJS Checks Utility.\n * @module utilities/Checks\n */\nimport Checks from \"../utilities/checks.js\";\n\n/**\n * AmplitudeJS Visual Meta Data Elements Module\n * @module visual/MetaDataElements\n */\nimport MetaDataElements from \"../visual/metaDataElements.js\";\n\n/**\n * AmplitudeJS SoundCloud Meta module\n * @module soundcloud/Soundcloud\n */\nimport SoundCloud from \"../soundcloud/soundcloud.js\";\n\n/**\n * Handles the initialization of the playlists.\n *\n * @module init/PlaylistsInitializer\n */\nlet PlaylistsInitializer = (function() {\n  /**\n   * Initializes the playlists for AmplitudeJS\n   *\n   * @param {Object} playlists - The playlists defined by the user.\n   */\n  function initialize(playlists) {\n    /*\n      Copy the playlists over to Amplitude\n    */\n    config.playlists = playlists;\n\n    /*\n      Copy songs over from songs array.\n    */\n    copySongsToPlaylists();\n\n    /*\n      Grab any SoundCloud Data for the playlist songs if needed.\n    */\n    grabSoundCloudData();\n\n    /*\n      Initialize a scoped active index for each playlist.\n    */\n    initializePlaylistActiveIndexes();\n\n    /*\n      Initialize the shuffle status of the playlists.\n    */\n    initializePlaylistShuffleStatuses();\n\n    /*\n      Initialize the repeat status for the playlits.\n    */\n    initializePlaylistsRepeatStatuses();\n\n    /*\n      Initialize temporary place holders for shuffle lists.\n    */\n    initializePlaylistShuffleLists();\n\n    /*\n      Initializes the first song in the playlist\n    */\n    initializeFirstSongInPlaylistMetaData();\n  }\n\n  /**\n   * Initializes a scoped active index for each playlist.\n   *\n   * @access private\n   */\n  function initializePlaylistActiveIndexes() {\n    /*\n  \t\tIterate over all of the playlists defined by the user\n      and add an active index.\n\t\t*/\n    for (let key in config.playlists) {\n      config.playlists[key].active_index = null;\n    }\n  }\n\n  /**\n   * Ensures the indexes in the playlists are valid indexes. The song has\n   * to exist in the Amplitude config to be played correctly. If the index\n   * is an integer, we ensure it exists and coy it to the array.\n   *\n   * @access private\n   */\n  function copySongsToPlaylists() {\n    /*\n      Iterate over all of the config's playlists\n    */\n    for (let key in config.playlists) {\n      /*\n        Checks if the playlist key is accurate.\n      */\n      if (config.playlists.hasOwnProperty(key)) {\n        /*\n          Checks if the playlist has songs.\n        */\n        if (config.playlists[key].songs) {\n          /*\n            Iterate over all of the songs in the playlist\n          */\n          for (let i = 0; i < config.playlists[key].songs.length; i++) {\n            if (Checks.isInt(config.playlists[key].songs[i])) {\n              config.playlists[key].songs[i] =\n                config.songs[config.playlists[key].songs[i]];\n\n              config.playlists[key].songs[i].index = i;\n            }\n            /*\n              Check to see if the index for the song in the playlist\n              exists in the songs config.\n            */\n            if (\n              Checks.isInt(config.playlists[key].songs[i]) &&\n              !config.songs[config.playlists[key].songs[i]]\n            ) {\n              Debug.writeMessage(\n                \"The song index: \" +\n                  config.playlists[key].songs[i] +\n                  \" in playlist with key: \" +\n                  key +\n                  \" is not defined in your songs array!\"\n              );\n            }\n\n            /*\n              If not an int, then is a dedicated song, just set the index.\n            */\n            if (!Checks.isInt(config.playlists[key].songs[i]) ){\n              config.playlists[key].songs[i].index = i;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  /**\n   * Grabs the SoundCloud data for any song in the playlist that\n   * the user needs to grab data for.\n   *\n   * @access private\n   */\n  function grabSoundCloudData() {\n    /*\n      Iterate over all of the config's playlists\n    */\n    for (let key in config.playlists) {\n      /*\n        Checks if the playlist key is accurate.\n      */\n      if (config.playlists.hasOwnProperty(key)) {\n        /*\n          Iterate over all of the songs in the playlist and see if\n          they need to grab the SoundCloud data for the song.\n        */\n        for (let i = 0; i < config.playlists[key].songs.length; i++) {\n          /*\n            Only Grab the data if the URL is a SoundCloud URL.\n          */\n          if (SoundCloud.isSoundCloudURL(config.playlists[key].songs[i].url)) {\n            /*\n              Only grab the data if the SoundCloud data has not already been\n              grabbed for the audio. This could happen if the user defined the\n              song in the songs array and was grabbed before.\n            */\n            if (config.playlists[key].songs[i].soundcloud_data == undefined) {\n              SoundCloud.resolveIndividualStreamableURL(\n                config.playlists[key].songs[i].url,\n                key,\n                i\n              );\n            }\n          }\n        }\n      }\n    }\n  }\n\n  /**\n   * Initializes the shuffle statuses for each of the playlists. These will\n   * be referenced when we shuffle individual playlists.\n   *\n   * @access private\n   */\n  function initializePlaylistShuffleStatuses() {\n    /*\n\t\t\tIterate over all of the playlists the user defined adding\n\t\t\tthe playlist key to the shuffled playlist array and creating\n\t\t\tand empty object to house the statuses.\n\t\t*/\n    for (let key in config.playlists) {\n      config.playlists[key].shuffle = false;\n    }\n  }\n\n  /**\n   * Initializes the repeat statuses for each of the playlists.  These will\n   * be referenced when we repeat individual playlits.\n   *\n   * @access private\n   */\n  function initializePlaylistsRepeatStatuses() {\n    /*\n      Iterate over all of the playlists the user defined adding\n      the playlist key to the repeated playlist array and creating\n      and empty object to house the statuses.\n    */\n    for (let key in config.playlists) {\n      config.playlists[key].repeat = false;\n    }\n  }\n\n  /**\n   * Initializes the shuffled playlist placeholders. These will be set for\n   * playlists that are shuffled and contain the shuffled songs.\n   *\n   * @access private\n   */\n  function initializePlaylistShuffleLists() {\n    /*\n \t\t\tIterate over all of the playlists the user defined adding\n \t\t\tthe playlist key to the shuffled playlists array and creating\n \t\t\tand empty object to house the shuffled playlists\n \t\t*/\n    for (let key in config.playlists) {\n      config.playlists[key].shuffle_list = [];\n    }\n  }\n\n  /**\n   * Intializes the display for the first song in the playlist meta data.\n   *\n   * @access private\n   */\n  function initializeFirstSongInPlaylistMetaData() {\n    /*\n\t\t\tIterates over all of the playlists setting the meta data for the\n\t\t\tfirst song.\n\t\t*/\n    for (let key in config.playlists) {\n      MetaDataElements.setFirstSongInPlaylist(\n        config.playlists[key].songs[0],\n        key\n      );\n    }\n  }\n\n  /*\n    Returns the public facing methods\n  */\n  return {\n    initialize: initialize\n  };\n})();\n\nexport default PlaylistsInitializer;\n\n\n\n// WEBPACK FOOTER //\n// ./src/init/playlists.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../config.js\";\n\n/**\n * Handles all of the container elements.\n *\n * @param visual/ContainerElements\n */\nlet ContainerElements = (function() {\n  /**\n   * Applies the class 'amplitude-active-song-container' to the element\n   * containing visual information regarding the active song.\n   *\n   * @access public\n   */\n  function setActive() {\n    /*\n      Gets all of the song container elements.\n    */\n    let songContainers = document.getElementsByClassName(\n      \"amplitude-song-container\"\n    );\n\n    /*\n\t\t\tRemoves all of the active song containrs.\n\t\t*/\n    for (let i = 0; i < songContainers.length; i++) {\n      songContainers[i].classList.remove(\"amplitude-active-song-container\");\n    }\n\n    /*\n\t\t\tFinds the active index and adds the active song container to the element\n\t\t\tthat represents the song at the index.\n\t\t*/\n    if (config.active_playlist == \"\" || config.active_playlist == null) {\n      let activeIndex = '';\n\n      if( config.shuffle_on ){\n        activeIndex = config.shuffle_list[ config.active_index ].index;\n      }else{\n        activeIndex = config.active_index;\n      }\n\n      if (\n        document.querySelectorAll(\n          '.amplitude-song-container[data-amplitude-song-index=\"' +\n            activeIndex +\n            '\"]'\n        )\n      ) {\n        let songContainers = document.querySelectorAll(\n          '.amplitude-song-container[data-amplitude-song-index=\"' +\n            activeIndex +\n            '\"]'\n        );\n\n        for (let i = 0; i < songContainers.length; i++) {\n          if (!songContainers[i].hasAttribute(\"data-amplitude-playlist\")) {\n            songContainers[i].classList.add(\"amplitude-active-song-container\");\n          }\n        }\n      }\n    } else {\n      if( config.active_playlist != null && config.active_playlist != '' ){\n        var activePlaylistIndex = config.playlists[ config.active_playlist ].active_index;\n      }else{\n        var activePlaylistIndex = '';\n\n        if( config.playlists[ config.active_playlist ].shuffle ){\n          activePlaylistIndex = config.playlists[ config.active_playlist ].shuffle_list[ config.playlists[config.active_playlist].active_index ].index;\n        }else{\n          activePlaylistIndex = config.playlists[ config.active_playlist ].active_index;\n        }\n      }\n\n      if (\n        document.querySelectorAll(\n          '.amplitude-song-container[data-amplitude-song-index=\"' +\n            activePlaylistIndex +\n            '\"][data-amplitude-playlist=\"' +\n            config.active_playlist +\n            '\"]'\n        )\n      ) {\n        let songContainers = document.querySelectorAll(\n          '.amplitude-song-container[data-amplitude-song-index=\"' +\n            activePlaylistIndex +\n            '\"][data-amplitude-playlist=\"' +\n            config.active_playlist +\n            '\"]'\n        );\n\n        for (let i = 0; i < songContainers.length; i++) {\n          songContainers[i].classList.add(\"amplitude-active-song-container\");\n        }\n      }\n    }\n  }\n\n  return {\n    setActive: setActive\n  };\n})();\n\nexport default ContainerElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/containerElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../../config.js\";\n\n/**\n * Handles all of the current time hour elements.\n *\n * @module visual/time/CurrentHourElements\n */\nlet CurrentHourElements = (function() {\n  function sync(hours) {\n    syncGlobal(hours);\n    syncPlaylist(hours);\n    syncSong(hours);\n    syncSongInPlaylist(hours);\n  }\n\n  /**\n   * Updates any elements that display the current hour for the song.\n   *\n   * @access public\n   * @param {number} hours \t- An integer conaining how many hours into the song.\n   */\n  function syncGlobal(hours) {\n    /*\n\t\t\tGet all of the hour selectors\n\t\t*/\n    const currentHourSelectors = document.querySelectorAll(\n      \".amplitude-current-hours\"\n    );\n\n    /*\n\t\t\tSet the current hour selector's inner html to hours passed in.\n\t\t*/\n    for (let i = 0; i < currentHourSelectors.length; i++) {\n      let playlist = currentHourSelectors[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n      let songIndex = currentHourSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      /*\n        Updates the current hour selector for a global display.\n      */\n      if (playlist == null && songIndex == null) {\n        currentHourSelectors[i].innerHTML = hours;\n      }\n    }\n  }\n\n  /**\n   * Syncs the playlist current hour elements.\n   *\n   * @param {Integer} hour - The current audio hour.\n   */\n  function syncPlaylist(hours) {\n    /*\n\t\t\tGet all of the hour selectors\n\t\t*/\n    const currentHourPlaylistSelectors = document.querySelectorAll(\n      '.amplitude-current-hours[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"]'\n    );\n\n    /*\n\t\t\tSet the current hour selector's inner html to hours passed in.\n\t\t*/\n    for (let i = 0; i < currentHourPlaylistSelectors.length; i++) {\n      let songIndex = currentHourPlaylistSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      /*\n        Updates the current hour selector for a global display.\n      */\n      if (songIndex == null) {\n        currentHourPlaylistSelectors[i].innerHTML = hours;\n      }\n    }\n  }\n\n  /**\n   * Syncs the song hour elements.\n   *\n   * @param {Integer} hour - The current audio hour.\n   */\n  function syncSong(hours) {\n    if (config.active_playlist == null) {\n      /*\n  \t\t\tGet all of the hour selectors\n  \t\t*/\n      const currentHourSongSelectors = document.querySelectorAll(\n        '.amplitude-current-hours[data-amplitude-song-index=\"' +\n          config.active_index +\n          '\"]'\n      );\n\n      /*\n  \t\t\tSet the current hour selector's inner html to hours passed in.\n  \t\t*/\n      for (let i = 0; i < currentHourSongSelectors.length; i++) {\n        let playlist = currentHourSongSelectors[i].getAttribute(\n          \"data-amplitude-playlist\"\n        );\n\n        /*\n          Updates the current hour selector for a global display.\n        */\n        if (playlist == null) {\n          currentHourSongSelectors[i].innerHTML = hours;\n        }\n      }\n    }\n  }\n\n  /**\n   * Syncs the song in playlist song hour elements.\n   *\n   * @param {Integer} hour - The current audio hour.\n   */\n  function syncSongInPlaylist(hours) {\n    let activePlaylistIndex =\n      config.active_playlist != \"\" && config.active_playlist != null\n        ? config.playlists[config.active_playlist].active_index\n        : null;\n    /*\n\t\t\tGet all of the hour selectors\n\t\t*/\n    const currentHourPlaylistSongSelectors = document.querySelectorAll(\n      '.amplitude-current-hours[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"][data-amplitude-song-index=\"' +\n        activePlaylistIndex +\n        '\"]'\n    );\n\n    /*\n\t\t\tSet the current hour selector's inner html to hours passed in.\n\t\t*/\n    for (let i = 0; i < currentHourPlaylistSongSelectors.length; i++) {\n      currentHourPlaylistSongSelectors[i].innerHTML = hours;\n    }\n  }\n\n  /**\n   * Reset the current hour elements.\n   */\n  function resetTimes() {\n    /*\n      Gets the hour display elements\n    */\n    let hourSelectors = document.querySelectorAll(\".amplitude-current-hours\");\n\n    /*\n      Iterates over all of the hour selectors and sets the inner HTML\n      to 00.\n    */\n    for (var i = 0; i < hourSelectors.length; i++) {\n      hourSelectors[i].innerHTML = \"00\";\n    }\n  }\n\n  /**\n   * Returns the publically facing methods.\n   */\n  return {\n    sync: sync,\n    resetTimes: resetTimes\n  };\n})();\n\nexport default CurrentHourElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/time/currentHourElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../../config.js\";\n\n/**\n * Handles all of the current time minutes elements.\n *\n * @module visual/time/CurrentMinuteElements\n */\nlet CurrentMinuteElements = (function() {\n  /**\n   * Syncs the current minutes elements.\n   *\n   * @param {Integer} minutes - The current audio minutes.\n   */\n  function sync(minutes) {\n    syncGlobal(minutes);\n    syncPlaylist(minutes);\n    syncSong(minutes);\n    syncSongInPlaylist(minutes);\n  }\n\n  /**\n   * Syncs the global current minutes elements.\n   *\n   * @param {Integer} minutes - The current audio minutes.\n   */\n  function syncGlobal(minutes) {\n    /*\n\t\t\tGet all of the minute selectors\n\t\t*/\n    const currentMinuteSelectors = document.querySelectorAll(\n      \".amplitude-current-minutes\"\n    );\n\n    /*\n\t\t\tSet the current minute selector's inner html to minutes passed in.\n\t\t*/\n    for (let i = 0; i < currentMinuteSelectors.length; i++) {\n      let playlist = currentMinuteSelectors[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n      let songIndex = currentMinuteSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      /*\n        Updates the current minute selector for a global display.\n      */\n      if (playlist == null && songIndex == null) {\n        currentMinuteSelectors[i].innerHTML = minutes;\n      }\n    }\n  }\n\n  /**\n   * Syncs the playlist minutes elements.\n   *\n   * @param {Integer} minutes - The current audio minutes.\n   */\n  function syncPlaylist(minutes) {\n    /*\n\t\t\tGet all of the minute selectors\n\t\t*/\n    const currentMinutePlaylistSelectors = document.querySelectorAll(\n      '.amplitude-current-minutes[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"]'\n    );\n\n    /*\n\t\t\tSet the current minute selector's inner html to minutes passed in.\n\t\t*/\n    for (let i = 0; i < currentMinutePlaylistSelectors.length; i++) {\n      let songIndex = currentMinutePlaylistSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      /*\n        Updates the current minute selector for a global display.\n      */\n      if (songIndex == null) {\n        currentMinutePlaylistSelectors[i].innerHTML = minutes;\n      }\n    }\n  }\n\n  /**\n   * Syncs the current song minutes elements.\n   *\n   * @param {Integer} minutes - The current audio minutes.\n   */\n  function syncSong(minutes) {\n    if (config.active_playlist == null) {\n      /*\n  \t\t\tGet all of the minute selectors\n  \t\t*/\n      const currentMinuteSongSelectors = document.querySelectorAll(\n        '.amplitude-current-minutes[data-amplitude-song-index=\"' +\n          config.active_index +\n          '\"]'\n      );\n\n      /*\n  \t\t\tSet the current minute selector's inner html to minutes passed in.\n  \t\t*/\n      for (let i = 0; i < currentMinuteSongSelectors.length; i++) {\n        let playlist = currentMinuteSongSelectors[i].getAttribute(\n          \"data-amplitude-playlist\"\n        );\n\n        /*\n          Updates the current minute selector for a global display.\n        */\n        if (playlist == null) {\n          currentMinuteSongSelectors[i].innerHTML = minutes;\n        }\n      }\n    }\n  }\n\n  /**\n   * Syncs the current song in playlist minutes elements.\n   *\n   * @param {Integer} minutes - The current audio minutes.\n   */\n  function syncSongInPlaylist(minutes) {\n    let activePlaylistIndex =\n      config.active_playlist != \"\" && config.active_playlist != null\n        ? config.playlists[config.active_playlist].active_index\n        : null;\n\n    /*\n\t\t\tGet all of the minute selectors\n\t\t*/\n    const currentMinutePlaylistSongSelectors = document.querySelectorAll(\n      '.amplitude-current-minutes[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"][data-amplitude-song-index=\"' +\n        activePlaylistIndex +\n        '\"]'\n    );\n\n    /*\n\t\t\tSet the current minute selector's inner html to minutes passed in.\n\t\t*/\n    for (let i = 0; i < currentMinutePlaylistSongSelectors.length; i++) {\n      currentMinutePlaylistSongSelectors[i].innerHTML = minutes;\n    }\n  }\n\n  /**\n   * Reset the current times.\n   */\n  function resetTimes() {\n    /*\n      Gets the minute display elements\n    */\n    let minuteSelectors = document.querySelectorAll(\n      \".amplitude-current-minutes\"\n    );\n\n    /*\n      Iterates over all of the minute selectors and sets the inner HTML\n      to 00.\n    */\n    for (var i = 0; i < minuteSelectors.length; i++) {\n      minuteSelectors[i].innerHTML = \"00\";\n    }\n  }\n\n  /**\n   * Returns the publically facing methods.\n   */\n  return {\n    sync: sync,\n    resetTimes: resetTimes\n  };\n})();\n\nexport default CurrentMinuteElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/time/currentMinuteElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../../config.js\";\n\n/**\n * Handles all of the current time seconds elements.\n *\n * @module visual/time/CurrentSecondElements\n */\nlet CurrentSecondElements = (function() {\n  /**\n   * Syncs the current seconds elements.\n   *\n   * @param {Integer} seconds - The current audio seconds.\n   */\n  function sync(seconds) {\n    syncGlobal(seconds);\n    syncPlaylist(seconds);\n    syncSong(seconds);\n    syncSongInPlaylist(seconds);\n  }\n\n  /**\n   * Syncs the global current seconds elements.\n   *\n   * @param {Integer} seconds - The current audio seconds.\n   */\n  function syncGlobal(seconds) {\n    /*\n\t\t\tGet all of the second selectors\n\t\t*/\n    const currentSecondSelectors = document.querySelectorAll(\n      \".amplitude-current-seconds\"\n    );\n\n    /*\n\t\t\tSet the current second selector's inner html to seconds passed in.\n\t\t*/\n    for (let i = 0; i < currentSecondSelectors.length; i++) {\n      let playlist = currentSecondSelectors[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n      let songIndex = currentSecondSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      /*\n        Updates the current second selector for a global display.\n      */\n      if (playlist == null && songIndex == null) {\n        currentSecondSelectors[i].innerHTML = seconds;\n      }\n    }\n  }\n\n  /**\n   * Syncs the playlist seconds elements.\n   *\n   * @param {Integer} seconds - The current audio seconds.\n   */\n  function syncPlaylist(seconds) {\n    /*\n\t\t\tGet all of the second selectors\n\t\t*/\n    const currentSecondPlaylistSelectors = document.querySelectorAll(\n      '.amplitude-current-seconds[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"]'\n    );\n\n    /*\n\t\t\tSet the current second selector's inner html to seconds passed in.\n\t\t*/\n    for (let i = 0; i < currentSecondPlaylistSelectors.length; i++) {\n      let songIndex = currentSecondPlaylistSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      /*\n        Updates the current second selector for a global display.\n      */\n      if (songIndex == null) {\n        currentSecondPlaylistSelectors[i].innerHTML = seconds;\n      }\n    }\n  }\n\n  /**\n   * Syncs the current song seconds elements.\n   *\n   * @param {Integer} seconds - The current audio seconds.\n   */\n  function syncSong(seconds) {\n    if (config.active_playlist == null) {\n      /*\n  \t\t\tGet all of the second selectors\n  \t\t*/\n      const currentSecondSongSelectors = document.querySelectorAll(\n        '.amplitude-current-seconds[data-amplitude-song-index=\"' +\n          config.active_index +\n          '\"]'\n      );\n\n      /*\n  \t\t\tSet the current second selector's inner html to seconds passed in.\n  \t\t*/\n      for (let i = 0; i < currentSecondSongSelectors.length; i++) {\n        let playlist = currentSecondSongSelectors[i].getAttribute(\n          \"data-amplitude-playlist\"\n        );\n\n        /*\n          Updates the current second selector for a global display.\n        */\n        if (playlist == null) {\n          currentSecondSongSelectors[i].innerHTML = seconds;\n        }\n      }\n    }\n  }\n\n  /**\n   * Syncs the current song in playlist seconds elements.\n   *\n   * @param {Integer} seconds - The current audio seconds.\n   */\n  function syncSongInPlaylist(seconds) {\n    let activePlaylistIndex =\n      config.active_playlist != \"\" && config.active_playlist != null\n        ? config.playlists[config.active_playlist].active_index\n        : null;\n    /*\n\t\t\tGet all of the second selectors\n\t\t*/\n    const currentSecondPlaylistSongSelectors = document.querySelectorAll(\n      '.amplitude-current-seconds[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"][data-amplitude-song-index=\"' +\n        activePlaylistIndex +\n        '\"]'\n    );\n\n    /*\n\t\t\tSet the current second selector's inner html to seconds passed in.\n\t\t*/\n    for (let i = 0; i < currentSecondPlaylistSongSelectors.length; i++) {\n      currentSecondPlaylistSongSelectors[i].innerHTML = seconds;\n    }\n  }\n\n  /**\n   * Reset the current seconds elements.\n   */\n  function resetTimes() {\n    /*\n      Gets the second display elements\n    */\n    let secondSelectors = document.querySelectorAll(\n      \".amplitude-current-seconds\"\n    );\n\n    /*\n      Iterates over all of the second selectors and sets the inner HTML\n      to 00.\n    */\n    for (var i = 0; i < secondSelectors.length; i++) {\n      secondSelectors[i].innerHTML = \"00\";\n    }\n  }\n\n  /**\n   * Returns the publically facing methods.\n   */\n  return {\n    sync: sync,\n    resetTimes: resetTimes\n  };\n})();\n\nexport default CurrentSecondElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/time/currentSecondElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../../config.js\";\n\n/**\n * These methods help sync visual time elements.\n *\n * @module visual/CurrentTimeElements\n */\nlet CurrentTimeElements = (function() {\n  /**\n   * Visually displays the current time on the screen. This is called on\n   * time update for the current song.\n   *\n   * @access public\n   * @param {object} currentTime \t\t\t\t\t- An object containing the current time for the song in seconds, minutes, and hours.\n   */\n  function sync(currentTime) {\n    /*\n\t\t\tSet current time display.\n\t\t*/\n    syncGlobal(currentTime);\n    syncPlaylist(currentTime);\n    syncSong(currentTime);\n    syncSongInPlaylist(currentTime);\n  }\n\n  /**\n   * Updates any elements that display the current time for the song. This\n   * is a computed field that will be commonly used.\n   *\n   * @access public\n   * @param {object} time \t- A json object conaining the parts for the current time for the song.\n   */\n  function syncGlobal(time) {\n    /*\n\t\t\tGet all of the time selectors.\n\t\t*/\n    let currentTimeSelectors = document.querySelectorAll(\n      \".amplitude-current-time\"\n    );\n\n    /*\n\t\t\tSet the time selector's inner html to the current time for the song. The current\n\t\t\ttime is computed by joining minutes and seconds.\n\t\t*/\n    var timeText = time.minutes + \":\" + time.seconds;\n\n    if (time.hours > 0) {\n      timeText = time.hours + \":\" + timeText;\n    }\n\n    for (let i = 0; i < currentTimeSelectors.length; i++) {\n      let playlist = currentTimeSelectors[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n      let songIndex = currentTimeSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      if (playlist == null && songIndex == null) {\n        currentTimeSelectors[i].innerHTML = timeText;\n      }\n    }\n  }\n\n  /**\n   * Updates any elements that display the current time for the song. This\n   * is a computed field that will be commonly used.\n   *\n   * @access public\n   * @param {object} time \t- A json object conaining the parts for the current time for the song.\n   */\n  function syncPlaylist(time) {\n    /*\n\t\t\tGet all of the time selectors.\n\t\t*/\n    let currentTimeSelectors = document.querySelectorAll(\n      '.amplitude-current-time[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"]'\n    );\n\n    /*\n\t\t\tSet the time selector's inner html to the current time for the song. The current\n\t\t\ttime is computed by joining minutes and seconds.\n\t\t*/\n    var timeText = time.minutes + \":\" + time.seconds;\n\n    if (time.hours > 0) {\n      timeText = time.hours + \":\" + timeText;\n    }\n\n    for (let i = 0; i < currentTimeSelectors.length; i++) {\n      let songIndex = currentTimeSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      if (songIndex == null) {\n        currentTimeSelectors[i].innerHTML = timeText;\n      }\n    }\n  }\n\n  /**\n   * Updates any elements that display the current time for the song. This\n   * is a computed field that will be commonly used.\n   *\n   * @access public\n   * @param {object} time \t- A json object conaining the parts for the current time for the song.\n   */\n  function syncSong(time) {\n    if (config.active_playlist == null) {\n      /*\n  \t\t\tGet all of the time selectors.\n  \t\t*/\n      let currentTimeSelectors = document.querySelectorAll(\n        '.amplitude-current-time[data-amplitude-song-index=\"' +\n          config.active_index +\n          '\"]'\n      );\n\n      /*\n  \t\t\tSet the time selector's inner html to the current time for the song. The current\n  \t\t\ttime is computed by joining minutes and seconds.\n  \t\t*/\n      var timeText = time.minutes + \":\" + time.seconds;\n\n      if (time.hours > 0) {\n        timeText = time.hours + \":\" + timeText;\n      }\n\n      for (let i = 0; i < currentTimeSelectors.length; i++) {\n        let playlist = currentTimeSelectors[i].getAttribute(\n          \"data-amplitude-playlist\"\n        );\n\n        if (playlist == null) {\n          currentTimeSelectors[i].innerHTML = timeText;\n        }\n      }\n    }\n  }\n\n  /**\n   * Updates any elements that display the current time for the song. This\n   * is a computed field that will be commonly used.\n   *\n   * @access public\n   * @param {object} time \t- A json object conaining the parts for the current time for the song.\n   */\n  function syncSongInPlaylist(time) {\n    let activePlaylistIndex =\n      config.active_playlist != \"\" && config.active_playlist != null\n        ? config.playlists[config.active_playlist].active_index\n        : null;\n    /*\n\t\t\tGet all of the time selectors.\n\t\t*/\n    let currentTimeSelectors = document.querySelectorAll(\n      '.amplitude-current-time[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"][data-amplitude-song-index=\"' +\n        activePlaylistIndex +\n        '\"]'\n    );\n\n    /*\n\t\t\tSet the time selector's inner html to the current time for the song. The current\n\t\t\ttime is computed by joining minutes and seconds.\n\t\t*/\n    var timeText = time.minutes + \":\" + time.seconds;\n\n    if (time.hours > 0) {\n      timeText = time.hours + \":\" + timeText;\n    }\n\n    for (let i = 0; i < currentTimeSelectors.length; i++) {\n      currentTimeSelectors[i].innerHTML = timeText;\n    }\n  }\n\n  /**\n   * Resets the current time displays to 00:00\n   *\n   * @access public\n   */\n  function resetTimes() {\n    /*\n\t\t\tGets the time selector display elements\n\t\t*/\n    let timeSelectors = document.querySelectorAll(\".amplitude-current-time\");\n\n    /*\n\t\t\tIterates over all of the time selectors and sets the inner HTML\n\t\t\tto 00.\n\t\t*/\n    for (let i = 0; i < timeSelectors.length; i++) {\n      timeSelectors[i].innerHTML = \"00:00\";\n    }\n  }\n\n  /**\n   * Returns the publically facing methods\n   */\n  return {\n    sync: sync,\n    resetTimes: resetTimes\n  };\n})();\n\nexport default CurrentTimeElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/time/currentTimeElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../../config.js\";\n\n/**\n * Handles all of the duration countdown elements.\n *\n * @module visual/time/DurationCountDownTimeElements.\n */\nlet DurationCountDownTimeElements = (function() {\n  /**\n   * Syncs all of the countdown time elements.\n   *\n   * @param {object} countDownTime - The current time of the audio.\n   * @param {object} songDuration - The song duration of the audio.\n   */\n  function sync(countDownTime, songDuration) {\n    let timeRemaining = computeTimeRemaining(countDownTime, songDuration);\n\n    syncGlobal(timeRemaining);\n    syncPlaylist(timeRemaining);\n    syncSong(timeRemaining);\n    syncSongInPlaylist(timeRemaining);\n  }\n\n  /**\n   * Syncs the global count down time elements.\n   *\n   * @param {string} timeRemaining - The time remaining for the audio.\n   */\n  function syncGlobal(timeRemaining) {\n    let durationTimeRemainingSelectors = document.querySelectorAll(\n      \".amplitude-time-remaining\"\n    );\n\n    for (let i = 0; i < durationTimeRemainingSelectors.length; i++) {\n      let playlist = durationTimeRemainingSelectors[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n      let songIndex = durationTimeRemainingSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      if (playlist == null && songIndex == null) {\n        durationTimeRemainingSelectors[i].innerHTML = timeRemaining;\n      }\n    }\n  }\n\n  /**\n   * Syncs the playlist count down time elements.\n   *\n   * @param {string} timeRemaining - The time remaining for the audio.\n   */\n  function syncPlaylist(timeRemaining) {\n    let durationTimeRemainingSelectors = document.querySelectorAll(\n      '.amplitude-time-remaining[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"]'\n    );\n\n    for (let i = 0; i < durationTimeRemainingSelectors.length; i++) {\n      let songIndex = durationTimeRemainingSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      if (songIndex == null) {\n        durationTimeRemainingSelectors[i].innerHTML = timeRemaining;\n      }\n    }\n  }\n\n  /**\n   * Syncs the song count down time elements.\n   *\n   * @param {string} timeRemaining - The time remaining for the audio.\n   */\n  function syncSong(timeRemaining) {\n    if (config.active_playlist == null) {\n      let durationTimeRemainingSelectors = document.querySelectorAll(\n        '.amplitude-time-remaining[data-amplitude-song-index=\"' +\n          config.active_index +\n          '\"]'\n      );\n\n      for (let i = 0; i < durationTimeRemainingSelectors.length; i++) {\n        let playlist = durationTimeRemainingSelectors[i].getAttribute(\n          \"data-amplitude-playlist\"\n        );\n\n        if (playlist == null) {\n          durationTimeRemainingSelectors[i].innerHTML = timeRemaining;\n        }\n      }\n    }\n  }\n\n  /**\n   * Syncs the song in playlist count down time elements.\n   *\n   * @param {string} timeRemaining - The time remaining for the audio.\n   */\n  function syncSongInPlaylist(timeRemaining) {\n    let activePlaylistIndex =\n      config.active_playlist != \"\" && config.active_playlist != null\n        ? config.playlists[config.active_playlist].active_index\n        : null;\n\n    let durationTimeRemainingSelectors = document.querySelectorAll(\n      '.amplitude-time-remaining[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"][data-amplitude-song-index=\"' +\n        activePlaylistIndex +\n        '\"]'\n    );\n\n    for (let i = 0; i < durationTimeRemainingSelectors.length; i++) {\n      durationTimeRemainingSelectors[i].innerHTML = timeRemaining;\n    }\n  }\n\n  /**\n   * Resets the count down times.\n   */\n  function resetTimes() {\n    let durationTimeRemainingSelectors = document.querySelectorAll(\n      \".amplitude-time-remaining\"\n    );\n\n    for (let i = 0; i < durationTimeRemainingSelectors.length; i++) {\n      durationTimeRemainingSelectors[i].innerHTML = \"00\";\n    }\n  }\n\n  /**\n   * Computes the time remaining for the audio.\n   *\n   * @param {object} currentTime - The current time of the audio.\n   * @param {object} songDuration - The duration of the audio.\n   */\n  function computeTimeRemaining(currentTime, songDuration) {\n    let timeRemaining = \"00:00\";\n\n    /*\n      Initialize the total current seconds and total duration seconds\n    */\n    let totalCurrentSeconds =\n      parseInt(currentTime.seconds) +\n      parseInt(currentTime.minutes) * 60 +\n      parseInt(currentTime.hours) * 60 * 60;\n    let totalDurationSeconds =\n      parseInt(songDuration.seconds) +\n      parseInt(songDuration.minutes) * 60 +\n      parseInt(songDuration.hours) * 60 * 60;\n\n    /*\n      If the two variables are numbers we continue the computing.\n    */\n    if (!isNaN(totalCurrentSeconds) && !isNaN(totalDurationSeconds)) {\n      /*\n        Find the total remaining seconds.\n      */\n      let timeRemainingTotalSeconds =\n        totalDurationSeconds - totalCurrentSeconds;\n\n      var remainingHours = Math.floor(timeRemainingTotalSeconds / 3600);\n      var remainingMinutes = Math.floor(\n        (timeRemainingTotalSeconds - remainingHours * 3600) / 60\n      );\n      var remainingSeconds =\n        timeRemainingTotalSeconds -\n        remainingHours * 3600 -\n        remainingMinutes * 60;\n\n      timeRemaining =\n        (remainingMinutes < 10 ? \"0\" + remainingMinutes : remainingMinutes) +\n        \":\" +\n        (remainingSeconds < 10 ? \"0\" + remainingSeconds : remainingSeconds);\n\n      if (remainingHours > 0) {\n        timeRemaining = remainingHours + \":\" + timeRemaining;\n      }\n    }\n\n    return timeRemaining;\n  }\n\n  /**\n   * Returns the publically facing methods.\n   */\n  return {\n    sync: sync,\n    resetTimes: resetTimes\n  };\n})();\n\nexport default DurationCountDownTimeElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/time/durationCountDownTimeElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../../config.js\";\n\n/**\n * Handles all of the duration hours elements.\n *\n * @module visual/time/DurationHourElements.\n */\nlet DurationHourElements = (function() {\n  /**\n   * Sync the duration hours elements.\n   *\n   * @param {Integer} hours - The duration hours for the audio.\n   */\n  function sync(hours) {\n    syncGlobal(hours);\n    syncPlaylist(hours);\n    syncSong(hours);\n    syncSongInPlaylist(hours);\n  }\n\n  /**\n   * Syncs the global duration hours elements.\n   *\n   * @param {Integer} hours - the duration hours for the audio.\n   */\n  function syncGlobal(hours) {\n    let durationHourSelectors = document.querySelectorAll(\n      \".amplitude-duration-hours\"\n    );\n\n    for (let i = 0; i < durationHourSelectors.length; i++) {\n      let playlist = durationHourSelectors[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n      let songIndex = durationHourSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      if (playlist == null && songIndex == null) {\n        durationHourSelectors[i].innerHTML = hours;\n      }\n    }\n  }\n\n  /**\n   * Syncs the playlist duration hours for the audio.\n   *\n   * @param {Integer} hours - The duration hours for the audio.\n   */\n  function syncPlaylist(hours) {\n    let durationHourSelectors = document.querySelectorAll(\n      '.amplitude-duration-hours[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"]'\n    );\n\n    for (let i = 0; i < durationHourSelectors.length; i++) {\n      let songIndex = durationHourSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      if (songIndex == null) {\n        durationHourSelectors[i].innerHTML = hours;\n      }\n    }\n  }\n\n  /**\n   * Syncs the song duration hours.\n   *\n   * @param {Integer} hours - The duration hours for the audio.\n   */\n  function syncSong(hours) {\n    if (config.active_playlist == null) {\n      let durationHourSelectors = document.querySelectorAll(\n        '.amplitude-duration-hours[data-amplitude-song-index=\"' +\n          config.active_index +\n          '\"]'\n      );\n\n      for (let i = 0; i < durationHourSelectors.length; i++) {\n        let playlist = durationHourSelectors[i].getAttribute(\n          \"data-amplitude-playlist\"\n        );\n\n        if (playlist == null) {\n          durationHourSelectors[i].innerHTML = hours;\n        }\n      }\n    }\n  }\n\n  /**\n   * Syncs the song in playlist duration hours.\n   *\n   * @param {Integer} hours - The duration hours of the audio.\n   */\n  function syncSongInPlaylist(hours) {\n    let activePlaylistIndex =\n      config.active_playlist != \"\" && config.active_playlist != null\n        ? config.playlists[config.active_playlist].active_index\n        : null;\n\n    let durationHourSelectors = document.querySelectorAll(\n      '.amplitude-duration-hours[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"][data-amplitude-song-index=\"' +\n        activePlaylistIndex +\n        '\"]'\n    );\n\n    for (let i = 0; i < durationHourSelectors.length; i++) {\n      durationHourSelectors[i].innerHTML = hours;\n    }\n  }\n\n  /**\n   * Resets the duration shours elements to '00'\n   */\n  function resetTimes() {\n    let durationHourSelectors = document.querySelectorAll(\n      \".amplitude-duration-hours\"\n    );\n\n    for (let i = 0; i < durationHourSelectors.length; i++) {\n      durationHourSelectors[i].innerHTML = \"00\";\n    }\n  }\n\n  /**\n   * Returns the publically facing methods.\n   */\n  return {\n    sync: sync,\n    resetTimes: resetTimes\n  };\n})();\n\nexport default DurationHourElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/time/durationHourElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../../config.js\";\n\n/**\n * Handles all of the duration minutes elements.\n *\n * @module visual/time/DurationMinuteElements.\n */\nlet DurationMinuteElements = (function() {\n  /**\n   * Sync the duration minutes elements.\n   *\n   * @param {Integer} minutes - The duration minutes for the audio.\n   */\n  function sync(minutes) {\n    syncGlobal(minutes);\n    syncPlaylist(minutes);\n    syncSong(minutes);\n    syncSongInPlaylist(minutes);\n  }\n\n  /**\n   * Syncs the global duration minutes elements.\n   *\n   * @param {Integer} minutes - the duration minutes for the audio.\n   */\n  function syncGlobal(minutes) {\n    let durationMinuteSelectors = document.querySelectorAll(\n      \".amplitude-duration-minutes\"\n    );\n\n    for (let i = 0; i < durationMinuteSelectors.length; i++) {\n      let playlist = durationMinuteSelectors[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n      let songIndex = durationMinuteSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      if (playlist == null && songIndex == null) {\n        durationMinuteSelectors[i].innerHTML = minutes;\n      }\n    }\n  }\n\n  /**\n   * Syncs the playlist duration minutes for the audio.\n   *\n   * @param {Integer} minutes - The duration minutes for the audio.\n   */\n  function syncPlaylist(minutes) {\n    let durationMinuteSelectors = document.querySelectorAll(\n      '.amplitude-duration-minutes[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"]'\n    );\n\n    for (let i = 0; i < durationMinuteSelectors.length; i++) {\n      let songIndex = durationMinuteSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      if (songIndex == null) {\n        durationMinuteSelectors[i].innerHTML = minutes;\n      }\n    }\n  }\n\n  /**\n   * Syncs the song duration minutes.\n   *\n   * @param {Integer} minutes - The duration minutes for the audio.\n   */\n  function syncSong(minutes) {\n    if (config.active_playlist == null) {\n      let durationMinuteSelectors = document.querySelectorAll(\n        '.amplitude-duration-minutes[data-amplitude-song-index=\"' +\n          config.active_index +\n          '\"]'\n      );\n\n      for (let i = 0; i < durationMinuteSelectors.length; i++) {\n        let playlist = durationMinuteSelectors[i].getAttribute(\n          \"data-amplitude-playlist\"\n        );\n\n        if (playlist == null) {\n          durationMinuteSelectors[i].innerHTML = minutes;\n        }\n      }\n    }\n  }\n\n  /**\n   * Syncs the song in playlist duration minutes.\n   *\n   * @param {Integer} minutes - The duration minutes of the audio.\n   */\n  function syncSongInPlaylist(minutes) {\n    let activePlaylistIndex =\n      config.active_playlist != \"\" && config.active_playlist != null\n        ? config.playlists[config.active_playlist].active_index\n        : null;\n\n    let durationMinuteSelectors = document.querySelectorAll(\n      '.amplitude-duration-minutes[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"][data-amplitude-song-index=\"' +\n        activePlaylistIndex +\n        '\"]'\n    );\n\n    for (let i = 0; i < durationMinuteSelectors.length; i++) {\n      durationMinuteSelectors[i].innerHTML = minutes;\n    }\n  }\n\n  /**\n   * Resets the duration minutes elements to '00'\n   */\n  function resetTimes() {\n    let durationMinuteSelectors = document.querySelectorAll(\n      \".amplitude-duration-minutes\"\n    );\n\n    for (let i = 0; i < durationMinuteSelectors.length; i++) {\n      durationMinuteSelectors[i].innerHTML = \"00\";\n    }\n  }\n\n  /**\n   * Returns the publically facing methods.\n   */\n  return {\n    sync: sync,\n    resetTimes: resetTimes\n  };\n})();\n\nexport default DurationMinuteElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/time/durationMinuteElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../../config.js\";\n\n/**\n * Handles all of the duration seconds elements.\n *\n * @module visual/time/DurationSecondElements.\n */\nlet DurationSecondElements = (function() {\n  /**\n   * Sync the duration seconds elements.\n   *\n   * @param {Integer} seconds - The duration seconds for the audio.\n   */\n  function sync(seconds) {\n    syncGlobal(seconds);\n    syncPlaylist(seconds);\n    syncSong(seconds);\n    syncSongInPlaylist(seconds);\n  }\n\n  /**\n   * Syncs the global duration seconds elements.\n   *\n   * @param {Integer} seconds - the duration seconds for the audio.\n   */\n  function syncGlobal(seconds) {\n    let durationSecondSelectors = document.querySelectorAll(\n      \".amplitude-duration-seconds\"\n    );\n\n    for (let i = 0; i < durationSecondSelectors.length; i++) {\n      let playlist = durationSecondSelectors[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n      let songIndex = durationSecondSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      if (playlist == null && songIndex == null) {\n        durationSecondSelectors[i].innerHTML = seconds;\n      }\n    }\n  }\n\n  /**\n   * Syncs the playlist duration seconds for the audio.\n   *\n   * @param {Integer} seconds - The duration seconds for the audio.\n   */\n  function syncPlaylist(seconds) {\n    let durationSecondSelectors = document.querySelectorAll(\n      '.amplitude-duration-seconds[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"]'\n    );\n\n    for (let i = 0; i < durationSecondSelectors.length; i++) {\n      let songIndex = durationSecondSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      if (songIndex == null) {\n        durationSecondSelectors[i].innerHTML = seconds;\n      }\n    }\n  }\n\n  /**\n   * Syncs the song duration seconds.\n   *\n   * @param {Integer} seconds - The duration seconds for the audio.\n   */\n  function syncSong(seconds) {\n    if (config.active_playlist == null) {\n      let durationSecondSelectors = document.querySelectorAll(\n        '.amplitude-duration-seconds[data-amplitude-song-index=\"' +\n          config.active_index +\n          '\"]'\n      );\n\n      for (let i = 0; i < durationSecondSelectors.length; i++) {\n        let playlist = durationSecondSelectors[i].getAttribute(\n          \"data--amplitude-playlist\"\n        );\n\n        if (playlist == null) {\n          durationSecondSelectors[i].innerHTML = seconds;\n        }\n      }\n    }\n  }\n\n  /**\n   * Syncs the song in playlist duration seconds.\n   *\n   * @param {Integer} seconds - The duration seconds of the audio.\n   */\n  function syncSongInPlaylist(seconds) {\n    let activePlaylistIndex =\n      config.active_playlist != \"\" && config.active_playlist != null\n        ? config.playlists[config.active_playlist].active_index\n        : null;\n\n    let durationSecondSelectors = document.querySelectorAll(\n      '.amplitude-duration-seconds[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"][data-amplitude-song-index=\"' +\n        activePlaylistIndex +\n        '\"]'\n    );\n\n    for (let i = 0; i < durationSecondSelectors.length; i++) {\n      durationSecondSelectors[i].innerHTML = seconds;\n    }\n  }\n\n  /**\n   * Resets the duration seconds elements to '00'\n   */\n  function resetTimes() {\n    let durationSecondSelectors = document.querySelectorAll(\n      \".amplitude-duration-seconds\"\n    );\n\n    for (let i = 0; i < durationSecondSelectors.length; i++) {\n      durationSecondSelectors[i].innerHTML = \"00\";\n    }\n  }\n\n  /**\n   * Returns the publically facing methods.\n   */\n  return {\n    sync: sync,\n    resetTimes: resetTimes\n  };\n})();\n\nexport default DurationSecondElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/time/durationSecondElements.js", "/**\n * Imports the config module\n * @module config\n */\nimport config from \"../../config.js\";\n\n/**\n * Handles all of the duration time elements.\n *\n * @module visual/time/DurationTimeElements.\n */\nlet DurationTimeElements = (function() {\n  /**\n   * Syncs the duration time for all elements.\n   *\n   * @param {Object} durationTime - The object containing all of the song duration times.\n   */\n  function sync(durationTime) {\n    let durationText = computeDurationText(durationTime);\n\n    syncGlobal(durationText);\n    syncPlaylist(durationText);\n    syncSong(durationText);\n    syncSongInPlaylist(durationText);\n  }\n\n  /**\n   * Sync the global song duration elements.\n   *\n   * @param {Object} durationText - The text for the song duration.\n   */\n  function syncGlobal(durationText) {\n    let durationTimeSelectors = document.querySelectorAll(\n      \".amplitude-duration-time\"\n    );\n\n    for (let i = 0; i < durationTimeSelectors.length; i++) {\n      let playlist = durationTimeSelectors[i].getAttribute(\n        \"data-amplitude-playlist\"\n      );\n      let songIndex = durationTimeSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      if (playlist == null && songIndex == null) {\n        durationTimeSelectors[i].innerHTML = durationText;\n      }\n    }\n  }\n\n  /**\n   * Sync the playlist duration times.\n   *\n   * @param {Object} durationText - The text for the song duration.\n   */\n  function syncPlaylist(durationText) {\n    let durationTimeSelectors = document.querySelectorAll(\n      '.amplitude-duration-time[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"]'\n    );\n\n    for (let i = 0; i < durationTimeSelectors.length; i++) {\n      let songIndex = durationTimeSelectors[i].getAttribute(\n        \"data-amplitude-song-index\"\n      );\n\n      if (songIndex == null) {\n        durationTimeSelectors[i].innerHTML = durationText;\n      }\n    }\n  }\n\n  /**\n   * Sync the song duration times.\n   *\n   * @param {Object} durationText - The text for the song duration.\n   */\n  function syncSong(durationText) {\n    if (config.active_playlist == null) {\n      let durationTimeSelectors = document.querySelectorAll(\n        '.amplitude-duration-time[data-amplitude-song-index=\"' +\n          config.active_index +\n          '\"]'\n      );\n\n      for (let i = 0; i < durationTimeSelectors.length; i++) {\n        let playlist = durationTimeSelectors[i].getAttribute(\n          \"data-amplitude-playlist\"\n        );\n\n        if (playlist == null) {\n          durationTimeSelectors[i].innerHTML = durationText;\n        }\n      }\n    }\n  }\n\n  /**\n   * Sync the song in playlist duration times.\n   *\n   * @param {Object} durationText - An object containing the duration text.\n   */\n  function syncSongInPlaylist(durationText) {\n    let activePlaylistIndex =\n      config.active_playlist != \"\" && config.active_playlist != null\n        ? config.playlists[config.active_playlist].active_index\n        : null;\n\n    let durationTimeSelectors = document.querySelectorAll(\n      '.amplitude-duration-time[data-amplitude-playlist=\"' +\n        config.active_playlist +\n        '\"][data-amplitude-song-index=\"' +\n        activePlaylistIndex +\n        '\"]'\n    );\n\n    for (let i = 0; i < durationTimeSelectors.length; i++) {\n      durationTimeSelectors[i].innerHTML = durationText;\n    }\n  }\n\n  /**\n   * Resets all of the duration times to empty.\n   */\n  function resetTimes() {\n    let durationTimeSelectors = document.querySelectorAll(\n      \".amplitude-duration-time\"\n    );\n\n    for (let i = 0; i < durationTimeSelectors.length; i++) {\n      durationTimeSelectors[i].innerHTML = \"00:00\";\n    }\n  }\n\n  /**\n   * Computes the duration text\n   *\n   * @param {Object} durationTime - An object containint the duration times.\n   */\n  function computeDurationText(durationTime) {\n    var durationText = \"00:00\";\n\n    if (!isNaN(durationTime.minutes) && !isNaN(durationTime.seconds)) {\n      durationText = durationTime.minutes + \":\" + durationTime.seconds;\n      if (!isNaN(durationTime.hours) && durationTime.hours > 0) {\n        durationText = durationTime.hours + \":\" + durationText;\n      }\n    }\n\n    return durationText;\n  }\n\n  /**\n   * Return publically accessible methods.\n   */\n  return {\n    sync: sync,\n    resetTimes: resetTimes\n  };\n})();\n\nexport default DurationTimeElements;\n\n\n\n// WEBPACK FOOTER //\n// ./src/visual/time/durationTimeElements.js", "module.exports = {\"name\":\"amplitudejs\",\"version\":\"5.0.2\",\"description\":\"A JavaScript library that allows you to control the design of your media controls in your webpage -- not the browser. No dependencies (jQuery not required) https://521dimensions.com/open-source/amplitudejs\",\"main\":\"dist/amplitude.js\",\"devDependencies\":{\"babel-core\":\"^6.26.3\",\"babel-loader\":\"^7.1.5\",\"babel-plugin-add-module-exports\":\"0.2.1\",\"babel-polyfill\":\"^6.26.0\",\"babel-preset-es2015\":\"^6.18.0\",\"husky\":\"^1.3.1\",\"jest\":\"^23.6.0\",\"prettier\":\"1.15.1\",\"pretty-quick\":\"^1.11.1\",\"watch\":\"^1.0.2\",\"webpack\":\"^2.7.0\"},\"directories\":{\"doc\":\"docs\"},\"files\":[\"dist\"],\"scripts\":{\"build\":\"node_modules/.bin/webpack\",\"watch\":\"watch 'node_modules/.bin/webpack' dist\",\"prettier\":\"npx pretty-quick\",\"test\":\"jest\"},\"repository\":{\"type\":\"git\",\"url\":\"git+https://github.com/521dimensions/amplitudejs.git\"},\"keywords\":[\"webaudio\",\"html5\",\"javascript\",\"audio-player\"],\"author\":\"521 Dimensions (https://521dimensions.com)\",\"license\":\"MIT\",\"bugs\":{\"url\":\"https://github.com/521dimensions/amplitudejs/issues\"},\"homepage\":\"https://github.com/521dimensions/amplitudejs#readme\"}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./package.json\n// module id = 59\n// module chunks = 0 1"], "sourceRoot": ""}