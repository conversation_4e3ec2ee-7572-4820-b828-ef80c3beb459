/**
 * TinyMCE version 6.2.0 (2022-09-08)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.ModelManager");const t=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(o=n=e,(r=String).prototype.isPrototypeOf(o)||(null===(s=n.constructor)||void 0===s?void 0:s.name)===r.name)?"string":t;var o,n,r,s})(t)===e,o=e=>t=>typeof t===e,n=e=>t=>e===t,r=t("string"),s=t("object"),l=t("array"),a=n(null),c=o("boolean"),i=n(void 0),m=e=>!(e=>null==e)(e),d=o("function"),u=o("number"),f=()=>{},g=e=>()=>e,h=e=>e,p=(e,t)=>e===t;function w(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const b=e=>t=>!e(t),v=e=>e(),y=g(!1),x=g(!0);class C{constructor(e,t){this.tag=e,this.value=t}static some(e){return new C(!0,e)}static none(){return C.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?C.some(e(this.value)):C.none()}bind(e){return this.tag?e(this.value):C.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:C.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return m(e)?C.some(e):C.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}C.singletonNone=new C(!1);const S=Array.prototype.slice,T=Array.prototype.indexOf,R=Array.prototype.push,D=(e,t)=>{return o=e,n=t,T.call(o,n)>-1;var o,n},O=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},k=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},E=(e,t)=>{const o=e.length,n=new Array(o);for(let r=0;r<o;r++){const o=e[r];n[r]=t(o,r)}return n},N=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},B=(e,t)=>{const o=[],n=[];for(let r=0,s=e.length;r<s;r++){const s=e[r];(t(s,r)?o:n).push(s)}return{pass:o,fail:n}},z=(e,t)=>{const o=[];for(let n=0,r=e.length;n<r;n++){const r=e[n];t(r,n)&&o.push(r)}return o},A=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),W=(e,t,o)=>(N(e,((e,n)=>{o=t(o,e,n)})),o),L=(e,t)=>((e,t,o)=>{for(let n=0,r=e.length;n<r;n++){const r=e[n];if(t(r,n))return C.some(r);if(o(r,n))break}return C.none()})(e,t,y),_=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return C.some(o);return C.none()},M=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);R.apply(t,e[o])}return t},j=(e,t)=>M(E(e,t)),I=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},P=(e,t)=>{const o={};for(let n=0,r=e.length;n<r;n++){const r=e[n];o[String(r)]=t(r,n)}return o},F=(e,t)=>t>=0&&t<e.length?C.some(e[t]):C.none(),H=e=>F(e,0),q=e=>F(e,e.length-1),V=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return C.none()},$=Object.keys,U=Object.hasOwnProperty,G=(e,t)=>{const o=$(e);for(let n=0,r=o.length;n<r;n++){const r=o[n];t(e[r],r)}},K=(e,t)=>Y(e,((e,o)=>({k:o,v:t(e,o)}))),Y=(e,t)=>{const o={};return G(e,((e,n)=>{const r=t(e,n);o[r.k]=r.v})),o},J=(e,t)=>{const o=[];return G(e,((e,n)=>{o.push(t(e,n))})),o},Q=e=>J(e,h),X=(e,t)=>U.call(e,t);"undefined"!=typeof window?window:Function("return this;")();const Z=e=>e.dom.nodeName.toLowerCase(),ee=e=>e.dom.nodeType,te=e=>t=>ee(t)===e,oe=e=>8===ee(e)||"#comment"===Z(e),ne=te(1),re=te(3),se=te(9),le=te(11),ae=e=>t=>ne(t)&&Z(t)===e,ce=(e,t,o)=>{if(!(r(o)||c(o)||u(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},ie=(e,t,o)=>{ce(e.dom,t,o)},me=(e,t)=>{const o=e.dom;G(t,((e,t)=>{ce(o,t,e)}))},de=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},ue=(e,t)=>C.from(de(e,t)),fe=(e,t)=>{e.dom.removeAttribute(t)},ge=e=>W(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}),he=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},pe={fromHtml:(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return he(o.childNodes[0])},fromTag:(e,t)=>{const o=(t||document).createElement(e);return he(o)},fromText:(e,t)=>{const o=(t||document).createTextNode(e);return he(o)},fromDom:he,fromPoint:(e,t,o)=>C.from(e.dom.elementFromPoint(t,o)).map(he)},we=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},be=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,ve=(e,t)=>{const o=void 0===t?document:t.dom;return be(o)?C.none():C.from(o.querySelector(e)).map(pe.fromDom)},ye=(e,t)=>e.dom===t.dom,xe=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},Ce=we,Se=e=>pe.fromDom(e.dom.ownerDocument),Te=e=>se(e)?e:Se(e),Re=e=>C.from(e.dom.parentNode).map(pe.fromDom),De=(e,t)=>{const o=d(t)?t:y;let n=e.dom;const r=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=pe.fromDom(e);if(r.push(t),!0===o(t))break;n=e}return r},Oe=e=>C.from(e.dom.previousSibling).map(pe.fromDom),ke=e=>C.from(e.dom.nextSibling).map(pe.fromDom),Ee=e=>E(e.dom.childNodes,pe.fromDom),Ne=(e,t)=>{const o=e.dom.childNodes;return C.from(o[t]).map(pe.fromDom)},Be=(e,t)=>{Re(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},ze=(e,t)=>{ke(e).fold((()=>{Re(e).each((e=>{We(e,t)}))}),(e=>{Be(e,t)}))},Ae=(e,t)=>{const o=(e=>Ne(e,0))(e);o.fold((()=>{We(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},We=(e,t)=>{e.dom.appendChild(t.dom)},Le=(e,t)=>{Be(e,t),We(t,e)},_e=(e,t)=>{N(t,((o,n)=>{const r=0===n?e:t[n-1];ze(r,o)}))},Me=(e,t)=>{N(t,(t=>{We(e,t)}))},je=e=>{e.dom.textContent="",N(Ee(e),(e=>{Ie(e)}))},Ie=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Pe=e=>{const t=Ee(e);t.length>0&&_e(e,t),Ie(e)},Fe=(e,t)=>pe.fromDom(e.dom.cloneNode(t)),He=e=>Fe(e,!1),qe=e=>Fe(e,!0),Ve=(e,t)=>{const o=pe.fromTag(t),n=ge(e);return me(o,n),o},$e=["tfoot","thead","tbody","colgroup"],Ue=(e,t,o)=>({element:e,rowspan:t,colspan:o}),Ge=(e,t,o)=>({element:e,cells:t,section:o}),Ke=(e,t,o)=>({element:e,isNew:t,isLocked:o}),Ye=(e,t,o,n)=>({element:e,cells:t,section:o,isNew:n}),Je=d(Element.prototype.attachShadow)&&d(Node.prototype.getRootNode),Qe=g(Je),Xe=Je?e=>pe.fromDom(e.dom.getRootNode()):Te,Ze=e=>pe.fromDom(e.dom.host),et=e=>{const t=re(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return(e=>{const t=Xe(e);return le(o=t)&&m(o.dom.host)?C.some(t):C.none();var o})(pe.fromDom(t)).fold((()=>o.body.contains(t)),(n=et,r=Ze,e=>n(r(e))));var n,r},tt=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return pe.fromDom(t)},ot=(e,t)=>{let o=[];return N(Ee(e),(e=>{t(e)&&(o=o.concat([e])),o=o.concat(ot(e,t))})),o},nt=(e,t,o)=>((e,o,n)=>z(De(e,n),(e=>we(e,t))))(e,0,o),rt=(e,t)=>((e,o)=>z(Ee(e),(e=>we(e,t))))(e),st=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return be(o)?[]:E(o.querySelectorAll(e),pe.fromDom)})(t,e);var lt=(e,t,o,n,r)=>e(o,n)?C.some(o):d(r)&&r(o)?C.none():t(o,n,r);const at=(e,t,o)=>{let n=e.dom;const r=d(o)?o:y;for(;n.parentNode;){n=n.parentNode;const e=pe.fromDom(n);if(t(e))return C.some(e);if(r(e))break}return C.none()},ct=(e,t,o)=>at(e,(e=>we(e,t)),o),it=(e,t)=>((e,o)=>L(e.dom.childNodes,(e=>{return o=pe.fromDom(e),we(o,t);var o})).map(pe.fromDom))(e),mt=(e,t)=>ve(t,e),dt=(e,t,o)=>lt(((e,t)=>we(e,t)),ct,e,t,o),ut=(e,t,o=p)=>e.exists((e=>o(e,t))),ft=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},gt=(e,t)=>e?C.some(t):C.none(),ht=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,pt=(e,t,o=0,n)=>{const r=e.indexOf(t,o);return-1!==r&&(!!i(n)||r+t.length<=n)},wt=(e,t)=>ht(e,t,0),bt=(e,t)=>ht(e,t,e.length-t.length),vt=(e=>t=>t.replace(e,""))(/^\s+|\s+$/g),yt=e=>e.length>0,xt=e=>void 0!==e.style&&d(e.style.getPropertyValue),Ct=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);xt(e)&&e.style.setProperty(t,o)},St=(e,t,o)=>{const n=e.dom;Ct(n,t,o)},Tt=(e,t)=>{const o=e.dom;G(t,((e,t)=>{Ct(o,t,e)}))},Rt=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||et(e)?n:Dt(o,t)},Dt=(e,t)=>xt(e)?e.style.getPropertyValue(t):"",Ot=(e,t)=>{const o=e.dom,n=Dt(o,t);return C.from(n).filter((e=>e.length>0))},kt=(e,t)=>{((e,t)=>{xt(e)&&e.style.removeProperty(t)})(e.dom,t),ut(ue(e,"style").map(vt),"")&&fe(e,"style")},Et=(e,t,o=0)=>ue(e,t).map((e=>parseInt(e,10))).getOr(o),Nt=(e,t)=>Et(e,t,1),Bt=e=>ae("col")(e)?Et(e,"span",1)>1:Nt(e,"colspan")>1,zt=e=>Nt(e,"rowspan")>1,At=(e,t)=>parseInt(Rt(e,t),10),Wt=g(10),Lt=g(10),_t=(e,t)=>Mt(e,t,x),Mt=(e,t,o)=>j(Ee(e),(e=>we(e,t)?o(e)?[e]:[]:Mt(e,t,o))),jt=(e,t)=>((e,t,o=y)=>o(t)?C.none():D(e,Z(t))?C.some(t):ct(t,e.join(","),(e=>we(e,"table")||o(e))))(["td","th"],e,t),It=e=>_t(e,"th,td"),Pt=e=>we(e,"colgroup")?rt(e,"col"):j(qt(e),(e=>rt(e,"col"))),Ft=(e,t)=>dt(e,"table",t),Ht=e=>_t(e,"tr"),qt=e=>Ft(e).fold(g([]),(e=>rt(e,"colgroup"))),Vt=(e,t)=>E(e,(e=>{if("colgroup"===Z(e)){const t=E(Pt(e),(e=>{const t=Et(e,"span",1);return Ue(e,1,t)}));return Ge(e,t,"colgroup")}{const o=E(It(e),(e=>{const t=Et(e,"rowspan",1),o=Et(e,"colspan",1);return Ue(e,t,o)}));return Ge(e,o,t(e))}})),$t=e=>Re(e).map((e=>{const t=Z(e);return(e=>D($e,e))(t)?t:"tbody"})).getOr("tbody"),Ut=e=>{const t=Ht(e),o=[...qt(e),...t];return Vt(o,$t)},Gt=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},Kt=()=>Yt(0,0),Yt=(e,t)=>({major:e,minor:t}),Jt={nu:Yt,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?Kt():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return Yt(n(1),n(2))})(e,o)},unknown:Kt},Qt=(e,t)=>{const o=String(t).toLowerCase();return L(e,(e=>e.search(o)))},Xt=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Zt=e=>t=>pt(t,e),eo=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>pt(e,"edge/")&&pt(e,"chrome")&&pt(e,"safari")&&pt(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Xt],search:e=>pt(e,"chrome")&&!pt(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>pt(e,"msie")||pt(e,"trident")},{name:"Opera",versionRegexes:[Xt,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Zt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Zt("firefox")},{name:"Safari",versionRegexes:[Xt,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(pt(e,"safari")||pt(e,"mobile/"))&&pt(e,"applewebkit")}],to=[{name:"Windows",search:Zt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>pt(e,"iphone")||pt(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Zt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:Zt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Zt("linux"),versionRegexes:[]},{name:"Solaris",search:Zt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Zt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Zt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],oo={browsers:g(eo),oses:g(to)},no="Edge",ro="Chromium",so="Opera",lo="Firefox",ao="Safari",co=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(no),isChromium:n(ro),isIE:n("IE"),isOpera:n(so),isFirefox:n(lo),isSafari:n(ao)}},io=()=>co({current:void 0,version:Jt.unknown()}),mo=co,uo=(g(no),g(ro),g("IE"),g(so),g(lo),g(ao),"Windows"),fo="Android",go="Linux",ho="macOS",po="Solaris",wo="FreeBSD",bo="ChromeOS",vo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(uo),isiOS:n("iOS"),isAndroid:n(fo),isMacOS:n(ho),isLinux:n(go),isSolaris:n(po),isFreeBSD:n(wo),isChromeOS:n(bo)}},yo=()=>vo({current:void 0,version:Jt.unknown()}),xo=vo,Co=(g(uo),g("iOS"),g(fo),g(go),g(ho),g(po),g(wo),g(bo),e=>window.matchMedia(e).matches);let So=Gt((()=>((e,t,o)=>{const n=oo.browsers(),r=oo.oses(),s=t.bind((e=>((e,t)=>V(t.brands,(t=>{const o=t.brand.toLowerCase();return L(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:Jt.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>Qt(e,t).map((e=>{const o=Jt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(io,mo),l=((e,t)=>Qt(e,t).map((e=>{const o=Jt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(r,e).fold(yo,xo),a=((e,t,o,n)=>{const r=e.isiOS()&&!0===/ipad/i.test(o),s=e.isiOS()&&!r,l=e.isiOS()||e.isAndroid(),a=l||n("(pointer:coarse)"),c=r||!s&&l&&n("(min-device-width:768px)"),i=s||l&&!c,m=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),d=!i&&!c&&!m;return{isiPad:g(r),isiPhone:g(s),isTablet:g(c),isPhone:g(i),isTouch:g(a),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:g(m),isDesktop:g(d)}})(l,s,e,o);return{browser:s,os:l,deviceType:a}})(navigator.userAgent,C.from(navigator.userAgentData),Co)));const To=()=>So(),Ro=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=Rt(o,e);return parseFloat(t)||0}return n},n=(e,t)=>W(t,((t,o)=>{const n=Rt(e,o),r=void 0===n?0:parseInt(n,10);return isNaN(r)?t:t+r}),0);return{set:(t,o)=>{if(!u(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;xt(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const r=n(e,o);return t>r?t-r:0}}},Do=(e,t,o)=>((e,t)=>(e=>{const t=parseFloat(e);return isNaN(t)?C.none():C.some(t)})(e).getOr(t))(Rt(e,t),o),Oo=Ro("width",(e=>e.dom.offsetWidth)),ko=e=>Oo.get(e),Eo=e=>Oo.getOuter(e),No=e=>((e,t)=>{const o=e.dom,n=o.getBoundingClientRect().width||o.offsetWidth;return"border-box"===t?n:((e,t,o,n)=>t-Do(e,"padding-left",0)-Do(e,"padding-right",0)-Do(e,"border-left-width",0)-Do(e,"border-right-width",0))(e,n)})(e,"content-box"),Bo=(e,t,o)=>{const n=e.cells,r=n.slice(0,t),s=n.slice(t),l=r.concat(o).concat(s);return Wo(e,l)},zo=(e,t,o)=>Bo(e,t,[o]),Ao=(e,t,o)=>{e.cells[t]=o},Wo=(e,t)=>Ye(e.element,t,e.section,e.isNew),Lo=(e,t)=>e.cells[t],_o=(e,t)=>Lo(e,t).element,Mo=e=>e.cells.length,jo=e=>{const t=B(e,(e=>"colgroup"===e.section));return{rows:t.fail,cols:t.pass}},Io=(e,t,o)=>{const n=E(e.cells,o);return Ye(t(e.element),n,e.section,!0)},Po="data-snooker-locked-cols",Fo=e=>ue(e,Po).bind((e=>C.from(e.match(/\d+/g)))).map((e=>P(e,x))),Ho=e=>{const t=W(jo(e).rows,((e,t)=>(N(t.cells,((t,o)=>{t.isLocked&&(e[o]=!0)})),e)),{}),o=J(t,((e,t)=>parseInt(t,10)));return((e,t)=>{const o=S.call(e,0);return o.sort(void 0),o})(o)},qo=(e,t)=>e+","+t,Vo=(e,t)=>{const o=j(e.all,(e=>e.cells));return z(o,t)},$o=e=>{const t={},o=[],n=H(e).map((e=>e.element)).bind(Ft).bind(Fo).getOr({});let r=0,s=0,l=0;const{pass:a,fail:c}=B(e,(e=>"colgroup"===e.section));N(c,(e=>{const a=[];N(e.cells,(e=>{let o=0;for(;void 0!==t[qo(l,o)];)o++;const r=((e,t)=>X(e,t)&&void 0!==e[t]&&null!==e[t])(n,o.toString()),c=((e,t,o,n,r,s)=>({element:e,rowspan:t,colspan:o,row:n,column:r,isLocked:s}))(e.element,e.rowspan,e.colspan,l,o,r);for(let n=0;n<e.colspan;n++)for(let r=0;r<e.rowspan;r++){const e=o+n,a=qo(l+r,e);t[a]=c,s=Math.max(s,e+1)}a.push(c)})),r++,o.push(Ge(e.element,a,e.section)),l++}));const{columns:i,colgroups:m}=q(a).map((e=>{const t=(e=>{const t={};let o=0;return N(e.cells,(e=>{const n=e.colspan;k(n,(r=>{const s=o+r;t[s]=((e,t,o)=>({element:e,colspan:t,column:o}))(e.element,n,s)})),o+=n})),t})(e),o=((e,t)=>({element:e,columns:t}))(e.element,Q(t));return{colgroups:[o],columns:t}})).getOrThunk((()=>({colgroups:[],columns:{}}))),d=((e,t)=>({rows:e,columns:t}))(r,s);return{grid:d,access:t,all:o,columns:i,colgroups:m}},Uo=e=>{const t=Ut(e);return $o(t)},Go=$o,Ko=(e,t,o)=>C.from(e.access[qo(t,o)]),Yo=(e,t,o)=>{const n=Vo(e,(e=>o(t,e.element)));return n.length>0?C.some(n[0]):C.none()},Jo=Vo,Qo=e=>j(e.all,(e=>e.cells)),Xo=e=>Q(e.columns),Zo=e=>$(e.columns).length>0,en=(e,t)=>C.from(e.columns[t]),tn=(e,t=x)=>{const o=e.grid,n=k(o.columns,h),r=k(o.rows,h);return E(n,(o=>on((()=>j(r,(t=>Ko(e,t,o).filter((e=>e.column===o)).toArray()))),(e=>1===e.colspan&&t(e.element)),(()=>Ko(e,0,o)))))},on=(e,t,o)=>{const n=e();return L(n,t).orThunk((()=>C.from(n[0]).orThunk(o))).map((e=>e.element))},nn=e=>{const t=e.grid,o=k(t.rows,h),n=k(t.columns,h);return E(o,(t=>on((()=>j(n,(o=>Ko(e,t,o).filter((e=>e.row===t)).fold(g([]),(e=>[e]))))),(e=>1===e.rowspan),(()=>Ko(e,t,0)))))},rn=(e,t)=>o=>"rtl"===sn(o)?t:e,sn=e=>"rtl"===Rt(e,"direction")?"rtl":"ltr",ln=Ro("height",(e=>{const t=e.dom;return et(e)?t.getBoundingClientRect().height:t.offsetHeight})),an=e=>ln.get(e),cn=e=>ln.getOuter(e),mn=(e,t)=>({left:e,top:t,translate:(o,n)=>mn(e+o,t+n)}),dn=mn,un=(e,t)=>void 0!==e?e:void 0!==t?t:0,fn=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,r=t.documentElement;if(o===e.dom)return dn(o.offsetLeft,o.offsetTop);const s=un(null==n?void 0:n.pageYOffset,r.scrollTop),l=un(null==n?void 0:n.pageXOffset,r.scrollLeft),a=un(r.clientTop,o.clientTop),c=un(r.clientLeft,o.clientLeft);return gn(e).translate(l-c,s-a)},gn=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?dn(o.offsetLeft,o.offsetTop):et(e)?(e=>{const t=e.getBoundingClientRect();return dn(t.left,t.top)})(t):dn(0,0)},hn=(e,t)=>({row:e,y:t}),pn=(e,t)=>({col:e,x:t}),wn=e=>fn(e).left+Eo(e),bn=e=>fn(e).left,vn=(e,t)=>pn(e,bn(t)),yn=(e,t)=>pn(e,wn(t)),xn=e=>fn(e).top,Cn=(e,t)=>hn(e,xn(t)),Sn=(e,t)=>hn(e,xn(t)+cn(t)),Tn=(e,t,o)=>{if(0===o.length)return[];const n=E(o.slice(1),((t,o)=>t.map((t=>e(o,t))))),r=o[o.length-1].map((e=>t(o.length-1,e)));return n.concat([r])},Rn={delta:h,positions:e=>Tn(Cn,Sn,e),edge:xn},Dn=rn({delta:h,edge:bn,positions:e=>Tn(vn,yn,e)},{delta:e=>-e,edge:wn,positions:e=>Tn(yn,vn,e)}),On={delta:(e,t)=>Dn(t).delta(e,t),positions:(e,t)=>Dn(t).positions(e,t),edge:e=>Dn(e).edge(e)},kn={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},En=(()=>{const e="[0-9]+",t="[eE][+-]?[0-9]+",o=e=>`(?:${e})?`,n=["Infinity","[0-9]+\\."+o(e)+o(t),"\\.[0-9]+"+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),Nn=/(\d+(\.\d+)?)%/,Bn=/(\d+(\.\d+)?)px|em/,zn=ae("col"),An=(e,t,o)=>{const n=(r=e,C.from(r.dom.parentElement).map(pe.fromDom)).getOrThunk((()=>tt(Se(e))));var r;return t(e)/o(n)*100},Wn=(e,t)=>{St(e,"width",t+"px")},Ln=(e,t)=>{St(e,"width",t+"%")},_n=(e,t)=>{St(e,"height",t+"px")},Mn=e=>{const t=(e=>{return Do(t=e,"height",t.dom.offsetHeight)+"px";var t})(e);return t?((e,t,o,n)=>{const r=parseFloat(e);return bt(e,"%")&&"table"!==Z(t)?((e,t,o,n)=>{const r=Ft(e).map((e=>{const n=o(e);return Math.floor(t/100*n)})).getOr(t);return n(e,r),r})(t,r,o,n):r})(t,e,an,_n):an(e)},jn=(e,t)=>Ot(e,t).orThunk((()=>ue(e,t).map((e=>e+"px")))),In=e=>jn(e,"width"),Pn=e=>An(e,ko,No),Fn=e=>{return zn(e)?ko(e):Do(t=e,"width",t.dom.offsetWidth);var t},Hn=e=>((e,t,o)=>o(e)/Nt(e,"rowspan"))(e,0,Mn),qn=(e,t,o)=>{St(e,"width",t+o)},Vn=e=>An(e,ko,No)+"%",$n=g(Nn),Un=ae("col"),Gn=e=>In(e).getOrThunk((()=>Fn(e)+"px")),Kn=e=>{return(t=e,jn(t,"height")).getOrThunk((()=>Hn(e)+"px"));var t},Yn=(e,t,o,n,r,s)=>e.filter(n).fold((()=>s(((e,t)=>{if(t<0||t>=e.length-1)return C.none();const o=e[t].fold((()=>{const o=(e=>{const t=S.call(e,0);return t.reverse(),t})(e.slice(0,t));return V(o,((e,t)=>e.map((e=>({value:e,delta:t+1})))))}),(e=>C.some({value:e,delta:0}))),n=e[t+1].fold((()=>{const o=e.slice(t+1);return V(o,((e,t)=>e.map((e=>({value:e,delta:t+1})))))}),(e=>C.some({value:e,delta:1})));return o.bind((e=>n.map((t=>{const o=t.delta+e.delta;return Math.abs(t.value-e.value)/o}))))})(o,t))),(e=>r(e))),Jn=(e,t,o,n)=>{const r=tn(e),s=Zo(e)?(e=>E(Xo(e),(e=>C.from(e.element))))(e):r,l=[C.some(On.edge(t))].concat(E(On.positions(r,t),(e=>e.map((e=>e.x))))),a=b(Bt);return E(s,((e,t)=>Yn(e,t,l,a,(e=>{if((e=>{const t=To().browser,o=t.isChromium()||t.isFirefox();return!Un(e)||o})(e))return o(e);{const e=null!=(s=r[t])?h(s):C.none();return Yn(e,t,l,a,(e=>n(C.some(ko(e)))),n)}var s}),n)))},Qn=e=>e.map((e=>e+"px")).getOr(""),Xn=(e,t,o)=>Jn(e,t,Fn,(e=>e.getOrThunk(o.minCellWidth))),Zn=(e,t,o,n,r)=>{const s=nn(e),l=[C.some(o.edge(t))].concat(E(o.positions(s,t),(e=>e.map((e=>e.y)))));return E(s,((e,t)=>Yn(e,t,l,b(zt),n,r)))},er=(e,t)=>()=>et(e)?t(e):parseFloat(Ot(e,"width").getOr("0")),tr=e=>{const t=er(e,(e=>parseFloat(Vn(e)))),o=er(e,ko);return{width:t,pixelWidth:o,getWidths:(t,o)=>((e,t,o)=>Jn(e,t,Pn,(e=>e.fold((()=>o.minCellWidth()),(e=>e/o.pixelWidth()*100)))))(t,e,o),getCellDelta:e=>e/o()*100,singleColumnWidth:(e,t)=>[100-e],minCellWidth:()=>Wt()/o()*100,setElementWidth:Ln,adjustTableWidth:o=>{const n=t();Ln(e,n+o/100*n)},isRelative:!0,label:"percent"}},or=e=>{const t=er(e,ko);return{width:t,pixelWidth:t,getWidths:(t,o)=>Xn(t,e,o),getCellDelta:h,singleColumnWidth:(e,t)=>[Math.max(Wt(),e+t)-e],minCellWidth:Wt,setElementWidth:Wn,adjustTableWidth:o=>{const n=t()+o;Wn(e,n)},isRelative:!1,label:"pixel"}},nr=e=>In(e).fold((()=>(e=>{const t=er(e,ko),o=g(0);return{width:t,pixelWidth:t,getWidths:(t,o)=>Xn(t,e,o),getCellDelta:o,singleColumnWidth:g([0]),minCellWidth:o,setElementWidth:f,adjustTableWidth:f,isRelative:!0,label:"none"}})(e)),(t=>((e,t)=>null!==$n().exec(t)?tr(e):or(e))(e,t))),rr=or,sr=tr,lr=(e,t,o)=>{const n=e[o].element,r=pe.fromTag("td");We(r,pe.fromTag("br")),(t?We:Ae)(n,r)},ar=((e,t)=>{const o=t=>e(t)?C.from(t.dom.nodeValue):C.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(re),cr=e=>ar.get(e),ir=e=>ar.getOption(e),mr=(e,t)=>ar.set(e,t),dr=e=>"img"===Z(e)?1:ir(e).fold((()=>Ee(e).length),(e=>e.length)),ur=["img","br"],fr=e=>ir(e).filter((e=>0!==e.trim().length||e.indexOf("\xa0")>-1)).isSome()||D(ur,Z(e)),gr=e=>((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const r=pe.fromDom(e.childNodes[n]);if(t(r))return C.some(r);const s=o(e.childNodes[n]);if(s.isSome())return s}return C.none()};return o(e.dom)})(e,fr),hr=e=>pr(e,fr),pr=(e,t)=>{const o=e=>{const n=Ee(e);for(let e=n.length-1;e>=0;e--){const r=n[e];if(t(r))return C.some(r);const s=o(r);if(s.isSome())return s}return C.none()};return o(e)},wr={scope:["row","col"]},br=e=>()=>{const t=pe.fromTag("td",e.dom);return We(t,pe.fromTag("br",e.dom)),t},vr=e=>()=>pe.fromTag("col",e.dom),yr=e=>()=>pe.fromTag("colgroup",e.dom),xr=e=>()=>pe.fromTag("tr",e.dom),Cr=(e,t,o)=>{const n=((e,t)=>{const o=Ve(e,t),n=Ee(qe(e));return Me(o,n),o})(e,t);return G(o,((e,t)=>{null===e?fe(n,t):ie(n,t,e)})),n},Sr=e=>e,Tr=(e,t,o)=>{const n=(e,t)=>{((e,t)=>{const o=e.dom,n=t.dom;xt(o)&&xt(n)&&(n.style.cssText=o.style.cssText)})(e.element,t),kt(t,"height"),1!==e.colspan&&kt(t,"width")};return{col:o=>{const r=pe.fromTag(Z(o.element),t.dom);return n(o,r),e(o.element,r),r},colgroup:yr(t),row:xr(t),cell:r=>{const s=pe.fromTag(Z(r.element),t.dom),l=o.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),a=l.length>0?((e,t,o)=>gr(e).map((n=>{const r=o.join(","),s=nt(n,r,(t=>ye(t,e)));return A(s,((e,t)=>{const o=He(t);return fe(o,"contenteditable"),We(e,o),o}),t)})).getOr(t))(r.element,s,l):s;return We(a,pe.fromTag("br")),n(r,s),((e,t)=>{G(wr,((o,n)=>ue(e,n).filter((e=>D(o,e))).each((e=>ie(t,n,e)))))})(r.element,s),e(r.element,s),s},replace:Cr,colGap:vr(t),gap:br(t)}},Rr=e=>({col:vr(e),colgroup:yr(e),row:xr(e),cell:br(e),replace:Sr,colGap:vr(e),gap:br(e)}),Dr=e=>pe.fromDom(e.getBody()),Or=e=>t=>ye(t,Dr(e)),kr=e=>{fe(e,"data-mce-style");const t=e=>fe(e,"data-mce-style");N(It(e),t),N(Pt(e),t),N(Ht(e),t)},Er=e=>pe.fromDom(e.selection.getStart()),Nr=e=>e.getBoundingClientRect().width,Br=e=>e.getBoundingClientRect().height,zr=(e,t)=>{const o=t.column,n=t.column+t.colspan-1,r=t.row,s=t.row+t.rowspan-1;return o<=e.finishCol&&n>=e.startCol&&r<=e.finishRow&&s>=e.startRow},Ar=(e,t)=>t.column>=e.startCol&&t.column+t.colspan-1<=e.finishCol&&t.row>=e.startRow&&t.row+t.rowspan-1<=e.finishRow,Wr=(e,t,o)=>{const n=Yo(e,t,ye),r=Yo(e,o,ye);return n.bind((e=>r.map((t=>{return o=e,n=t,{startRow:Math.min(o.row,n.row),startCol:Math.min(o.column,n.column),finishRow:Math.max(o.row+o.rowspan-1,n.row+n.rowspan-1),finishCol:Math.max(o.column+o.colspan-1,n.column+n.colspan-1)};var o,n}))))},Lr=(e,t,o)=>Wr(e,t,o).map((t=>{const o=Jo(e,w(zr,t));return E(o,(e=>e.element))})),_r=(e,t)=>Yo(e,t,((e,t)=>xe(t,e))).map((e=>e.element)),Mr=(e,t,o)=>{const n=Ir(e);return Lr(n,t,o)},jr=(e,t,o,n,r)=>{const s=Ir(e),l=ye(e,o)?C.some(t):_r(s,t),a=ye(e,r)?C.some(n):_r(s,n);return l.bind((e=>a.bind((t=>Lr(s,e,t)))))},Ir=Uo;var Pr=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],Fr=()=>({up:g({selector:ct,closest:dt,predicate:at,all:De}),down:g({selector:st,predicate:ot}),styles:g({get:Rt,getRaw:Ot,set:St,remove:kt}),attrs:g({get:de,set:ie,remove:fe,copyTo:(e,t)=>{const o=ge(e);me(t,o)}}),insert:g({before:Be,after:ze,afterAll:_e,append:We,appendAll:Me,prepend:Ae,wrap:Le}),remove:g({unwrap:Pe,remove:Ie}),create:g({nu:pe.fromTag,clone:e=>pe.fromDom(e.dom.cloneNode(!1)),text:pe.fromText}),query:g({comparePosition:(e,t)=>e.dom.compareDocumentPosition(t.dom),prevSibling:Oe,nextSibling:ke}),property:g({children:Ee,name:Z,parent:Re,document:e=>Te(e).dom,isText:re,isComment:oe,isElement:ne,isSpecial:e=>{const t=Z(e);return D(["script","noscript","iframe","noframes","noembed","title","style","textarea","xmp"],t)},getLanguage:e=>ne(e)?ue(e,"lang"):C.none(),getText:cr,setText:mr,isBoundary:e=>!!ne(e)&&("body"===Z(e)||D(Pr,Z(e))),isEmptyTag:e=>!!ne(e)&&D(["br","img","hr","input"],Z(e)),isNonEditable:e=>ne(e)&&"false"===de(e,"contenteditable")}),eq:ye,is:Ce});const Hr=(e,t,o,n)=>{const r=t(e,o);return A(n,((o,n)=>{const r=t(e,n);return qr(e,o,r)}),r)},qr=(e,t,o)=>t.bind((t=>o.filter(w(e.eq,t)))),Vr=Fr(),$r=(e,t)=>((e,t,o)=>o.length>0?((e,t,o,n)=>n(e,t,o[0],o.slice(1)))(e,t,o,Hr):C.none())(Vr,((t,o)=>e(o)),t),Ur=e=>ct(e,"table"),Gr=(e,t,o)=>{const n=e=>t=>void 0!==o&&o(t)||ye(t,e);return ye(e,t)?C.some({boxes:C.some([e]),start:e,finish:t}):Ur(e).bind((r=>Ur(t).bind((s=>{if(ye(r,s))return C.some({boxes:Mr(r,e,t),start:e,finish:t});if(xe(r,s)){const o=nt(t,"td,th",n(r)),l=o.length>0?o[o.length-1]:t;return C.some({boxes:jr(r,e,r,t,s),start:e,finish:l})}if(xe(s,r)){const o=nt(e,"td,th",n(s)),l=o.length>0?o[o.length-1]:e;return C.some({boxes:jr(s,e,r,t,s),start:e,finish:l})}return((e,t,o)=>((e,t,o,n=y)=>{const r=[t].concat(e.up().all(t)),s=[o].concat(e.up().all(o)),l=e=>_(e,n).fold((()=>e),(t=>e.slice(0,t+1))),a=l(r),c=l(s),i=L(a,(t=>O(c,((e,t)=>w(e.eq,t))(e,t))));return{firstpath:a,secondpath:c,shared:i}})(Vr,e,t,void 0))(e,t).shared.bind((l=>dt(l,"table",o).bind((o=>{const l=nt(t,"td,th",n(o)),a=l.length>0?l[l.length-1]:t,c=nt(e,"td,th",n(o)),i=c.length>0?c[c.length-1]:e;return C.some({boxes:jr(o,e,r,t,s),start:i,finish:a})}))))}))))},Kr=(e,t)=>{const o=st(e,t);return o.length>0?C.some(o):C.none()},Yr=(e,t,o)=>mt(e,t).bind((t=>mt(e,o).bind((e=>$r(Ur,[t,e]).map((o=>({first:t,last:e,table:o}))))))),Jr=(e,t,o,n,r)=>((e,t)=>L(e,(e=>we(e,t))))(e,r).bind((e=>((e,t,o)=>Ft(e).bind((n=>((e,t,o,n)=>Yo(e,t,ye).bind((t=>{const r=o>0?t.row+t.rowspan-1:t.row,s=n>0?t.column+t.colspan-1:t.column;return Ko(e,r+o,s+n).map((e=>e.element))})))(Ir(n),e,t,o))))(e,t,o).bind((e=>((e,t)=>ct(e,"table").bind((o=>mt(o,t).bind((t=>Gr(t,e).bind((e=>e.boxes.map((t=>({boxes:t,start:e.start,finish:e.finish}))))))))))(e,n))))),Qr=(e,t)=>Kr(e,t),Xr=(e,t,o)=>Yr(e,t,o).bind((t=>{const o=t=>ye(e,t),n="thead,tfoot,tbody,table",r=ct(t.first,n,o),s=ct(t.last,n,o);return r.bind((e=>s.bind((o=>ye(e,o)?((e,t,o)=>((e,t,o)=>Wr(e,t,o).bind((t=>((e,t)=>{let o=!0;const n=w(Ar,t);for(let r=t.startRow;r<=t.finishRow;r++)for(let s=t.startCol;s<=t.finishCol;s++)o=o&&Ko(e,r,s).exists(n);return o?C.some(t):C.none()})(e,t))))(Ir(e),t,o))(t.table,t.first,t.last):C.none()))))})),Zr=h,es=e=>{const t=(e,t)=>ue(e,t).exists((e=>parseInt(e,10)>1));return e.length>0&&I(e,(e=>t(e,"rowspan")||t(e,"colspan")))?C.some(e):C.none()},ts=(e,t,o)=>t.length<=1?C.none():Xr(e,o.firstSelectedSelector,o.lastSelectedSelector).map((e=>({bounds:e,cells:t}))),os={selected:"data-mce-selected",selectedSelector:"td[data-mce-selected],th[data-mce-selected]",firstSelected:"data-mce-first-selected",firstSelectedSelector:"td[data-mce-first-selected],th[data-mce-first-selected]",lastSelected:"data-mce-last-selected",lastSelectedSelector:"td[data-mce-last-selected],th[data-mce-last-selected]"},ns=(e,t,o)=>({element:o,mergable:ts(t,e,os),unmergable:es(e),selection:Zr(e)}),rs=e=>(t,o)=>{const n=Z(t),r="col"===n||"colgroup"===n?Ft(s=t).bind((e=>Qr(e,os.firstSelectedSelector))).fold(g(s),(e=>e[0])):t;var s;return dt(r,e,o)},ss=rs("th,td,caption"),ls=rs("th,td"),as=e=>{return t=e.model.table.getSelectedCells(),E(t,pe.fromDom);var t},cs=(e,t)=>{e.on("BeforeGetContent",(t=>{const o=o=>{t.preventDefault(),(e=>Ft(e[0]).map((e=>{const t=((e,t)=>{const o=e=>we(e.element,t),n=qe(e),r=Ut(n),s=nr(e),l=Go(r),a=((e,t)=>{const o=e.grid.columns;let n=e.grid.rows,r=o,s=0,l=0;const a=[],c=[];return G(e.access,(e=>{if(a.push(e),t(e)){c.push(e);const t=e.row,o=t+e.rowspan-1,a=e.column,i=a+e.colspan-1;t<n?n=t:o>s&&(s=o),a<r?r=a:i>l&&(l=i)}})),((e,t,o,n,r,s)=>({minRow:e,minCol:t,maxRow:o,maxCol:n,allCells:r,selectedCells:s}))(n,r,s,l,a,c)})(l,o),c="th:not("+t+"),td:not("+t+")",i=Mt(n,"th,td",(e=>we(e,c)));N(i,Ie),((e,t,o,n)=>{const r=z(e,(e=>"colgroup"!==e.section)),s=t.grid.columns,l=t.grid.rows;for(let e=0;e<l;e++){let l=!1;for(let a=0;a<s;a++)e<o.minRow||e>o.maxRow||a<o.minCol||a>o.maxCol||(Ko(t,e,a).filter(n).isNone()?lr(r,l,e):l=!0)}})(r,l,a,o);const m=((e,t,o,n)=>{if(0===n.minCol&&t.grid.columns===n.maxCol+1)return 0;const r=Xn(t,e,o),s=W(r,((e,t)=>e+t),0),l=W(r.slice(n.minCol,n.maxCol+1),((e,t)=>e+t),0),a=l/s*o.pixelWidth()-o.pixelWidth();return o.getCellDelta(a)})(e,Uo(e),s,a);return((e,t,o,n)=>{G(o.columns,(e=>{(e.column<t.minCol||e.column>t.maxCol)&&Ie(e.element)}));const r=z(_t(e,"tr"),(e=>0===e.dom.childElementCount));N(r,Ie),t.minCol!==t.maxCol&&t.minRow!==t.maxRow||N(_t(e,"th,td"),(e=>{fe(e,"rowspan"),fe(e,"colspan")})),fe(e,Po),fe(e,"data-snooker-col-series"),nr(e).adjustTableWidth(n)})(n,a,l,m),n})(e,"[data-mce-selected]");return kr(t),[t]})))(o).each((o=>{t.content="text"===t.format?(e=>E(e,(e=>e.dom.innerText)).join(""))(o):((e,t)=>E(t,(t=>e.selection.serializer.serialize(t.dom,{}))).join(""))(e,o)}))};if(!0===t.selection){const t=(e=>z(as(e),(e=>we(e,os.selectedSelector))))(e);t.length>=1&&o(t)}})),e.on("BeforeSetContent",(o=>{if(!0===o.selection&&!0===o.paste){const n=as(e);H(n).each((n=>{Ft(n).each((r=>{const s=z(((e,t)=>{const o=document.createElement("div");return o.innerHTML=e,Ee(pe.fromDom(o))})(o.content),(e=>"meta"!==Z(e))),l=ae("table");if(1===s.length&&l(s[0])){o.preventDefault();const l=pe.fromDom(e.getDoc()),a=Rr(l),c=((e,t,o)=>({element:e,clipboard:t,generators:o}))(n,s[0],a);t.pasteCells(r,c).each((()=>{e.focus()}))}}))}))}}))},is=(e,t)=>({element:e,offset:t}),ms=(e,t,o)=>e.property().isText(t)&&0===e.property().getText(t).trim().length||e.property().isComment(t)?o(t).bind((t=>ms(e,t,o).orThunk((()=>C.some(t))))):C.none(),ds=(e,t)=>e.property().isText(t)?e.property().getText(t).length:e.property().children(t).length,us=(e,t)=>{const o=ms(e,t,e.query().prevSibling).getOr(t);if(e.property().isText(o))return is(o,ds(e,o));const n=e.property().children(o);return n.length>0?us(e,n[n.length-1]):is(o,ds(e,o))},fs=us,gs=Fr(),hs=(e,t)=>{if(!Bt(e)){const o=(e=>In(e).bind((e=>{return t=e,o=["fixed","relative","empty"],C.from(En.exec(t)).bind((e=>{const t=Number(e[1]),n=e[2];return((e,t)=>O(t,(t=>O(kn[t],(t=>e===t)))))(n,o)?C.some({value:t,unit:n}):C.none()}));var t,o})))(e);o.each((o=>{const n=o.value/2;qn(e,n,o.unit),qn(t,n,o.unit)}))}},ps=e=>E(e,g(0)),ws=(e,t,o,n,r)=>r(e.slice(0,t)).concat(n).concat(r(e.slice(o))),bs=e=>(t,o,n,r)=>{if(e(n)){const e=Math.max(r,t[o]-Math.abs(n)),s=Math.abs(e-t[o]);return n>=0?s:-s}return n},vs=bs((e=>e<0)),ys=bs(x),xs=()=>{const e=(e,t,o,n)=>{const r=(100+o)/100,s=Math.max(n,(e[t]+o)/r);return E(e,((e,o)=>(o===t?s:e/r)-e))},t=(t,o,n,r,s,l)=>l?e(t,o,r,s):((e,t,o,n,r)=>{const s=vs(e,t,n,r);return ws(e,t,o+1,[s,0],ps)})(t,o,n,r,s);return{resizeTable:(e,t)=>e(t),clampTableDelta:vs,calcLeftEdgeDeltas:t,calcMiddleDeltas:(e,o,n,r,s,l,a)=>t(e,n,r,s,l,a),calcRightEdgeDeltas:(t,o,n,r,s,l)=>{if(l)return e(t,n,r,s);{const e=vs(t,n,r,s);return ps(t.slice(0,n)).concat([e])}},calcRedestributedWidths:(e,t,o,n)=>{if(n){const n=(t+o)/t,r=E(e,(e=>e/n));return{delta:100*n-100,newSizes:r}}return{delta:o,newSizes:e}}}},Cs=()=>{const e=(e,t,o,n,r)=>{const s=ys(e,n>=0?o:t,n,r);return ws(e,t,o+1,[s,-s],ps)};return{resizeTable:(e,t,o)=>{o&&e(t)},clampTableDelta:(e,t,o,n,r)=>{if(r){if(o>=0)return o;{const t=W(e,((e,t)=>e+t-n),0);return Math.max(-t,o)}}return vs(e,t,o,n)},calcLeftEdgeDeltas:e,calcMiddleDeltas:(t,o,n,r,s,l)=>e(t,n,r,s,l),calcRightEdgeDeltas:(e,t,o,n,r,s)=>{if(s)return ps(e);{const t=n/e.length;return E(e,g(t))}},calcRedestributedWidths:(e,t,o,n)=>({delta:0,newSizes:e})}},Ss=e=>Uo(e).grid,Ts=ae("th"),Rs=e=>I(e,(e=>Ts(e.element))),Ds=(e,t)=>e&&t?"sectionCells":e?"section":"cells",Os=e=>{const t="thead"===e.section,o=ut(ks(e.cells),"th");return"tfoot"===e.section?{type:"footer"}:t||o?{type:"header",subType:Ds(t,o)}:{type:"body"}},ks=e=>{const t=z(e,(e=>Ts(e.element)));return 0===t.length?C.some("td"):t.length===e.length?C.some("th"):C.none()},Es=(e,t,o)=>Ke(o(e.element,t),!0,e.isLocked),Ns=(e,t)=>e.section!==t?Ye(e.element,e.cells,t,e.isNew):e,Bs=()=>({transformRow:Ns,transformCell:(e,t,o)=>{const n=o(e.element,t),r="td"!==Z(n)?((e,t)=>{const o=Ve(e,"td");ze(e,o);const n=Ee(e);return Me(o,n),Ie(e),o})(n):n;return Ke(r,e.isNew,e.isLocked)}}),zs=()=>({transformRow:Ns,transformCell:Es}),As=()=>({transformRow:(e,t)=>Ns(e,"thead"===t?"tbody":t),transformCell:Es}),Ws=Bs,Ls=zs,_s=As,Ms=()=>({transformRow:h,transformCell:Es}),js=e=>dt(e,"[contenteditable]"),Is=(e,t=!1)=>et(e)?e.dom.isContentEditable:js(e).fold(g(t),(e=>"true"===Ps(e))),Ps=e=>e.dom.contentEditable,Fs=(e,t,o,n)=>{o===n?fe(e,t):ie(e,t,o)},Hs=(e,t,o)=>{q(rt(e,t)).fold((()=>Ae(e,o)),(e=>ze(e,o)))},qs=(e,t)=>{const o=[],n=[],r=e=>E(e,(e=>{e.isNew&&o.push(e.element);const t=e.element;return je(t),N(e.cells,(e=>{e.isNew&&n.push(e.element),Fs(e.element,"colspan",e.colspan,1),Fs(e.element,"rowspan",e.rowspan,1),We(t,e.element)})),t})),s=e=>j(e,(e=>E(e.cells,(e=>(Fs(e.element,"span",e.colspan,1),e.element))))),l=(t,o)=>{const n=((e,t)=>{const o=it(e,t).getOrThunk((()=>{const o=pe.fromTag(t,Se(e).dom);return"thead"===t?Hs(e,"caption,colgroup",o):"colgroup"===t?Hs(e,"caption",o):We(e,o),o}));return je(o),o})(e,o),l=("colgroup"===o?s:r)(t);Me(n,l)},a=(t,o)=>{t.length>0?l(t,o):(t=>{it(e,t).each(Ie)})(o)},c=[],i=[],m=[],d=[];return N(t,(e=>{switch(e.section){case"thead":c.push(e);break;case"tbody":i.push(e);break;case"tfoot":m.push(e);break;case"colgroup":d.push(e)}})),a(d,"colgroup"),a(c,"thead"),a(i,"tbody"),a(m,"tfoot"),{newRows:o,newCells:n}},Vs=(e,t)=>{if(0===e.length)return 0;const o=e[0];return _(e,(e=>!t(o.element,e.element))).getOr(e.length)},$s=(e,t)=>{const o=E(e,(e=>E(e.cells,y)));return E(e,((n,r)=>{const s=j(n.cells,((n,s)=>{if(!1===o[r][s]){const m=((e,t,o,n)=>{const r=((e,t)=>e[t])(e,t),s="colgroup"===r.section,l=Vs(r.cells.slice(o),n),a=s?1:Vs(((e,t)=>E(e,(e=>Lo(e,t))))(e.slice(t),o),n);return{colspan:l,rowspan:a}})(e,r,s,t);return((e,t,n,r)=>{for(let s=e;s<e+n;s++)for(let e=t;e<t+r;e++)o[s][e]=!0})(r,s,m.rowspan,m.colspan),[(l=n.element,a=m.rowspan,c=m.colspan,i=n.isNew,{element:l,rowspan:a,colspan:c,isNew:i})]}return[];var l,a,c,i}));return((e,t,o,n)=>({element:e,cells:t,section:o,isNew:n}))(n.element,s,n.section,n.isNew)}))},Us=(e,t,o)=>{const n=[];N(e.colgroups,(r=>{const s=[];for(let n=0;n<e.grid.columns;n++){const r=en(e,n).map((e=>Ke(e.element,o,!1))).getOrThunk((()=>Ke(t.colGap(),!0,!1)));s.push(r)}n.push(Ye(r.element,s,"colgroup",o))}));for(let r=0;r<e.grid.rows;r++){const s=[];for(let n=0;n<e.grid.columns;n++){const l=Ko(e,r,n).map((e=>Ke(e.element,o,e.isLocked))).getOrThunk((()=>Ke(t.gap(),!0,!1)));s.push(l)}const l=e.all[r],a=Ye(l.element,s,l.section,o);n.push(a)}return n},Gs=e=>$s(e,ye),Ks=(e,t)=>V(e.all,(e=>L(e.cells,(e=>ye(t,e.element))))),Ys=(e,t,o)=>{const n=E(t.selection,(t=>jt(t).bind((t=>Ks(e,t))).filter(o))),r=ft(n);return gt(r.length>0,r)},Js=(e,t,o,n,r)=>(s,l,a,c)=>{const i=Uo(s),m=C.from(null==c?void 0:c.section).getOrThunk(Ms);return t(i,l).map((t=>{const o=((e,t)=>Us(e,t,!1))(i,a),n=e(o,t,ye,r(a),m),s=Ho(n.grid);return{info:t,grid:Gs(n.grid),cursor:n.cursor,lockedColumns:s}})).bind((e=>{const t=qs(s,e.grid),r=C.from(null==c?void 0:c.sizing).getOrThunk((()=>nr(s))),l=C.from(null==c?void 0:c.resize).getOrThunk(Cs);return o(s,e.grid,e.info,{sizing:r,resize:l,section:m}),n(s),fe(s,Po),e.lockedColumns.length>0&&ie(s,Po,e.lockedColumns.join(",")),C.some({cursor:e.cursor,newRows:t.newRows,newCells:t.newCells})}))},Qs=(e,t)=>Ys(e,t,x).map((e=>({cells:e,generators:t.generators,clipboard:t.clipboard}))),Xs=(e,t)=>Ys(e,t,x),Zs=(e,t)=>Ys(e,t,(e=>!e.isLocked)),el=(e,t)=>I(t,(t=>((e,t)=>Ks(e,t).exists((e=>!e.isLocked)))(e,t))),tl=(e,t,o,n)=>{const r=jo(e).rows;let s=!0;for(let e=0;e<r.length;e++)for(let l=0;l<Mo(r[0]);l++){const a=r[e],c=Lo(a,l),i=o(c.element,t);i&&!s?Ao(a,l,Ke(n(),!0,c.isLocked)):i&&(s=!1)}return e},ol=e=>{const t=t=>t(e),o=g(e),n=()=>r,r={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:x,isError:y,map:t=>rl.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>C.some(e)};return r},nl=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:y,isError:x,map:t,mapError:t=>rl.error(t(e)),bind:t,exists:y,forall:x,getOr:h,or:h,getOrThunk:v,orThunk:v,getOrDie:(n=String(e),()=>{throw new Error(n)}),each:f,toOptional:C.none};var n;return o},rl={value:ol,error:nl,fromOption:(e,t)=>e.fold((()=>nl(t)),ol)},sl=(e,t)=>({rowDelta:0,colDelta:Mo(e[0])-Mo(t[0])}),ll=(e,t)=>({rowDelta:e.length-t.length,colDelta:0}),al=(e,t,o,n)=>{const r="colgroup"===t.section?o.col:o.cell;return k(e,(e=>Ke(r(),!0,n(e))))},cl=(e,t,o,n)=>{const r=e[e.length-1];return e.concat(k(t,(()=>{const e="colgroup"===r.section?o.colgroup:o.row,t=Io(r,e,h),s=al(t.cells.length,t,o,(e=>X(n,e.toString())));return Wo(t,s)})))},il=(e,t,o,n)=>E(e,(e=>{const r=al(t,e,o,y);return Bo(e,n,r)})),ml=(e,t,o)=>{const n=t.colDelta<0?il:h,r=t.rowDelta<0?cl:h,s=Ho(e),l=Mo(e[0]),a=O(s,(e=>e===l-1)),c=n(e,Math.abs(t.colDelta),o,a?l-1:l),i=Ho(c);return r(c,Math.abs(t.rowDelta),o,P(i,x))},dl=(e,t,o,n)=>{const r=w(n,Lo(e[t],o).element),s=e[t];return e.length>1&&Mo(s)>1&&(o>0&&r(_o(s,o-1))||o<s.cells.length-1&&r(_o(s,o+1))||t>0&&r(_o(e[t-1],o))||t<e.length-1&&r(_o(e[t+1],o)))},ul=(e,t,o)=>z(o,(o=>o>=e.column&&o<=Mo(t[0])+e.column)),fl=(e,t,o,n,r)=>{((e,t,o,n)=>{t>0&&t<e[0].cells.length&&N(e,(e=>{const r=e.cells[t-1];let s=0;const l=n();for(;e.cells.length>t+s&&o(r.element,e.cells[t+s].element);)Ao(e,t+s,Ke(l,!0,e.cells[t+s].isLocked)),s++}))})(t,e,r,n.cell);const s=ll(o,t),l=ml(o,s,n),a=ll(t,l),c=ml(t,a,n);return E(c,((t,o)=>Bo(t,e,l[o].cells)))},gl=(e,t,o,n,r)=>{((e,t,o,n)=>{const r=jo(e).rows;if(t>0&&t<r.length){const e=((e,t)=>W(e,((e,o)=>O(e,(e=>t(e.element,o.element)))?e:e.concat([o])),[]))(r[t-1].cells,o);N(e,(e=>{let s=C.none();for(let l=t;l<r.length;l++)for(let t=0;t<Mo(r[0]);t++){const a=r[l],c=Lo(a,t);o(c.element,e.element)&&(s.isNone()&&(s=C.some(n())),s.each((e=>{Ao(a,t,Ke(e,!0,c.isLocked))})))}}))}})(t,e,r,n.cell);const s=Ho(t),l=sl(t,o),a={...l,colDelta:l.colDelta-s.length},c=ml(t,a,n),{cols:i,rows:m}=jo(c),d=Ho(c),u=sl(o,t),f={...u,colDelta:u.colDelta+d.length},g=(p=n,w=d,E(o,(e=>W(w,((t,o)=>{const n=al(1,e,p,x)[0];return zo(t,o,n)}),e)))),h=ml(g,f,n);var p,w;return[...i,...m.slice(0,e),...h,...m.slice(e,m.length)]},hl=(e,t,o,n,r)=>{const{rows:s,cols:l}=jo(e),a=s.slice(0,t),c=s.slice(t);return[...l,...a,((e,t,o,n)=>Io(e,(e=>n(e,o)),t))(s[o],((e,o)=>t>0&&t<s.length&&n(_o(s[t-1],o),_o(s[t],o))?Lo(s[t],o):Ke(r(e.element,n),!0,e.isLocked)),n,r),...c]},pl=(e,t,o,n,r)=>E(e,(e=>{const s=t>0&&t<Mo(e)&&n(_o(e,t-1),_o(e,t)),l=((e,t,o,n,r,s,l)=>{if("colgroup"!==o&&n)return Lo(e,t);{const t=Lo(e,r);return Ke(l(t.element,s),!0,!1)}})(e,t,e.section,s,o,n,r);return zo(e,t,l)})),wl=(e,t,o,n)=>((e,t,o,n)=>void 0!==_o(e[t],o)&&t>0&&n(_o(e[t-1],o),_o(e[t],o)))(e,t,o,n)||((e,t,o)=>t>0&&o(_o(e,t-1),_o(e,t)))(e[t],o,n),bl=(e,t,o,n)=>{const r=e=>(e=>"row"===e?zt(t):Bt(t))(e)?`${e}group`:e;return e?Ts(t)?r(o):null:n&&Ts(t)?r("row"===o?"col":"row"):null},vl=(e,t,o)=>Ke(o(e.element,t),!0,e.isLocked),yl=(e,t,o,n,r,s,l)=>E(e,((e,a)=>((e,c)=>{const i=e.cells,m=E(i,((e,c)=>{if((e=>O(t,(t=>o(e.element,t.element))))(e)){const t=l(e,a,c)?r(e,o,n):e;return s(t,a,c).each((e=>{var o,n;o=t.element,n={scope:C.from(e)},G(n,((e,t)=>{e.fold((()=>{fe(o,t)}),(e=>{ce(o.dom,t,e)}))}))})),t}return e}));return Ye(e.element,m,e.section,e.isNew)})(e))),xl=(e,t,o)=>j(e,((n,r)=>wl(e,r,t,o)?[]:[Lo(n,t)])),Cl=(e,t,o,n,r)=>{const s=jo(e).rows,l=j(t,(e=>xl(s,e,n))),a=E(s,(e=>Rs(e.cells))),c=((e,t)=>I(t,h)&&Rs(e)?x:(e,o,n)=>!("th"===Z(e.element)&&t[o]))(l,a),i=((e,t)=>(o,n)=>C.some(bl(e,o.element,"row",t[n])))(o,a);return yl(e,l,n,r,vl,i,c)},Sl=(e,t,o,n)=>{const r=jo(e).rows,s=E(t,(e=>Lo(r[e.row],e.column)));return yl(e,s,o,n,vl,C.none,x)},Tl=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return N(e,((n,r)=>{const s=$(n);if(1!==s.length)throw new Error("one and only one name per case");const a=s[0],c=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(c))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==c.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+c.length+" ("+c+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[r].apply(null,o)},match:e=>{const n=$(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!I(t,(e=>D(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o},Rl={...Tl([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}])},Dl=(e,t,o)=>{let n=0;for(let r=e;r<t;r++)n+=void 0!==o[r]?o[r]:0;return n},Ol=(e,t)=>{const o=Qo(e);return E(o,(e=>{const o=Dl(e.row,e.row+e.rowspan,t);return{element:e.element,height:o,rowspan:e.rowspan}}))},kl=(e,t,o)=>{const n=((e,t)=>Zo(e)?((e,t)=>{const o=Xo(e);return E(o,((e,o)=>({element:e.element,width:t[o],colspan:e.colspan})))})(e,t):((e,t)=>{const o=Qo(e);return E(o,(e=>{const o=Dl(e.column,e.column+e.colspan,t);return{element:e.element,width:o,colspan:e.colspan}}))})(e,t))(e,t);N(n,(e=>{o.setElementWidth(e.element,e.width)}))},El=(e,t,o,n,r)=>{const s=Uo(e),l=r.getCellDelta(t),a=r.getWidths(s,r),c=o===s.grid.columns-1,i=n.clampTableDelta(a,o,l,r.minCellWidth(),c),m=((e,t,o,n,r)=>{const s=e.slice(0),l=((e,t)=>0===e.length?Rl.none():1===e.length?Rl.only(0):0===t?Rl.left(0,1):t===e.length-1?Rl.right(t-1,t):t>0&&t<e.length-1?Rl.middle(t-1,t,t+1):Rl.none())(e,t),a=g(E(s,g(0)));return l.fold(a,(e=>n.singleColumnWidth(s[e],o)),((e,t)=>r.calcLeftEdgeDeltas(s,e,t,o,n.minCellWidth(),n.isRelative)),((e,t,l)=>r.calcMiddleDeltas(s,e,t,l,o,n.minCellWidth(),n.isRelative)),((e,t)=>r.calcRightEdgeDeltas(s,e,t,o,n.minCellWidth(),n.isRelative)))})(a,o,i,r,n),d=E(m,((e,t)=>e+a[t]));kl(s,d,r),n.resizeTable(r.adjustTableWidth,i,c)},Nl=e=>W(e,((e,t)=>O(e,(e=>e.column===t.column))?e:e.concat([t])),[]).sort(((e,t)=>e.column-t.column)),Bl=ae("col"),zl=ae("colgroup"),Al=e=>"tr"===Z(e)||zl(e),Wl=e=>({element:e,colspan:Et(e,"colspan",1),rowspan:Et(e,"rowspan",1)}),Ll=e=>ue(e,"scope").map((e=>e.substr(0,3))),_l=(e,t=Wl)=>{const o=o=>{if(Al(o))return zl((r={element:o}).element)?e.colgroup(r):e.row(r);{const r=o,s=(t=>Bl(t.element)?e.col(t):e.cell(t))(t(r));return n=C.some({item:r,replacement:s}),s}var r};let n=C.none();return{getOrInit:(e,t)=>n.fold((()=>o(e)),(n=>t(e,n.item)?n.replacement:o(e)))}},Ml=e=>t=>{const o=[],n=n=>{const r="td"===e?{scope:null}:{},s=t.replace(n,e,r);return o.push({item:n,sub:s}),s};return{replaceOrInit:(e,t)=>{if(Al(e)||Bl(e))return e;{const r=e;return((e,t)=>L(o,(o=>t(o.item,e))))(r,t).fold((()=>n(r)),(o=>t(e,o.item)?o.sub:n(r)))}}}},jl=e=>({unmerge:t=>{const o=Ll(t);return o.each((e=>ie(t,"scope",e))),()=>{const n=e.cell({element:t,colspan:1,rowspan:1});return kt(n,"width"),kt(t,"width"),o.each((e=>ie(n,"scope",e))),n}},merge:e=>(kt(e[0],"width"),(()=>{const t=ft(E(e,Ll));if(0===t.length)return C.none();{const e=t[0],o=["row","col"];return O(t,(t=>t!==e&&D(o,t)))?C.none():C.from(e)}})().fold((()=>fe(e[0],"scope")),(t=>ie(e[0],"scope",t+"group"))),g(e[0]))}),Il=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],Pl=Fr(),Fl=e=>((e,t)=>{const o=e.property().name(t);return D(Il,o)})(Pl,e),Hl=e=>((e,t)=>{const o=e.property().name(t);return D(["ol","ul"],o)})(Pl,e),ql=e=>{const t=ae("br"),o=e=>hr(e).bind((o=>{const n=ke(o).map((e=>!!Fl(e)||!!((e,t)=>D(["br","img","hr","input"],e.property().name(t)))(Pl,e)&&"img"!==Z(e))).getOr(!1);return Re(o).map((r=>{return!0===n||("li"===Z(s=r)||at(s,Hl).isSome())||t(o)||Fl(r)&&!ye(e,r)?[]:[pe.fromTag("br")];var s}))})).getOr([]),n=(()=>{const n=j(e,(e=>{const n=Ee(e);return(e=>I(e,(e=>t(e)||re(e)&&0===cr(e).trim().length)))(n)?[]:n.concat(o(e))}));return 0===n.length?[pe.fromTag("br")]:n})();je(e[0]),Me(e[0],n)},Vl=e=>Is(e,!0),$l=e=>{0===It(e).length&&Ie(e)},Ul=(e,t)=>({grid:e,cursor:t}),Gl=(e,t,o)=>{const n=((e,t,o)=>{var n,r;const s=jo(e).rows;return C.from(null===(r=null===(n=s[t])||void 0===n?void 0:n.cells[o])||void 0===r?void 0:r.element).filter(Vl).orThunk((()=>(e=>V(e,(e=>V(e.cells,(e=>{const t=e.element;return gt(Vl(t),t)})))))(s)))})(e,t,o);return Ul(e,n)},Kl=e=>W(e,((e,t)=>O(e,(e=>e.row===t.row))?e:e.concat([t])),[]).sort(((e,t)=>e.row-t.row)),Yl=(e,t)=>(o,n,r,s,l)=>{const a=Kl(n),c=E(a,(e=>e.row)),i=((e,t,o,n,r,s,l)=>{const{cols:a,rows:c}=jo(e),i=c[t[0]],m=j(t,(e=>((e,t,o)=>{const n=e[t];return j(n.cells,((n,r)=>wl(e,t,r,o)?[]:[n]))})(c,e,r))),d=E(i.cells,((e,t)=>Rs(xl(c,t,r)))),u=[...c];N(t,(e=>{u[e]=l.transformRow(c[e],o)}));const f=[...a,...u],g=((e,t)=>I(t,h)&&Rs(e.cells)?x:(e,o,n)=>!("th"===Z(e.element)&&t[n]))(i,d),p=((e,t)=>(o,n,r)=>C.some(bl(e,o.element,"col",t[r])))(n,d);return yl(f,m,r,s,l.transformCell,p,g)})(o,c,e,t,r,s.replaceOrInit,l);return Gl(i,n[0].row,n[0].column)},Jl=Yl("thead",!0),Ql=Yl("tbody",!1),Xl=Yl("tfoot",!1),Zl=(e,t,o)=>{const n=((e,t)=>Vt(e,(()=>t)))(e,o.section),r=Go(n);return Us(r,t,!0)},ea=(e,t,o,n)=>((e,t,o,n)=>{const r=Go(t),s=n.getWidths(r,n);kl(r,s,n)})(0,t,0,n.sizing),ta=(e,t,o,n)=>((e,t,o,n,r)=>{const s=Go(t),l=n.getWidths(s,n),a=n.pixelWidth(),{newSizes:c,delta:i}=r.calcRedestributedWidths(l,a,o.pixelDelta,n.isRelative);kl(s,c,n),n.adjustTableWidth(i)})(0,t,o,n.sizing,n.resize),oa=(e,t)=>O(t,(e=>0===e.column&&e.isLocked)),na=(e,t)=>O(t,(t=>t.column+t.colspan>=e.grid.columns&&t.isLocked)),ra=(e,t)=>{const o=tn(e),n=Nl(t);return W(n,((e,t)=>e+o[t.column].map(Eo).getOr(0)),0)},sa=e=>(t,o)=>Xs(t,o).filter((o=>!(e?oa:na)(t,o))).map((e=>({details:e,pixelDelta:ra(t,e)}))),la=e=>(t,o)=>Qs(t,o).filter((o=>!(e?oa:na)(t,o.cells))),aa=Ml("th"),ca=Ml("td"),ia=Js(((e,t,o,n)=>{const r=t[0].row,s=Kl(t),l=A(s,((e,t)=>({grid:hl(e.grid,r,t.row+e.delta,o,n.getOrInit),delta:e.delta+1})),{grid:e,delta:0}).grid;return Gl(l,r,t[0].column)}),Xs,f,f,_l),ma=Js(((e,t,o,n)=>{const r=Kl(t),s=r[r.length-1],l=s.row+s.rowspan,a=A(r,((e,t)=>hl(e,l,t.row,o,n.getOrInit)),e);return Gl(a,l,t[0].column)}),Xs,f,f,_l),da=Js(((e,t,o,n)=>{const r=t.details,s=Nl(r),l=s[0].column,a=A(s,((e,t)=>({grid:pl(e.grid,l,t.column+e.delta,o,n.getOrInit),delta:e.delta+1})),{grid:e,delta:0}).grid;return Gl(a,r[0].row,l)}),sa(!0),ta,f,_l),ua=Js(((e,t,o,n)=>{const r=t.details,s=r[r.length-1],l=s.column+s.colspan,a=Nl(r),c=A(a,((e,t)=>pl(e,l,t.column,o,n.getOrInit)),e);return Gl(c,r[0].row,l)}),sa(!1),ta,f,_l),fa=Js(((e,t,o,n)=>{const r=Nl(t.details),s=((e,t)=>j(e,(e=>{const o=e.cells,n=A(t,((e,t)=>t>=0&&t<e.length?e.slice(0,t).concat(e.slice(t+1)):e),o);return n.length>0?[Ye(e.element,n,e.section,e.isNew)]:[]})))(e,E(r,(e=>e.column))),l=s.length>0?s[0].cells.length-1:0;return Gl(s,r[0].row,Math.min(r[0].column,l))}),((e,t)=>Zs(e,t).map((t=>({details:t,pixelDelta:-ra(e,t)})))),ta,$l,_l),ga=Js(((e,t,o,n)=>{const r=Kl(t),s=((e,t,o)=>{const{rows:n,cols:r}=jo(e);return[...r,...n.slice(0,t),...n.slice(o+1)]})(e,r[0].row,r[r.length-1].row),l=s.length>0?s.length-1:0;return Gl(s,Math.min(t[0].row,l),t[0].column)}),Xs,f,$l,_l),ha=Js(((e,t,o,n)=>{const r=Nl(t),s=E(r,(e=>e.column)),l=Cl(e,s,!0,o,n.replaceOrInit);return Gl(l,t[0].row,t[0].column)}),Zs,f,f,aa),pa=Js(((e,t,o,n)=>{const r=Nl(t),s=E(r,(e=>e.column)),l=Cl(e,s,!1,o,n.replaceOrInit);return Gl(l,t[0].row,t[0].column)}),Zs,f,f,ca),wa=Js(Jl,Zs,f,f,aa),ba=Js(Ql,Zs,f,f,ca),va=Js(Xl,Zs,f,f,ca),ya=Js(((e,t,o,n)=>{const r=Sl(e,t,o,n.replaceOrInit);return Gl(r,t[0].row,t[0].column)}),Zs,f,f,aa),xa=Js(((e,t,o,n)=>{const r=Sl(e,t,o,n.replaceOrInit);return Gl(r,t[0].row,t[0].column)}),Zs,f,f,ca),Ca=Js(((e,t,o,n)=>{const r=t.cells;ql(r);const s=((e,t,o,n)=>{const r=jo(e).rows;if(0===r.length)return e;for(let e=t.startRow;e<=t.finishRow;e++)for(let o=t.startCol;o<=t.finishCol;o++){const t=r[e],s=Lo(t,o).isLocked;Ao(t,o,Ke(n(),!1,s))}return e})(e,t.bounds,0,n.merge(r));return Ul(s,C.from(r[0]))}),((e,t)=>((e,t)=>t.mergable)(0,t).filter((t=>el(e,t.cells)))),ea,f,jl),Sa=Js(((e,t,o,n)=>{const r=A(t,((e,t)=>tl(e,t,o,n.unmerge(t))),e);return Ul(r,C.from(t[0]))}),((e,t)=>((e,t)=>t.unmergable)(0,t).filter((t=>el(e,t)))),ea,f,jl),Ta=Js(((e,t,o,n)=>{const r=((e,t)=>{const o=Uo(e);return Us(o,t,!0)})(t.clipboard,t.generators);var s,l;return((e,t,o,n,r)=>{const s=Ho(t),l=((e,t,o)=>{const n=Mo(t[0]),r=jo(t).cols.length+e.row,s=k(n-e.column,(t=>t+e.column));return{row:r,column:L(s,(e=>I(o,(t=>t!==e)))).getOr(n-1)}})(e,t,s),a=jo(o).rows,c=ul(l,a,s),i=((e,t,o)=>{if(e.row>=t.length||e.column>Mo(t[0]))return rl.error("invalid start address out of table bounds, row: "+e.row+", column: "+e.column);const n=t.slice(e.row),r=n[0].cells.slice(e.column),s=Mo(o[0]),l=o.length;return rl.value({rowDelta:n.length-l,colDelta:r.length-s})})(l,t,a);return i.map((e=>{const o={...e,colDelta:e.colDelta-c.length},s=ml(t,o,n),i=Ho(s),m=ul(l,a,i);return((e,t,o,n,r,s)=>{const l=e.row,a=e.column,c=l+o.length,i=a+Mo(o[0])+s.length,m=P(s,x);for(let e=l;e<c;e++){let s=0;for(let c=a;c<i;c++){if(m[c]){s++;continue}dl(t,e,c,r)&&tl(t,_o(t[e],c),r,n.cell);const i=c-a-s,d=Lo(o[e-l],i),u=d.element,f=n.replace(u);Ao(t[e],c,Ke(f,!0,d.isLocked))}}return t})(l,s,a,n,r,m)}))})((s=t.row,l=t.column,{row:s,column:l}),e,r,t.generators,o).fold((()=>Ul(e,C.some(t.element))),(e=>Gl(e,t.row,t.column)))}),((e,t)=>jt(t.element).bind((o=>Ks(e,o).map((e=>({...e,generators:t.generators,clipboard:t.clipboard})))))),ea,f,_l),Ra=Js(((e,t,o,n)=>{const r=jo(e).rows,s=t.cells[0].column,l=r[t.cells[0].row],a=Zl(t.clipboard,t.generators,l),c=fl(s,e,a,t.generators,o);return Gl(c,t.cells[0].row,t.cells[0].column)}),la(!0),f,f,_l),Da=Js(((e,t,o,n)=>{const r=jo(e).rows,s=t.cells[t.cells.length-1].column+t.cells[t.cells.length-1].colspan,l=r[t.cells[0].row],a=Zl(t.clipboard,t.generators,l),c=fl(s,e,a,t.generators,o);return Gl(c,t.cells[0].row,t.cells[0].column)}),la(!1),f,f,_l),Oa=Js(((e,t,o,n)=>{const r=jo(e).rows,s=t.cells[0].row,l=r[s],a=Zl(t.clipboard,t.generators,l),c=gl(s,e,a,t.generators,o);return Gl(c,t.cells[0].row,t.cells[0].column)}),Qs,f,f,_l),ka=Js(((e,t,o,n)=>{const r=jo(e).rows,s=t.cells[t.cells.length-1].row+t.cells[t.cells.length-1].rowspan,l=r[t.cells[0].row],a=Zl(t.clipboard,t.generators,l),c=gl(s,e,a,t.generators,o);return Gl(c,t.cells[0].row,t.cells[0].column)}),Qs,f,f,_l),Ea=(e,t)=>{const o=Uo(e);return Xs(o,t).bind((e=>{const t=e[e.length-1],n=e[0].column,r=t.column+t.colspan,s=M(E(o.all,(e=>z(e.cells,(e=>e.column>=n&&e.column<r)))));return ks(s)})).getOr("")},Na=(e,t)=>{const o=Uo(e);return Xs(o,t).bind(ks).getOr("")},Ba=(e,t)=>{const o=Uo(e);return Xs(o,t).bind((e=>{const t=e[e.length-1],n=e[0].row,r=t.row+t.rowspan;return(e=>{const t=E(e,(e=>Os(e).type)),o=D(t,"header"),n=D(t,"footer");if(o||n){const e=D(t,"body");return!o||e||n?o||e||!n?C.none():C.some("footer"):C.some("header")}return C.some("body")})(o.all.slice(n,r))})).getOr("")},za=(e,t)=>e.dispatch("NewRow",{node:t}),Aa=(e,t)=>e.dispatch("NewCell",{node:t}),Wa=(e,t,o)=>{e.dispatch("TableModified",{...o,table:t})},La={structure:!1,style:!0},_a={structure:!0,style:!1},Ma={structure:!0,style:!0},ja=e=>t=>t.options.get(e),Ia="100%",Pa=e=>{var t;const o=e.dom,n=null!==(t=o.getParent(e.selection.getStart(),o.isBlock))&&void 0!==t?t:e.getBody();return No(pe.fromDom(n))+"px"},Fa=e=>C.from(e.options.get("table_clone_elements")),Ha=ja("table_header_type"),qa=ja("table_column_resizing"),Va=e=>"preservetable"===qa(e),$a=e=>"resizetable"===qa(e),Ua=ja("table_sizing_mode"),Ga=e=>"relative"===Ua(e),Ka=e=>"fixed"===Ua(e),Ya=e=>"responsive"===Ua(e),Ja=ja("table_resize_bars"),Qa=ja("table_style_by_css"),Xa=e=>{const t=e.options,o=t.get("table_default_attributes");return t.isSet("table_default_attributes")?o:((e,t)=>Ya(e)||Qa(e)?t:Ka(e)?{...t,width:Pa(e)}:{...t,width:Ia})(e,o)},Za=ja("table_use_colgroups"),ec=(e,t)=>Ga(e)?sr(t):Ka(e)?rr(t):nr(t),tc=(e,t,o)=>{const n=e=>"table"===Z(Dr(e)),r=Fa(e),s=$a(e)?f:hs,l=t=>{switch(Ha(e)){case"section":return Ws();case"sectionCells":return Ls();case"cells":return _s();default:return((e,t)=>{var o;switch((o=Uo(e),V(o.all,(e=>{const t=Os(e);return"header"===t.type?C.from(t.subType):C.none()}))).getOr(t)){case"section":return Bs();case"sectionCells":return zs();case"cells":return As()}})(t,"section")}},a=(n,s,a,c)=>(i,m,d=!1)=>{kr(i);const u=pe.fromDom(e.getDoc()),f=Tr(a,u,r),g={sizing:ec(e,i),resize:$a(e)?xs():Cs(),section:l(i)};return s(i)?n(i,m,f,g).bind((n=>{t.refresh(i.dom),N(n.newRows,(t=>{za(e,t.dom)})),N(n.newCells,(t=>{Aa(e,t.dom)}));const r=((t,n)=>n.cursor.fold((()=>{const n=It(t);return H(n).filter(et).map((n=>{o.clearSelectedCells(t.dom);const r=e.dom.createRng();return r.selectNode(n.dom),e.selection.setRng(r),ie(n,"data-mce-selected","1"),r}))}),(n=>{const r=fs(gs,n),s=e.dom.createRng();return s.setStart(r.element.dom,r.offset),s.setEnd(r.element.dom,r.offset),e.selection.setRng(s),o.clearSelectedCells(t.dom),C.some(s)})))(i,n);return et(i)&&(kr(i),d||Wa(e,i.dom,c)),r.map((e=>({rng:e,effect:c})))})):C.none()},c=a(ga,(t=>!n(e)||Ss(t).rows>1),f,_a),i=a(fa,(t=>!n(e)||Ss(t).columns>1),f,_a);return{deleteRow:c,deleteColumn:i,insertRowsBefore:a(ia,x,f,_a),insertRowsAfter:a(ma,x,f,_a),insertColumnsBefore:a(da,x,s,_a),insertColumnsAfter:a(ua,x,s,_a),mergeCells:a(Ca,x,f,_a),unmergeCells:a(Sa,x,f,_a),pasteColsBefore:a(Ra,x,f,_a),pasteColsAfter:a(Da,x,f,_a),pasteRowsBefore:a(Oa,x,f,_a),pasteRowsAfter:a(ka,x,f,_a),pasteCells:a(Ta,x,f,Ma),makeCellsHeader:a(ya,x,f,_a),unmakeCellsHeader:a(xa,x,f,_a),makeColumnsHeader:a(ha,x,f,_a),unmakeColumnsHeader:a(pa,x,f,_a),makeRowsHeader:a(wa,x,f,_a),makeRowsBody:a(ba,x,f,_a),makeRowsFooter:a(va,x,f,_a),getTableRowType:Ba,getTableCellType:Na,getTableColType:Ea}},oc=(e,t,o)=>{const n=Et(e,t,1);1===o||n<=1?fe(e,t):ie(e,t,Math.min(o,n))},nc=(e,t)=>o=>{const n=o.column+o.colspan-1,r=o.column;return n>=e&&r<t},rc=Tl([{invalid:["raw"]},{pixels:["value"]},{percent:["value"]}]),sc=(e,t,o)=>{const n=o.substring(0,o.length-e.length),r=parseFloat(n);return n===r.toString()?t(r):rc.invalid(o)},lc={...rc,from:e=>bt(e,"%")?sc("%",rc.percent,e):bt(e,"px")?sc("px",rc.pixels,e):rc.invalid(e)},ac=(e,t,o)=>{const n=lc.from(o),r=I(e,(e=>"0px"===e))?((e,t)=>{const o=e.fold((()=>g("")),(e=>g(e/t+"px")),(()=>g(100/t+"%")));return k(t,o)})(n,e.length):((e,t,o)=>e.fold((()=>t),(e=>((e,t,o)=>{const n=o/t;return E(e,(e=>lc.from(e).fold((()=>e),(e=>e*n+"px"),(e=>e/100*o+"px"))))})(t,o,e)),(e=>((e,t)=>E(e,(e=>lc.from(e).fold((()=>e),(e=>e/t*100+"%"),(e=>e+"%")))))(t,o))))(n,e,t);return mc(r)},cc=(e,t)=>0===e.length?t:A(e,((e,t)=>lc.from(t).fold(g(0),h,h)+e),0),ic=(e,t)=>lc.from(e).fold(g(e),(e=>e+t+"px"),(e=>e+t+"%")),mc=e=>{if(0===e.length)return e;const t=A(e,((e,t)=>{const o=lc.from(t).fold((()=>({value:t,remainder:0})),(e=>((e,t)=>{const o=Math.floor(e);return{value:o+"px",remainder:e-o}})(e)),(e=>({value:e+"%",remainder:0})));return{output:[o.value].concat(e.output),remainder:e.remainder+o.remainder}}),{output:[],remainder:0}),o=t.output;return o.slice(0,o.length-1).concat([ic(o[o.length-1],Math.round(t.remainder))])},dc=lc.from,uc=e=>dc(e).fold(g("px"),g("px"),g("%")),fc=(e,t,o)=>{const n=Uo(e),r=n.all,s=Qo(n),l=Xo(n);t.each((t=>{const o=uc(t),r=ko(e),a=((e,t)=>Jn(e,t,Gn,Qn))(n,e),c=ac(a,r,t);Zo(n)?((e,t,o)=>{N(t,((t,n)=>{const r=cc([e[n]],Wt());St(t.element,"width",r+o)}))})(c,l,o):((e,t,o)=>{N(t,(t=>{const n=e.slice(t.column,t.colspan+t.column),r=cc(n,Wt());St(t.element,"width",r+o)}))})(c,s,o),St(e,"width",t)})),o.each((t=>{const o=uc(t),l=an(e),a=((e,t,o)=>Zn(e,t,o,Kn,Qn))(n,e,Rn);((e,t,o,n)=>{N(o,(t=>{const o=e.slice(t.row,t.rowspan+t.row),r=cc(o,Lt());St(t.element,"height",r+n)})),N(t,((t,o)=>{St(t.element,"height",e[o])}))})(ac(a,l,t),r,s,o),St(e,"height",t)}))},gc=e=>In(e).exists((e=>Nn.test(e))),hc=e=>In(e).exists((e=>Bn.test(e))),pc=e=>In(e).isNone(),wc=e=>{fe(e,"width")},bc=e=>{const t=Vn(e);fc(e,C.some(t),C.none()),wc(e)},vc=e=>{const t=(e=>ko(e)+"px")(e);fc(e,C.some(t),C.none()),wc(e)},yc=e=>{kt(e,"width");const t=Pt(e),o=t.length>0?t:It(e);N(o,(e=>{kt(e,"width"),wc(e)})),wc(e)},xc={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},colGroups:!1},Cc=(e,t,o,n)=>k(e,(e=>((e,t,o,n)=>{const r=pe.fromTag("tr");for(let s=0;s<e;s++){const e=pe.fromTag(n<t||s<o?"th":"td");s<o&&ie(e,"scope","row"),n<t&&ie(e,"scope","col"),We(e,pe.fromTag("br")),We(r,e)}return r})(t,o,n,e))),Sc=(e,t)=>{e.selection.select(t.dom,!0),e.selection.collapse(!0)},Tc=(e,t,o,n,s)=>{const l=(e=>{const t=e.options,o=t.get("table_default_styles");return t.isSet("table_default_styles")?o:((e,t)=>Ya(e)||!Qa(e)?t:Ka(e)?{...t,width:Pa(e)}:{...t,width:Ia})(e,o)})(e),a={styles:l,attributes:Xa(e),colGroups:Za(e)};return e.undoManager.ignore((()=>{const r=((e,t,o,n,r,s=xc)=>{const l=pe.fromTag("table"),a="cells"!==r;Tt(l,s.styles),me(l,s.attributes),s.colGroups&&We(l,(e=>{const t=pe.fromTag("colgroup");return k(e,(()=>We(t,pe.fromTag("col")))),t})(t));const c=Math.min(e,o);if(a&&o>0){const e=pe.fromTag("thead");We(l,e);const s=Cc(o,t,"sectionCells"===r?c:0,n);Me(e,s)}const i=pe.fromTag("tbody");We(l,i);const m=Cc(a?e-c:e,t,a?0:o,n);return Me(i,m),l})(o,t,s,n,Ha(e),a);ie(r,"data-mce-id","__mce");const l=(e=>{const t=pe.fromTag("div"),o=pe.fromDom(e.dom.cloneNode(!0));return We(t,o),(e=>e.dom.innerHTML)(t)})(r);e.insertContent(l),e.addVisual()})),mt(Dr(e),'table[data-mce-id="__mce"]').map((t=>(Ka(e)?vc(t):Ya(e)?yc(t):(Ga(e)||(e=>r(e)&&-1!==e.indexOf("%"))(l.width))&&bc(t),kr(t),fe(t,"data-mce-id"),((e,t)=>{N(st(t,"tr"),(t=>{za(e,t.dom),N(st(t,"th,td"),(t=>{Aa(e,t.dom)}))}))})(e,t),((e,t)=>{mt(t,"td,th").each(w(Sc,e))})(e,t),t.dom))).getOrNull()};var Rc=tinymce.util.Tools.resolve("tinymce.FakeClipboard");const Dc="x-tinymce/dom-table-",Oc=Dc+"rows",kc=Dc+"columns",Ec=e=>{const t=Rc.FakeClipboardItem(e);Rc.write([t])},Nc=e=>{var t;const o=null!==(t=Rc.read())&&void 0!==t?t:[];return V(o,(t=>C.from(t.getType(e))))},Bc=e=>{Nc(e).isSome()&&Rc.clear()},zc=e=>{e.fold(Wc,(e=>Ec({[Oc]:e})))},Ac=()=>Nc(Oc),Wc=()=>Bc(Oc),Lc=e=>{e.fold(Mc,(e=>Ec({[kc]:e})))},_c=()=>Nc(kc),Mc=()=>Bc(kc),jc=e=>ss(Er(e),Or(e)),Ic=(e,t)=>{const o=Or(e),n=e=>Ft(e,o),l=t=>(e=>ls(Er(e),Or(e)))(e).bind((e=>n(e).map((o=>t(o,e))))),a=t=>{e.focus()},c=(t,o=!1)=>l(((n,r)=>{const s=ns(as(e),n,r);t(n,s,o).each(a)})),i=()=>l(((t,o)=>((e,t,o)=>{const n=Uo(e);return Xs(n,t).bind((e=>{const t=Us(n,o,!1),r=jo(t).rows.slice(e[0].row,e[e.length-1].row+e[e.length-1].rowspan),s=j(r,(e=>{const t=z(e.cells,(e=>!e.isLocked));return t.length>0?[{...e,cells:t}]:[]})),l=Gs(s);return gt(l.length>0,l)})).map((e=>E(e,(e=>{const t=He(e.element);return N(e.cells,(e=>{const o=qe(e.element);Fs(o,"colspan",e.colspan,1),Fs(o,"rowspan",e.rowspan,1),We(t,o)})),t}))))})(t,ns(as(e),t,o),Tr(f,pe.fromDom(e.getDoc()),C.none())))),m=()=>l(((t,o)=>((e,t)=>{const o=Uo(e);return Zs(o,t).map((e=>{const t=e[e.length-1],n=e[0].column,r=t.column+t.colspan,s=((e,t,o)=>{if(Zo(e)){const n=z(Xo(e),nc(t,o)),r=E(n,(e=>{const n=qe(e.element);return oc(n,"span",o-t),n})),s=pe.fromTag("colgroup");return Me(s,r),[s]}return[]})(o,n,r),l=((e,t,o)=>E(e.all,(e=>{const n=z(e.cells,nc(t,o)),r=E(n,(e=>{const n=qe(e.element);return oc(n,"colspan",o-t),n})),s=pe.fromTag("tr");return Me(s,r),s})))(o,n,r);return[...s,...l]}))})(t,ns(as(e),t,o)))),d=(t,o)=>o().each((o=>{const n=E(o,(e=>qe(e)));l(((o,r)=>{const s=Rr(pe.fromDom(e.getDoc())),l=((e,t,o,n)=>({selection:Zr(e),clipboard:o,generators:n}))(as(e),0,n,s);t(o,l).each(a)}))})),g=e=>(t,o)=>((e,t)=>X(e,t)?C.from(e.type):C.none())(o,"type").each((t=>{c(e(t),o.no_events)}));G({mceTableSplitCells:()=>c(t.unmergeCells),mceTableMergeCells:()=>c(t.mergeCells),mceTableInsertRowBefore:()=>c(t.insertRowsBefore),mceTableInsertRowAfter:()=>c(t.insertRowsAfter),mceTableInsertColBefore:()=>c(t.insertColumnsBefore),mceTableInsertColAfter:()=>c(t.insertColumnsAfter),mceTableDeleteCol:()=>c(t.deleteColumn),mceTableDeleteRow:()=>c(t.deleteRow),mceTableCutCol:()=>m().each((e=>{Lc(e),c(t.deleteColumn)})),mceTableCutRow:()=>i().each((e=>{zc(e),c(t.deleteRow)})),mceTableCopyCol:()=>m().each((e=>Lc(e))),mceTableCopyRow:()=>i().each((e=>zc(e))),mceTablePasteColBefore:()=>d(t.pasteColsBefore,_c),mceTablePasteColAfter:()=>d(t.pasteColsAfter,_c),mceTablePasteRowBefore:()=>d(t.pasteRowsBefore,Ac),mceTablePasteRowAfter:()=>d(t.pasteRowsAfter,Ac),mceTableDelete:()=>jc(e).each((t=>{Ft(t,o).filter(b(o)).each((t=>{const o=pe.fromText("");if(ze(t,o),Ie(t),e.dom.isEmpty(e.getBody()))e.setContent(""),e.selection.setCursorLocation();else{const t=e.dom.createRng();t.setStart(o.dom,0),t.setEnd(o.dom,0),e.selection.setRng(t),e.nodeChanged()}}))})),mceTableCellToggleClass:(t,o)=>{l((t=>{const n=as(e),r=I(n,(t=>e.formatter.match("tablecellclass",{value:o},t.dom))),s=r?e.formatter.remove:e.formatter.apply;N(n,(e=>s("tablecellclass",{value:o},e.dom))),Wa(e,t.dom,La)}))},mceTableToggleClass:(t,o)=>{l((t=>{e.formatter.toggle("tableclass",{value:o},t.dom),Wa(e,t.dom,La)}))},mceTableToggleCaption:()=>{jc(e).each((t=>{Ft(t,o).each((o=>{it(o,"caption").fold((()=>{const t=pe.fromTag("caption");We(t,pe.fromText("Caption")),((e,t,o)=>{Ne(e,0).fold((()=>{We(e,t)}),(e=>{Be(e,t)}))})(o,t),e.selection.setCursorLocation(t.dom,0)}),(n=>{ae("caption")(t)&&ve("td",o).each((t=>e.selection.setCursorLocation(t.dom,0))),Ie(n)})),Wa(e,o.dom,_a)}))}))},mceTableSizingMode:(t,n)=>(t=>jc(e).each((n=>{Ya(e)||Ka(e)||Ga(e)||Ft(n,o).each((o=>{"relative"!==t||gc(o)?"fixed"!==t||hc(o)?"responsive"!==t||pc(o)||yc(o):vc(o):bc(o),kr(o),Wa(e,o.dom,_a)}))})))(n),mceTableCellType:g((e=>"th"===e?t.makeCellsHeader:t.unmakeCellsHeader)),mceTableColType:g((e=>"th"===e?t.makeColumnsHeader:t.unmakeColumnsHeader)),mceTableRowType:g((e=>{switch(e){case"header":return t.makeRowsHeader;case"footer":return t.makeRowsFooter;default:return t.makeRowsBody}}))},((t,o)=>e.addCommand(o,t))),e.addCommand("mceInsertTable",((t,o)=>{((e,t,o,n={})=>{const r=e=>u(e)&&e>0;if(r(t)&&r(o)){const r=n.headerRows||0,s=n.headerColumns||0;return Tc(e,o,t,s,r)}console.error("Invalid values for mceInsertTable - rows and columns values are required to insert a table.")})(e,o.rows,o.columns,o.options)})),e.addCommand("mceTableApplyCellStyle",((t,o)=>{const l=e=>"tablecell"+e.toLowerCase().replace("-","");if(!s(o))return;const a=as(e);if(0===a.length)return;const c=((e,t)=>{const o={};return((e,t,o,n)=>{G(e,((e,r)=>{(t(e,r)?o:n)(e,r)}))})(e,t,(e=>(t,o)=>{e[o]=t})(o),f),o})(o,((t,o)=>e.formatter.has(l(o))&&r(t)));(e=>{for(const t in e)if(U.call(e,t))return!1;return!0})(c)||(G(c,((t,o)=>{const n=l(o);N(a,(o=>{""===t?e.formatter.remove(n,{value:null},o.dom,!0):e.formatter.apply(n,{value:t},o.dom)}))})),n(a[0]).each((t=>Wa(e,t.dom,La))))}))},Pc=Tl([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Fc={before:Pc.before,on:Pc.on,after:Pc.after,cata:(e,t,o,n)=>e.fold(t,o,n),getStart:e=>e.fold(h,h,h)},Hc=(e,t)=>({selection:e,kill:t}),qc=(e,t)=>{const o=e.document.createRange();return o.selectNode(t.dom),o},Vc=(e,t)=>{const o=e.document.createRange();return $c(o,t),o},$c=(e,t)=>e.selectNodeContents(t.dom),Uc=(e,t,o)=>{const n=e.document.createRange();var r;return r=n,t.fold((e=>{r.setStartBefore(e.dom)}),((e,t)=>{r.setStart(e.dom,t)}),(e=>{r.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},Gc=(e,t,o,n,r)=>{const s=e.document.createRange();return s.setStart(t.dom,o),s.setEnd(n.dom,r),s},Kc=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),Yc=Tl([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Jc=(e,t,o)=>t(pe.fromDom(o.startContainer),o.startOffset,pe.fromDom(o.endContainer),o.endOffset),Qc=(e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:g(e),rtl:C.none}),relative:(t,o)=>({ltr:Gt((()=>Uc(e,t,o))),rtl:Gt((()=>C.some(Uc(e,o,t))))}),exact:(t,o,n,r)=>({ltr:Gt((()=>Gc(e,t,o,n,r))),rtl:Gt((()=>C.some(Gc(e,n,r,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>Yc.rtl(pe.fromDom(e.endContainer),e.endOffset,pe.fromDom(e.startContainer),e.startOffset))).getOrThunk((()=>Jc(0,Yc.ltr,o))):Jc(0,Yc.ltr,o)})(0,o)},Xc=(e,t)=>Qc(e,t).match({ltr:(t,o,n,r)=>{const s=e.document.createRange();return s.setStart(t.dom,o),s.setEnd(n.dom,r),s},rtl:(t,o,n,r)=>{const s=e.document.createRange();return s.setStart(n.dom,r),s.setEnd(t.dom,o),s}});Yc.ltr,Yc.rtl;const Zc=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),ei=(e,t,o,n)=>({start:Fc.on(e,t),finish:Fc.on(o,n)}),ti=(e,t)=>{const o=Xc(e,t);return Zc(pe.fromDom(o.startContainer),o.startOffset,pe.fromDom(o.endContainer),o.endOffset)},oi=ei,ni=(e,t,o,n,r)=>ye(o,n)?C.none():Gr(o,n,t).bind((t=>{const n=t.boxes.getOr([]);return n.length>1?(r(e,n,t.start,t.finish),C.some(Hc(C.some(oi(o,0,o,dr(o))),!0))):C.none()})),ri=(e,t)=>({item:e,mode:t}),si=(e,t,o,n=li)=>e.property().parent(t).map((e=>ri(e,n))),li=(e,t,o,n=ai)=>o.sibling(e,t).map((e=>ri(e,n))),ai=(e,t,o,n=ai)=>{const r=e.property().children(t);return o.first(r).map((e=>ri(e,n)))},ci=[{current:si,next:li,fallback:C.none()},{current:li,next:ai,fallback:C.some(si)},{current:ai,next:ai,fallback:C.some(li)}],ii=(e,t,o,n,r=ci)=>L(r,(e=>e.current===o)).bind((o=>o.current(e,t,n,o.next).orThunk((()=>o.fallback.bind((o=>ii(e,t,o,n))))))),mi=(e,t,o,n,r,s)=>ii(e,t,n,r).bind((t=>s(t.item)?C.none():o(t.item)?C.some(t.item):mi(e,t.item,o,t.mode,r,s))),di=e=>t=>0===e.property().children(t).length,ui=(e,t,o,n)=>mi(e,t,o,li,{sibling:(e,t)=>e.query().prevSibling(t),first:e=>e.length>0?C.some(e[e.length-1]):C.none()},n),fi=(e,t,o,n)=>mi(e,t,o,li,{sibling:(e,t)=>e.query().nextSibling(t),first:e=>e.length>0?C.some(e[0]):C.none()},n),gi=Fr(),hi=(e,t)=>((e,t,o)=>ui(e,t,di(e),o))(gi,e,t),pi=(e,t)=>((e,t,o)=>fi(e,t,di(e),o))(gi,e,t),wi=Tl([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),bi=e=>dt(e,"tr"),vi={...wi,verify:(e,t,o,n,r,s,l)=>dt(n,"td,th",l).bind((o=>dt(t,"td,th",l).map((t=>ye(o,t)?ye(n,o)&&dr(o)===r?s(t):wi.none("in same cell"):$r(bi,[o,t]).fold((()=>((e,t,o)=>{const n=e.getRect(t),r=e.getRect(o);return r.right>n.left&&r.left<n.right})(e,t,o)?wi.success():s(t)),(e=>s(t))))))).getOr(wi.none("default")),cata:(e,t,o,n,r)=>e.fold(t,o,n,r)},yi=ae("br"),xi=(e,t,o)=>t(e,o).bind((e=>re(e)&&0===cr(e).trim().length?xi(e,t,o):C.some(e))),Ci=(e,t,o,n)=>((e,t)=>Ne(e,t).filter(yi).orThunk((()=>Ne(e,t-1).filter(yi))))(t,o).bind((t=>n.traverse(t).fold((()=>xi(t,n.gather,e).map(n.relative)),(e=>(e=>Re(e).bind((t=>{const o=Ee(t);return((e,t)=>_(e,w(ye,t)))(o,e).map((n=>((e,t,o,n)=>({parent:e,children:t,element:o,index:n}))(t,o,e,n)))})))(e).map((e=>Fc.on(e.parent,e.index))))))),Si=(e,t)=>({left:e.left,top:e.top+t,right:e.right,bottom:e.bottom+t}),Ti=(e,t)=>({left:e.left,top:e.top-t,right:e.right,bottom:e.bottom-t}),Ri=(e,t,o)=>({left:e.left+t,top:e.top+o,right:e.right+t,bottom:e.bottom+o}),Di=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom}),Oi=(e,t)=>C.some(e.getRect(t)),ki=(e,t,o)=>ne(t)?Oi(e,t).map(Di):re(t)?((e,t,o)=>o>=0&&o<dr(t)?e.getRangedRect(t,o,t,o+1):o>0?e.getRangedRect(t,o-1,t,o):C.none())(e,t,o).map(Di):C.none(),Ei=(e,t)=>ne(t)?Oi(e,t).map(Di):re(t)?e.getRangedRect(t,0,t,dr(t)).map(Di):C.none(),Ni=Tl([{none:[]},{retry:["caret"]}]),Bi=(e,t,o)=>{return(n=t,r=Fl,lt(((e,t)=>t(e)),at,n,r,undefined)).fold(y,(t=>Ei(e,t).exists((e=>((e,t)=>e.left<t.left||Math.abs(t.right-e.left)<1||e.left>t.right)(o,e)))));var n,r},zi={point:e=>e.bottom,adjuster:(e,t,o,n,r)=>{const s=Si(r,5);return Math.abs(o.bottom-n.bottom)<1||o.top>r.bottom?Ni.retry(s):o.top===r.bottom?Ni.retry(Si(r,1)):Bi(e,t,r)?Ni.retry(Ri(s,5,0)):Ni.none()},move:Si,gather:pi},Ai=(e,t,o,n,r)=>0===r?C.some(n):((e,t,o)=>e.elementFromPoint(t,o).filter((e=>"table"===Z(e))).isSome())(e,n.left,t.point(n))?((e,t,o,n,r)=>Ai(e,t,o,t.move(n,5),r))(e,t,o,n,r-1):e.situsFromPoint(n.left,t.point(n)).bind((s=>s.start.fold(C.none,(s=>Ei(e,s).bind((l=>t.adjuster(e,s,l,o,n).fold(C.none,(n=>Ai(e,t,o,n,r-1))))).orThunk((()=>C.some(n)))),C.none))),Wi=(e,t,o)=>{const n=e.move(o,5),r=Ai(t,e,o,n,100).getOr(n);return((e,t,o)=>e.point(t)>o.getInnerHeight()?C.some(e.point(t)-o.getInnerHeight()):e.point(t)<0?C.some(-e.point(t)):C.none())(e,r,t).fold((()=>t.situsFromPoint(r.left,e.point(r))),(o=>(t.scrollBy(0,o),t.situsFromPoint(r.left,e.point(r)-o))))},Li={tryUp:w(Wi,{point:e=>e.top,adjuster:(e,t,o,n,r)=>{const s=Ti(r,5);return Math.abs(o.top-n.top)<1||o.bottom<r.top?Ni.retry(s):o.bottom===r.top?Ni.retry(Ti(r,1)):Bi(e,t,r)?Ni.retry(Ri(s,5,0)):Ni.none()},move:Ti,gather:hi}),tryDown:w(Wi,zi),getJumpSize:g(5)},_i=(e,t,o)=>e.getSelection().bind((n=>((e,t,o,n)=>{const r=yi(t)?((e,t,o)=>o.traverse(t).orThunk((()=>xi(t,o.gather,e))).map(o.relative))(e,t,n):Ci(e,t,o,n);return r.map((e=>({start:e,finish:e})))})(t,n.finish,n.foffset,o).fold((()=>C.some(is(n.finish,n.foffset))),(r=>{const s=e.fromSitus(r);return l=vi.verify(e,n.finish,n.foffset,s.finish,s.foffset,o.failure,t),vi.cata(l,(e=>C.none()),(()=>C.none()),(e=>C.some(is(e,0))),(e=>C.some(is(e,dr(e)))));var l})))),Mi=(e,t,o,n,r,s)=>0===s?C.none():Pi(e,t,o,n,r).bind((l=>{const a=e.fromSitus(l),c=vi.verify(e,o,n,a.finish,a.foffset,r.failure,t);return vi.cata(c,(()=>C.none()),(()=>C.some(l)),(l=>ye(o,l)&&0===n?ji(e,o,n,Ti,r):Mi(e,t,l,0,r,s-1)),(l=>ye(o,l)&&n===dr(l)?ji(e,o,n,Si,r):Mi(e,t,l,dr(l),r,s-1)))})),ji=(e,t,o,n,r)=>ki(e,t,o).bind((t=>Ii(e,r,n(t,Li.getJumpSize())))),Ii=(e,t,o)=>{const n=To().browser;return n.isChromium()||n.isSafari()||n.isFirefox()?t.retry(e,o):C.none()},Pi=(e,t,o,n,r)=>ki(e,o,n).bind((t=>Ii(e,r,t))),Fi=(e,t,o,n,r)=>dt(n,"td,th",t).bind((n=>dt(n,"table",t).bind((s=>((e,t)=>at(e,(e=>Re(e).exists((e=>ye(e,t)))),void 0).isSome())(r,s)?((e,t,o)=>_i(e,t,o).bind((n=>Mi(e,t,n.element,n.offset,o,20).map(e.fromSitus))))(e,t,o).bind((e=>dt(e.finish,"td,th",t).map((t=>({start:n,finish:t,range:e}))))):C.none())))),Hi=(e,t,o,n,r,s)=>s(n,t).orThunk((()=>Fi(e,t,o,n,r).map((e=>{const t=e.range;return Hc(C.some(oi(t.start,t.soffset,t.finish,t.foffset)),!0)})))),qi=(e,t)=>dt(e,"tr",t).bind((e=>dt(e,"table",t).bind((o=>{const n=st(o,"tr");return ye(e,n[0])?((e,t,o)=>ui(gi,e,(e=>hr(e).isSome()),o))(o,0,t).map((e=>{const t=dr(e);return Hc(C.some(oi(e,t,e,t)),!0)})):C.none()})))),Vi=(e,t)=>dt(e,"tr",t).bind((e=>dt(e,"table",t).bind((o=>{const n=st(o,"tr");return ye(e,n[n.length-1])?((e,t,o)=>fi(gi,e,(e=>gr(e).isSome()),o))(o,0,t).map((e=>Hc(C.some(oi(e,0,e,0)),!0))):C.none()})))),$i=(e,t,o,n,r,s,l)=>Fi(e,o,n,r,s).bind((e=>ni(t,o,e.start,e.finish,l))),Ui=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},Gi=()=>{const e=(e=>{const t=Ui(C.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(C.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(C.some(e))}}})(f);return{...e,on:t=>e.get().each(t)}},Ki=(e,t)=>dt(e,"td,th",t),Yi={traverse:ke,gather:pi,relative:Fc.before,retry:Li.tryDown,failure:vi.failedDown},Ji={traverse:Oe,gather:hi,relative:Fc.before,retry:Li.tryUp,failure:vi.failedUp},Qi=e=>t=>t===e,Xi=Qi(38),Zi=Qi(40),em=e=>e>=37&&e<=40,tm={isBackward:Qi(37),isForward:Qi(39)},om={isBackward:Qi(39),isForward:Qi(37)},nm=Tl([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),rm={domRange:nm.domRange,relative:nm.relative,exact:nm.exact,exactFromRange:e=>nm.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>pe.fromDom(e.startContainer),relative:(e,t)=>Fc.getStart(e),exact:(e,t,o,n)=>e}))(e);return pe.fromDom(Te(t).dom.defaultView)},range:Zc},sm=document.caretPositionFromPoint?(e,t,o)=>{var n,r;return C.from(null===(r=(n=e.dom).caretPositionFromPoint)||void 0===r?void 0:r.call(n,t,o)).bind((t=>{if(null===t.offsetNode)return C.none();const o=e.dom.createRange();return o.setStart(t.offsetNode,t.offset),o.collapse(),C.some(o)}))}:document.caretRangeFromPoint?(e,t,o)=>{var n,r;return C.from(null===(r=(n=e.dom).caretRangeFromPoint)||void 0===r?void 0:r.call(n,t,o))}:C.none,lm=(e,t)=>{const o=Z(e);return"input"===o?Fc.after(e):D(["br","img"],o)?0===t?Fc.before(e):Fc.after(e):Fc.on(e,t)},am=e=>C.from(e.getSelection()),cm=(e,t)=>{am(e).each((e=>{e.removeAllRanges(),e.addRange(t)}))},im=(e,t,o,n,r)=>{const s=Gc(e,t,o,n,r);cm(e,s)},mm=(e,t)=>Qc(e,t).match({ltr:(t,o,n,r)=>{im(e,t,o,n,r)},rtl:(t,o,n,r)=>{am(e).each((s=>{if(s.setBaseAndExtent)s.setBaseAndExtent(t.dom,o,n.dom,r);else if(s.extend)try{((e,t,o,n,r,s)=>{t.collapse(o.dom,n),t.extend(r.dom,s)})(0,s,t,o,n,r)}catch(s){im(e,n,r,t,o)}else im(e,n,r,t,o)}))}}),dm=(e,t,o,n,r)=>{const s=((e,t,o,n)=>{const r=lm(e,t),s=lm(o,n);return rm.relative(r,s)})(t,o,n,r);mm(e,s)},um=(e,t,o)=>{const n=((e,t)=>{const o=e.fold(Fc.before,lm,Fc.after),n=t.fold(Fc.before,lm,Fc.after);return rm.relative(o,n)})(t,o);mm(e,n)},fm=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return C.some(Zc(pe.fromDom(t.startContainer),t.startOffset,pe.fromDom(o.endContainer),o.endOffset))}return C.none()},gm=e=>{if(null===e.anchorNode||null===e.focusNode)return fm(e);{const t=pe.fromDom(e.anchorNode),o=pe.fromDom(e.focusNode);return((e,t,o,n)=>{const r=((e,t,o,n)=>{const r=Se(e).dom.createRange();return r.setStart(e.dom,t),r.setEnd(o.dom,n),r})(e,t,o,n),s=ye(e,o)&&t===n;return r.collapsed&&!s})(t,e.anchorOffset,o,e.focusOffset)?C.some(Zc(t,e.anchorOffset,o,e.focusOffset)):fm(e)}},hm=(e,t,o=!0)=>{const n=(o?Vc:qc)(e,t);cm(e,n)},pm=e=>(e=>am(e).filter((e=>e.rangeCount>0)).bind(gm))(e).map((e=>rm.exact(e.start,e.soffset,e.finish,e.foffset))),wm=e=>({elementFromPoint:(t,o)=>pe.fromPoint(pe.fromDom(e.document),t,o),getRect:e=>e.dom.getBoundingClientRect(),getRangedRect:(t,o,n,r)=>{const s=rm.exact(t,o,n,r);return((e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?C.some(o).map(Kc):C.none()})(Xc(e,t)))(e,s)},getSelection:()=>pm(e).map((t=>ti(e,t))),fromSitus:t=>{const o=rm.relative(t.start,t.finish);return ti(e,o)},situsFromPoint:(t,o)=>((e,t,o)=>((e,t,o)=>{const n=pe.fromDom(e.document);return sm(n,t,o).map((e=>Zc(pe.fromDom(e.startContainer),e.startOffset,pe.fromDom(e.endContainer),e.endOffset)))})(e,t,o))(e,t,o).map((e=>ei(e.start,e.soffset,e.finish,e.foffset))),clearSelection:()=>{(e=>{am(e).each((e=>e.removeAllRanges()))})(e)},collapseSelection:(t=!1)=>{pm(e).each((o=>o.fold((e=>e.collapse(t)),((o,n)=>{const r=t?o:n;um(e,r,r)}),((o,n,r,s)=>{const l=t?o:r,a=t?n:s;dm(e,l,a,l,a)}))))},setSelection:t=>{dm(e,t.start,t.soffset,t.finish,t.foffset)},setRelativeSelection:(t,o)=>{um(e,t,o)},selectNode:t=>{hm(e,t,!1)},selectContents:t=>{hm(e,t)},getInnerHeight:()=>e.innerHeight,getScrollY:()=>(e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return dn(o,n)})(pe.fromDom(e.document)).top,scrollBy:(t,o)=>{((e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollBy(e,t)})(t,o,pe.fromDom(e.document))}}),bm=(e,t)=>({rows:e,cols:t}),vm=e=>void 0!==e.dom.classList,ym=(e,t)=>((e,t,o)=>{const n=((e,t)=>{const o=de(e,t);return void 0===o||""===o?[]:o.split(" ")})(e,t).concat([o]);return ie(e,t,n.join(" ")),!0})(e,"class",t),xm=(e,t)=>{vm(e)?e.dom.classList.add(t):ym(e,t)},Cm=(e,t)=>vm(e)&&e.dom.classList.contains(t),Sm=()=>({tag:"none"}),Tm=e=>({tag:"multiple",elements:e}),Rm=e=>({tag:"single",element:e}),Dm=e=>{const t=pe.fromDom((e=>{if(Qe()&&m(e.target)){const t=pe.fromDom(e.target);if(ne(t)&&m(t.dom.shadowRoot)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return H(t)}}return C.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),r=(s=n,l=o,(...e)=>s(l.apply(null,e)));var s,l;return((e,t,o,n,r,s,l)=>({target:e,x:t,y:o,stop:n,prevent:r,kill:s,raw:l}))(t,e.clientX,e.clientY,o,n,r,e)},Om=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},km=x,Em=(e,t,o)=>((e,t,o,n)=>((e,t,o,n,r)=>{const s=((e,t)=>o=>{e(o)&&t(Dm(o))})(o,n);return e.dom.addEventListener(t,s,r),{unbind:w(Om,e,t,s,r)}})(e,t,o,n,!1))(e,t,km,o),Nm=Dm,Bm=e=>!Cm(pe.fromDom(e.target),"ephox-snooker-resizer-bar"),zm=(e,t)=>{const o=(r=os.selectedSelector,{get:()=>Qr(pe.fromDom(e.getBody()),r).fold((()=>ls(Er(e),Or(e)).fold(Sm,Rm)),Tm)}),n=((e,t,o)=>{const n=t=>{fe(t,e.selected),fe(t,e.firstSelected),fe(t,e.lastSelected)},r=t=>{ie(t,e.selected,"1")},s=e=>{l(e),o()},l=t=>{const o=st(t,`${e.selectedSelector},${e.firstSelectedSelector},${e.lastSelectedSelector}`);N(o,n)};return{clearBeforeUpdate:l,clear:s,selectRange:(o,n,l,a)=>{s(o),N(n,r),ie(l,e.firstSelected,"1"),ie(a,e.lastSelected,"1"),t(n,l,a)},selectedSelector:e.selectedSelector,firstSelectedSelector:e.firstSelectedSelector,lastSelectedSelector:e.lastSelectedSelector}})(os,((t,o,n)=>{Ft(o).each((r=>{const s=Fa(e),l=Tr(f,pe.fromDom(e.getDoc()),s),a=((e,t,o)=>{const n=Uo(e);return Xs(n,t).map((e=>{const t=Us(n,o,!1),{rows:r}=jo(t),s=((e,t)=>{const o=e.slice(0,t[t.length-1].row+1),n=Gs(o);return j(n,(e=>{const o=e.cells.slice(0,t[t.length-1].column+1);return E(o,(e=>e.element))}))})(r,e),l=((e,t)=>{const o=e.slice(t[0].row+t[0].rowspan-1,e.length),n=Gs(o);return j(n,(e=>{const o=e.cells.slice(t[0].column+t[0].colspan-1,e.cells.length);return E(o,(e=>e.element))}))})(r,e);return{upOrLeftCells:s,downOrRightCells:l}}))})(r,{selection:as(e)},l);((e,t,o,n,r)=>{e.dispatch("TableSelectionChange",{cells:t,start:o,finish:n,otherCells:r})})(e,t,o,n,a)}))}),(()=>(e=>{e.dispatch("TableSelectionClear")})(e)));var r;return e.on("init",(o=>{const r=e.getWin(),s=Dr(e),l=Or(e),a=((e,t,o,n)=>{const r=((e,t,o,n)=>{const r=Gi(),s=r.clear,l=s=>{r.on((r=>{n.clearBeforeUpdate(t),Ki(s.target,o).each((l=>{Gr(r,l,o).each((o=>{const r=o.boxes.getOr([]);if(1===r.length){const o=r[0],l="false"===Ps(o),a=ut(js(s.target),o,ye);l&&a&&(n.selectRange(t,r,o,o),e.selectContents(o))}else r.length>1&&(n.selectRange(t,r,o.start,o.finish),e.selectContents(l))}))}))}))};return{clearstate:s,mousedown:e=>{n.clear(t),Ki(e.target,o).each(r.set)},mouseover:e=>{l(e)},mouseup:e=>{l(e),s()}}})(wm(e),t,o,n);return{clearstate:r.clearstate,mousedown:r.mousedown,mouseover:r.mouseover,mouseup:r.mouseup}})(r,s,l,n),c=((e,t,o,n)=>{const r=wm(e),s=()=>(n.clear(t),C.none());return{keydown:(e,l,a,c,i,m)=>{const d=e.raw,u=d.which,f=!0===d.shiftKey,g=Kr(t,n.selectedSelector).fold((()=>(em(u)&&!f&&n.clearBeforeUpdate(t),Zi(u)&&f?w($i,r,t,o,Yi,c,l,n.selectRange):Xi(u)&&f?w($i,r,t,o,Ji,c,l,n.selectRange):Zi(u)?w(Hi,r,o,Yi,c,l,Vi):Xi(u)?w(Hi,r,o,Ji,c,l,qi):C.none)),(e=>{const o=o=>()=>{const s=V(o,(o=>((e,t,o,n,r)=>Jr(n,e,t,r.firstSelectedSelector,r.lastSelectedSelector).map((e=>(r.clearBeforeUpdate(o),r.selectRange(o,e.boxes,e.start,e.finish),e.boxes))))(o.rows,o.cols,t,e,n)));return s.fold((()=>Yr(t,n.firstSelectedSelector,n.lastSelectedSelector).map((e=>{const o=Zi(u)||m.isForward(u)?Fc.after:Fc.before;return r.setRelativeSelection(Fc.on(e.first,0),o(e.table)),n.clear(t),Hc(C.none(),!0)}))),(e=>C.some(Hc(C.none(),!0))))};return Zi(u)&&f?o([bm(1,0)]):Xi(u)&&f?o([bm(-1,0)]):m.isBackward(u)&&f?o([bm(0,-1),bm(-1,0)]):m.isForward(u)&&f?o([bm(0,1),bm(1,0)]):em(u)&&!f?s:C.none}));return g()},keyup:(e,r,s,l,a)=>Kr(t,n.selectedSelector).fold((()=>{const c=e.raw,i=c.which;return!0===c.shiftKey&&em(i)?((e,t,o,n,r,s,l)=>ye(o,r)&&n===s?C.none():dt(o,"td,th",t).bind((o=>dt(r,"td,th",t).bind((n=>ni(e,t,o,n,l))))))(t,o,r,s,l,a,n.selectRange):C.none()}),C.none)}})(r,s,l,n),i=((e,t,o,n)=>{const r=wm(e);return(e,s)=>{n.clearBeforeUpdate(t),Gr(e,s,o).each((e=>{const o=e.boxes.getOr([]);n.selectRange(t,o,e.start,e.finish),r.selectContents(s),r.collapseSelection()}))}})(r,s,l,n);e.on("TableSelectorChange",(e=>i(e.start,e.finish)));const m=(t,o)=>{(e=>!0===e.raw.shiftKey)(t)&&(o.kill&&t.kill(),o.selection.each((t=>{const o=rm.relative(t.start,t.finish),n=Xc(r,o);e.selection.setRng(n)})))},d=e=>0===e.button,u=(()=>{const e=Ui(pe.fromDom(s)),t=Ui(0);return{touchEnd:o=>{const n=pe.fromDom(o.target);if(ae("td")(n)||ae("th")(n)){const r=e.get(),s=t.get();ye(r,n)&&o.timeStamp-s<300&&(o.preventDefault(),i(n,n))}e.set(n),t.set(o.timeStamp)}}})();e.on("dragstart",(e=>{a.clearstate()})),e.on("mousedown",(e=>{d(e)&&Bm(e)&&a.mousedown(Nm(e))})),e.on("mouseover",(e=>{var t;void 0!==(t=e).buttons&&0==(1&t.buttons)||!Bm(e)||a.mouseover(Nm(e))})),e.on("mouseup",(e=>{d(e)&&Bm(e)&&a.mouseup(Nm(e))})),e.on("touchend",u.touchEnd),e.on("keyup",(t=>{const o=Nm(t);if(o.raw.shiftKey&&em(o.raw.which)){const t=e.selection.getRng(),n=pe.fromDom(t.startContainer),r=pe.fromDom(t.endContainer);c.keyup(o,n,t.startOffset,r,t.endOffset).each((e=>{m(o,e)}))}})),e.on("keydown",(o=>{const n=Nm(o);t.hide();const r=e.selection.getRng(),s=pe.fromDom(r.startContainer),l=pe.fromDom(r.endContainer),a=rn(tm,om)(pe.fromDom(e.selection.getStart()));c.keydown(n,s,r.startOffset,l,r.endOffset,a).each((e=>{m(n,e)})),t.show()})),e.on("NodeChange",(()=>{const t=e.selection,o=pe.fromDom(t.getStart()),r=pe.fromDom(t.getEnd());$r(Ft,[o,r]).fold((()=>n.clear(s)),f)}))})),e.on("PreInit",(()=>{e.serializer.addTempAttr(os.firstSelected),e.serializer.addTempAttr(os.lastSelected)})),{getSelectedCells:()=>((e,t,o,n)=>{switch(e.tag){case"none":return t();case"single":return(e=>[e.dom])(e.element);case"multiple":return(e=>E(e,(e=>e.dom)))(e.elements)}})(o.get(),g([])),clearSelectedCells:e=>n.clear(pe.fromDom(e))}},Am=e=>{let t=[];return{bind:e=>{if(void 0===e)throw new Error("Event bind error: undefined handler");t.push(e)},unbind:e=>{t=z(t,(t=>t!==e))},trigger:(...o)=>{const n={};N(e,((e,t)=>{n[e]=o[t]})),N(t,(e=>{e(n)}))}}},Wm=e=>({registry:K(e,(e=>({bind:e.bind,unbind:e.unbind}))),trigger:K(e,(e=>e.trigger))}),Lm=e=>e.slice(0).sort(),_m=(e,t)=>{const o=z(t,(t=>!D(e,t)));o.length>0&&(e=>{throw new Error("Unsupported keys for object: "+Lm(e).join(", "))})(o)},Mm=e=>((e,t)=>((e,t,o)=>{if(0===t.length)throw new Error("You must specify at least one required field.");return((e,t)=>{if(!l(t))throw new Error("The required fields must be an array. Was: "+t+".");N(t,(t=>{if(!r(t))throw new Error("The value "+t+" in the "+e+" fields was not a string.")}))})("required",t),(e=>{const t=Lm(e);L(t,((e,o)=>o<t.length-1&&e===t[o+1])).each((e=>{throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+t.join(", ")+"].")}))})(t),n=>{const r=$(n);I(t,(e=>D(r,e)))||((e,t)=>{throw new Error("All required keys ("+Lm(e).join(", ")+") were not specified. Specified keys were: "+Lm(t).join(", ")+".")})(t,r),e(t,r);const s=z(t,(e=>!o.validate(n[e],e)));return s.length>0&&((e,t)=>{throw new Error("All values need to be of type: "+t+". Keys ("+Lm(e).join(", ")+") were not.")})(s,o.label),n}})(e,t,{validate:d,label:"function"}))(_m,e),jm=Mm(["compare","extract","mutate","sink"]),Im=Mm(["element","start","stop","destroy"]),Pm=Mm(["forceDrop","drop","move","delayDrop"]),Fm=()=>{const e=(()=>{const e=Wm({move:Am(["info"])});return{onEvent:f,reset:f,events:e.registry}})(),t=(()=>{let e=C.none();const t=Wm({move:Am(["info"])});return{onEvent:(o,n)=>{n.extract(o).each((o=>{const r=((t,o)=>{const n=e.map((e=>t.compare(e,o)));return e=C.some(o),n})(n,o);r.each((e=>{t.trigger.move(e)}))}))},reset:()=>{e=C.none()},events:t.registry}})();let o=e;return{on:()=>{o.reset(),o=t},off:()=>{o.reset(),o=e},isOn:()=>o===t,onEvent:(e,t)=>{o.onEvent(e,t)},events:t.events}},Hm=e=>{const t=e.replace(/\./g,"-");return{resolve:e=>t+"-"+e}},qm=Hm("ephox-dragster").resolve;var Vm=jm({compare:(e,t)=>dn(t.left-e.left,t.top-e.top),extract:e=>C.some(dn(e.x,e.y)),sink:(e,t)=>{const o=(e=>{const t={layerClass:qm("blocker"),...e},o=pe.fromTag("div");return ie(o,"role","presentation"),Tt(o,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),xm(o,qm("blocker")),xm(o,t.layerClass),{element:g(o),destroy:()=>{Ie(o)}}})(t),n=Em(o.element(),"mousedown",e.forceDrop),r=Em(o.element(),"mouseup",e.drop),s=Em(o.element(),"mousemove",e.move),l=Em(o.element(),"mouseout",e.delayDrop);return Im({element:o.element,start:e=>{We(e,o.element())},stop:()=>{Ie(o.element())},destroy:()=>{o.destroy(),r.unbind(),s.unbind(),l.unbind(),n.unbind()}})},mutate:(e,t)=>{e.mutate(t.left,t.top)}});const $m=Hm("ephox-snooker").resolve,Um=$m("resizer-bar"),Gm=$m("resizer-rows"),Km=$m("resizer-cols"),Ym=e=>{const t=st(e.parent(),"."+Um);N(t,Ie)},Jm=(e,t,o)=>{const n=e.origin();N(t,(t=>{t.each((t=>{const r=o(n,t);xm(r,Um),We(e.parent(),r)}))}))},Qm=(e,t,o,n,r)=>{const s=fn(o),l=t.isResizable,a=n.length>0?Rn.positions(n,o):[],c=a.length>0?((e,t)=>j(e.all,((e,o)=>t(e.element)?[o]:[])))(e,l):[];((e,t,o,n)=>{Jm(e,t,((e,t)=>{const r=((e,t,o,n,r)=>{const s=pe.fromTag("div");return Tt(s,{position:"absolute",left:t+"px",top:o-3.5+"px",height:"7px",width:n+"px"}),me(s,{"data-row":e,role:"presentation"}),s})(t.row,o.left-e.left,t.y-e.top,n);return xm(r,Gm),r}))})(t,z(a,((e,t)=>O(c,(e=>t===e)))),s,Eo(o));const i=r.length>0?On.positions(r,o):[],m=i.length>0?((e,t)=>{const o=[];return k(e.grid.columns,(n=>{en(e,n).map((e=>e.element)).forall(t)&&o.push(n)})),z(o,(o=>{const n=Jo(e,(e=>e.column===o));return I(n,(e=>t(e.element)))}))})(e,l):[];((e,t,o,n)=>{Jm(e,t,((e,t)=>{const r=((e,t,o,n,r)=>{const s=pe.fromTag("div");return Tt(s,{position:"absolute",left:t-3.5+"px",top:o+"px",height:r+"px",width:"7px"}),me(s,{"data-column":e,role:"presentation"}),s})(t.col,t.x-e.left,o.top-e.top,0,n);return xm(r,Km),r}))})(t,z(i,((e,t)=>O(m,(e=>t===e)))),s,cn(o))},Xm=(e,t)=>{if(Ym(e),e.isResizable(t)){const o=Uo(t),n=nn(o),r=tn(o);Qm(o,e,t,n,r)}},Zm=(e,t)=>{const o=st(e.parent(),"."+Um);N(o,t)},ed=e=>{Zm(e,(e=>{St(e,"display","none")}))},td=e=>{Zm(e,(e=>{St(e,"display","block")}))},od=$m("resizer-bar-dragging"),nd=e=>{const t=(()=>{const e=Wm({drag:Am(["xDelta","yDelta","target"])});let t=C.none();const o=(()=>{const e=Wm({drag:Am(["xDelta","yDelta"])});return{mutate:(t,o)=>{e.trigger.drag(t,o)},events:e.registry}})();return o.events.drag.bind((o=>{t.each((t=>{e.trigger.drag(o.xDelta,o.yDelta,t)}))})),{assign:e=>{t=C.some(e)},get:()=>t,mutate:o.mutate,events:e.registry}})(),o=((e,t={})=>{var o;return((e,t,o)=>{let n=!1;const r=Wm({start:Am([]),stop:Am([])}),s=Fm(),l=()=>{m.stop(),s.isOn()&&(s.off(),r.trigger.stop())},c=((e,t)=>{let o=null;const n=()=>{a(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...t)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,t)}),200)}}})(l);s.events.move.bind((o=>{t.mutate(e,o.info)}));const i=e=>(...t)=>{n&&e.apply(null,t)},m=t.sink(Pm({forceDrop:l,drop:i(l),move:i((e=>{c.cancel(),s.onEvent(e,t)})),delayDrop:i(c.throttle)}),o);return{element:m.element,go:e=>{m.start(e),s.on(),r.trigger.start()},on:()=>{n=!0},off:()=>{n=!1},destroy:()=>{m.destroy()},events:r.registry}})(e,null!==(o=t.mode)&&void 0!==o?o:Vm,t)})(t,{});let n=C.none();const r=(e,t)=>C.from(de(e,t));t.events.drag.bind((e=>{r(e.target,"data-row").each((t=>{const o=At(e.target,"top");St(e.target,"top",o+e.yDelta+"px")})),r(e.target,"data-column").each((t=>{const o=At(e.target,"left");St(e.target,"left",o+e.xDelta+"px")}))}));const s=(e,t)=>At(e,t)-Et(e,"data-initial-"+t,0);o.events.stop.bind((()=>{t.get().each((t=>{n.each((o=>{r(t,"data-row").each((e=>{const n=s(t,"top");fe(t,"data-initial-top"),d.trigger.adjustHeight(o,n,parseInt(e,10))})),r(t,"data-column").each((e=>{const n=s(t,"left");fe(t,"data-initial-left"),d.trigger.adjustWidth(o,n,parseInt(e,10))})),Xm(e,o)}))}))}));const l=(n,r)=>{d.trigger.startAdjust(),t.assign(n),ie(n,"data-initial-"+r,At(n,r)),xm(n,od),St(n,"opacity","0.2"),o.go(e.parent())},c=Em(e.parent(),"mousedown",(e=>{var t;t=e.target,Cm(t,Gm)&&l(e.target,"top"),(e=>Cm(e,Km))(e.target)&&l(e.target,"left")})),i=t=>ye(t,e.view()),m=Em(e.view(),"mouseover",(t=>{var o;(o=t.target,dt(o,"table",i).filter(Is)).fold((()=>{et(t.target)&&Ym(e)}),(t=>{n=C.some(t),Xm(e,t)}))})),d=Wm({adjustHeight:Am(["table","delta","row"]),adjustWidth:Am(["table","delta","column"]),startAdjust:Am([])});return{destroy:()=>{c.unbind(),m.unbind(),o.destroy(),Ym(e)},refresh:t=>{Xm(e,t)},on:o.on,off:o.off,hideBars:w(ed,e),showBars:w(td,e),events:d.registry}},rd=(e,t,o)=>{const n=Rn,r=On,s=nd(e),l=Wm({beforeResize:Am(["table","type"]),afterResize:Am(["table","type"]),startDrag:Am([])});return s.events.adjustHeight.bind((e=>{const t=e.table;l.trigger.beforeResize(t,"row");((e,t,o,n)=>{const r=Uo(e),s=((e,t,o)=>Zn(e,t,o,Hn,(e=>e.getOrThunk(Lt))))(r,e,n),l=E(s,((e,n)=>o===n?Math.max(t+e,Lt()):e)),a=Ol(r,l),c=((e,t)=>E(e.all,((e,o)=>({element:e.element,height:t[o]}))))(r,l);N(c,(e=>{_n(e.element,e.height)})),N(a,(e=>{_n(e.element,e.height)}));const i=A(l,((e,t)=>e+t),0);_n(e,i)})(t,n.delta(e.delta,t),e.row,n),l.trigger.afterResize(t,"row")})),s.events.startAdjust.bind((e=>{l.trigger.startDrag()})),s.events.adjustWidth.bind((e=>{const n=e.table;l.trigger.beforeResize(n,"col");const s=r.delta(e.delta,n),a=o(n);El(n,s,e.column,t,a),l.trigger.afterResize(n,"col")})),{on:s.on,off:s.off,refreshBars:s.refresh,hideBars:s.hideBars,showBars:s.showBars,destroy:s.destroy,events:l.registry}},sd=e=>m(e)&&"TABLE"===e.nodeName,ld="bar-",ad=e=>"false"!==de(e,"data-mce-resize"),cd=e=>{const t=Gi(),o=Gi(),n=Gi();let r,s;const l=t=>ec(e,t),a=()=>Va(e)?Cs():xs();return e.on("init",(()=>{const r=((e,t)=>e.inline?((e,t,o)=>({parent:g(t),view:g(e),origin:g(dn(0,0)),isResizable:o}))(pe.fromDom(e.getBody()),(()=>{const e=pe.fromTag("div");return Tt(e,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),We(tt(pe.fromDom(document)),e),e})(),t):((e,t)=>{const o=se(e)?(e=>pe.fromDom(Te(e).dom.documentElement))(e):e;return{parent:g(o),view:g(e),origin:g(dn(0,0)),isResizable:t}})(pe.fromDom(e.getDoc()),t))(e,ad);if(n.set(r),(e=>{const t=e.options.get("object_resizing");return D(t.split(","),"table")})(e)&&Ja(e)){const n=a(),s=rd(r,n,l);s.on(),s.events.startDrag.bind((o=>{t.set(e.selection.getRng())})),s.events.beforeResize.bind((t=>{const o=t.table.dom;((e,t,o,n,r)=>{e.dispatch("ObjectResizeStart",{target:t,width:o,height:n,origin:r})})(e,o,Nr(o),Br(o),ld+t.type)})),s.events.afterResize.bind((o=>{const n=o.table,r=n.dom;kr(n),t.on((t=>{e.selection.setRng(t),e.focus()})),((e,t,o,n,r)=>{e.dispatch("ObjectResized",{target:t,width:o,height:n,origin:r})})(e,r,Nr(r),Br(r),ld+o.type),e.undoManager.add()})),o.set(s)}})),e.on("ObjectResizeStart",(t=>{const o=t.target;if(sd(o)){const n=pe.fromDom(o);N(e.dom.select(".mce-clonedresizable"),(t=>{e.dom.addClass(t,"mce-"+qa(e)+"-columns")})),!hc(n)&&Ka(e)?vc(n):!gc(n)&&Ga(e)&&bc(n),pc(n)&&wt(t.origin,ld)&&bc(n),r=t.width,s=Ya(e)?"":((e,t)=>{const o=e.dom.getStyle(t,"width")||e.dom.getAttrib(t,"width");return C.from(o).filter(yt)})(e,o).getOr("")}})),e.on("ObjectResized",(t=>{const o=t.target;if(sd(o)){const n=pe.fromDom(o),c=t.origin;wt(c,"corner-")&&((t,o,n)=>{const c=bt(o,"e");if(""===s&&bc(t),n!==r&&""!==s){St(t,"width",s);const o=a(),i=l(t),m=Va(e)||c?(e=>Ss(e).columns)(t)-1:0;El(t,n-r,m,o,i)}else if((e=>/^(\d+(\.\d+)?)%$/.test(e))(s)){const e=parseFloat(s.replace("%",""));St(t,"width",n*e/r+"%")}(e=>/^(\d+(\.\d+)?)px$/.test(e))(s)&&(e=>{const t=Uo(e);Zo(t)||N(It(e),(e=>{const t=Rt(e,"width");St(e,"width",t),fe(e,"width")}))})(t)})(n,c,t.width),kr(n),Wa(e,n.dom,La)}})),e.on("SwitchMode",(()=>{o.on((t=>{e.mode.isReadOnly()?t.hideBars():t.showBars()}))})),e.on("remove",(()=>{o.on((e=>{e.destroy()})),n.on((t=>{((e,t)=>{e.inline&&Ie(t.parent())})(e,t)}))})),{refresh:e=>{o.on((t=>t.refreshBars(pe.fromDom(e))))},hide:()=>{o.on((e=>e.hideBars()))},show:()=>{o.on((e=>e.showBars()))}}},id=e=>{(e=>{const t=e.options.register;t("table_clone_elements",{processor:"string[]"}),t("table_use_colgroups",{processor:"boolean",default:!0}),t("table_header_type",{processor:e=>{const t=D(["section","cells","sectionCells","auto"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: section, cells, sectionCells or auto."}},default:"section"}),t("table_sizing_mode",{processor:"string",default:"auto"}),t("table_default_attributes",{processor:"object",default:{border:"1"}}),t("table_default_styles",{processor:"object",default:{"border-collapse":"collapse"}}),t("table_column_resizing",{processor:e=>{const t=D(["preservetable","resizetable"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be preservetable, or resizetable."}},default:"preservetable"}),t("table_resize_bars",{processor:"boolean",default:!0}),t("table_style_by_css",{processor:"boolean",default:!0})})(e);const t=cd(e),o=zm(e,t),n=tc(e,t,o);return Ic(e,n),((e,t)=>{const o=Or(e),n=t=>ls(Er(e)).bind((n=>Ft(n,o).map((o=>{const r=ns(as(e),o,n);return t(o,r)})))).getOr("");G({mceTableRowType:()=>n(t.getTableRowType),mceTableCellType:()=>n(t.getTableCellType),mceTableColType:()=>n(t.getTableColType)},((t,o)=>e.addQueryValueHandler(o,t)))})(e,n),cs(e,n),{getSelectedCells:o.getSelectedCells,clearSelectedCells:o.clearSelectedCells}};e.add("dom",(e=>({table:id(e)})))}();