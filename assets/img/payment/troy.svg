<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="256" height="100" viewBox="0 0 256 100">
  <metadata><?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c142 79.160924, 2017/07/13-01:06:39        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""/>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?></metadata>
<image id="Layer_0" data-name="Layer 0" x="20" width="216" height="100" xlink:href="data:img/png;base64,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"/>
</svg>
