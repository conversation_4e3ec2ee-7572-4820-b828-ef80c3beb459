# Context
Filename: Custom_Fields_Auto_Title_Task.md
Created On: 2025-06-28
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
Phân tích codebase hiện tại để hiểu cấu trúc Custom Fields và đưa ra phương án kỹ thuật chi tiết cho việc tự động tạo title dựa trên thứ tự (order) của các Custom Fields. Cụ thể:

1. Khảo sát và phân tích:
   - Cấu trúc hiện tại của Custom Fields trong hệ thống
   - Cách thức lưu trữ và quản lý thứ tự (order) của các fields
   - Các file/class/function liên quan đến việc xử lý Custom Fields
   - Cơ chế tạo và quản lý title hiện tại (nếu có)

2. <PERSON><PERSON> xuất giải pháp kỹ thuật:
   - Thuật toán để tự động tạo title từ các Custom Fields theo thứ tự
   - Các file cần chỉnh sửa và logic cần implement
   - Cách xử lý các trường hợp đặc biệt (fields trống, thứ tự không liên tục, etc.)
   - Phương án tích hợp với workflow hiện tại

3. Cung cấp:
   - Kế hoạch implementation chi tiết từng bước
   - Ví dụ cụ thể về cách title sẽ được tạo
   - Các lưu ý về performance và maintainability

# Project Overview
Đây là một ứng dụng e-commerce được xây dựng trên CodeIgniter 4 với hệ thống Custom Fields phức tạp cho phép người dùng tạo các trường tùy chỉnh cho sản phẩm theo từng category. Hệ thống hiện tại có:
- Custom Fields với nhiều loại: text, number, textarea, date, dropdown, radio_button, checkbox
- Hệ thống thứ tự (field_order) để sắp xếp các fields
- Liên kết Custom Fields với categories thông qua bảng custom_fields_category
- Lưu trữ giá trị Custom Fields cho từng product trong bảng custom_fields_product

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## Cấu trúc Database Custom Fields

### Bảng `custom_fields`:
- `id`: Primary key
- `name_array`: Tên field được serialize theo ngôn ngữ
- `field_type`: Loại field (text, number, textarea, date, dropdown, radio_button, checkbox)
- `row_width`: Độ rộng hiển thị (half, full)
- `is_required`: Bắt buộc hay không (0/1)
- `status`: Trạng thái active (0/1)
- `field_order`: **Thứ tự sắp xếp** (quan trọng cho việc tạo title)
- `is_product_filter`: Có dùng làm filter không (0/1)
- `product_filter_key`: Key để filter
- `sort_options`: Cách sắp xếp options (alphabetically, date, date_desc)
- `where_to_display`: Vị trí hiển thị (1=top, 2=bottom)

### Bảng `custom_fields_category`:
- `id`: Primary key
- `category_id`: ID của category
- `field_id`: ID của custom field
- Liên kết many-to-many giữa fields và categories

### Bảng `custom_fields_options`:
- `id`: Primary key
- `field_id`: ID của custom field
- `option_key`: Key của option
- `name_data`: Tên option được serialize theo ngôn ngữ

### Bảng `custom_fields_product`:
- `id`: Primary key
- `field_id`: ID của custom field
- `product_id`: ID của product
- `product_filter_key`: Key để filter
- `field_value`: Giá trị text cho text/number/textarea/date fields
- `selected_option_id`: ID option được chọn cho dropdown/radio/checkbox

## Cấu trúc Code hiện tại

### Model chính: `app/Models/FieldModel.php`
- `getCustomFieldsByCategory($categoryId)`: Lấy custom fields theo category, **đã sắp xếp theo field_order**
- `getProductCustomFieldValues($fieldId, $productId)`: Lấy giá trị custom field của product
- `getProductCustomFieldInputValue($fieldId, $productId)`: Lấy giá trị input text
- `getProductFilterValuesArray($productId, $brand)`: Lấy array values để hiển thị, **đã sắp xếp theo field_order**

### Cách title được quản lý hiện tại:
- Title được lưu trong bảng `product_details` với `lang_id`
- Được tạo thủ công bởi user trong form add/edit product
- Không có cơ chế tự động tạo title từ Custom Fields

### Workflow hiện tại:
1. User chọn category khi tạo product
2. System load Custom Fields theo category (đã sắp xếp theo field_order)
3. User nhập giá trị cho các Custom Fields
4. User nhập title thủ công
5. System lưu title và Custom Fields values riêng biệt

## Các file quan trọng đã phân tích:
- `app/Models/FieldModel.php`: Model chính xử lý Custom Fields
- `app/Models/ProductModel.php`: Model xử lý product và title
- `app/Controllers/DashboardController.php`: Controller xử lý add/edit product
- `app/Views/dashboard/product/_custom_fields.php`: View hiển thị Custom Fields form
- `app/Views/dashboard/product/add_product.php`: Form thêm product
- `app/Views/dashboard/product/edit_product.php`: Form sửa product

## Phát hiện quan trọng:
1. **field_order đã được implement và sử dụng nhất quán** trong toàn bộ hệ thống
2. Custom Fields đã được sắp xếp theo field_order trong tất cả queries
3. Có sẵn functions để lấy Custom Fields values theo thứ tự
4. Title hiện tại được quản lý độc lập, không liên quan đến Custom Fields
5. Hệ thống đã có cơ chế multi-language cho cả Custom Fields và title

# Proposed Solution (Populated by INNOVATE mode)

## Phân tích các phương án

### Phương án 1: Auto-generate Title hoàn toàn
Tự động tạo title bằng cách nối các giá trị Custom Fields theo thứ tự field_order.
- **Ưu điểm**: Đơn giản, nhất quán, tự động cập nhật
- **Nhược điểm**: Mất tính linh hoạt, title có thể dài, khó xử lý fields trống

### Phương án 2: Template-based Title Generation
Tạo hệ thống template cho phép admin định nghĩa cách tạo title cho từng category.
- **Ưu điểm**: Linh hoạt cao, tùy chỉnh theo category, có thể thêm text tĩnh
- **Nhược điểm**: Phức tạp implement, cần interface quản lý, cần validation

### Phương án 3: Smart Auto-suggest với Manual Override
Tự động suggest title nhưng cho phép user chỉnh sửa thủ công.
- **Ưu điểm**: Cân bằng automation và flexibility, giảm công việc nhập liệu
- **Nhược điểm**: Cần implement UI auto-suggest, logic phức tạp

### Phương án 4: Priority-based Field Selection
Chỉ sử dụng một số Custom Fields quan trọng nhất để tạo title.
- **Ưu điểm**: Title ngắn gọn, performance tốt, có thể config
- **Nhược điểm**: Có thể bỏ sót thông tin, cần logic xác định priority

## Phương án được chọn: Hybrid Approach

Kết hợp các phương án với cấu hình linh hoạt:

1. **Mặc định**: Auto-generate từ 3-4 Custom Fields đầu tiên (theo field_order)
2. **Tùy chọn**: Admin có thể config template cho từng category
3. **Override**: User vẫn có thể chỉnh sửa title thủ công
4. **Fallback**: Nếu không có Custom Fields, dùng title thủ công như hiện tại

### Lý do chọn phương án này:
- Tận dụng tối đa cấu trúc field_order đã có sẵn
- Cân bằng giữa automation và user control
- Có thể mở rộng dần từ đơn giản đến phức tạp
- Tương thích ngược với workflow hiện tại

# Implementation Plan (Generated by PLAN mode)

## Kế hoạch Implementation Chi tiết

### Phase 1: Core Logic Implementation

**1. Tạo Helper Function cho Auto Title Generation**
- File: `app/Helpers/custom_field_helper.php` (tạo mới)
- Function: `generateTitleFromCustomFields($productId, $categoryId, $maxFields = 4)`
- Logic: Lấy Custom Fields theo field_order, kết hợp giá trị thành title

**2. Mở rộng FieldModel**
- File: `app/Models/FieldModel.php`
- Thêm method: `getCustomFieldsForTitleGeneration($categoryId, $limit = 4)`
- Thêm method: `getProductCustomFieldValuesForTitle($productId, $fieldIds)`

**3. Mở rộng ProductModel**
- File: `app/Models/ProductModel.php`
- Thêm method: `generateAndUpdateTitle($productId, $categoryId)`
- Modify method: `addProductTitleDesc()` để tích hợp auto-generation
- Modify method: `editProductTitleDesc()` để tích hợp auto-generation

### Phase 2: Database Enhancement

**4. Tạo bảng cấu hình title template (tùy chọn)**
- File: Migration mới
- Bảng: `custom_field_title_templates`
- Columns: id, category_id, template, is_active, created_at

**5. Thêm cột cấu hình vào bảng categories**
- File: Migration mới
- Thêm cột: `auto_title_enabled` (tinyint, default 1)
- Thêm cột: `title_template` (text, nullable)

### Phase 3: Controller Logic Update

**6. Cập nhật DashboardController**
- File: `app/Controllers/DashboardController.php`
- Modify: `addProductPost()` để auto-generate title
- Modify: `editProductPost()` để auto-generate title khi cần
- Thêm: `generateTitleAjax()` cho real-time preview

**7. Cập nhật CategoryController (Admin)**
- File: `app/Controllers/CategoryController.php`
- Thêm: `titleTemplateSettings()` để config template
- Thêm: `updateTitleTemplateSettings()` để lưu config

### Phase 4: Frontend Implementation

**8. Cập nhật Add Product Form**
- File: `app/Views/dashboard/product/add_product.php`
- Thêm: Auto-generate button bên cạnh title field
- Thêm: JavaScript để preview title real-time

**9. Cập nhật Edit Product Form**
- File: `app/Views/dashboard/product/edit_product.php`
- Thêm: Tương tự như add product form
- Thêm: Option để re-generate title

**10. Tạo Admin Interface cho Title Templates**
- File: `app/Views/admin/category/title_template_settings.php` (tạo mới)
- Interface để admin config template cho từng category

### Phase 5: JavaScript Enhancement

**11. Tạo JavaScript Module**
- File: `assets/js/custom-field-title-generator.js` (tạo mới)
- Functions: Real-time title generation, AJAX calls, UI updates

**12. Cập nhật existing JavaScript**
- File: `assets/js/dashboard.js`
- Tích hợp với custom field title generator

### Phase 6: Configuration & Settings

**13. Thêm Settings cho Admin**
- File: `app/Views/admin/settings/general_settings.php`
- Thêm: Enable/disable auto title generation globally
- Thêm: Default max fields for title generation

**14. Cập nhật Language Files**
- File: `app/Language/*/default.php`
- Thêm: Translations cho các text mới

## Implementation Checklist:

1. Tạo helper function `generateTitleFromCustomFields()` trong file mới `app/Helpers/custom_field_helper.php`
2. Mở rộng `FieldModel` với methods mới cho title generation
3. Mở rộng `ProductModel` với logic auto-generate title
4. Tạo migration cho bảng title templates (optional)
5. Tạo migration thêm cột config vào bảng categories
6. Cập nhật `DashboardController` với logic auto-generate
7. Cập nhật `CategoryController` với admin settings
8. Cập nhật add product form với auto-generate UI
9. Cập nhật edit product form với auto-generate UI
10. Tạo admin interface cho title template settings
11. Tạo JavaScript module cho real-time generation
12. Cập nhật existing JavaScript files
13. Thêm admin settings cho global configuration
14. Cập nhật language files với translations mới
15. Tạo unit tests cho các functions mới
16. Tạo documentation cho admin về cách sử dụng tính năng
