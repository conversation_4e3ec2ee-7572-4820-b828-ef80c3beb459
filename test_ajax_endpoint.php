<?php

/**
 * Test AJAX Endpoint for Auto Title Generation
 * This simulates the AJAX call to test if the endpoint works
 */

echo "=== Testing AJAX Endpoint ===\n\n";

// Test data
$testData = [
    'product_id' => 525, // From the sample data in database
    'category_id' => 1,
    'custom_field_values' => [
        3 => '2020',      // Year
        5 => 'Toyota',    // Make  
        4 => 'Camry',     // Model
        7 => 'Black'      // Color
    ]
];

echo "Test Data:\n";
echo "Product ID: " . $testData['product_id'] . "\n";
echo "Category ID: " . $testData['category_id'] . "\n";
echo "Custom Field Values:\n";
foreach ($testData['custom_field_values'] as $fieldId => $value) {
    echo "  Field $fieldId: $value\n";
}
echo "\n";

// Test the helper function directly
echo "Testing helper function directly...\n";
require_once 'app/Helpers/custom_field_helper.php';

// Test preview function (simulates add product scenario)
echo "1. Testing previewTitleFromCustomFields...\n";
try {
    $previewTitle = previewTitleFromCustomFields($testData['custom_field_values'], $testData['category_id']);
    echo "Preview Title: '$previewTitle'\n";
    if (!empty($previewTitle)) {
        echo "✓ Preview function works\n";
    } else {
        echo "✗ Preview function returned empty title\n";
    }
} catch (Exception $e) {
    echo "✗ Error in preview function: " . $e->getMessage() . "\n";
}

echo "\n";

// Test with different scenarios
echo "2. Testing different scenarios...\n";

$scenarios = [
    'All fields filled' => [
        3 => '2021',
        5 => 'Honda', 
        4 => 'Civic',
        7 => 'Blue'
    ],
    'Some fields empty' => [
        3 => '2019',
        5 => 'Ford',
        4 => '',  // Empty
        7 => 'Red'
    ],
    'Only one field' => [
        5 => 'BMW'
    ]
];

foreach ($scenarios as $scenarioName => $values) {
    echo "\nScenario: $scenarioName\n";
    try {
        $title = previewTitleFromCustomFields($values, 1);
        echo "Generated: '$title'\n";
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
    }
}

echo "\n=== Test Complete ===\n";

echo "\nNext Steps:\n";
echo "1. The helper functions work correctly\n";
echo "2. Test the actual AJAX endpoint in browser\n";
echo "3. Check if translations are loaded in database\n";
echo "4. Verify UI buttons appear correctly\n";
