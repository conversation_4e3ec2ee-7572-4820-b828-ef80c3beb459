<?php

/**
 * Custom Field Helper Functions
 * 
 * Helper functions for Custom Fields auto title generation (Phương án 1: Auto-generate hoàn toàn)
 */

if (!function_exists('generateTitleFromCustomFields')) {
    /**
     * Generate product title from ALL custom fields based on field order
     * Auto-generate title completely from all available custom fields
     * 
     * @param int $productId Product ID
     * @param int $categoryId Category ID
     * @param string $separator Separator between field values (default: ' ')
     * @return string Generated title
     */
    function generateTitleFromCustomFields($productId, $categoryId, $separator = ' ')
    {
        $fieldModel = model('FieldModel');
        
        // Get ALL custom fields for this category (ordered by field_order)
        $customFields = $fieldModel->getCustomFieldsByCategory($categoryId);
        
        if (empty($customFields)) {
            return '';
        }
        
        $titleParts = [];

        foreach ($customFields as $field) {
            // Skip radio_button fields from title generation
            if ($field->field_type === 'radio_button') {
                continue;
            }

            $fieldValue = getCustomFieldValueForTitle($field, $productId);

            if (!empty($fieldValue)) {
                $titleParts[] = $fieldValue;
            }
        }
        
        // Join ALL title parts with separator
        $generatedTitle = implode($separator, $titleParts);
        
        // Clean and format title
        $generatedTitle = cleanTitleString($generatedTitle);
        
        return $generatedTitle;
    }
}

if (!function_exists('getCustomFieldValueForTitle')) {
    /**
     * Get custom field value formatted for title generation
     * 
     * @param object $field Custom field object
     * @param int $productId Product ID
     * @return string Formatted field value
     */
    function getCustomFieldValueForTitle($field, $productId)
    {
        $fieldModel = model('FieldModel');
        
        switch ($field->field_type) {
            case 'text':
            case 'number':
            case 'textarea':
            case 'date':
                // Get direct input value
                $value = $fieldModel->getProductCustomFieldInputValue($field->id, $productId);
                return trim($value);

            case 'dropdown':
                // Get selected option name
                $fieldValues = $fieldModel->getProductCustomFieldValues($field->id, $productId);
                if (!empty($fieldValues)) {
                    $firstValue = $fieldValues[0];
                    if (!empty($firstValue->selected_option_id)) {
                        return getCustomFieldOptionName($firstValue->name_data, selectedLangId(), false);
                    }
                }
                break;

            case 'radio_button':
                // Exclude radio_button fields from title generation
                return '';
                break;
                
            case 'checkbox':
                // Get ALL selected options for checkbox (join with comma)
                $fieldValues = $fieldModel->getProductCustomFieldValues($field->id, $productId);
                if (!empty($fieldValues)) {
                    $optionNames = [];
                    foreach ($fieldValues as $value) {
                        if (!empty($value->selected_option_id)) {
                            $optionName = getCustomFieldOptionName($value->name_data, selectedLangId(), false);
                            if (!empty($optionName)) {
                                $optionNames[] = $optionName;
                            }
                        }
                    }
                    return implode(', ', $optionNames);
                }
                break;
        }
        
        return '';
    }
}

if (!function_exists('cleanTitleString')) {
    /**
     * Clean and format title string
     * 
     * @param string $title Raw title string
     * @return string Cleaned title
     */
    function cleanTitleString($title)
    {
        // Remove extra spaces
        $title = preg_replace('/\s+/', ' ', $title);
        
        // Trim whitespace
        $title = trim($title);
        
        // Limit title length to reasonable size
        $maxLength = 150; // Reasonable length for product titles
        if (strlen($title) > $maxLength) {
            $title = substr($title, 0, $maxLength);
            // Try to cut at word boundary
            $lastSpace = strrpos($title, ' ');
            if ($lastSpace !== false && $lastSpace > $maxLength * 0.8) {
                $title = substr($title, 0, $lastSpace);
            }
            $title = rtrim($title, '.,;:-') . '...';
        }
        
        return $title;
    }
}

if (!function_exists('autoGenerateTitleOnSave')) {
    /**
     * Automatically generate and update title when product is saved
     * This is the main function called during product save operations
     * 
     * @param int $productId Product ID
     * @param int $categoryId Category ID
     * @return bool Success status
     */
    function autoGenerateTitleOnSave($productId, $categoryId)
    {
        // Generate title from custom fields
        $generatedTitle = generateTitleFromCustomFields($productId, $categoryId);

        if (empty($generatedTitle)) {
            return false;
        }

        // Update title in database for all languages
        $productModel = model('ProductModel');
        return $productModel->updateAutoGeneratedTitle($productId, $generatedTitle);
    }
}

if (!function_exists('previewTitleFromCustomFields')) {
    /**
     * Preview title generation without saving (for AJAX calls)
     * 
     * @param array $customFieldValues Array of field_id => value
     * @param int $categoryId Category ID
     * @return string Preview title
     */
    function previewTitleFromCustomFields($customFieldValues, $categoryId)
    {
        $fieldModel = model('FieldModel');
        
        // Get ALL custom fields for this category
        $customFields = $fieldModel->getCustomFieldsByCategory($categoryId);
        
        if (empty($customFields)) {
            return '';
        }
        
        $titleParts = [];
        
        foreach ($customFields as $field) {
            // Skip radio_button fields from title generation
            if ($field->field_type === 'radio_button') {
                continue;
            }

            $fieldValue = '';

            // Get value from provided array
            if (isset($customFieldValues[$field->id])) {
                $value = $customFieldValues[$field->id];
                
                switch ($field->field_type) {
                    case 'text':
                    case 'number':
                    case 'textarea':
                    case 'date':
                        $fieldValue = trim($value);
                        break;
                        
                    case 'dropdown':
                        // Get option name by ID
                        if (!empty($value)) {
                            $option = $fieldModel->getFieldOption($value);
                            if (!empty($option)) {
                                $fieldValue = getCustomFieldOptionName($option->name_data, selectedLangId(), false);
                            }
                        }
                        break;
                        
                    case 'checkbox':
                        // Handle multiple values for checkbox
                        if (is_array($value) && !empty($value)) {
                            $optionNames = [];
                            foreach ($value as $optionId) {
                                $option = $fieldModel->getFieldOption($optionId);
                                if (!empty($option)) {
                                    $optionNames[] = getCustomFieldOptionName($option->name_data, selectedLangId(), false);
                                }
                            }
                            $fieldValue = implode(', ', $optionNames);
                        }
                        break;
                }
            }
            
            if (!empty($fieldValue)) {
                $titleParts[] = $fieldValue;
            }
        }
        
        $generatedTitle = implode(' ', $titleParts);
        return cleanTitleString($generatedTitle);
    }
}

if (!function_exists('generateSlugFromCustomFields')) {
    /**
     * Generate SEO-friendly product slug from custom fields
     * Similar to title generation but optimized for URL slugs
     *
     * @param int $productId Product ID
     * @param int $categoryId Category ID
     * @param string $separator Separator between field values (default: '-')
     * @return string Generated slug
     */
    function generateSlugFromCustomFields($productId, $categoryId, $separator = '-')
    {
        $fieldModel = model('FieldModel');

        // Get ALL custom fields for this category (ordered by field_order)
        $customFields = $fieldModel->getCustomFieldsByCategory($categoryId);

        if (empty($customFields)) {
            return '';
        }

        $slugParts = [];

        foreach ($customFields as $field) {
            // Skip radio_button fields from slug generation
            if ($field->field_type === 'radio_button') {
                continue;
            }

            $fieldValue = getCustomFieldValueForSlug($field, $productId);

            if (!empty($fieldValue)) {
                $slugParts[] = $fieldValue;
            }
        }

        // Join slug parts with separator
        $generatedSlug = implode($separator, $slugParts);

        // Apply SEO optimization
        $generatedSlug = optimizeSlugForSEO($generatedSlug);

        return $generatedSlug;
    }
}

if (!function_exists('getCustomFieldValueForSlug')) {
    /**
     * Get custom field value formatted for slug generation
     * Similar to getCustomFieldValueForTitle but optimized for slugs
     *
     * @param object $field Custom field object
     * @param int $productId Product ID
     * @return string Formatted field value for slug
     */
    function getCustomFieldValueForSlug($field, $productId)
    {
        $fieldModel = model('FieldModel');

        switch ($field->field_type) {
            case 'text':
            case 'number':
            case 'textarea':
            case 'date':
                // Get direct input value
                $value = $fieldModel->getProductCustomFieldInputValue($field->id, $productId);
                return trim($value);

            case 'dropdown':
                // Get selected option name
                $fieldValues = $fieldModel->getProductCustomFieldValues($field->id, $productId);
                if (!empty($fieldValues)) {
                    $firstValue = $fieldValues[0];
                    if (!empty($firstValue->selected_option_id)) {
                        return getCustomFieldOptionName($firstValue->name_data, selectedLangId(), false);
                    }
                }
                break;

            case 'radio_button':
                // Exclude radio_button fields from slug generation
                return '';
                break;

            case 'checkbox':
                // Get ALL selected options for checkbox (join with separator)
                $fieldValues = $fieldModel->getProductCustomFieldValues($field->id, $productId);
                if (!empty($fieldValues)) {
                    $optionNames = [];
                    foreach ($fieldValues as $value) {
                        if (!empty($value->selected_option_id)) {
                            $optionName = getCustomFieldOptionName($value->name_data, selectedLangId(), false);
                            if (!empty($optionName)) {
                                $optionNames[] = $optionName;
                            }
                        }
                    }
                    return implode(' ', $optionNames); // Use space for slug optimization
                }
                break;
        }

        return '';
    }
}

if (!function_exists('optimizeSlugForSEO')) {
    /**
     * Optimize slug for SEO best practices
     *
     * @param string $slug Raw slug string
     * @return string SEO-optimized slug
     */
    function optimizeSlugForSEO($slug)
    {
        if (empty($slug)) {
            return '';
        }

        // Convert to lowercase
        $slug = mb_strtolower($slug, 'UTF-8');

        // Use CodeIgniter's url_title function for basic slug conversion
        $slug = url_title(convert_accented_characters($slug), '-', true);

        // Additional SEO optimizations
        // Remove extra hyphens and clean up
        $slug = preg_replace('/\-+/', '-', $slug); // Remove consecutive hyphens
        $slug = trim($slug, '-'); // Remove hyphens from start/end

        // Limit length for SEO (50-60 characters is optimal)
        $maxLength = 60;
        if (strlen($slug) > $maxLength) {
            $slug = substr($slug, 0, $maxLength);
            // Try to cut at word boundary (hyphen)
            $lastHyphen = strrpos($slug, '-');
            if ($lastHyphen !== false && $lastHyphen > $maxLength * 0.7) {
                $slug = substr($slug, 0, $lastHyphen);
            }
        }

        // Final cleanup
        $slug = trim($slug, '-');

        // Ensure slug is not empty after optimization
        if (empty($slug)) {
            return 'product';
        }

        return $slug;
    }
}

if (!function_exists('previewSlugFromCustomFields')) {
    /**
     * Preview slug generation without saving (for AJAX calls)
     *
     * @param array $customFieldValues Array of field_id => value
     * @param int $categoryId Category ID
     * @return string Preview slug
     */
    function previewSlugFromCustomFields($customFieldValues, $categoryId)
    {
        $fieldModel = model('FieldModel');

        // Get ALL custom fields for this category
        $customFields = $fieldModel->getCustomFieldsByCategory($categoryId);

        if (empty($customFields)) {
            return '';
        }

        $slugParts = [];

        foreach ($customFields as $field) {
            // Skip radio_button fields from slug generation
            if ($field->field_type === 'radio_button') {
                continue;
            }

            $fieldValue = '';

            // Get value from provided array
            if (isset($customFieldValues[$field->id])) {
                $value = $customFieldValues[$field->id];

                switch ($field->field_type) {
                    case 'text':
                    case 'number':
                    case 'textarea':
                    case 'date':
                        $fieldValue = trim($value);
                        break;

                    case 'dropdown':
                        // Get option name by ID
                        if (!empty($value)) {
                            $option = $fieldModel->getFieldOption($value);
                            if (!empty($option)) {
                                $fieldValue = getCustomFieldOptionName($option->name_data, selectedLangId(), false);
                            }
                        }
                        break;

                    case 'checkbox':
                        // Handle multiple values for checkbox
                        if (is_array($value) && !empty($value)) {
                            $optionNames = [];
                            foreach ($value as $optionId) {
                                $option = $fieldModel->getFieldOption($optionId);
                                if (!empty($option)) {
                                    $optionNames[] = getCustomFieldOptionName($option->name_data, selectedLangId(), false);
                                }
                            }
                            $fieldValue = implode(' ', $optionNames);
                        }
                        break;
                }
            }

            if (!empty($fieldValue)) {
                $slugParts[] = $fieldValue;
            }
        }

        $generatedSlug = implode('-', $slugParts);
        return optimizeSlugForSEO($generatedSlug);
    }
}
