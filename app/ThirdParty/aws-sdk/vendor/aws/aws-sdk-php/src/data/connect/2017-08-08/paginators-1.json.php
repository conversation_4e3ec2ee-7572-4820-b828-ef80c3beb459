<?php
// This file was auto-generated from sdk-root/src/data/connect/2017-08-08/paginators-1.json
return [ 'pagination' => [ 'GetCurrentMetricData' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'GetMetricData' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'ListAgentStatuses' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'AgentStatusSummaryList', ], 'ListApprovedOrigins' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Origins', ], 'ListBots' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'LexBots', ], 'ListContactFlowModules' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'ContactFlowModulesSummaryList', ], 'ListContactFlows' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'ContactFlowSummaryList', ], 'ListContactReferences' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'result_key' => 'ReferenceSummaryList', ], 'ListHoursOfOperations' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'HoursOfOperationSummaryList', ], 'ListInstanceAttributes' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Attributes', ], 'ListInstanceStorageConfigs' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'StorageConfigs', ], 'ListInstances' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'InstanceSummaryList', ], 'ListIntegrationAssociations' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'IntegrationAssociationSummaryList', ], 'ListLambdaFunctions' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'LambdaFunctions', ], 'ListLexBots' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'LexBots', ], 'ListPhoneNumbers' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'PhoneNumberSummaryList', ], 'ListPrompts' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'PromptSummaryList', ], 'ListQueueQuickConnects' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'QuickConnectSummaryList', ], 'ListQueues' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'QueueSummaryList', ], 'ListQuickConnects' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'QuickConnectSummaryList', ], 'ListRoutingProfileQueues' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'RoutingProfileQueueConfigSummaryList', ], 'ListRoutingProfiles' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'RoutingProfileSummaryList', ], 'ListSecurityKeys' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'SecurityKeys', ], 'ListSecurityProfilePermissions' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Permissions', ], 'ListSecurityProfiles' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'SecurityProfileSummaryList', ], 'ListUseCases' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'UseCaseSummaryList', ], 'ListUserHierarchyGroups' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'UserHierarchyGroupSummaryList', ], 'ListUsers' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'UserSummaryList', ], ],];
