<?php
// This file was auto-generated from sdk-root/src/data/rekognition/2016-06-27/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2016-06-27', 'endpointPrefix' => 'rekognition', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'Amazon Rekognition', 'serviceId' => 'Rekognition', 'signatureVersion' => 'v4', 'targetPrefix' => 'RekognitionService', 'uid' => 'rekognition-2016-06-27', ], 'operations' => [ 'CompareFaces' => [ 'name' => 'CompareFaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CompareFacesRequest', ], 'output' => [ 'shape' => 'CompareFacesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'ImageTooLargeException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'InvalidImageFormatException', ], ], ], 'CreateCollection' => [ 'name' => 'CreateCollection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCollectionRequest', ], 'output' => [ 'shape' => 'CreateCollectionResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateDataset' => [ 'name' => 'CreateDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatasetRequest', ], 'output' => [ 'shape' => 'CreateDatasetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateProject' => [ 'name' => 'CreateProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateProjectRequest', ], 'output' => [ 'shape' => 'CreateProjectResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], ], ], 'CreateProjectVersion' => [ 'name' => 'CreateProjectVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateProjectVersionRequest', ], 'output' => [ 'shape' => 'CreateProjectVersionResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateStreamProcessor' => [ 'name' => 'CreateStreamProcessor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStreamProcessorRequest', ], 'output' => [ 'shape' => 'CreateStreamProcessorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'DeleteCollection' => [ 'name' => 'DeleteCollection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCollectionRequest', ], 'output' => [ 'shape' => 'DeleteCollectionResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteDataset' => [ 'name' => 'DeleteDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDatasetRequest', ], 'output' => [ 'shape' => 'DeleteDatasetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteFaces' => [ 'name' => 'DeleteFaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFacesRequest', ], 'output' => [ 'shape' => 'DeleteFacesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteProject' => [ 'name' => 'DeleteProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteProjectRequest', ], 'output' => [ 'shape' => 'DeleteProjectResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], ], ], 'DeleteProjectVersion' => [ 'name' => 'DeleteProjectVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteProjectVersionRequest', ], 'output' => [ 'shape' => 'DeleteProjectVersionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], ], ], 'DeleteStreamProcessor' => [ 'name' => 'DeleteStreamProcessor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteStreamProcessorRequest', ], 'output' => [ 'shape' => 'DeleteStreamProcessorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], ], ], 'DescribeCollection' => [ 'name' => 'DescribeCollection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCollectionRequest', ], 'output' => [ 'shape' => 'DescribeCollectionResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeDataset' => [ 'name' => 'DescribeDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDatasetRequest', ], 'output' => [ 'shape' => 'DescribeDatasetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeProjectVersions' => [ 'name' => 'DescribeProjectVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeProjectVersionsRequest', ], 'output' => [ 'shape' => 'DescribeProjectVersionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], ], ], 'DescribeProjects' => [ 'name' => 'DescribeProjects', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeProjectsRequest', ], 'output' => [ 'shape' => 'DescribeProjectsResponse', ], 'errors' => [ [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], ], ], 'DescribeStreamProcessor' => [ 'name' => 'DescribeStreamProcessor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStreamProcessorRequest', ], 'output' => [ 'shape' => 'DescribeStreamProcessorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], ], ], 'DetectCustomLabels' => [ 'name' => 'DetectCustomLabels', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectCustomLabelsRequest', ], 'output' => [ 'shape' => 'DetectCustomLabelsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceNotReadyException', ], [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ImageTooLargeException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'InvalidImageFormatException', ], ], ], 'DetectFaces' => [ 'name' => 'DetectFaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectFacesRequest', ], 'output' => [ 'shape' => 'DetectFacesResponse', ], 'errors' => [ [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ImageTooLargeException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'InvalidImageFormatException', ], ], ], 'DetectLabels' => [ 'name' => 'DetectLabels', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectLabelsRequest', ], 'output' => [ 'shape' => 'DetectLabelsResponse', ], 'errors' => [ [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ImageTooLargeException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'InvalidImageFormatException', ], ], ], 'DetectModerationLabels' => [ 'name' => 'DetectModerationLabels', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectModerationLabelsRequest', ], 'output' => [ 'shape' => 'DetectModerationLabelsResponse', ], 'errors' => [ [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ImageTooLargeException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'InvalidImageFormatException', ], [ 'shape' => 'HumanLoopQuotaExceededException', ], ], ], 'DetectProtectiveEquipment' => [ 'name' => 'DetectProtectiveEquipment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectProtectiveEquipmentRequest', ], 'output' => [ 'shape' => 'DetectProtectiveEquipmentResponse', ], 'errors' => [ [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ImageTooLargeException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'InvalidImageFormatException', ], ], ], 'DetectText' => [ 'name' => 'DetectText', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectTextRequest', ], 'output' => [ 'shape' => 'DetectTextResponse', ], 'errors' => [ [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ImageTooLargeException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'InvalidImageFormatException', ], ], ], 'DistributeDatasetEntries' => [ 'name' => 'DistributeDatasetEntries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DistributeDatasetEntriesRequest', ], 'output' => [ 'shape' => 'DistributeDatasetEntriesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotReadyException', ], ], ], 'GetCelebrityInfo' => [ 'name' => 'GetCelebrityInfo', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCelebrityInfoRequest', ], 'output' => [ 'shape' => 'GetCelebrityInfoResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetCelebrityRecognition' => [ 'name' => 'GetCelebrityRecognition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCelebrityRecognitionRequest', ], 'output' => [ 'shape' => 'GetCelebrityRecognitionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetContentModeration' => [ 'name' => 'GetContentModeration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetContentModerationRequest', ], 'output' => [ 'shape' => 'GetContentModerationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetFaceDetection' => [ 'name' => 'GetFaceDetection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetFaceDetectionRequest', ], 'output' => [ 'shape' => 'GetFaceDetectionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetFaceSearch' => [ 'name' => 'GetFaceSearch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetFaceSearchRequest', ], 'output' => [ 'shape' => 'GetFaceSearchResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetLabelDetection' => [ 'name' => 'GetLabelDetection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLabelDetectionRequest', ], 'output' => [ 'shape' => 'GetLabelDetectionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetPersonTracking' => [ 'name' => 'GetPersonTracking', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPersonTrackingRequest', ], 'output' => [ 'shape' => 'GetPersonTrackingResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetSegmentDetection' => [ 'name' => 'GetSegmentDetection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSegmentDetectionRequest', ], 'output' => [ 'shape' => 'GetSegmentDetectionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetTextDetection' => [ 'name' => 'GetTextDetection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTextDetectionRequest', ], 'output' => [ 'shape' => 'GetTextDetectionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'IndexFaces' => [ 'name' => 'IndexFaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'IndexFacesRequest', ], 'output' => [ 'shape' => 'IndexFacesResponse', ], 'errors' => [ [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ImageTooLargeException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidImageFormatException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ListCollections' => [ 'name' => 'ListCollections', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCollectionsRequest', ], 'output' => [ 'shape' => 'ListCollectionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListDatasetEntries' => [ 'name' => 'ListDatasetEntries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDatasetEntriesRequest', ], 'output' => [ 'shape' => 'ListDatasetEntriesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ResourceNotReadyException', ], ], ], 'ListDatasetLabels' => [ 'name' => 'ListDatasetLabels', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDatasetLabelsRequest', ], 'output' => [ 'shape' => 'ListDatasetLabelsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceNotReadyException', ], ], ], 'ListFaces' => [ 'name' => 'ListFaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFacesRequest', ], 'output' => [ 'shape' => 'ListFacesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListStreamProcessors' => [ 'name' => 'ListStreamProcessors', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStreamProcessorsRequest', ], 'output' => [ 'shape' => 'ListStreamProcessorsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], ], ], 'RecognizeCelebrities' => [ 'name' => 'RecognizeCelebrities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RecognizeCelebritiesRequest', ], 'output' => [ 'shape' => 'RecognizeCelebritiesResponse', ], 'errors' => [ [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidImageFormatException', ], [ 'shape' => 'ImageTooLargeException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'InvalidImageFormatException', ], ], ], 'SearchFaces' => [ 'name' => 'SearchFaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchFacesRequest', ], 'output' => [ 'shape' => 'SearchFacesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'SearchFacesByImage' => [ 'name' => 'SearchFacesByImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchFacesByImageRequest', ], 'output' => [ 'shape' => 'SearchFacesByImageResponse', ], 'errors' => [ [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ImageTooLargeException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidImageFormatException', ], ], ], 'StartCelebrityRecognition' => [ 'name' => 'StartCelebrityRecognition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartCelebrityRecognitionRequest', ], 'output' => [ 'shape' => 'StartCelebrityRecognitionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'VideoTooLargeException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'StartContentModeration' => [ 'name' => 'StartContentModeration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartContentModerationRequest', ], 'output' => [ 'shape' => 'StartContentModerationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'VideoTooLargeException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'StartFaceDetection' => [ 'name' => 'StartFaceDetection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartFaceDetectionRequest', ], 'output' => [ 'shape' => 'StartFaceDetectionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'VideoTooLargeException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'StartFaceSearch' => [ 'name' => 'StartFaceSearch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartFaceSearchRequest', ], 'output' => [ 'shape' => 'StartFaceSearchResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'VideoTooLargeException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'StartLabelDetection' => [ 'name' => 'StartLabelDetection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartLabelDetectionRequest', ], 'output' => [ 'shape' => 'StartLabelDetectionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'VideoTooLargeException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'StartPersonTracking' => [ 'name' => 'StartPersonTracking', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartPersonTrackingRequest', ], 'output' => [ 'shape' => 'StartPersonTrackingResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'VideoTooLargeException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'StartProjectVersion' => [ 'name' => 'StartProjectVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartProjectVersionRequest', ], 'output' => [ 'shape' => 'StartProjectVersionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], ], ], 'StartSegmentDetection' => [ 'name' => 'StartSegmentDetection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartSegmentDetectionRequest', ], 'output' => [ 'shape' => 'StartSegmentDetectionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'VideoTooLargeException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'StartStreamProcessor' => [ 'name' => 'StartStreamProcessor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartStreamProcessorRequest', ], 'output' => [ 'shape' => 'StartStreamProcessorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], ], ], 'StartTextDetection' => [ 'name' => 'StartTextDetection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartTextDetectionRequest', ], 'output' => [ 'shape' => 'StartTextDetectionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidS3ObjectException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'VideoTooLargeException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'StopProjectVersion' => [ 'name' => 'StopProjectVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopProjectVersionRequest', ], 'output' => [ 'shape' => 'StopProjectVersionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], ], ], 'StopStreamProcessor' => [ 'name' => 'StopStreamProcessor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopStreamProcessorRequest', ], 'output' => [ 'shape' => 'StopStreamProcessorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], ], ], 'UpdateDatasetEntries' => [ 'name' => 'UpdateDatasetEntries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDatasetEntriesRequest', ], 'output' => [ 'shape' => 'UpdateDatasetEntriesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'AgeRange' => [ 'type' => 'structure', 'members' => [ 'Low' => [ 'shape' => 'UInteger', ], 'High' => [ 'shape' => 'UInteger', ], ], ], 'Asset' => [ 'type' => 'structure', 'members' => [ 'GroundTruthManifest' => [ 'shape' => 'GroundTruthManifest', ], ], ], 'Assets' => [ 'type' => 'list', 'member' => [ 'shape' => 'Asset', ], ], 'Attribute' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'ALL', ], ], 'Attributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Attribute', ], ], 'AudioMetadata' => [ 'type' => 'structure', 'members' => [ 'Codec' => [ 'shape' => 'String', ], 'DurationMillis' => [ 'shape' => 'ULong', ], 'SampleRate' => [ 'shape' => 'ULong', ], 'NumberOfChannels' => [ 'shape' => 'ULong', ], ], ], 'AudioMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudioMetadata', ], ], 'Beard' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'Boolean', ], 'Confidence' => [ 'shape' => 'Percent', ], ], ], 'BlackFrame' => [ 'type' => 'structure', 'members' => [ 'MaxPixelThreshold' => [ 'shape' => 'MaxPixelThreshold', ], 'MinCoveragePercentage' => [ 'shape' => 'MinCoveragePercentage', ], ], ], 'BodyPart' => [ 'type' => 'string', 'enum' => [ 'FACE', 'HEAD', 'LEFT_HAND', 'RIGHT_HAND', ], ], 'BodyParts' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProtectiveEquipmentBodyPart', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BoundingBox' => [ 'type' => 'structure', 'members' => [ 'Width' => [ 'shape' => 'Float', ], 'Height' => [ 'shape' => 'Float', ], 'Left' => [ 'shape' => 'Float', ], 'Top' => [ 'shape' => 'Float', ], ], ], 'BoundingBoxHeight' => [ 'type' => 'float', 'max' => 1, 'min' => 0, ], 'BoundingBoxWidth' => [ 'type' => 'float', 'max' => 1, 'min' => 0, ], 'Celebrity' => [ 'type' => 'structure', 'members' => [ 'Urls' => [ 'shape' => 'Urls', ], 'Name' => [ 'shape' => 'String', ], 'Id' => [ 'shape' => 'RekognitionUniqueId', ], 'Face' => [ 'shape' => 'ComparedFace', ], 'MatchConfidence' => [ 'shape' => 'Percent', ], 'KnownGender' => [ 'shape' => 'KnownGender', ], ], ], 'CelebrityDetail' => [ 'type' => 'structure', 'members' => [ 'Urls' => [ 'shape' => 'Urls', ], 'Name' => [ 'shape' => 'String', ], 'Id' => [ 'shape' => 'RekognitionUniqueId', ], 'Confidence' => [ 'shape' => 'Percent', ], 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'Face' => [ 'shape' => 'FaceDetail', ], 'KnownGender' => [ 'shape' => 'KnownGender', ], ], ], 'CelebrityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Celebrity', ], ], 'CelebrityRecognition' => [ 'type' => 'structure', 'members' => [ 'Timestamp' => [ 'shape' => 'Timestamp', ], 'Celebrity' => [ 'shape' => 'CelebrityDetail', ], ], ], 'CelebrityRecognitionSortBy' => [ 'type' => 'string', 'enum' => [ 'ID', 'TIMESTAMP', ], ], 'CelebrityRecognitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'CelebrityRecognition', ], ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_]+$', ], 'CollectionId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.\\-]+', ], 'CollectionIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollectionId', ], ], 'CompareFacesMatch' => [ 'type' => 'structure', 'members' => [ 'Similarity' => [ 'shape' => 'Percent', ], 'Face' => [ 'shape' => 'ComparedFace', ], ], ], 'CompareFacesMatchList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CompareFacesMatch', ], ], 'CompareFacesRequest' => [ 'type' => 'structure', 'required' => [ 'SourceImage', 'TargetImage', ], 'members' => [ 'SourceImage' => [ 'shape' => 'Image', ], 'TargetImage' => [ 'shape' => 'Image', ], 'SimilarityThreshold' => [ 'shape' => 'Percent', ], 'QualityFilter' => [ 'shape' => 'QualityFilter', ], ], ], 'CompareFacesResponse' => [ 'type' => 'structure', 'members' => [ 'SourceImageFace' => [ 'shape' => 'ComparedSourceImageFace', ], 'FaceMatches' => [ 'shape' => 'CompareFacesMatchList', ], 'UnmatchedFaces' => [ 'shape' => 'CompareFacesUnmatchList', ], 'SourceImageOrientationCorrection' => [ 'shape' => 'OrientationCorrection', ], 'TargetImageOrientationCorrection' => [ 'shape' => 'OrientationCorrection', ], ], ], 'CompareFacesUnmatchList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComparedFace', ], ], 'ComparedFace' => [ 'type' => 'structure', 'members' => [ 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'Confidence' => [ 'shape' => 'Percent', ], 'Landmarks' => [ 'shape' => 'Landmarks', ], 'Pose' => [ 'shape' => 'Pose', ], 'Quality' => [ 'shape' => 'ImageQuality', ], 'Emotions' => [ 'shape' => 'Emotions', ], 'Smile' => [ 'shape' => 'Smile', ], ], ], 'ComparedFaceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComparedFace', ], ], 'ComparedSourceImageFace' => [ 'type' => 'structure', 'members' => [ 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'Confidence' => [ 'shape' => 'Percent', ], ], ], 'ContentClassifier' => [ 'type' => 'string', 'enum' => [ 'FreeOfPersonallyIdentifiableInformation', 'FreeOfAdultContent', ], ], 'ContentClassifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContentClassifier', ], 'max' => 256, ], 'ContentModerationDetection' => [ 'type' => 'structure', 'members' => [ 'Timestamp' => [ 'shape' => 'Timestamp', ], 'ModerationLabel' => [ 'shape' => 'ModerationLabel', ], ], ], 'ContentModerationDetections' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContentModerationDetection', ], ], 'ContentModerationSortBy' => [ 'type' => 'string', 'enum' => [ 'NAME', 'TIMESTAMP', ], ], 'CoversBodyPart' => [ 'type' => 'structure', 'members' => [ 'Confidence' => [ 'shape' => 'Percent', ], 'Value' => [ 'shape' => 'Boolean', ], ], ], 'CreateCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionId', ], 'members' => [ 'CollectionId' => [ 'shape' => 'CollectionId', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateCollectionResponse' => [ 'type' => 'structure', 'members' => [ 'StatusCode' => [ 'shape' => 'UInteger', ], 'CollectionArn' => [ 'shape' => 'String', ], 'FaceModelVersion' => [ 'shape' => 'String', ], ], ], 'CreateDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetType', 'ProjectArn', ], 'members' => [ 'DatasetSource' => [ 'shape' => 'DatasetSource', ], 'DatasetType' => [ 'shape' => 'DatasetType', ], 'ProjectArn' => [ 'shape' => 'ProjectArn', ], ], ], 'CreateDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetArn' => [ 'shape' => 'DatasetArn', ], ], ], 'CreateProjectRequest' => [ 'type' => 'structure', 'required' => [ 'ProjectName', ], 'members' => [ 'ProjectName' => [ 'shape' => 'ProjectName', ], ], ], 'CreateProjectResponse' => [ 'type' => 'structure', 'members' => [ 'ProjectArn' => [ 'shape' => 'ProjectArn', ], ], ], 'CreateProjectVersionRequest' => [ 'type' => 'structure', 'required' => [ 'ProjectArn', 'VersionName', 'OutputConfig', ], 'members' => [ 'ProjectArn' => [ 'shape' => 'ProjectArn', ], 'VersionName' => [ 'shape' => 'VersionName', ], 'OutputConfig' => [ 'shape' => 'OutputConfig', ], 'TrainingData' => [ 'shape' => 'TrainingData', ], 'TestingData' => [ 'shape' => 'TestingData', ], 'Tags' => [ 'shape' => 'TagMap', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'CreateProjectVersionResponse' => [ 'type' => 'structure', 'members' => [ 'ProjectVersionArn' => [ 'shape' => 'ProjectVersionArn', ], ], ], 'CreateStreamProcessorRequest' => [ 'type' => 'structure', 'required' => [ 'Input', 'Output', 'Name', 'Settings', 'RoleArn', ], 'members' => [ 'Input' => [ 'shape' => 'StreamProcessorInput', ], 'Output' => [ 'shape' => 'StreamProcessorOutput', ], 'Name' => [ 'shape' => 'StreamProcessorName', ], 'Settings' => [ 'shape' => 'StreamProcessorSettings', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateStreamProcessorResponse' => [ 'type' => 'structure', 'members' => [ 'StreamProcessorArn' => [ 'shape' => 'StreamProcessorArn', ], ], ], 'CustomLabel' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Confidence' => [ 'shape' => 'Percent', ], 'Geometry' => [ 'shape' => 'Geometry', ], ], ], 'CustomLabels' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomLabel', ], ], 'DatasetArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '(^arn:[a-z\\d-]+:rekognition:[a-z\\d-]+:\\d{12}:project\\/[a-zA-Z0-9_.\\-]{1,255}\\/dataset\\/(train|test)\\/[0-9]+$)', ], 'DatasetChanges' => [ 'type' => 'structure', 'required' => [ 'GroundTruth', ], 'members' => [ 'GroundTruth' => [ 'shape' => 'GroundTruthBlob', ], ], ], 'DatasetDescription' => [ 'type' => 'structure', 'members' => [ 'CreationTimestamp' => [ 'shape' => 'DateTime', ], 'LastUpdatedTimestamp' => [ 'shape' => 'DateTime', ], 'Status' => [ 'shape' => 'DatasetStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'StatusMessageCode' => [ 'shape' => 'DatasetStatusMessageCode', ], 'DatasetStats' => [ 'shape' => 'DatasetStats', ], ], ], 'DatasetEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetEntry', ], ], 'DatasetEntry' => [ 'type' => 'string', 'max' => 100000, 'min' => 1, 'pattern' => '^\\{.*\\}$', ], 'DatasetLabel' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.{1,}', ], 'DatasetLabelDescription' => [ 'type' => 'structure', 'members' => [ 'LabelName' => [ 'shape' => 'DatasetLabel', ], 'LabelStats' => [ 'shape' => 'DatasetLabelStats', ], ], ], 'DatasetLabelDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetLabelDescription', ], ], 'DatasetLabelStats' => [ 'type' => 'structure', 'members' => [ 'EntryCount' => [ 'shape' => 'UInteger', ], 'BoundingBoxCount' => [ 'shape' => 'UInteger', ], ], ], 'DatasetLabels' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetLabel', ], 'max' => 10, 'min' => 1, ], 'DatasetMetadata' => [ 'type' => 'structure', 'members' => [ 'CreationTimestamp' => [ 'shape' => 'DateTime', ], 'DatasetType' => [ 'shape' => 'DatasetType', ], 'DatasetArn' => [ 'shape' => 'DatasetArn', ], 'Status' => [ 'shape' => 'DatasetStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'StatusMessageCode' => [ 'shape' => 'DatasetStatusMessageCode', ], ], ], 'DatasetMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetMetadata', ], ], 'DatasetSource' => [ 'type' => 'structure', 'members' => [ 'GroundTruthManifest' => [ 'shape' => 'GroundTruthManifest', ], 'DatasetArn' => [ 'shape' => 'DatasetArn', ], ], ], 'DatasetStats' => [ 'type' => 'structure', 'members' => [ 'LabeledEntries' => [ 'shape' => 'UInteger', ], 'TotalEntries' => [ 'shape' => 'UInteger', ], 'TotalLabels' => [ 'shape' => 'UInteger', ], 'ErrorEntries' => [ 'shape' => 'UInteger', ], ], ], 'DatasetStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_COMPLETE', 'CREATE_FAILED', 'UPDATE_IN_PROGRESS', 'UPDATE_COMPLETE', 'UPDATE_FAILED', 'DELETE_IN_PROGRESS', ], ], 'DatasetStatusMessageCode' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'SERVICE_ERROR', 'CLIENT_ERROR', ], ], 'DatasetType' => [ 'type' => 'string', 'enum' => [ 'TRAIN', 'TEST', ], ], 'DateTime' => [ 'type' => 'timestamp', ], 'Degree' => [ 'type' => 'float', 'max' => 180, 'min' => -180, ], 'DeleteCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionId', ], 'members' => [ 'CollectionId' => [ 'shape' => 'CollectionId', ], ], ], 'DeleteCollectionResponse' => [ 'type' => 'structure', 'members' => [ 'StatusCode' => [ 'shape' => 'UInteger', ], ], ], 'DeleteDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetArn', ], 'members' => [ 'DatasetArn' => [ 'shape' => 'DatasetArn', ], ], ], 'DeleteDatasetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFacesRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionId', 'FaceIds', ], 'members' => [ 'CollectionId' => [ 'shape' => 'CollectionId', ], 'FaceIds' => [ 'shape' => 'FaceIdList', ], ], ], 'DeleteFacesResponse' => [ 'type' => 'structure', 'members' => [ 'DeletedFaces' => [ 'shape' => 'FaceIdList', ], ], ], 'DeleteProjectRequest' => [ 'type' => 'structure', 'required' => [ 'ProjectArn', ], 'members' => [ 'ProjectArn' => [ 'shape' => 'ProjectArn', ], ], ], 'DeleteProjectResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'ProjectStatus', ], ], ], 'DeleteProjectVersionRequest' => [ 'type' => 'structure', 'required' => [ 'ProjectVersionArn', ], 'members' => [ 'ProjectVersionArn' => [ 'shape' => 'ProjectVersionArn', ], ], ], 'DeleteProjectVersionResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'ProjectVersionStatus', ], ], ], 'DeleteStreamProcessorRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'StreamProcessorName', ], ], ], 'DeleteStreamProcessorResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionId', ], 'members' => [ 'CollectionId' => [ 'shape' => 'CollectionId', ], ], ], 'DescribeCollectionResponse' => [ 'type' => 'structure', 'members' => [ 'FaceCount' => [ 'shape' => 'ULong', ], 'FaceModelVersion' => [ 'shape' => 'String', ], 'CollectionARN' => [ 'shape' => 'String', ], 'CreationTimestamp' => [ 'shape' => 'DateTime', ], ], ], 'DescribeDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetArn', ], 'members' => [ 'DatasetArn' => [ 'shape' => 'DatasetArn', ], ], ], 'DescribeDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetDescription' => [ 'shape' => 'DatasetDescription', ], ], ], 'DescribeProjectVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'ProjectArn', ], 'members' => [ 'ProjectArn' => [ 'shape' => 'ProjectArn', ], 'VersionNames' => [ 'shape' => 'VersionNames', ], 'NextToken' => [ 'shape' => 'ExtendedPaginationToken', ], 'MaxResults' => [ 'shape' => 'ProjectVersionsPageSize', ], ], ], 'DescribeProjectVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'ProjectVersionDescriptions' => [ 'shape' => 'ProjectVersionDescriptions', ], 'NextToken' => [ 'shape' => 'ExtendedPaginationToken', ], ], ], 'DescribeProjectsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'ExtendedPaginationToken', ], 'MaxResults' => [ 'shape' => 'ProjectsPageSize', ], 'ProjectNames' => [ 'shape' => 'ProjectNames', ], ], ], 'DescribeProjectsResponse' => [ 'type' => 'structure', 'members' => [ 'ProjectDescriptions' => [ 'shape' => 'ProjectDescriptions', ], 'NextToken' => [ 'shape' => 'ExtendedPaginationToken', ], ], ], 'DescribeStreamProcessorRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'StreamProcessorName', ], ], ], 'DescribeStreamProcessorResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'StreamProcessorName', ], 'StreamProcessorArn' => [ 'shape' => 'StreamProcessorArn', ], 'Status' => [ 'shape' => 'StreamProcessorStatus', ], 'StatusMessage' => [ 'shape' => 'String', ], 'CreationTimestamp' => [ 'shape' => 'DateTime', ], 'LastUpdateTimestamp' => [ 'shape' => 'DateTime', ], 'Input' => [ 'shape' => 'StreamProcessorInput', ], 'Output' => [ 'shape' => 'StreamProcessorOutput', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Settings' => [ 'shape' => 'StreamProcessorSettings', ], ], ], 'DetectCustomLabelsRequest' => [ 'type' => 'structure', 'required' => [ 'ProjectVersionArn', 'Image', ], 'members' => [ 'ProjectVersionArn' => [ 'shape' => 'ProjectVersionArn', ], 'Image' => [ 'shape' => 'Image', ], 'MaxResults' => [ 'shape' => 'UInteger', ], 'MinConfidence' => [ 'shape' => 'Percent', ], ], ], 'DetectCustomLabelsResponse' => [ 'type' => 'structure', 'members' => [ 'CustomLabels' => [ 'shape' => 'CustomLabels', ], ], ], 'DetectFacesRequest' => [ 'type' => 'structure', 'required' => [ 'Image', ], 'members' => [ 'Image' => [ 'shape' => 'Image', ], 'Attributes' => [ 'shape' => 'Attributes', ], ], ], 'DetectFacesResponse' => [ 'type' => 'structure', 'members' => [ 'FaceDetails' => [ 'shape' => 'FaceDetailList', ], 'OrientationCorrection' => [ 'shape' => 'OrientationCorrection', ], ], ], 'DetectLabelsRequest' => [ 'type' => 'structure', 'required' => [ 'Image', ], 'members' => [ 'Image' => [ 'shape' => 'Image', ], 'MaxLabels' => [ 'shape' => 'UInteger', ], 'MinConfidence' => [ 'shape' => 'Percent', ], ], ], 'DetectLabelsResponse' => [ 'type' => 'structure', 'members' => [ 'Labels' => [ 'shape' => 'Labels', ], 'OrientationCorrection' => [ 'shape' => 'OrientationCorrection', ], 'LabelModelVersion' => [ 'shape' => 'String', ], ], ], 'DetectModerationLabelsRequest' => [ 'type' => 'structure', 'required' => [ 'Image', ], 'members' => [ 'Image' => [ 'shape' => 'Image', ], 'MinConfidence' => [ 'shape' => 'Percent', ], 'HumanLoopConfig' => [ 'shape' => 'HumanLoopConfig', ], ], ], 'DetectModerationLabelsResponse' => [ 'type' => 'structure', 'members' => [ 'ModerationLabels' => [ 'shape' => 'ModerationLabels', ], 'ModerationModelVersion' => [ 'shape' => 'String', ], 'HumanLoopActivationOutput' => [ 'shape' => 'HumanLoopActivationOutput', ], ], ], 'DetectProtectiveEquipmentRequest' => [ 'type' => 'structure', 'required' => [ 'Image', ], 'members' => [ 'Image' => [ 'shape' => 'Image', ], 'SummarizationAttributes' => [ 'shape' => 'ProtectiveEquipmentSummarizationAttributes', ], ], ], 'DetectProtectiveEquipmentResponse' => [ 'type' => 'structure', 'members' => [ 'ProtectiveEquipmentModelVersion' => [ 'shape' => 'String', ], 'Persons' => [ 'shape' => 'ProtectiveEquipmentPersons', ], 'Summary' => [ 'shape' => 'ProtectiveEquipmentSummary', ], ], ], 'DetectTextFilters' => [ 'type' => 'structure', 'members' => [ 'WordFilter' => [ 'shape' => 'DetectionFilter', ], 'RegionsOfInterest' => [ 'shape' => 'RegionsOfInterest', ], ], ], 'DetectTextRequest' => [ 'type' => 'structure', 'required' => [ 'Image', ], 'members' => [ 'Image' => [ 'shape' => 'Image', ], 'Filters' => [ 'shape' => 'DetectTextFilters', ], ], ], 'DetectTextResponse' => [ 'type' => 'structure', 'members' => [ 'TextDetections' => [ 'shape' => 'TextDetectionList', ], 'TextModelVersion' => [ 'shape' => 'String', ], ], ], 'DetectionFilter' => [ 'type' => 'structure', 'members' => [ 'MinConfidence' => [ 'shape' => 'Percent', ], 'MinBoundingBoxHeight' => [ 'shape' => 'BoundingBoxHeight', ], 'MinBoundingBoxWidth' => [ 'shape' => 'BoundingBoxWidth', ], ], ], 'DistributeDataset' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'DatasetArn', ], ], ], 'DistributeDatasetEntriesRequest' => [ 'type' => 'structure', 'required' => [ 'Datasets', ], 'members' => [ 'Datasets' => [ 'shape' => 'DistributeDatasetMetadataList', ], ], ], 'DistributeDatasetEntriesResponse' => [ 'type' => 'structure', 'members' => [], ], 'DistributeDatasetMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DistributeDataset', ], 'max' => 2, 'min' => 2, ], 'Emotion' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'EmotionName', ], 'Confidence' => [ 'shape' => 'Percent', ], ], ], 'EmotionName' => [ 'type' => 'string', 'enum' => [ 'HAPPY', 'SAD', 'ANGRY', 'CONFUSED', 'DISGUSTED', 'SURPRISED', 'CALM', 'UNKNOWN', 'FEAR', ], ], 'Emotions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Emotion', ], ], 'EquipmentDetection' => [ 'type' => 'structure', 'members' => [ 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'Confidence' => [ 'shape' => 'Percent', ], 'Type' => [ 'shape' => 'ProtectiveEquipmentType', ], 'CoversBodyPart' => [ 'shape' => 'CoversBodyPart', ], ], ], 'EquipmentDetections' => [ 'type' => 'list', 'member' => [ 'shape' => 'EquipmentDetection', ], ], 'EvaluationResult' => [ 'type' => 'structure', 'members' => [ 'F1Score' => [ 'shape' => 'Float', ], 'Summary' => [ 'shape' => 'Summary', ], ], ], 'ExtendedPaginationToken' => [ 'type' => 'string', 'max' => 1024, ], 'ExternalImageId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.\\-:]+', ], 'EyeOpen' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'Boolean', ], 'Confidence' => [ 'shape' => 'Percent', ], ], ], 'Eyeglasses' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'Boolean', ], 'Confidence' => [ 'shape' => 'Percent', ], ], ], 'Face' => [ 'type' => 'structure', 'members' => [ 'FaceId' => [ 'shape' => 'FaceId', ], 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'ImageId' => [ 'shape' => 'ImageId', ], 'ExternalImageId' => [ 'shape' => 'ExternalImageId', ], 'Confidence' => [ 'shape' => 'Percent', ], ], ], 'FaceAttributes' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'ALL', ], ], 'FaceDetail' => [ 'type' => 'structure', 'members' => [ 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'AgeRange' => [ 'shape' => 'AgeRange', ], 'Smile' => [ 'shape' => 'Smile', ], 'Eyeglasses' => [ 'shape' => 'Eyeglasses', ], 'Sunglasses' => [ 'shape' => 'Sunglasses', ], 'Gender' => [ 'shape' => 'Gender', ], 'Beard' => [ 'shape' => 'Beard', ], 'Mustache' => [ 'shape' => 'Mustache', ], 'EyesOpen' => [ 'shape' => 'EyeOpen', ], 'MouthOpen' => [ 'shape' => 'MouthOpen', ], 'Emotions' => [ 'shape' => 'Emotions', ], 'Landmarks' => [ 'shape' => 'Landmarks', ], 'Pose' => [ 'shape' => 'Pose', ], 'Quality' => [ 'shape' => 'ImageQuality', ], 'Confidence' => [ 'shape' => 'Percent', ], ], ], 'FaceDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FaceDetail', ], ], 'FaceDetection' => [ 'type' => 'structure', 'members' => [ 'Timestamp' => [ 'shape' => 'Timestamp', ], 'Face' => [ 'shape' => 'FaceDetail', ], ], ], 'FaceDetections' => [ 'type' => 'list', 'member' => [ 'shape' => 'FaceDetection', ], ], 'FaceId' => [ 'type' => 'string', 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'FaceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FaceId', ], 'max' => 4096, 'min' => 1, ], 'FaceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Face', ], ], 'FaceMatch' => [ 'type' => 'structure', 'members' => [ 'Similarity' => [ 'shape' => 'Percent', ], 'Face' => [ 'shape' => 'Face', ], ], ], 'FaceMatchList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FaceMatch', ], ], 'FaceModelVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'FaceRecord' => [ 'type' => 'structure', 'members' => [ 'Face' => [ 'shape' => 'Face', ], 'FaceDetail' => [ 'shape' => 'FaceDetail', ], ], ], 'FaceRecordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FaceRecord', ], ], 'FaceSearchSettings' => [ 'type' => 'structure', 'members' => [ 'CollectionId' => [ 'shape' => 'CollectionId', ], 'FaceMatchThreshold' => [ 'shape' => 'Percent', ], ], ], 'FaceSearchSortBy' => [ 'type' => 'string', 'enum' => [ 'INDEX', 'TIMESTAMP', ], ], 'Float' => [ 'type' => 'float', ], 'FlowDefinitionArn' => [ 'type' => 'string', 'max' => 256, ], 'Gender' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'GenderType', ], 'Confidence' => [ 'shape' => 'Percent', ], ], ], 'GenderType' => [ 'type' => 'string', 'enum' => [ 'Male', 'Female', ], ], 'Geometry' => [ 'type' => 'structure', 'members' => [ 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'Polygon' => [ 'shape' => 'Polygon', ], ], ], 'GetCelebrityInfoRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'RekognitionUniqueId', ], ], ], 'GetCelebrityInfoResponse' => [ 'type' => 'structure', 'members' => [ 'Urls' => [ 'shape' => 'Urls', ], 'Name' => [ 'shape' => 'String', ], 'KnownGender' => [ 'shape' => 'KnownGender', ], ], ], 'GetCelebrityRecognitionRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'SortBy' => [ 'shape' => 'CelebrityRecognitionSortBy', ], ], ], 'GetCelebrityRecognitionResponse' => [ 'type' => 'structure', 'members' => [ 'JobStatus' => [ 'shape' => 'VideoJobStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'VideoMetadata' => [ 'shape' => 'VideoMetadata', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'Celebrities' => [ 'shape' => 'CelebrityRecognitions', ], ], ], 'GetContentModerationRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'SortBy' => [ 'shape' => 'ContentModerationSortBy', ], ], ], 'GetContentModerationResponse' => [ 'type' => 'structure', 'members' => [ 'JobStatus' => [ 'shape' => 'VideoJobStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'VideoMetadata' => [ 'shape' => 'VideoMetadata', ], 'ModerationLabels' => [ 'shape' => 'ContentModerationDetections', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'ModerationModelVersion' => [ 'shape' => 'String', ], ], ], 'GetFaceDetectionRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'GetFaceDetectionResponse' => [ 'type' => 'structure', 'members' => [ 'JobStatus' => [ 'shape' => 'VideoJobStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'VideoMetadata' => [ 'shape' => 'VideoMetadata', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'Faces' => [ 'shape' => 'FaceDetections', ], ], ], 'GetFaceSearchRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'SortBy' => [ 'shape' => 'FaceSearchSortBy', ], ], ], 'GetFaceSearchResponse' => [ 'type' => 'structure', 'members' => [ 'JobStatus' => [ 'shape' => 'VideoJobStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'VideoMetadata' => [ 'shape' => 'VideoMetadata', ], 'Persons' => [ 'shape' => 'PersonMatches', ], ], ], 'GetLabelDetectionRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'SortBy' => [ 'shape' => 'LabelDetectionSortBy', ], ], ], 'GetLabelDetectionResponse' => [ 'type' => 'structure', 'members' => [ 'JobStatus' => [ 'shape' => 'VideoJobStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'VideoMetadata' => [ 'shape' => 'VideoMetadata', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'Labels' => [ 'shape' => 'LabelDetections', ], 'LabelModelVersion' => [ 'shape' => 'String', ], ], ], 'GetPersonTrackingRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'SortBy' => [ 'shape' => 'PersonTrackingSortBy', ], ], ], 'GetPersonTrackingResponse' => [ 'type' => 'structure', 'members' => [ 'JobStatus' => [ 'shape' => 'VideoJobStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'VideoMetadata' => [ 'shape' => 'VideoMetadata', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'Persons' => [ 'shape' => 'PersonDetections', ], ], ], 'GetSegmentDetectionRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'GetSegmentDetectionResponse' => [ 'type' => 'structure', 'members' => [ 'JobStatus' => [ 'shape' => 'VideoJobStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'VideoMetadata' => [ 'shape' => 'VideoMetadataList', ], 'AudioMetadata' => [ 'shape' => 'AudioMetadataList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'Segments' => [ 'shape' => 'SegmentDetections', ], 'SelectedSegmentTypes' => [ 'shape' => 'SegmentTypesInfo', ], ], ], 'GetTextDetectionRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'GetTextDetectionResponse' => [ 'type' => 'structure', 'members' => [ 'JobStatus' => [ 'shape' => 'VideoJobStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'VideoMetadata' => [ 'shape' => 'VideoMetadata', ], 'TextDetections' => [ 'shape' => 'TextDetectionResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'TextModelVersion' => [ 'shape' => 'String', ], ], ], 'GroundTruthBlob' => [ 'type' => 'blob', 'max' => 5242880, 'min' => 1, ], 'GroundTruthManifest' => [ 'type' => 'structure', 'members' => [ 'S3Object' => [ 'shape' => 'S3Object', ], ], ], 'HasErrors' => [ 'type' => 'boolean', ], 'HumanLoopActivationConditionsEvaluationResults' => [ 'type' => 'string', 'max' => 10240, ], 'HumanLoopActivationOutput' => [ 'type' => 'structure', 'members' => [ 'HumanLoopArn' => [ 'shape' => 'HumanLoopArn', ], 'HumanLoopActivationReasons' => [ 'shape' => 'HumanLoopActivationReasons', ], 'HumanLoopActivationConditionsEvaluationResults' => [ 'shape' => 'HumanLoopActivationConditionsEvaluationResults', 'jsonvalue' => true, ], ], ], 'HumanLoopActivationReason' => [ 'type' => 'string', ], 'HumanLoopActivationReasons' => [ 'type' => 'list', 'member' => [ 'shape' => 'HumanLoopActivationReason', ], 'min' => 1, ], 'HumanLoopArn' => [ 'type' => 'string', 'max' => 256, ], 'HumanLoopConfig' => [ 'type' => 'structure', 'required' => [ 'HumanLoopName', 'FlowDefinitionArn', ], 'members' => [ 'HumanLoopName' => [ 'shape' => 'HumanLoopName', ], 'FlowDefinitionArn' => [ 'shape' => 'FlowDefinitionArn', ], 'DataAttributes' => [ 'shape' => 'HumanLoopDataAttributes', ], ], ], 'HumanLoopDataAttributes' => [ 'type' => 'structure', 'members' => [ 'ContentClassifiers' => [ 'shape' => 'ContentClassifiers', ], ], ], 'HumanLoopName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-z0-9](-*[a-z0-9])*', ], 'HumanLoopQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'String', ], 'QuotaCode' => [ 'shape' => 'String', ], 'ServiceCode' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'IdempotentParameterMismatchException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Image' => [ 'type' => 'structure', 'members' => [ 'Bytes' => [ 'shape' => 'ImageBlob', ], 'S3Object' => [ 'shape' => 'S3Object', ], ], ], 'ImageBlob' => [ 'type' => 'blob', 'max' => 5242880, 'min' => 1, ], 'ImageId' => [ 'type' => 'string', 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'ImageQuality' => [ 'type' => 'structure', 'members' => [ 'Brightness' => [ 'shape' => 'Float', ], 'Sharpness' => [ 'shape' => 'Float', ], ], ], 'ImageTooLargeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'IndexFacesRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionId', 'Image', ], 'members' => [ 'CollectionId' => [ 'shape' => 'CollectionId', ], 'Image' => [ 'shape' => 'Image', ], 'ExternalImageId' => [ 'shape' => 'ExternalImageId', ], 'DetectionAttributes' => [ 'shape' => 'Attributes', ], 'MaxFaces' => [ 'shape' => 'MaxFacesToIndex', ], 'QualityFilter' => [ 'shape' => 'QualityFilter', ], ], ], 'IndexFacesResponse' => [ 'type' => 'structure', 'members' => [ 'FaceRecords' => [ 'shape' => 'FaceRecordList', ], 'OrientationCorrection' => [ 'shape' => 'OrientationCorrection', ], 'FaceModelVersion' => [ 'shape' => 'String', ], 'UnindexedFaces' => [ 'shape' => 'UnindexedFaces', ], ], ], 'InferenceUnits' => [ 'type' => 'integer', 'min' => 1, ], 'Instance' => [ 'type' => 'structure', 'members' => [ 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'Confidence' => [ 'shape' => 'Percent', ], ], ], 'Instances' => [ 'type' => 'list', 'member' => [ 'shape' => 'Instance', ], ], 'InternalServerError' => [ 'type' => 'structure', 'members' => [], 'exception' => true, 'fault' => true, ], 'InvalidImageFormatException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidPaginationTokenException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidS3ObjectException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'IsLabeled' => [ 'type' => 'boolean', ], 'JobId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_]+$', ], 'JobTag' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.\\-:]+', ], 'KinesisDataArn' => [ 'type' => 'string', 'pattern' => '(^arn:([a-z\\d-]+):kinesis:([a-z\\d-]+):\\d{12}:.+$)', ], 'KinesisDataStream' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'KinesisDataArn', ], ], ], 'KinesisVideoArn' => [ 'type' => 'string', 'pattern' => '(^arn:([a-z\\d-]+):kinesisvideo:([a-z\\d-]+):\\d{12}:.+$)', ], 'KinesisVideoStream' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'KinesisVideoArn', ], ], ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,2048}$', ], 'KnownGender' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'KnownGenderType', ], ], ], 'KnownGenderType' => [ 'type' => 'string', 'enum' => [ 'Male', 'Female', 'Nonbinary', 'Unlisted', ], ], 'Label' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Confidence' => [ 'shape' => 'Percent', ], 'Instances' => [ 'shape' => 'Instances', ], 'Parents' => [ 'shape' => 'Parents', ], ], ], 'LabelDetection' => [ 'type' => 'structure', 'members' => [ 'Timestamp' => [ 'shape' => 'Timestamp', ], 'Label' => [ 'shape' => 'Label', ], ], ], 'LabelDetectionSortBy' => [ 'type' => 'string', 'enum' => [ 'NAME', 'TIMESTAMP', ], ], 'LabelDetections' => [ 'type' => 'list', 'member' => [ 'shape' => 'LabelDetection', ], ], 'Labels' => [ 'type' => 'list', 'member' => [ 'shape' => 'Label', ], ], 'Landmark' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'LandmarkType', ], 'X' => [ 'shape' => 'Float', ], 'Y' => [ 'shape' => 'Float', ], ], ], 'LandmarkType' => [ 'type' => 'string', 'enum' => [ 'eyeLeft', 'eyeRight', 'nose', 'mouthLeft', 'mouthRight', 'leftEyeBrowLeft', 'leftEyeBrowRight', 'leftEyeBrowUp', 'rightEyeBrowLeft', 'rightEyeBrowRight', 'rightEyeBrowUp', 'leftEyeLeft', 'leftEyeRight', 'leftEyeUp', 'leftEyeDown', 'rightEyeLeft', 'rightEyeRight', 'rightEyeUp', 'rightEyeDown', 'noseLeft', 'noseRight', 'mouthUp', 'mouthDown', 'leftPupil', 'rightPupil', 'upperJawlineLeft', 'midJawlineLeft', 'chinBottom', 'midJawlineRight', 'upperJawlineRight', ], ], 'Landmarks' => [ 'type' => 'list', 'member' => [ 'shape' => 'Landmark', ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ListCollectionsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'ListCollectionsResponse' => [ 'type' => 'structure', 'members' => [ 'CollectionIds' => [ 'shape' => 'CollectionIdList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'FaceModelVersions' => [ 'shape' => 'FaceModelVersionList', ], ], ], 'ListDatasetEntriesPageSize' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ListDatasetEntriesRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetArn', ], 'members' => [ 'DatasetArn' => [ 'shape' => 'DatasetArn', ], 'ContainsLabels' => [ 'shape' => 'DatasetLabels', ], 'Labeled' => [ 'shape' => 'IsLabeled', ], 'SourceRefContains' => [ 'shape' => 'QueryString', ], 'HasErrors' => [ 'shape' => 'HasErrors', ], 'NextToken' => [ 'shape' => 'ExtendedPaginationToken', ], 'MaxResults' => [ 'shape' => 'ListDatasetEntriesPageSize', ], ], ], 'ListDatasetEntriesResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetEntries' => [ 'shape' => 'DatasetEntries', ], 'NextToken' => [ 'shape' => 'ExtendedPaginationToken', ], ], ], 'ListDatasetLabelsPageSize' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ListDatasetLabelsRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetArn', ], 'members' => [ 'DatasetArn' => [ 'shape' => 'DatasetArn', ], 'NextToken' => [ 'shape' => 'ExtendedPaginationToken', ], 'MaxResults' => [ 'shape' => 'ListDatasetLabelsPageSize', ], ], ], 'ListDatasetLabelsResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetLabelDescriptions' => [ 'shape' => 'DatasetLabelDescriptions', ], 'NextToken' => [ 'shape' => 'ExtendedPaginationToken', ], ], ], 'ListFacesRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionId', ], 'members' => [ 'CollectionId' => [ 'shape' => 'CollectionId', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'ListFacesResponse' => [ 'type' => 'structure', 'members' => [ 'Faces' => [ 'shape' => 'FaceList', ], 'NextToken' => [ 'shape' => 'String', ], 'FaceModelVersion' => [ 'shape' => 'String', ], ], ], 'ListStreamProcessorsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListStreamProcessorsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'StreamProcessors' => [ 'shape' => 'StreamProcessorList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'MaxFaces' => [ 'type' => 'integer', 'max' => 4096, 'min' => 1, ], 'MaxFacesToIndex' => [ 'type' => 'integer', 'min' => 1, ], 'MaxPixelThreshold' => [ 'type' => 'float', 'max' => 1, 'min' => 0, ], 'MaxResults' => [ 'type' => 'integer', 'min' => 1, ], 'MinCoveragePercentage' => [ 'type' => 'float', 'max' => 100, 'min' => 0, ], 'ModerationLabel' => [ 'type' => 'structure', 'members' => [ 'Confidence' => [ 'shape' => 'Percent', ], 'Name' => [ 'shape' => 'String', ], 'ParentName' => [ 'shape' => 'String', ], ], ], 'ModerationLabels' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModerationLabel', ], ], 'MouthOpen' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'Boolean', ], 'Confidence' => [ 'shape' => 'Percent', ], ], ], 'Mustache' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'Boolean', ], 'Confidence' => [ 'shape' => 'Percent', ], ], ], 'NotificationChannel' => [ 'type' => 'structure', 'required' => [ 'SNSTopicArn', 'RoleArn', ], 'members' => [ 'SNSTopicArn' => [ 'shape' => 'SNSTopicArn', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'OrientationCorrection' => [ 'type' => 'string', 'enum' => [ 'ROTATE_0', 'ROTATE_90', 'ROTATE_180', 'ROTATE_270', ], ], 'OutputConfig' => [ 'type' => 'structure', 'members' => [ 'S3Bucket' => [ 'shape' => 'S3Bucket', ], 'S3KeyPrefix' => [ 'shape' => 'S3KeyPrefix', ], ], ], 'PageSize' => [ 'type' => 'integer', 'max' => 4096, 'min' => 0, ], 'PaginationToken' => [ 'type' => 'string', 'max' => 255, ], 'Parent' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'Parents' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parent', ], ], 'Percent' => [ 'type' => 'float', 'max' => 100, 'min' => 0, ], 'PersonDetail' => [ 'type' => 'structure', 'members' => [ 'Index' => [ 'shape' => 'PersonIndex', ], 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'Face' => [ 'shape' => 'FaceDetail', ], ], ], 'PersonDetection' => [ 'type' => 'structure', 'members' => [ 'Timestamp' => [ 'shape' => 'Timestamp', ], 'Person' => [ 'shape' => 'PersonDetail', ], ], ], 'PersonDetections' => [ 'type' => 'list', 'member' => [ 'shape' => 'PersonDetection', ], ], 'PersonIndex' => [ 'type' => 'long', ], 'PersonMatch' => [ 'type' => 'structure', 'members' => [ 'Timestamp' => [ 'shape' => 'Timestamp', ], 'Person' => [ 'shape' => 'PersonDetail', ], 'FaceMatches' => [ 'shape' => 'FaceMatchList', ], ], ], 'PersonMatches' => [ 'type' => 'list', 'member' => [ 'shape' => 'PersonMatch', ], ], 'PersonTrackingSortBy' => [ 'type' => 'string', 'enum' => [ 'INDEX', 'TIMESTAMP', ], ], 'Point' => [ 'type' => 'structure', 'members' => [ 'X' => [ 'shape' => 'Float', ], 'Y' => [ 'shape' => 'Float', ], ], ], 'Polygon' => [ 'type' => 'list', 'member' => [ 'shape' => 'Point', ], ], 'Pose' => [ 'type' => 'structure', 'members' => [ 'Roll' => [ 'shape' => 'Degree', ], 'Yaw' => [ 'shape' => 'Degree', ], 'Pitch' => [ 'shape' => 'Degree', ], ], ], 'ProjectArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '(^arn:[a-z\\d-]+:rekognition:[a-z\\d-]+:\\d{12}:project\\/[a-zA-Z0-9_.\\-]{1,255}\\/[0-9]+$)', ], 'ProjectDescription' => [ 'type' => 'structure', 'members' => [ 'ProjectArn' => [ 'shape' => 'ProjectArn', ], 'CreationTimestamp' => [ 'shape' => 'DateTime', ], 'Status' => [ 'shape' => 'ProjectStatus', ], 'Datasets' => [ 'shape' => 'DatasetMetadataList', ], ], ], 'ProjectDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProjectDescription', ], ], 'ProjectName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.\\-]+', ], 'ProjectNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProjectName', ], 'max' => 10, 'min' => 1, ], 'ProjectStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATED', 'DELETING', ], ], 'ProjectVersionArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '(^arn:[a-z\\d-]+:rekognition:[a-z\\d-]+:\\d{12}:project\\/[a-zA-Z0-9_.\\-]{1,255}\\/version\\/[a-zA-Z0-9_.\\-]{1,255}\\/[0-9]+$)', ], 'ProjectVersionDescription' => [ 'type' => 'structure', 'members' => [ 'ProjectVersionArn' => [ 'shape' => 'ProjectVersionArn', ], 'CreationTimestamp' => [ 'shape' => 'DateTime', ], 'MinInferenceUnits' => [ 'shape' => 'InferenceUnits', ], 'Status' => [ 'shape' => 'ProjectVersionStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'BillableTrainingTimeInSeconds' => [ 'shape' => 'ULong', ], 'TrainingEndTimestamp' => [ 'shape' => 'DateTime', ], 'OutputConfig' => [ 'shape' => 'OutputConfig', ], 'TrainingDataResult' => [ 'shape' => 'TrainingDataResult', ], 'TestingDataResult' => [ 'shape' => 'TestingDataResult', ], 'EvaluationResult' => [ 'shape' => 'EvaluationResult', ], 'ManifestSummary' => [ 'shape' => 'GroundTruthManifest', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'ProjectVersionDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProjectVersionDescription', ], ], 'ProjectVersionStatus' => [ 'type' => 'string', 'enum' => [ 'TRAINING_IN_PROGRESS', 'TRAINING_COMPLETED', 'TRAINING_FAILED', 'STARTING', 'RUNNING', 'FAILED', 'STOPPING', 'STOPPED', 'DELETING', ], ], 'ProjectVersionsPageSize' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ProjectsPageSize' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ProtectiveEquipmentBodyPart' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'BodyPart', ], 'Confidence' => [ 'shape' => 'Percent', ], 'EquipmentDetections' => [ 'shape' => 'EquipmentDetections', ], ], ], 'ProtectiveEquipmentPerson' => [ 'type' => 'structure', 'members' => [ 'BodyParts' => [ 'shape' => 'BodyParts', ], 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'Confidence' => [ 'shape' => 'Percent', ], 'Id' => [ 'shape' => 'UInteger', ], ], ], 'ProtectiveEquipmentPersonIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'UInteger', ], ], 'ProtectiveEquipmentPersons' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProtectiveEquipmentPerson', ], ], 'ProtectiveEquipmentSummarizationAttributes' => [ 'type' => 'structure', 'required' => [ 'MinConfidence', 'RequiredEquipmentTypes', ], 'members' => [ 'MinConfidence' => [ 'shape' => 'Percent', ], 'RequiredEquipmentTypes' => [ 'shape' => 'ProtectiveEquipmentTypes', ], ], ], 'ProtectiveEquipmentSummary' => [ 'type' => 'structure', 'members' => [ 'PersonsWithRequiredEquipment' => [ 'shape' => 'ProtectiveEquipmentPersonIds', ], 'PersonsWithoutRequiredEquipment' => [ 'shape' => 'ProtectiveEquipmentPersonIds', ], 'PersonsIndeterminate' => [ 'shape' => 'ProtectiveEquipmentPersonIds', ], ], ], 'ProtectiveEquipmentType' => [ 'type' => 'string', 'enum' => [ 'FACE_COVER', 'HAND_COVER', 'HEAD_COVER', ], ], 'ProtectiveEquipmentTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProtectiveEquipmentType', ], ], 'ProvisionedThroughputExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'QualityFilter' => [ 'type' => 'string', 'enum' => [ 'NONE', 'AUTO', 'LOW', 'MEDIUM', 'HIGH', ], ], 'QueryString' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '.*\\S.*', ], 'Reason' => [ 'type' => 'string', 'enum' => [ 'EXCEEDS_MAX_FACES', 'EXTREME_POSE', 'LOW_BRIGHTNESS', 'LOW_SHARPNESS', 'LOW_CONFIDENCE', 'SMALL_BOUNDING_BOX', 'LOW_FACE_QUALITY', ], ], 'Reasons' => [ 'type' => 'list', 'member' => [ 'shape' => 'Reason', ], ], 'RecognizeCelebritiesRequest' => [ 'type' => 'structure', 'required' => [ 'Image', ], 'members' => [ 'Image' => [ 'shape' => 'Image', ], ], ], 'RecognizeCelebritiesResponse' => [ 'type' => 'structure', 'members' => [ 'CelebrityFaces' => [ 'shape' => 'CelebrityList', ], 'UnrecognizedFaces' => [ 'shape' => 'ComparedFaceList', ], 'OrientationCorrection' => [ 'shape' => 'OrientationCorrection', ], ], ], 'RegionOfInterest' => [ 'type' => 'structure', 'members' => [ 'BoundingBox' => [ 'shape' => 'BoundingBox', ], ], ], 'RegionsOfInterest' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegionOfInterest', ], 'max' => 10, 'min' => 0, ], 'RekognitionUniqueId' => [ 'type' => 'string', 'pattern' => '[0-9A-Za-z]*', ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResourceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResourceNotReadyException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+', ], 'S3Bucket' => [ 'type' => 'string', 'max' => 255, 'min' => 3, 'pattern' => '[0-9A-Za-z\\.\\-_]*', ], 'S3KeyPrefix' => [ 'type' => 'string', 'max' => 1024, ], 'S3Object' => [ 'type' => 'structure', 'members' => [ 'Bucket' => [ 'shape' => 'S3Bucket', ], 'Name' => [ 'shape' => 'S3ObjectName', ], 'Version' => [ 'shape' => 'S3ObjectVersion', ], ], ], 'S3ObjectName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'S3ObjectVersion' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'SNSTopicArn' => [ 'type' => 'string', 'pattern' => '(^arn:aws:sns:.*:\\w{12}:.+$)', ], 'SearchFacesByImageRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionId', 'Image', ], 'members' => [ 'CollectionId' => [ 'shape' => 'CollectionId', ], 'Image' => [ 'shape' => 'Image', ], 'MaxFaces' => [ 'shape' => 'MaxFaces', ], 'FaceMatchThreshold' => [ 'shape' => 'Percent', ], 'QualityFilter' => [ 'shape' => 'QualityFilter', ], ], ], 'SearchFacesByImageResponse' => [ 'type' => 'structure', 'members' => [ 'SearchedFaceBoundingBox' => [ 'shape' => 'BoundingBox', ], 'SearchedFaceConfidence' => [ 'shape' => 'Percent', ], 'FaceMatches' => [ 'shape' => 'FaceMatchList', ], 'FaceModelVersion' => [ 'shape' => 'String', ], ], ], 'SearchFacesRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionId', 'FaceId', ], 'members' => [ 'CollectionId' => [ 'shape' => 'CollectionId', ], 'FaceId' => [ 'shape' => 'FaceId', ], 'MaxFaces' => [ 'shape' => 'MaxFaces', ], 'FaceMatchThreshold' => [ 'shape' => 'Percent', ], ], ], 'SearchFacesResponse' => [ 'type' => 'structure', 'members' => [ 'SearchedFaceId' => [ 'shape' => 'FaceId', ], 'FaceMatches' => [ 'shape' => 'FaceMatchList', ], 'FaceModelVersion' => [ 'shape' => 'String', ], ], ], 'SegmentConfidence' => [ 'type' => 'float', 'max' => 100, 'min' => 50, ], 'SegmentDetection' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'SegmentType', ], 'StartTimestampMillis' => [ 'shape' => 'Timestamp', ], 'EndTimestampMillis' => [ 'shape' => 'Timestamp', ], 'DurationMillis' => [ 'shape' => 'ULong', ], 'StartTimecodeSMPTE' => [ 'shape' => 'Timecode', ], 'EndTimecodeSMPTE' => [ 'shape' => 'Timecode', ], 'DurationSMPTE' => [ 'shape' => 'Timecode', ], 'TechnicalCueSegment' => [ 'shape' => 'TechnicalCueSegment', ], 'ShotSegment' => [ 'shape' => 'ShotSegment', ], 'StartFrameNumber' => [ 'shape' => 'ULong', ], 'EndFrameNumber' => [ 'shape' => 'ULong', ], 'DurationFrames' => [ 'shape' => 'ULong', ], ], ], 'SegmentDetections' => [ 'type' => 'list', 'member' => [ 'shape' => 'SegmentDetection', ], ], 'SegmentType' => [ 'type' => 'string', 'enum' => [ 'TECHNICAL_CUE', 'SHOT', ], ], 'SegmentTypeInfo' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'SegmentType', ], 'ModelVersion' => [ 'shape' => 'String', ], ], ], 'SegmentTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'SegmentType', ], 'min' => 1, ], 'SegmentTypesInfo' => [ 'type' => 'list', 'member' => [ 'shape' => 'SegmentTypeInfo', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ShotSegment' => [ 'type' => 'structure', 'members' => [ 'Index' => [ 'shape' => 'ULong', ], 'Confidence' => [ 'shape' => 'SegmentConfidence', ], ], ], 'Smile' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'Boolean', ], 'Confidence' => [ 'shape' => 'Percent', ], ], ], 'StartCelebrityRecognitionRequest' => [ 'type' => 'structure', 'required' => [ 'Video', ], 'members' => [ 'Video' => [ 'shape' => 'Video', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'NotificationChannel' => [ 'shape' => 'NotificationChannel', ], 'JobTag' => [ 'shape' => 'JobTag', ], ], ], 'StartCelebrityRecognitionResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StartContentModerationRequest' => [ 'type' => 'structure', 'required' => [ 'Video', ], 'members' => [ 'Video' => [ 'shape' => 'Video', ], 'MinConfidence' => [ 'shape' => 'Percent', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'NotificationChannel' => [ 'shape' => 'NotificationChannel', ], 'JobTag' => [ 'shape' => 'JobTag', ], ], ], 'StartContentModerationResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StartFaceDetectionRequest' => [ 'type' => 'structure', 'required' => [ 'Video', ], 'members' => [ 'Video' => [ 'shape' => 'Video', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'NotificationChannel' => [ 'shape' => 'NotificationChannel', ], 'FaceAttributes' => [ 'shape' => 'FaceAttributes', ], 'JobTag' => [ 'shape' => 'JobTag', ], ], ], 'StartFaceDetectionResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StartFaceSearchRequest' => [ 'type' => 'structure', 'required' => [ 'Video', 'CollectionId', ], 'members' => [ 'Video' => [ 'shape' => 'Video', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'FaceMatchThreshold' => [ 'shape' => 'Percent', ], 'CollectionId' => [ 'shape' => 'CollectionId', ], 'NotificationChannel' => [ 'shape' => 'NotificationChannel', ], 'JobTag' => [ 'shape' => 'JobTag', ], ], ], 'StartFaceSearchResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StartLabelDetectionRequest' => [ 'type' => 'structure', 'required' => [ 'Video', ], 'members' => [ 'Video' => [ 'shape' => 'Video', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'MinConfidence' => [ 'shape' => 'Percent', ], 'NotificationChannel' => [ 'shape' => 'NotificationChannel', ], 'JobTag' => [ 'shape' => 'JobTag', ], ], ], 'StartLabelDetectionResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StartPersonTrackingRequest' => [ 'type' => 'structure', 'required' => [ 'Video', ], 'members' => [ 'Video' => [ 'shape' => 'Video', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'NotificationChannel' => [ 'shape' => 'NotificationChannel', ], 'JobTag' => [ 'shape' => 'JobTag', ], ], ], 'StartPersonTrackingResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StartProjectVersionRequest' => [ 'type' => 'structure', 'required' => [ 'ProjectVersionArn', 'MinInferenceUnits', ], 'members' => [ 'ProjectVersionArn' => [ 'shape' => 'ProjectVersionArn', ], 'MinInferenceUnits' => [ 'shape' => 'InferenceUnits', ], ], ], 'StartProjectVersionResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'ProjectVersionStatus', ], ], ], 'StartSegmentDetectionFilters' => [ 'type' => 'structure', 'members' => [ 'TechnicalCueFilter' => [ 'shape' => 'StartTechnicalCueDetectionFilter', ], 'ShotFilter' => [ 'shape' => 'StartShotDetectionFilter', ], ], ], 'StartSegmentDetectionRequest' => [ 'type' => 'structure', 'required' => [ 'Video', 'SegmentTypes', ], 'members' => [ 'Video' => [ 'shape' => 'Video', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'NotificationChannel' => [ 'shape' => 'NotificationChannel', ], 'JobTag' => [ 'shape' => 'JobTag', ], 'Filters' => [ 'shape' => 'StartSegmentDetectionFilters', ], 'SegmentTypes' => [ 'shape' => 'SegmentTypes', ], ], ], 'StartSegmentDetectionResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StartShotDetectionFilter' => [ 'type' => 'structure', 'members' => [ 'MinSegmentConfidence' => [ 'shape' => 'SegmentConfidence', ], ], ], 'StartStreamProcessorRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'StreamProcessorName', ], ], ], 'StartStreamProcessorResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartTechnicalCueDetectionFilter' => [ 'type' => 'structure', 'members' => [ 'MinSegmentConfidence' => [ 'shape' => 'SegmentConfidence', ], 'BlackFrame' => [ 'shape' => 'BlackFrame', ], ], ], 'StartTextDetectionFilters' => [ 'type' => 'structure', 'members' => [ 'WordFilter' => [ 'shape' => 'DetectionFilter', ], 'RegionsOfInterest' => [ 'shape' => 'RegionsOfInterest', ], ], ], 'StartTextDetectionRequest' => [ 'type' => 'structure', 'required' => [ 'Video', ], 'members' => [ 'Video' => [ 'shape' => 'Video', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'NotificationChannel' => [ 'shape' => 'NotificationChannel', ], 'JobTag' => [ 'shape' => 'JobTag', ], 'Filters' => [ 'shape' => 'StartTextDetectionFilters', ], ], ], 'StartTextDetectionResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StatusMessage' => [ 'type' => 'string', ], 'StopProjectVersionRequest' => [ 'type' => 'structure', 'required' => [ 'ProjectVersionArn', ], 'members' => [ 'ProjectVersionArn' => [ 'shape' => 'ProjectVersionArn', ], ], ], 'StopProjectVersionResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'ProjectVersionStatus', ], ], ], 'StopStreamProcessorRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'StreamProcessorName', ], ], ], 'StopStreamProcessorResponse' => [ 'type' => 'structure', 'members' => [], ], 'StreamProcessor' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'StreamProcessorName', ], 'Status' => [ 'shape' => 'StreamProcessorStatus', ], ], ], 'StreamProcessorArn' => [ 'type' => 'string', 'pattern' => '(^arn:[a-z\\d-]+:rekognition:[a-z\\d-]+:\\d{12}:streamprocessor\\/.+$)', ], 'StreamProcessorInput' => [ 'type' => 'structure', 'members' => [ 'KinesisVideoStream' => [ 'shape' => 'KinesisVideoStream', ], ], ], 'StreamProcessorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamProcessor', ], ], 'StreamProcessorName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.\\-]+', ], 'StreamProcessorOutput' => [ 'type' => 'structure', 'members' => [ 'KinesisDataStream' => [ 'shape' => 'KinesisDataStream', ], ], ], 'StreamProcessorSettings' => [ 'type' => 'structure', 'members' => [ 'FaceSearch' => [ 'shape' => 'FaceSearchSettings', ], ], ], 'StreamProcessorStatus' => [ 'type' => 'string', 'enum' => [ 'STOPPED', 'STARTING', 'RUNNING', 'FAILED', 'STOPPING', ], ], 'String' => [ 'type' => 'string', ], 'Summary' => [ 'type' => 'structure', 'members' => [ 'S3Object' => [ 'shape' => 'S3Object', ], ], ], 'Sunglasses' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'Boolean', ], 'Confidence' => [ 'shape' => 'Percent', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TechnicalCueSegment' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'TechnicalCueType', ], 'Confidence' => [ 'shape' => 'SegmentConfidence', ], ], ], 'TechnicalCueType' => [ 'type' => 'string', 'enum' => [ 'ColorBars', 'EndCredits', 'BlackFrames', 'OpeningCredits', 'StudioLogo', 'Slate', 'Content', ], ], 'TestingData' => [ 'type' => 'structure', 'members' => [ 'Assets' => [ 'shape' => 'Assets', ], 'AutoCreate' => [ 'shape' => 'Boolean', ], ], ], 'TestingDataResult' => [ 'type' => 'structure', 'members' => [ 'Input' => [ 'shape' => 'TestingData', ], 'Output' => [ 'shape' => 'TestingData', ], 'Validation' => [ 'shape' => 'ValidationData', ], ], ], 'TextDetection' => [ 'type' => 'structure', 'members' => [ 'DetectedText' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'TextTypes', ], 'Id' => [ 'shape' => 'UInteger', ], 'ParentId' => [ 'shape' => 'UInteger', ], 'Confidence' => [ 'shape' => 'Percent', ], 'Geometry' => [ 'shape' => 'Geometry', ], ], ], 'TextDetectionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TextDetection', ], ], 'TextDetectionResult' => [ 'type' => 'structure', 'members' => [ 'Timestamp' => [ 'shape' => 'Timestamp', ], 'TextDetection' => [ 'shape' => 'TextDetection', ], ], ], 'TextDetectionResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'TextDetectionResult', ], ], 'TextTypes' => [ 'type' => 'string', 'enum' => [ 'LINE', 'WORD', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, 'fault' => true, ], 'Timecode' => [ 'type' => 'string', ], 'Timestamp' => [ 'type' => 'long', ], 'TrainingData' => [ 'type' => 'structure', 'members' => [ 'Assets' => [ 'shape' => 'Assets', ], ], ], 'TrainingDataResult' => [ 'type' => 'structure', 'members' => [ 'Input' => [ 'shape' => 'TrainingData', ], 'Output' => [ 'shape' => 'TrainingData', ], 'Validation' => [ 'shape' => 'ValidationData', ], ], ], 'UInteger' => [ 'type' => 'integer', 'min' => 0, ], 'ULong' => [ 'type' => 'long', 'min' => 0, ], 'UnindexedFace' => [ 'type' => 'structure', 'members' => [ 'Reasons' => [ 'shape' => 'Reasons', ], 'FaceDetail' => [ 'shape' => 'FaceDetail', ], ], ], 'UnindexedFaces' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnindexedFace', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDatasetEntriesRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetArn', 'Changes', ], 'members' => [ 'DatasetArn' => [ 'shape' => 'DatasetArn', ], 'Changes' => [ 'shape' => 'DatasetChanges', ], ], ], 'UpdateDatasetEntriesResponse' => [ 'type' => 'structure', 'members' => [], ], 'Url' => [ 'type' => 'string', ], 'Urls' => [ 'type' => 'list', 'member' => [ 'shape' => 'Url', ], 'max' => 255, 'min' => 0, ], 'ValidationData' => [ 'type' => 'structure', 'members' => [ 'Assets' => [ 'shape' => 'Assets', ], ], ], 'VersionName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.\\-]+', ], 'VersionNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'VersionName', ], 'max' => 10, 'min' => 1, ], 'Video' => [ 'type' => 'structure', 'members' => [ 'S3Object' => [ 'shape' => 'S3Object', ], ], ], 'VideoColorRange' => [ 'type' => 'string', 'enum' => [ 'FULL', 'LIMITED', ], ], 'VideoJobStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', ], ], 'VideoMetadata' => [ 'type' => 'structure', 'members' => [ 'Codec' => [ 'shape' => 'String', ], 'DurationMillis' => [ 'shape' => 'ULong', ], 'Format' => [ 'shape' => 'String', ], 'FrameRate' => [ 'shape' => 'Float', ], 'FrameHeight' => [ 'shape' => 'ULong', ], 'FrameWidth' => [ 'shape' => 'ULong', ], 'ColorRange' => [ 'shape' => 'VideoColorRange', ], ], ], 'VideoMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VideoMetadata', ], ], 'VideoTooLargeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], ],];
