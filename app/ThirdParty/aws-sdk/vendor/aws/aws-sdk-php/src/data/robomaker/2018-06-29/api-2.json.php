<?php
// This file was auto-generated from sdk-root/src/data/robomaker/2018-06-29/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-06-29', 'endpointPrefix' => 'robomaker', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'RoboMaker', 'serviceFullName' => 'AWS RoboMaker', 'serviceId' => 'RoboMaker', 'signatureVersion' => 'v4', 'signingName' => 'robomaker', 'uid' => 'robomaker-2018-06-29', ], 'operations' => [ 'BatchDeleteWorlds' => [ 'name' => 'BatchDeleteWorlds', 'http' => [ 'method' => 'POST', 'requestUri' => '/batchDeleteWorlds', ], 'input' => [ 'shape' => 'BatchDeleteWorldsRequest', ], 'output' => [ 'shape' => 'BatchDeleteWorldsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchDescribeSimulationJob' => [ 'name' => 'BatchDescribeSimulationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/batchDescribeSimulationJob', ], 'input' => [ 'shape' => 'BatchDescribeSimulationJobRequest', ], 'output' => [ 'shape' => 'BatchDescribeSimulationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CancelDeploymentJob' => [ 'name' => 'CancelDeploymentJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/cancelDeploymentJob', ], 'input' => [ 'shape' => 'CancelDeploymentJobRequest', ], 'output' => [ 'shape' => 'CancelDeploymentJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CancelSimulationJob' => [ 'name' => 'CancelSimulationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/cancelSimulationJob', ], 'input' => [ 'shape' => 'CancelSimulationJobRequest', ], 'output' => [ 'shape' => 'CancelSimulationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CancelSimulationJobBatch' => [ 'name' => 'CancelSimulationJobBatch', 'http' => [ 'method' => 'POST', 'requestUri' => '/cancelSimulationJobBatch', ], 'input' => [ 'shape' => 'CancelSimulationJobBatchRequest', ], 'output' => [ 'shape' => 'CancelSimulationJobBatchResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CancelWorldExportJob' => [ 'name' => 'CancelWorldExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/cancelWorldExportJob', ], 'input' => [ 'shape' => 'CancelWorldExportJobRequest', ], 'output' => [ 'shape' => 'CancelWorldExportJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CancelWorldGenerationJob' => [ 'name' => 'CancelWorldGenerationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/cancelWorldGenerationJob', ], 'input' => [ 'shape' => 'CancelWorldGenerationJobRequest', ], 'output' => [ 'shape' => 'CancelWorldGenerationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateDeploymentJob' => [ 'name' => 'CreateDeploymentJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/createDeploymentJob', ], 'input' => [ 'shape' => 'CreateDeploymentJobRequest', ], 'output' => [ 'shape' => 'CreateDeploymentJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConcurrentDeploymentException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], ], ], 'CreateFleet' => [ 'name' => 'CreateFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/createFleet', ], 'input' => [ 'shape' => 'CreateFleetRequest', ], 'output' => [ 'shape' => 'CreateFleetResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateRobot' => [ 'name' => 'CreateRobot', 'http' => [ 'method' => 'POST', 'requestUri' => '/createRobot', ], 'input' => [ 'shape' => 'CreateRobotRequest', ], 'output' => [ 'shape' => 'CreateRobotResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], ], ], 'CreateRobotApplication' => [ 'name' => 'CreateRobotApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/createRobotApplication', ], 'input' => [ 'shape' => 'CreateRobotApplicationRequest', ], 'output' => [ 'shape' => 'CreateRobotApplicationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], ], ], 'CreateRobotApplicationVersion' => [ 'name' => 'CreateRobotApplicationVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/createRobotApplicationVersion', ], 'input' => [ 'shape' => 'CreateRobotApplicationVersionRequest', ], 'output' => [ 'shape' => 'CreateRobotApplicationVersionResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateSimulationApplication' => [ 'name' => 'CreateSimulationApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/createSimulationApplication', ], 'input' => [ 'shape' => 'CreateSimulationApplicationRequest', ], 'output' => [ 'shape' => 'CreateSimulationApplicationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], ], ], 'CreateSimulationApplicationVersion' => [ 'name' => 'CreateSimulationApplicationVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/createSimulationApplicationVersion', ], 'input' => [ 'shape' => 'CreateSimulationApplicationVersionRequest', ], 'output' => [ 'shape' => 'CreateSimulationApplicationVersionResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateSimulationJob' => [ 'name' => 'CreateSimulationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/createSimulationJob', ], 'input' => [ 'shape' => 'CreateSimulationJobRequest', ], 'output' => [ 'shape' => 'CreateSimulationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CreateWorldExportJob' => [ 'name' => 'CreateWorldExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/createWorldExportJob', ], 'input' => [ 'shape' => 'CreateWorldExportJobRequest', ], 'output' => [ 'shape' => 'CreateWorldExportJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CreateWorldGenerationJob' => [ 'name' => 'CreateWorldGenerationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/createWorldGenerationJob', ], 'input' => [ 'shape' => 'CreateWorldGenerationJobRequest', ], 'output' => [ 'shape' => 'CreateWorldGenerationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CreateWorldTemplate' => [ 'name' => 'CreateWorldTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/createWorldTemplate', ], 'input' => [ 'shape' => 'CreateWorldTemplateRequest', ], 'output' => [ 'shape' => 'CreateWorldTemplateResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteFleet' => [ 'name' => 'DeleteFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/deleteFleet', ], 'input' => [ 'shape' => 'DeleteFleetRequest', ], 'output' => [ 'shape' => 'DeleteFleetResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteRobot' => [ 'name' => 'DeleteRobot', 'http' => [ 'method' => 'POST', 'requestUri' => '/deleteRobot', ], 'input' => [ 'shape' => 'DeleteRobotRequest', ], 'output' => [ 'shape' => 'DeleteRobotResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteRobotApplication' => [ 'name' => 'DeleteRobotApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/deleteRobotApplication', ], 'input' => [ 'shape' => 'DeleteRobotApplicationRequest', ], 'output' => [ 'shape' => 'DeleteRobotApplicationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteSimulationApplication' => [ 'name' => 'DeleteSimulationApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/deleteSimulationApplication', ], 'input' => [ 'shape' => 'DeleteSimulationApplicationRequest', ], 'output' => [ 'shape' => 'DeleteSimulationApplicationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteWorldTemplate' => [ 'name' => 'DeleteWorldTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/deleteWorldTemplate', ], 'input' => [ 'shape' => 'DeleteWorldTemplateRequest', ], 'output' => [ 'shape' => 'DeleteWorldTemplateResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeregisterRobot' => [ 'name' => 'DeregisterRobot', 'http' => [ 'method' => 'POST', 'requestUri' => '/deregisterRobot', ], 'input' => [ 'shape' => 'DeregisterRobotRequest', ], 'output' => [ 'shape' => 'DeregisterRobotResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeDeploymentJob' => [ 'name' => 'DescribeDeploymentJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/describeDeploymentJob', ], 'input' => [ 'shape' => 'DescribeDeploymentJobRequest', ], 'output' => [ 'shape' => 'DescribeDeploymentJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeFleet' => [ 'name' => 'DescribeFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/describeFleet', ], 'input' => [ 'shape' => 'DescribeFleetRequest', ], 'output' => [ 'shape' => 'DescribeFleetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeRobot' => [ 'name' => 'DescribeRobot', 'http' => [ 'method' => 'POST', 'requestUri' => '/describeRobot', ], 'input' => [ 'shape' => 'DescribeRobotRequest', ], 'output' => [ 'shape' => 'DescribeRobotResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeRobotApplication' => [ 'name' => 'DescribeRobotApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/describeRobotApplication', ], 'input' => [ 'shape' => 'DescribeRobotApplicationRequest', ], 'output' => [ 'shape' => 'DescribeRobotApplicationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeSimulationApplication' => [ 'name' => 'DescribeSimulationApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/describeSimulationApplication', ], 'input' => [ 'shape' => 'DescribeSimulationApplicationRequest', ], 'output' => [ 'shape' => 'DescribeSimulationApplicationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeSimulationJob' => [ 'name' => 'DescribeSimulationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/describeSimulationJob', ], 'input' => [ 'shape' => 'DescribeSimulationJobRequest', ], 'output' => [ 'shape' => 'DescribeSimulationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeSimulationJobBatch' => [ 'name' => 'DescribeSimulationJobBatch', 'http' => [ 'method' => 'POST', 'requestUri' => '/describeSimulationJobBatch', ], 'input' => [ 'shape' => 'DescribeSimulationJobBatchRequest', ], 'output' => [ 'shape' => 'DescribeSimulationJobBatchResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeWorld' => [ 'name' => 'DescribeWorld', 'http' => [ 'method' => 'POST', 'requestUri' => '/describeWorld', ], 'input' => [ 'shape' => 'DescribeWorldRequest', ], 'output' => [ 'shape' => 'DescribeWorldResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeWorldExportJob' => [ 'name' => 'DescribeWorldExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/describeWorldExportJob', ], 'input' => [ 'shape' => 'DescribeWorldExportJobRequest', ], 'output' => [ 'shape' => 'DescribeWorldExportJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeWorldGenerationJob' => [ 'name' => 'DescribeWorldGenerationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/describeWorldGenerationJob', ], 'input' => [ 'shape' => 'DescribeWorldGenerationJobRequest', ], 'output' => [ 'shape' => 'DescribeWorldGenerationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeWorldTemplate' => [ 'name' => 'DescribeWorldTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/describeWorldTemplate', ], 'input' => [ 'shape' => 'DescribeWorldTemplateRequest', ], 'output' => [ 'shape' => 'DescribeWorldTemplateResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetWorldTemplateBody' => [ 'name' => 'GetWorldTemplateBody', 'http' => [ 'method' => 'POST', 'requestUri' => '/getWorldTemplateBody', ], 'input' => [ 'shape' => 'GetWorldTemplateBodyRequest', ], 'output' => [ 'shape' => 'GetWorldTemplateBodyResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDeploymentJobs' => [ 'name' => 'ListDeploymentJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/listDeploymentJobs', ], 'input' => [ 'shape' => 'ListDeploymentJobsRequest', ], 'output' => [ 'shape' => 'ListDeploymentJobsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListFleets' => [ 'name' => 'ListFleets', 'http' => [ 'method' => 'POST', 'requestUri' => '/listFleets', ], 'input' => [ 'shape' => 'ListFleetsRequest', ], 'output' => [ 'shape' => 'ListFleetsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListRobotApplications' => [ 'name' => 'ListRobotApplications', 'http' => [ 'method' => 'POST', 'requestUri' => '/listRobotApplications', ], 'input' => [ 'shape' => 'ListRobotApplicationsRequest', ], 'output' => [ 'shape' => 'ListRobotApplicationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListRobots' => [ 'name' => 'ListRobots', 'http' => [ 'method' => 'POST', 'requestUri' => '/listRobots', ], 'input' => [ 'shape' => 'ListRobotsRequest', ], 'output' => [ 'shape' => 'ListRobotsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListSimulationApplications' => [ 'name' => 'ListSimulationApplications', 'http' => [ 'method' => 'POST', 'requestUri' => '/listSimulationApplications', ], 'input' => [ 'shape' => 'ListSimulationApplicationsRequest', ], 'output' => [ 'shape' => 'ListSimulationApplicationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSimulationJobBatches' => [ 'name' => 'ListSimulationJobBatches', 'http' => [ 'method' => 'POST', 'requestUri' => '/listSimulationJobBatches', ], 'input' => [ 'shape' => 'ListSimulationJobBatchesRequest', ], 'output' => [ 'shape' => 'ListSimulationJobBatchesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSimulationJobs' => [ 'name' => 'ListSimulationJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/listSimulationJobs', ], 'input' => [ 'shape' => 'ListSimulationJobsRequest', ], 'output' => [ 'shape' => 'ListSimulationJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListWorldExportJobs' => [ 'name' => 'ListWorldExportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/listWorldExportJobs', ], 'input' => [ 'shape' => 'ListWorldExportJobsRequest', ], 'output' => [ 'shape' => 'ListWorldExportJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListWorldGenerationJobs' => [ 'name' => 'ListWorldGenerationJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/listWorldGenerationJobs', ], 'input' => [ 'shape' => 'ListWorldGenerationJobsRequest', ], 'output' => [ 'shape' => 'ListWorldGenerationJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListWorldTemplates' => [ 'name' => 'ListWorldTemplates', 'http' => [ 'method' => 'POST', 'requestUri' => '/listWorldTemplates', ], 'input' => [ 'shape' => 'ListWorldTemplatesRequest', ], 'output' => [ 'shape' => 'ListWorldTemplatesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListWorlds' => [ 'name' => 'ListWorlds', 'http' => [ 'method' => 'POST', 'requestUri' => '/listWorlds', ], 'input' => [ 'shape' => 'ListWorldsRequest', ], 'output' => [ 'shape' => 'ListWorldsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'RegisterRobot' => [ 'name' => 'RegisterRobot', 'http' => [ 'method' => 'POST', 'requestUri' => '/registerRobot', ], 'input' => [ 'shape' => 'RegisterRobotRequest', ], 'output' => [ 'shape' => 'RegisterRobotResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RestartSimulationJob' => [ 'name' => 'RestartSimulationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/restartSimulationJob', ], 'input' => [ 'shape' => 'RestartSimulationJobRequest', ], 'output' => [ 'shape' => 'RestartSimulationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartSimulationJobBatch' => [ 'name' => 'StartSimulationJobBatch', 'http' => [ 'method' => 'POST', 'requestUri' => '/startSimulationJobBatch', ], 'input' => [ 'shape' => 'StartSimulationJobBatchRequest', ], 'output' => [ 'shape' => 'StartSimulationJobBatchResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SyncDeploymentJob' => [ 'name' => 'SyncDeploymentJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/syncDeploymentJob', ], 'input' => [ 'shape' => 'SyncDeploymentJobRequest', ], 'output' => [ 'shape' => 'SyncDeploymentJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConcurrentDeploymentException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateRobotApplication' => [ 'name' => 'UpdateRobotApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/updateRobotApplication', ], 'input' => [ 'shape' => 'UpdateRobotApplicationRequest', ], 'output' => [ 'shape' => 'UpdateRobotApplicationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateSimulationApplication' => [ 'name' => 'UpdateSimulationApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/updateSimulationApplication', ], 'input' => [ 'shape' => 'UpdateSimulationApplicationRequest', ], 'output' => [ 'shape' => 'UpdateSimulationApplicationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateWorldTemplate' => [ 'name' => 'UpdateWorldTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/updateWorldTemplate', ], 'input' => [ 'shape' => 'UpdateWorldTemplateRequest', ], 'output' => [ 'shape' => 'UpdateWorldTemplateResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'Architecture' => [ 'type' => 'string', 'enum' => [ 'X86_64', 'ARM64', 'ARMHF', ], ], 'Arn' => [ 'type' => 'string', 'max' => 1224, 'min' => 1, 'pattern' => 'arn:.*', ], 'Arns' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 100, 'min' => 1, ], 'BatchDeleteWorldsRequest' => [ 'type' => 'structure', 'required' => [ 'worlds', ], 'members' => [ 'worlds' => [ 'shape' => 'Arns', ], ], ], 'BatchDeleteWorldsResponse' => [ 'type' => 'structure', 'members' => [ 'unprocessedWorlds' => [ 'shape' => 'Arns', ], ], ], 'BatchDescribeSimulationJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobs', ], 'members' => [ 'jobs' => [ 'shape' => 'Arns', ], ], ], 'BatchDescribeSimulationJobResponse' => [ 'type' => 'structure', 'members' => [ 'jobs' => [ 'shape' => 'SimulationJobs', ], 'unprocessedJobs' => [ 'shape' => 'Arns', ], ], ], 'BatchPolicy' => [ 'type' => 'structure', 'members' => [ 'timeoutInSeconds' => [ 'shape' => 'BatchTimeoutInSeconds', ], 'maxConcurrency' => [ 'shape' => 'MaxConcurrency', ], ], ], 'BatchTimeoutInSeconds' => [ 'type' => 'long', ], 'Boolean' => [ 'type' => 'boolean', ], 'BoxedBoolean' => [ 'type' => 'boolean', ], 'CancelDeploymentJobRequest' => [ 'type' => 'structure', 'required' => [ 'job', ], 'members' => [ 'job' => [ 'shape' => 'Arn', ], ], ], 'CancelDeploymentJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'CancelSimulationJobBatchRequest' => [ 'type' => 'structure', 'required' => [ 'batch', ], 'members' => [ 'batch' => [ 'shape' => 'Arn', ], ], ], 'CancelSimulationJobBatchResponse' => [ 'type' => 'structure', 'members' => [], ], 'CancelSimulationJobRequest' => [ 'type' => 'structure', 'required' => [ 'job', ], 'members' => [ 'job' => [ 'shape' => 'Arn', ], ], ], 'CancelSimulationJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'CancelWorldExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'job', ], 'members' => [ 'job' => [ 'shape' => 'Arn', ], ], ], 'CancelWorldExportJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'CancelWorldGenerationJobRequest' => [ 'type' => 'structure', 'required' => [ 'job', ], 'members' => [ 'job' => [ 'shape' => 'Arn', ], ], ], 'CancelWorldGenerationJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_\\-=]*', ], 'Command' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.\\-]*', ], 'CommandList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'Compute' => [ 'type' => 'structure', 'members' => [ 'simulationUnitLimit' => [ 'shape' => 'SimulationUnit', ], 'computeType' => [ 'shape' => 'ComputeType', ], 'gpuUnitLimit' => [ 'shape' => 'GPUUnit', ], ], ], 'ComputeResponse' => [ 'type' => 'structure', 'members' => [ 'simulationUnitLimit' => [ 'shape' => 'SimulationUnit', ], 'computeType' => [ 'shape' => 'ComputeType', ], 'gpuUnitLimit' => [ 'shape' => 'GPUUnit', ], ], ], 'ComputeType' => [ 'type' => 'string', 'enum' => [ 'CPU', 'GPU_AND_CPU', ], ], 'ConcurrentDeploymentException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'CreateDeploymentJobRequest' => [ 'type' => 'structure', 'required' => [ 'clientRequestToken', 'fleet', 'deploymentApplicationConfigs', ], 'members' => [ 'deploymentConfig' => [ 'shape' => 'DeploymentConfig', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'fleet' => [ 'shape' => 'Arn', ], 'deploymentApplicationConfigs' => [ 'shape' => 'DeploymentApplicationConfigs', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateDeploymentJobResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'fleet' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'DeploymentStatus', ], 'deploymentApplicationConfigs' => [ 'shape' => 'DeploymentApplicationConfigs', ], 'failureReason' => [ 'shape' => 'GenericString', ], 'failureCode' => [ 'shape' => 'DeploymentJobErrorCode', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'deploymentConfig' => [ 'shape' => 'DeploymentConfig', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateFleetRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateFleetResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateRobotApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'robotSoftwareSuite', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'sources' => [ 'shape' => 'SourceConfigs', ], 'robotSoftwareSuite' => [ 'shape' => 'RobotSoftwareSuite', ], 'tags' => [ 'shape' => 'TagMap', ], 'environment' => [ 'shape' => 'Environment', ], ], ], 'CreateRobotApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'version' => [ 'shape' => 'Version', ], 'sources' => [ 'shape' => 'Sources', ], 'robotSoftwareSuite' => [ 'shape' => 'RobotSoftwareSuite', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'revisionId' => [ 'shape' => 'RevisionId', ], 'tags' => [ 'shape' => 'TagMap', ], 'environment' => [ 'shape' => 'Environment', ], ], ], 'CreateRobotApplicationVersionRequest' => [ 'type' => 'structure', 'required' => [ 'application', ], 'members' => [ 'application' => [ 'shape' => 'Arn', ], 'currentRevisionId' => [ 'shape' => 'RevisionId', ], 's3Etags' => [ 'shape' => 'S3Etags', ], 'imageDigest' => [ 'shape' => 'ImageDigest', ], ], ], 'CreateRobotApplicationVersionResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'version' => [ 'shape' => 'Version', ], 'sources' => [ 'shape' => 'Sources', ], 'robotSoftwareSuite' => [ 'shape' => 'RobotSoftwareSuite', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'revisionId' => [ 'shape' => 'RevisionId', ], 'environment' => [ 'shape' => 'Environment', ], ], ], 'CreateRobotRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'architecture', 'greengrassGroupId', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'architecture' => [ 'shape' => 'Architecture', ], 'greengrassGroupId' => [ 'shape' => 'Id', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateRobotResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'greengrassGroupId' => [ 'shape' => 'Id', ], 'architecture' => [ 'shape' => 'Architecture', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateSimulationApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'simulationSoftwareSuite', 'robotSoftwareSuite', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'sources' => [ 'shape' => 'SourceConfigs', ], 'simulationSoftwareSuite' => [ 'shape' => 'SimulationSoftwareSuite', ], 'robotSoftwareSuite' => [ 'shape' => 'RobotSoftwareSuite', ], 'renderingEngine' => [ 'shape' => 'RenderingEngine', ], 'tags' => [ 'shape' => 'TagMap', ], 'environment' => [ 'shape' => 'Environment', ], ], ], 'CreateSimulationApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'version' => [ 'shape' => 'Version', ], 'sources' => [ 'shape' => 'Sources', ], 'simulationSoftwareSuite' => [ 'shape' => 'SimulationSoftwareSuite', ], 'robotSoftwareSuite' => [ 'shape' => 'RobotSoftwareSuite', ], 'renderingEngine' => [ 'shape' => 'RenderingEngine', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'revisionId' => [ 'shape' => 'RevisionId', ], 'tags' => [ 'shape' => 'TagMap', ], 'environment' => [ 'shape' => 'Environment', ], ], ], 'CreateSimulationApplicationVersionRequest' => [ 'type' => 'structure', 'required' => [ 'application', ], 'members' => [ 'application' => [ 'shape' => 'Arn', ], 'currentRevisionId' => [ 'shape' => 'RevisionId', ], 's3Etags' => [ 'shape' => 'S3Etags', ], 'imageDigest' => [ 'shape' => 'ImageDigest', ], ], ], 'CreateSimulationApplicationVersionResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'version' => [ 'shape' => 'Version', ], 'sources' => [ 'shape' => 'Sources', ], 'simulationSoftwareSuite' => [ 'shape' => 'SimulationSoftwareSuite', ], 'robotSoftwareSuite' => [ 'shape' => 'RobotSoftwareSuite', ], 'renderingEngine' => [ 'shape' => 'RenderingEngine', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'revisionId' => [ 'shape' => 'RevisionId', ], 'environment' => [ 'shape' => 'Environment', ], ], ], 'CreateSimulationJobRequest' => [ 'type' => 'structure', 'required' => [ 'maxJobDurationInSeconds', 'iamRole', ], 'members' => [ 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'outputLocation' => [ 'shape' => 'OutputLocation', ], 'loggingConfig' => [ 'shape' => 'LoggingConfig', ], 'maxJobDurationInSeconds' => [ 'shape' => 'JobDuration', ], 'iamRole' => [ 'shape' => 'IamRole', ], 'failureBehavior' => [ 'shape' => 'FailureBehavior', ], 'robotApplications' => [ 'shape' => 'RobotApplicationConfigs', ], 'simulationApplications' => [ 'shape' => 'SimulationApplicationConfigs', ], 'dataSources' => [ 'shape' => 'DataSourceConfigs', ], 'tags' => [ 'shape' => 'TagMap', ], 'vpcConfig' => [ 'shape' => 'VPCConfig', ], 'compute' => [ 'shape' => 'Compute', ], ], ], 'CreateSimulationJobRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'SimulationJobRequest', ], 'max' => 1000, 'min' => 1, ], 'CreateSimulationJobResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'SimulationJobStatus', ], 'lastStartedAt' => [ 'shape' => 'LastStartedAt', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'failureBehavior' => [ 'shape' => 'FailureBehavior', ], 'failureCode' => [ 'shape' => 'SimulationJobErrorCode', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'outputLocation' => [ 'shape' => 'OutputLocation', ], 'loggingConfig' => [ 'shape' => 'LoggingConfig', ], 'maxJobDurationInSeconds' => [ 'shape' => 'JobDuration', ], 'simulationTimeMillis' => [ 'shape' => 'SimulationTimeMillis', ], 'iamRole' => [ 'shape' => 'IamRole', ], 'robotApplications' => [ 'shape' => 'RobotApplicationConfigs', ], 'simulationApplications' => [ 'shape' => 'SimulationApplicationConfigs', ], 'dataSources' => [ 'shape' => 'DataSources', ], 'tags' => [ 'shape' => 'TagMap', ], 'vpcConfig' => [ 'shape' => 'VPCConfigResponse', ], 'compute' => [ 'shape' => 'ComputeResponse', ], ], ], 'CreateWorldExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'worlds', 'outputLocation', 'iamRole', ], 'members' => [ 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'worlds' => [ 'shape' => 'Arns', ], 'outputLocation' => [ 'shape' => 'OutputLocation', ], 'iamRole' => [ 'shape' => 'IamRole', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateWorldExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'WorldExportJobStatus', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'failureCode' => [ 'shape' => 'WorldExportJobErrorCode', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'outputLocation' => [ 'shape' => 'OutputLocation', ], 'iamRole' => [ 'shape' => 'IamRole', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateWorldGenerationJobRequest' => [ 'type' => 'structure', 'required' => [ 'template', 'worldCount', ], 'members' => [ 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'template' => [ 'shape' => 'Arn', ], 'worldCount' => [ 'shape' => 'WorldCount', ], 'tags' => [ 'shape' => 'TagMap', ], 'worldTags' => [ 'shape' => 'TagMap', ], ], ], 'CreateWorldGenerationJobResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'WorldGenerationJobStatus', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'failureCode' => [ 'shape' => 'WorldGenerationJobErrorCode', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'template' => [ 'shape' => 'Arn', ], 'worldCount' => [ 'shape' => 'WorldCount', ], 'tags' => [ 'shape' => 'TagMap', ], 'worldTags' => [ 'shape' => 'TagMap', ], ], ], 'CreateWorldTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'name' => [ 'shape' => 'TemplateName', ], 'templateBody' => [ 'shape' => 'Json', ], 'templateLocation' => [ 'shape' => 'TemplateLocation', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateWorldTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'name' => [ 'shape' => 'TemplateName', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreatedAt' => [ 'type' => 'timestamp', ], 'DataSource' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 's3Bucket' => [ 'shape' => 'S3Bucket', ], 's3Keys' => [ 'shape' => 'S3KeyOutputs', ], 'type' => [ 'shape' => 'DataSourceType', ], 'destination' => [ 'shape' => 'Path', ], ], ], 'DataSourceConfig' => [ 'type' => 'structure', 'required' => [ 'name', 's3Bucket', 's3Keys', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 's3Bucket' => [ 'shape' => 'S3Bucket', ], 's3Keys' => [ 'shape' => 'S3KeysOrPrefixes', ], 'type' => [ 'shape' => 'DataSourceType', ], 'destination' => [ 'shape' => 'Path', ], ], ], 'DataSourceConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceConfig', ], 'max' => 5, 'min' => 1, ], 'DataSourceNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'Name', ], ], 'DataSourceType' => [ 'type' => 'string', 'enum' => [ 'Prefix', 'Archive', 'File', ], ], 'DataSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSource', ], ], 'DeleteFleetRequest' => [ 'type' => 'structure', 'required' => [ 'fleet', ], 'members' => [ 'fleet' => [ 'shape' => 'Arn', ], ], ], 'DeleteFleetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRobotApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'application', ], 'members' => [ 'application' => [ 'shape' => 'Arn', ], 'applicationVersion' => [ 'shape' => 'Version', ], ], ], 'DeleteRobotApplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRobotRequest' => [ 'type' => 'structure', 'required' => [ 'robot', ], 'members' => [ 'robot' => [ 'shape' => 'Arn', ], ], ], 'DeleteRobotResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSimulationApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'application', ], 'members' => [ 'application' => [ 'shape' => 'Arn', ], 'applicationVersion' => [ 'shape' => 'Version', ], ], ], 'DeleteSimulationApplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWorldTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'template', ], 'members' => [ 'template' => [ 'shape' => 'Arn', ], ], ], 'DeleteWorldTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeploymentApplicationConfig' => [ 'type' => 'structure', 'required' => [ 'application', 'applicationVersion', 'launchConfig', ], 'members' => [ 'application' => [ 'shape' => 'Arn', ], 'applicationVersion' => [ 'shape' => 'DeploymentVersion', ], 'launchConfig' => [ 'shape' => 'DeploymentLaunchConfig', ], ], ], 'DeploymentApplicationConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeploymentApplicationConfig', ], 'max' => 1, 'min' => 1, ], 'DeploymentConfig' => [ 'type' => 'structure', 'members' => [ 'concurrentDeploymentPercentage' => [ 'shape' => 'Percentage', ], 'failureThresholdPercentage' => [ 'shape' => 'Percentage', ], 'robotDeploymentTimeoutInSeconds' => [ 'shape' => 'DeploymentTimeout', ], 'downloadConditionFile' => [ 'shape' => 'S3Object', ], ], ], 'DeploymentJob' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'fleet' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'DeploymentStatus', ], 'deploymentApplicationConfigs' => [ 'shape' => 'DeploymentApplicationConfigs', ], 'deploymentConfig' => [ 'shape' => 'DeploymentConfig', ], 'failureReason' => [ 'shape' => 'GenericString', ], 'failureCode' => [ 'shape' => 'DeploymentJobErrorCode', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], ], ], 'DeploymentJobErrorCode' => [ 'type' => 'string', 'enum' => [ 'ResourceNotFound', 'EnvironmentSetupError', 'EtagMismatch', 'FailureThresholdBreached', 'RobotDeploymentAborted', 'RobotDeploymentNoResponse', 'RobotAgentConnectionTimeout', 'GreengrassDeploymentFailed', 'InvalidGreengrassGroup', 'MissingRobotArchitecture', 'MissingRobotApplicationArchitecture', 'MissingRobotDeploymentResource', 'GreengrassGroupVersionDoesNotExist', 'LambdaDeleted', 'ExtractingBundleFailure', 'PreLaunchFileFailure', 'PostLaunchFileFailure', 'BadPermissionError', 'DownloadConditionFailed', 'BadLambdaAssociated', 'InternalServerError', 'RobotApplicationDoesNotExist', 'DeploymentFleetDoesNotExist', 'FleetDeploymentTimeout', ], ], 'DeploymentJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeploymentJob', ], 'max' => 200, 'min' => 0, ], 'DeploymentLaunchConfig' => [ 'type' => 'structure', 'required' => [ 'packageName', 'launchFile', ], 'members' => [ 'packageName' => [ 'shape' => 'Command', ], 'preLaunchFile' => [ 'shape' => 'Path', ], 'launchFile' => [ 'shape' => 'Command', ], 'postLaunchFile' => [ 'shape' => 'Path', ], 'environmentVariables' => [ 'shape' => 'EnvironmentVariableMap', ], ], ], 'DeploymentStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'Preparing', 'InProgress', 'Failed', 'Succeeded', 'Canceled', ], ], 'DeploymentTimeout' => [ 'type' => 'long', ], 'DeploymentVersion' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[0-9]*', ], 'DeregisterRobotRequest' => [ 'type' => 'structure', 'required' => [ 'fleet', 'robot', ], 'members' => [ 'fleet' => [ 'shape' => 'Arn', ], 'robot' => [ 'shape' => 'Arn', ], ], ], 'DeregisterRobotResponse' => [ 'type' => 'structure', 'members' => [ 'fleet' => [ 'shape' => 'Arn', ], 'robot' => [ 'shape' => 'Arn', ], ], ], 'DescribeDeploymentJobRequest' => [ 'type' => 'structure', 'required' => [ 'job', ], 'members' => [ 'job' => [ 'shape' => 'Arn', ], ], ], 'DescribeDeploymentJobResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'fleet' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'DeploymentStatus', ], 'deploymentConfig' => [ 'shape' => 'DeploymentConfig', ], 'deploymentApplicationConfigs' => [ 'shape' => 'DeploymentApplicationConfigs', ], 'failureReason' => [ 'shape' => 'GenericString', ], 'failureCode' => [ 'shape' => 'DeploymentJobErrorCode', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'robotDeploymentSummary' => [ 'shape' => 'RobotDeploymentSummary', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'DescribeFleetRequest' => [ 'type' => 'structure', 'required' => [ 'fleet', ], 'members' => [ 'fleet' => [ 'shape' => 'Arn', ], ], ], 'DescribeFleetResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'arn' => [ 'shape' => 'Arn', ], 'robots' => [ 'shape' => 'Robots', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'lastDeploymentStatus' => [ 'shape' => 'DeploymentStatus', ], 'lastDeploymentJob' => [ 'shape' => 'Arn', ], 'lastDeploymentTime' => [ 'shape' => 'CreatedAt', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'DescribeRobotApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'application', ], 'members' => [ 'application' => [ 'shape' => 'Arn', ], 'applicationVersion' => [ 'shape' => 'Version', ], ], ], 'DescribeRobotApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'version' => [ 'shape' => 'Version', ], 'sources' => [ 'shape' => 'Sources', ], 'robotSoftwareSuite' => [ 'shape' => 'RobotSoftwareSuite', ], 'revisionId' => [ 'shape' => 'RevisionId', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'tags' => [ 'shape' => 'TagMap', ], 'environment' => [ 'shape' => 'Environment', ], 'imageDigest' => [ 'shape' => 'ImageDigest', ], ], ], 'DescribeRobotRequest' => [ 'type' => 'structure', 'required' => [ 'robot', ], 'members' => [ 'robot' => [ 'shape' => 'Arn', ], ], ], 'DescribeRobotResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'fleetArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'RobotStatus', ], 'greengrassGroupId' => [ 'shape' => 'Id', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'architecture' => [ 'shape' => 'Architecture', ], 'lastDeploymentJob' => [ 'shape' => 'Arn', ], 'lastDeploymentTime' => [ 'shape' => 'CreatedAt', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'DescribeSimulationApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'application', ], 'members' => [ 'application' => [ 'shape' => 'Arn', ], 'applicationVersion' => [ 'shape' => 'Version', ], ], ], 'DescribeSimulationApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'version' => [ 'shape' => 'Version', ], 'sources' => [ 'shape' => 'Sources', ], 'simulationSoftwareSuite' => [ 'shape' => 'SimulationSoftwareSuite', ], 'robotSoftwareSuite' => [ 'shape' => 'RobotSoftwareSuite', ], 'renderingEngine' => [ 'shape' => 'RenderingEngine', ], 'revisionId' => [ 'shape' => 'RevisionId', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'tags' => [ 'shape' => 'TagMap', ], 'environment' => [ 'shape' => 'Environment', ], 'imageDigest' => [ 'shape' => 'ImageDigest', ], ], ], 'DescribeSimulationJobBatchRequest' => [ 'type' => 'structure', 'required' => [ 'batch', ], 'members' => [ 'batch' => [ 'shape' => 'Arn', ], ], ], 'DescribeSimulationJobBatchResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'SimulationJobBatchStatus', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'batchPolicy' => [ 'shape' => 'BatchPolicy', ], 'failureCode' => [ 'shape' => 'SimulationJobBatchErrorCode', ], 'failureReason' => [ 'shape' => 'GenericString', ], 'failedRequests' => [ 'shape' => 'FailedCreateSimulationJobRequests', ], 'pendingRequests' => [ 'shape' => 'CreateSimulationJobRequests', ], 'createdRequests' => [ 'shape' => 'SimulationJobSummaries', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'DescribeSimulationJobRequest' => [ 'type' => 'structure', 'required' => [ 'job', ], 'members' => [ 'job' => [ 'shape' => 'Arn', ], ], ], 'DescribeSimulationJobResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'SimulationJobStatus', ], 'lastStartedAt' => [ 'shape' => 'LastStartedAt', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'failureBehavior' => [ 'shape' => 'FailureBehavior', ], 'failureCode' => [ 'shape' => 'SimulationJobErrorCode', ], 'failureReason' => [ 'shape' => 'GenericString', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'outputLocation' => [ 'shape' => 'OutputLocation', ], 'loggingConfig' => [ 'shape' => 'LoggingConfig', ], 'maxJobDurationInSeconds' => [ 'shape' => 'JobDuration', ], 'simulationTimeMillis' => [ 'shape' => 'SimulationTimeMillis', ], 'iamRole' => [ 'shape' => 'IamRole', ], 'robotApplications' => [ 'shape' => 'RobotApplicationConfigs', ], 'simulationApplications' => [ 'shape' => 'SimulationApplicationConfigs', ], 'dataSources' => [ 'shape' => 'DataSources', ], 'tags' => [ 'shape' => 'TagMap', ], 'vpcConfig' => [ 'shape' => 'VPCConfigResponse', ], 'networkInterface' => [ 'shape' => 'NetworkInterface', ], 'compute' => [ 'shape' => 'ComputeResponse', ], ], ], 'DescribeWorldExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'job', ], 'members' => [ 'job' => [ 'shape' => 'Arn', ], ], ], 'DescribeWorldExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'WorldExportJobStatus', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'failureCode' => [ 'shape' => 'WorldExportJobErrorCode', ], 'failureReason' => [ 'shape' => 'GenericString', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'worlds' => [ 'shape' => 'Arns', ], 'outputLocation' => [ 'shape' => 'OutputLocation', ], 'iamRole' => [ 'shape' => 'IamRole', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'DescribeWorldGenerationJobRequest' => [ 'type' => 'structure', 'required' => [ 'job', ], 'members' => [ 'job' => [ 'shape' => 'Arn', ], ], ], 'DescribeWorldGenerationJobResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'WorldGenerationJobStatus', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'failureCode' => [ 'shape' => 'WorldGenerationJobErrorCode', ], 'failureReason' => [ 'shape' => 'GenericString', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'template' => [ 'shape' => 'Arn', ], 'worldCount' => [ 'shape' => 'WorldCount', ], 'finishedWorldsSummary' => [ 'shape' => 'FinishedWorldsSummary', ], 'tags' => [ 'shape' => 'TagMap', ], 'worldTags' => [ 'shape' => 'TagMap', ], ], ], 'DescribeWorldRequest' => [ 'type' => 'structure', 'required' => [ 'world', ], 'members' => [ 'world' => [ 'shape' => 'Arn', ], ], ], 'DescribeWorldResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'generationJob' => [ 'shape' => 'Arn', ], 'template' => [ 'shape' => 'Arn', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'tags' => [ 'shape' => 'TagMap', ], 'worldDescriptionBody' => [ 'shape' => 'Json', ], ], ], 'DescribeWorldTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'template', ], 'members' => [ 'template' => [ 'shape' => 'Arn', ], ], ], 'DescribeWorldTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'name' => [ 'shape' => 'TemplateName', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'tags' => [ 'shape' => 'TagMap', ], 'version' => [ 'shape' => 'GenericString', ], ], ], 'Environment' => [ 'type' => 'structure', 'members' => [ 'uri' => [ 'shape' => 'RepositoryUrl', ], ], ], 'EnvironmentVariableKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[A-Z_][A-Z0-9_]*', ], 'EnvironmentVariableMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'EnvironmentVariableKey', ], 'value' => [ 'shape' => 'EnvironmentVariableValue', ], 'max' => 16, 'min' => 0, ], 'EnvironmentVariableValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.*', ], 'ExitBehavior' => [ 'type' => 'string', 'enum' => [ 'FAIL', 'RESTART', ], ], 'FailedAt' => [ 'type' => 'timestamp', ], 'FailedCreateSimulationJobRequest' => [ 'type' => 'structure', 'members' => [ 'request' => [ 'shape' => 'SimulationJobRequest', ], 'failureReason' => [ 'shape' => 'GenericString', ], 'failureCode' => [ 'shape' => 'SimulationJobErrorCode', ], 'failedAt' => [ 'shape' => 'FailedAt', ], ], ], 'FailedCreateSimulationJobRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedCreateSimulationJobRequest', ], ], 'FailureBehavior' => [ 'type' => 'string', 'enum' => [ 'Fail', 'Continue', ], ], 'FailureSummary' => [ 'type' => 'structure', 'members' => [ 'totalFailureCount' => [ 'shape' => 'Integer', ], 'failures' => [ 'shape' => 'WorldFailures', ], ], ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'values' => [ 'shape' => 'FilterValues', ], ], ], 'FilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'Name', ], 'max' => 1, 'min' => 1, ], 'Filters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], 'max' => 1, 'min' => 1, ], 'FinishedWorldsSummary' => [ 'type' => 'structure', 'members' => [ 'finishedCount' => [ 'shape' => 'Integer', ], 'succeededWorlds' => [ 'shape' => 'Arns', ], 'failureSummary' => [ 'shape' => 'FailureSummary', ], ], ], 'Fleet' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'arn' => [ 'shape' => 'Arn', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'lastDeploymentStatus' => [ 'shape' => 'DeploymentStatus', ], 'lastDeploymentJob' => [ 'shape' => 'Arn', ], 'lastDeploymentTime' => [ 'shape' => 'CreatedAt', ], ], ], 'Fleets' => [ 'type' => 'list', 'member' => [ 'shape' => 'Fleet', ], 'max' => 200, 'min' => 0, ], 'FloorplanCount' => [ 'type' => 'integer', ], 'GPUUnit' => [ 'type' => 'integer', 'max' => 1, 'min' => 0, ], 'GenericInteger' => [ 'type' => 'integer', ], 'GenericString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*', ], 'GetWorldTemplateBodyRequest' => [ 'type' => 'structure', 'members' => [ 'template' => [ 'shape' => 'Arn', ], 'generationJob' => [ 'shape' => 'Arn', ], ], ], 'GetWorldTemplateBodyResponse' => [ 'type' => 'structure', 'members' => [ 'templateBody' => [ 'shape' => 'Json', ], ], ], 'IamRole' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => 'arn:aws:iam::\\w+:role/.*', ], 'Id' => [ 'type' => 'string', 'max' => 1224, 'min' => 1, 'pattern' => '.*', ], 'IdempotentParameterMismatchException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ImageDigest' => [ 'type' => 'string', 'max' => 72, 'min' => 0, 'pattern' => '[Ss][Hh][Aa]256:[0-9a-fA-F]{64}', ], 'Integer' => [ 'type' => 'integer', ], 'InteriorCountPerFloorplan' => [ 'type' => 'integer', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'JobDuration' => [ 'type' => 'long', ], 'Json' => [ 'type' => 'string', 'max' => 262144, 'min' => 1, 'pattern' => '[\\S\\s]+', ], 'LastStartedAt' => [ 'type' => 'timestamp', ], 'LastUpdatedAt' => [ 'type' => 'timestamp', ], 'LaunchConfig' => [ 'type' => 'structure', 'members' => [ 'packageName' => [ 'shape' => 'Command', ], 'launchFile' => [ 'shape' => 'Command', ], 'environmentVariables' => [ 'shape' => 'EnvironmentVariableMap', ], 'portForwardingConfig' => [ 'shape' => 'PortForwardingConfig', ], 'streamUI' => [ 'shape' => 'Boolean', ], 'command' => [ 'shape' => 'CommandList', ], ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ListDeploymentJobsRequest' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'Filters', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDeploymentJobsResponse' => [ 'type' => 'structure', 'members' => [ 'deploymentJobs' => [ 'shape' => 'DeploymentJobs', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListFleetsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filters' => [ 'shape' => 'Filters', ], ], ], 'ListFleetsResponse' => [ 'type' => 'structure', 'members' => [ 'fleetDetails' => [ 'shape' => 'Fleets', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListRobotApplicationsRequest' => [ 'type' => 'structure', 'members' => [ 'versionQualifier' => [ 'shape' => 'VersionQualifier', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filters' => [ 'shape' => 'Filters', ], ], ], 'ListRobotApplicationsResponse' => [ 'type' => 'structure', 'members' => [ 'robotApplicationSummaries' => [ 'shape' => 'RobotApplicationSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListRobotsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filters' => [ 'shape' => 'Filters', ], ], ], 'ListRobotsResponse' => [ 'type' => 'structure', 'members' => [ 'robots' => [ 'shape' => 'Robots', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSimulationApplicationsRequest' => [ 'type' => 'structure', 'members' => [ 'versionQualifier' => [ 'shape' => 'VersionQualifier', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filters' => [ 'shape' => 'Filters', ], ], ], 'ListSimulationApplicationsResponse' => [ 'type' => 'structure', 'members' => [ 'simulationApplicationSummaries' => [ 'shape' => 'SimulationApplicationSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSimulationJobBatchesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filters' => [ 'shape' => 'Filters', ], ], ], 'ListSimulationJobBatchesResponse' => [ 'type' => 'structure', 'members' => [ 'simulationJobBatchSummaries' => [ 'shape' => 'SimulationJobBatchSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSimulationJobsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filters' => [ 'shape' => 'Filters', ], ], ], 'ListSimulationJobsResponse' => [ 'type' => 'structure', 'required' => [ 'simulationJobSummaries', ], 'members' => [ 'simulationJobSummaries' => [ 'shape' => 'SimulationJobSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListWorldExportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filters' => [ 'shape' => 'Filters', ], ], ], 'ListWorldExportJobsResponse' => [ 'type' => 'structure', 'required' => [ 'worldExportJobSummaries', ], 'members' => [ 'worldExportJobSummaries' => [ 'shape' => 'WorldExportJobSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListWorldGenerationJobsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filters' => [ 'shape' => 'Filters', ], ], ], 'ListWorldGenerationJobsResponse' => [ 'type' => 'structure', 'required' => [ 'worldGenerationJobSummaries', ], 'members' => [ 'worldGenerationJobSummaries' => [ 'shape' => 'WorldGenerationJobSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListWorldTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListWorldTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'templateSummaries' => [ 'shape' => 'TemplateSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListWorldsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filters' => [ 'shape' => 'Filters', ], ], ], 'ListWorldsResponse' => [ 'type' => 'structure', 'members' => [ 'worldSummaries' => [ 'shape' => 'WorldSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'LoggingConfig' => [ 'type' => 'structure', 'required' => [ 'recordAllRosTopics', ], 'members' => [ 'recordAllRosTopics' => [ 'shape' => 'BoxedBoolean', ], ], ], 'MaxConcurrency' => [ 'type' => 'integer', ], 'MaxResults' => [ 'type' => 'integer', ], 'Name' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[a-zA-Z0-9_\\-]*', ], 'NetworkInterface' => [ 'type' => 'structure', 'members' => [ 'networkInterfaceId' => [ 'shape' => 'GenericString', ], 'privateIpAddress' => [ 'shape' => 'GenericString', ], 'publicIpAddress' => [ 'shape' => 'GenericString', ], ], ], 'NonEmptyString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.+', ], 'NonSystemPort' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1024, ], 'OutputLocation' => [ 'type' => 'structure', 'members' => [ 's3Bucket' => [ 'shape' => 'S3Bucket', ], 's3Prefix' => [ 'shape' => 'S3Key', ], ], ], 'PaginationToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.\\-\\/+=]*', ], 'Path' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.*', ], 'PercentDone' => [ 'type' => 'float', 'max' => 100, 'min' => 0, ], 'Percentage' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'Port' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1, ], 'PortForwardingConfig' => [ 'type' => 'structure', 'members' => [ 'portMappings' => [ 'shape' => 'PortMappingList', ], ], ], 'PortMapping' => [ 'type' => 'structure', 'required' => [ 'jobPort', 'applicationPort', ], 'members' => [ 'jobPort' => [ 'shape' => 'Port', ], 'applicationPort' => [ 'shape' => 'NonSystemPort', ], 'enableOnPublicIp' => [ 'shape' => 'Boolean', ], ], ], 'PortMappingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortMapping', ], 'max' => 10, 'min' => 0, ], 'ProgressDetail' => [ 'type' => 'structure', 'members' => [ 'currentProgress' => [ 'shape' => 'RobotDeploymentStep', ], 'percentDone' => [ 'shape' => 'PercentDone', ], 'estimatedTimeRemainingSeconds' => [ 'shape' => 'GenericInteger', ], 'targetResource' => [ 'shape' => 'GenericString', ], ], ], 'RegisterRobotRequest' => [ 'type' => 'structure', 'required' => [ 'fleet', 'robot', ], 'members' => [ 'fleet' => [ 'shape' => 'Arn', ], 'robot' => [ 'shape' => 'Arn', ], ], ], 'RegisterRobotResponse' => [ 'type' => 'structure', 'members' => [ 'fleet' => [ 'shape' => 'Arn', ], 'robot' => [ 'shape' => 'Arn', ], ], ], 'RenderingEngine' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'RenderingEngineType', ], 'version' => [ 'shape' => 'RenderingEngineVersionType', ], ], ], 'RenderingEngineType' => [ 'type' => 'string', 'enum' => [ 'OGRE', ], ], 'RenderingEngineVersionType' => [ 'type' => 'string', 'max' => 4, 'min' => 1, 'pattern' => '1.x', ], 'RepositoryUrl' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.+', ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'RestartSimulationJobRequest' => [ 'type' => 'structure', 'required' => [ 'job', ], 'members' => [ 'job' => [ 'shape' => 'Arn', ], ], ], 'RestartSimulationJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'RevisionId' => [ 'type' => 'string', 'max' => 40, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.\\-]*', ], 'Robot' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'fleetArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'RobotStatus', ], 'greenGrassGroupId' => [ 'shape' => 'Id', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'architecture' => [ 'shape' => 'Architecture', ], 'lastDeploymentJob' => [ 'shape' => 'Arn', ], 'lastDeploymentTime' => [ 'shape' => 'CreatedAt', ], ], ], 'RobotApplicationConfig' => [ 'type' => 'structure', 'required' => [ 'application', 'launchConfig', ], 'members' => [ 'application' => [ 'shape' => 'Arn', ], 'applicationVersion' => [ 'shape' => 'Version', ], 'launchConfig' => [ 'shape' => 'LaunchConfig', ], 'uploadConfigurations' => [ 'shape' => 'UploadConfigurations', ], 'useDefaultUploadConfigurations' => [ 'shape' => 'BoxedBoolean', ], 'tools' => [ 'shape' => 'Tools', ], 'useDefaultTools' => [ 'shape' => 'BoxedBoolean', ], ], ], 'RobotApplicationConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'RobotApplicationConfig', ], 'max' => 1, 'min' => 1, ], 'RobotApplicationNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'Name', ], ], 'RobotApplicationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'RobotApplicationSummary', ], 'max' => 100, 'min' => 0, ], 'RobotApplicationSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'arn' => [ 'shape' => 'Arn', ], 'version' => [ 'shape' => 'Version', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'robotSoftwareSuite' => [ 'shape' => 'RobotSoftwareSuite', ], ], ], 'RobotDeployment' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'deploymentStartTime' => [ 'shape' => 'CreatedAt', ], 'deploymentFinishTime' => [ 'shape' => 'CreatedAt', ], 'status' => [ 'shape' => 'RobotStatus', ], 'progressDetail' => [ 'shape' => 'ProgressDetail', ], 'failureReason' => [ 'shape' => 'GenericString', ], 'failureCode' => [ 'shape' => 'DeploymentJobErrorCode', ], ], ], 'RobotDeploymentStep' => [ 'type' => 'string', 'enum' => [ 'Validating', 'DownloadingExtracting', 'ExecutingDownloadCondition', 'ExecutingPreLaunch', 'Launching', 'ExecutingPostLaunch', 'Finished', ], ], 'RobotDeploymentSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'RobotDeployment', ], ], 'RobotSoftwareSuite' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'RobotSoftwareSuiteType', ], 'version' => [ 'shape' => 'RobotSoftwareSuiteVersionType', ], ], ], 'RobotSoftwareSuiteType' => [ 'type' => 'string', 'enum' => [ 'ROS', 'ROS2', 'General', ], ], 'RobotSoftwareSuiteVersionType' => [ 'type' => 'string', 'enum' => [ 'Kinetic', 'Melodic', 'Dashing', 'Foxy', ], ], 'RobotStatus' => [ 'type' => 'string', 'enum' => [ 'Available', 'Registered', 'PendingNewDeployment', 'Deploying', 'Failed', 'InSync', 'NoResponse', ], ], 'Robots' => [ 'type' => 'list', 'member' => [ 'shape' => 'Robot', ], 'max' => 1000, 'min' => 0, ], 'S3Bucket' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '[a-z0-9][a-z0-9.\\-]*[a-z0-9]', ], 'S3Etag' => [ 'type' => 'string', ], 'S3Etags' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Etag', ], ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.*', ], 'S3KeyOrPrefix' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*', ], 'S3KeyOutput' => [ 'type' => 'structure', 'members' => [ 's3Key' => [ 'shape' => 'S3KeyOrPrefix', ], 'etag' => [ 'shape' => 'S3Etag', ], ], ], 'S3KeyOutputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3KeyOutput', ], ], 'S3KeysOrPrefixes' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3KeyOrPrefix', ], 'max' => 100, 'min' => 1, ], 'S3Object' => [ 'type' => 'structure', 'required' => [ 'bucket', 'key', ], 'members' => [ 'bucket' => [ 'shape' => 'S3Bucket', ], 'key' => [ 'shape' => 'S3Key', ], 'etag' => [ 'shape' => 'S3Etag', ], ], ], 'SecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 5, 'min' => 1, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, ], 'SimulationApplicationConfig' => [ 'type' => 'structure', 'required' => [ 'application', 'launchConfig', ], 'members' => [ 'application' => [ 'shape' => 'Arn', ], 'applicationVersion' => [ 'shape' => 'Version', ], 'launchConfig' => [ 'shape' => 'LaunchConfig', ], 'uploadConfigurations' => [ 'shape' => 'UploadConfigurations', ], 'worldConfigs' => [ 'shape' => 'WorldConfigs', ], 'useDefaultUploadConfigurations' => [ 'shape' => 'BoxedBoolean', ], 'tools' => [ 'shape' => 'Tools', ], 'useDefaultTools' => [ 'shape' => 'BoxedBoolean', ], ], ], 'SimulationApplicationConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SimulationApplicationConfig', ], 'max' => 1, 'min' => 1, ], 'SimulationApplicationNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'Name', ], ], 'SimulationApplicationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SimulationApplicationSummary', ], 'max' => 100, 'min' => 0, ], 'SimulationApplicationSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'arn' => [ 'shape' => 'Arn', ], 'version' => [ 'shape' => 'Version', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'robotSoftwareSuite' => [ 'shape' => 'RobotSoftwareSuite', ], 'simulationSoftwareSuite' => [ 'shape' => 'SimulationSoftwareSuite', ], ], ], 'SimulationJob' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'SimulationJobStatus', ], 'lastStartedAt' => [ 'shape' => 'LastStartedAt', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'failureBehavior' => [ 'shape' => 'FailureBehavior', ], 'failureCode' => [ 'shape' => 'SimulationJobErrorCode', ], 'failureReason' => [ 'shape' => 'GenericString', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'outputLocation' => [ 'shape' => 'OutputLocation', ], 'loggingConfig' => [ 'shape' => 'LoggingConfig', ], 'maxJobDurationInSeconds' => [ 'shape' => 'JobDuration', ], 'simulationTimeMillis' => [ 'shape' => 'SimulationTimeMillis', ], 'iamRole' => [ 'shape' => 'IamRole', ], 'robotApplications' => [ 'shape' => 'RobotApplicationConfigs', ], 'simulationApplications' => [ 'shape' => 'SimulationApplicationConfigs', ], 'dataSources' => [ 'shape' => 'DataSources', ], 'tags' => [ 'shape' => 'TagMap', ], 'vpcConfig' => [ 'shape' => 'VPCConfigResponse', ], 'networkInterface' => [ 'shape' => 'NetworkInterface', ], 'compute' => [ 'shape' => 'ComputeResponse', ], ], ], 'SimulationJobBatchErrorCode' => [ 'type' => 'string', 'enum' => [ 'InternalServiceError', ], ], 'SimulationJobBatchStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Failed', 'Completed', 'Canceled', 'Canceling', 'Completing', 'TimingOut', 'TimedOut', ], ], 'SimulationJobBatchSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SimulationJobBatchSummary', ], ], 'SimulationJobBatchSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'status' => [ 'shape' => 'SimulationJobBatchStatus', ], 'failedRequestCount' => [ 'shape' => 'Integer', ], 'pendingRequestCount' => [ 'shape' => 'Integer', ], 'createdRequestCount' => [ 'shape' => 'Integer', ], ], ], 'SimulationJobErrorCode' => [ 'type' => 'string', 'enum' => [ 'InternalServiceError', 'RobotApplicationCrash', 'SimulationApplicationCrash', 'RobotApplicationHealthCheckFailure', 'SimulationApplicationHealthCheckFailure', 'BadPermissionsRobotApplication', 'BadPermissionsSimulationApplication', 'BadPermissionsS3Object', 'BadPermissionsS3Output', 'BadPermissionsCloudwatchLogs', 'SubnetIpLimitExceeded', 'ENILimitExceeded', 'BadPermissionsUserCredentials', 'InvalidBundleRobotApplication', 'InvalidBundleSimulationApplication', 'InvalidS3Resource', 'ThrottlingError', 'LimitExceeded', 'MismatchedEtag', 'RobotApplicationVersionMismatchedEtag', 'SimulationApplicationVersionMismatchedEtag', 'ResourceNotFound', 'RequestThrottled', 'BatchTimedOut', 'BatchCanceled', 'InvalidInput', 'WrongRegionS3Bucket', 'WrongRegionS3Output', 'WrongRegionRobotApplication', 'WrongRegionSimulationApplication', 'UploadContentMismatchError', ], ], 'SimulationJobRequest' => [ 'type' => 'structure', 'required' => [ 'maxJobDurationInSeconds', ], 'members' => [ 'outputLocation' => [ 'shape' => 'OutputLocation', ], 'loggingConfig' => [ 'shape' => 'LoggingConfig', ], 'maxJobDurationInSeconds' => [ 'shape' => 'JobDuration', ], 'iamRole' => [ 'shape' => 'IamRole', ], 'failureBehavior' => [ 'shape' => 'FailureBehavior', ], 'useDefaultApplications' => [ 'shape' => 'BoxedBoolean', ], 'robotApplications' => [ 'shape' => 'RobotApplicationConfigs', ], 'simulationApplications' => [ 'shape' => 'SimulationApplicationConfigs', ], 'dataSources' => [ 'shape' => 'DataSourceConfigs', ], 'vpcConfig' => [ 'shape' => 'VPCConfig', ], 'compute' => [ 'shape' => 'Compute', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'SimulationJobStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'Preparing', 'Running', 'Restarting', 'Completed', 'Failed', 'RunningFailed', 'Terminating', 'Terminated', 'Canceled', ], ], 'SimulationJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SimulationJobSummary', ], 'max' => 100, 'min' => 0, ], 'SimulationJobSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'name' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'SimulationJobStatus', ], 'simulationApplicationNames' => [ 'shape' => 'SimulationApplicationNames', ], 'robotApplicationNames' => [ 'shape' => 'RobotApplicationNames', ], 'dataSourceNames' => [ 'shape' => 'DataSourceNames', ], 'computeType' => [ 'shape' => 'ComputeType', ], ], ], 'SimulationJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SimulationJob', ], ], 'SimulationSoftwareSuite' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'SimulationSoftwareSuiteType', ], 'version' => [ 'shape' => 'SimulationSoftwareSuiteVersionType', ], ], ], 'SimulationSoftwareSuiteType' => [ 'type' => 'string', 'enum' => [ 'Gazebo', 'RosbagPlay', 'SimulationRuntime', ], ], 'SimulationSoftwareSuiteVersionType' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '7|9|11|Kinetic|Melodic|Dashing|Foxy', ], 'SimulationTimeMillis' => [ 'type' => 'long', ], 'SimulationUnit' => [ 'type' => 'integer', 'max' => 15, 'min' => 1, ], 'Source' => [ 'type' => 'structure', 'members' => [ 's3Bucket' => [ 'shape' => 'S3Bucket', ], 's3Key' => [ 'shape' => 'S3Key', ], 'etag' => [ 'shape' => 'S3Etag', ], 'architecture' => [ 'shape' => 'Architecture', ], ], ], 'SourceConfig' => [ 'type' => 'structure', 'members' => [ 's3Bucket' => [ 'shape' => 'S3Bucket', ], 's3Key' => [ 'shape' => 'S3Key', ], 'architecture' => [ 'shape' => 'Architecture', ], ], ], 'SourceConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceConfig', ], ], 'Sources' => [ 'type' => 'list', 'member' => [ 'shape' => 'Source', ], ], 'StartSimulationJobBatchRequest' => [ 'type' => 'structure', 'required' => [ 'createSimulationJobRequests', ], 'members' => [ 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'batchPolicy' => [ 'shape' => 'BatchPolicy', ], 'createSimulationJobRequests' => [ 'shape' => 'CreateSimulationJobRequests', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'StartSimulationJobBatchResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'SimulationJobBatchStatus', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'batchPolicy' => [ 'shape' => 'BatchPolicy', ], 'failureCode' => [ 'shape' => 'SimulationJobBatchErrorCode', ], 'failureReason' => [ 'shape' => 'GenericString', ], 'failedRequests' => [ 'shape' => 'FailedCreateSimulationJobRequests', ], 'pendingRequests' => [ 'shape' => 'CreateSimulationJobRequests', ], 'createdRequests' => [ 'shape' => 'SimulationJobSummaries', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'Subnets' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 16, 'min' => 1, ], 'SyncDeploymentJobRequest' => [ 'type' => 'structure', 'required' => [ 'clientRequestToken', 'fleet', ], 'members' => [ 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'fleet' => [ 'shape' => 'Arn', ], ], ], 'SyncDeploymentJobResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'fleet' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'DeploymentStatus', ], 'deploymentConfig' => [ 'shape' => 'DeploymentConfig', ], 'deploymentApplicationConfigs' => [ 'shape' => 'DeploymentApplicationConfigs', ], 'failureReason' => [ 'shape' => 'GenericString', ], 'failureCode' => [ 'shape' => 'DeploymentJobErrorCode', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9 _.\\-\\/+=:]*', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[a-zA-Z0-9 _.\\-\\/+=:]*', ], 'TemplateLocation' => [ 'type' => 'structure', 'required' => [ 's3Bucket', 's3Key', ], 'members' => [ 's3Bucket' => [ 'shape' => 'S3Bucket', ], 's3Key' => [ 'shape' => 'S3Key', ], ], ], 'TemplateName' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '.*', ], 'TemplateSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateSummary', ], ], 'TemplateSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'name' => [ 'shape' => 'TemplateName', ], 'version' => [ 'shape' => 'GenericString', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Tool' => [ 'type' => 'structure', 'required' => [ 'name', 'command', ], 'members' => [ 'streamUI' => [ 'shape' => 'BoxedBoolean', ], 'name' => [ 'shape' => 'Name', ], 'command' => [ 'shape' => 'UnrestrictedCommand', ], 'streamOutputToCloudWatch' => [ 'shape' => 'BoxedBoolean', ], 'exitBehavior' => [ 'shape' => 'ExitBehavior', ], ], ], 'Tools' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tool', ], 'max' => 10, 'min' => 0, ], 'UnrestrictedCommand' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.*', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRobotApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'application', 'robotSoftwareSuite', ], 'members' => [ 'application' => [ 'shape' => 'Arn', ], 'sources' => [ 'shape' => 'SourceConfigs', ], 'robotSoftwareSuite' => [ 'shape' => 'RobotSoftwareSuite', ], 'currentRevisionId' => [ 'shape' => 'RevisionId', ], 'environment' => [ 'shape' => 'Environment', ], ], ], 'UpdateRobotApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'version' => [ 'shape' => 'Version', ], 'sources' => [ 'shape' => 'Sources', ], 'robotSoftwareSuite' => [ 'shape' => 'RobotSoftwareSuite', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'revisionId' => [ 'shape' => 'RevisionId', ], 'environment' => [ 'shape' => 'Environment', ], ], ], 'UpdateSimulationApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'application', 'simulationSoftwareSuite', 'robotSoftwareSuite', ], 'members' => [ 'application' => [ 'shape' => 'Arn', ], 'sources' => [ 'shape' => 'SourceConfigs', ], 'simulationSoftwareSuite' => [ 'shape' => 'SimulationSoftwareSuite', ], 'robotSoftwareSuite' => [ 'shape' => 'RobotSoftwareSuite', ], 'renderingEngine' => [ 'shape' => 'RenderingEngine', ], 'currentRevisionId' => [ 'shape' => 'RevisionId', ], 'environment' => [ 'shape' => 'Environment', ], ], ], 'UpdateSimulationApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'version' => [ 'shape' => 'Version', ], 'sources' => [ 'shape' => 'Sources', ], 'simulationSoftwareSuite' => [ 'shape' => 'SimulationSoftwareSuite', ], 'robotSoftwareSuite' => [ 'shape' => 'RobotSoftwareSuite', ], 'renderingEngine' => [ 'shape' => 'RenderingEngine', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'revisionId' => [ 'shape' => 'RevisionId', ], 'environment' => [ 'shape' => 'Environment', ], ], ], 'UpdateWorldTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'template', ], 'members' => [ 'template' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'TemplateName', ], 'templateBody' => [ 'shape' => 'Json', ], 'templateLocation' => [ 'shape' => 'TemplateLocation', ], ], ], 'UpdateWorldTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'TemplateName', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], ], ], 'UploadBehavior' => [ 'type' => 'string', 'enum' => [ 'UPLOAD_ON_TERMINATE', 'UPLOAD_ROLLING_AUTO_REMOVE', ], ], 'UploadConfiguration' => [ 'type' => 'structure', 'required' => [ 'name', 'path', 'uploadBehavior', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'path' => [ 'shape' => 'Path', ], 'uploadBehavior' => [ 'shape' => 'UploadBehavior', ], ], ], 'UploadConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'UploadConfiguration', ], 'max' => 10, 'min' => 0, ], 'VPCConfig' => [ 'type' => 'structure', 'required' => [ 'subnets', ], 'members' => [ 'subnets' => [ 'shape' => 'Subnets', ], 'securityGroups' => [ 'shape' => 'SecurityGroups', ], 'assignPublicIp' => [ 'shape' => 'Boolean', ], ], ], 'VPCConfigResponse' => [ 'type' => 'structure', 'members' => [ 'subnets' => [ 'shape' => 'Subnets', ], 'securityGroups' => [ 'shape' => 'SecurityGroups', ], 'vpcId' => [ 'shape' => 'GenericString', ], 'assignPublicIp' => [ 'shape' => 'Boolean', ], ], ], 'Version' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '(\\$LATEST)|[0-9]*', ], 'VersionQualifier' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => 'ALL', ], 'WorldConfig' => [ 'type' => 'structure', 'members' => [ 'world' => [ 'shape' => 'Arn', ], ], ], 'WorldConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorldConfig', ], 'max' => 1, 'min' => 0, ], 'WorldCount' => [ 'type' => 'structure', 'members' => [ 'floorplanCount' => [ 'shape' => 'FloorplanCount', ], 'interiorCountPerFloorplan' => [ 'shape' => 'InteriorCountPerFloorplan', ], ], ], 'WorldExportJobErrorCode' => [ 'type' => 'string', 'enum' => [ 'InternalServiceError', 'LimitExceeded', 'ResourceNotFound', 'RequestThrottled', 'InvalidInput', 'AccessDenied', ], ], 'WorldExportJobStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'Running', 'Completed', 'Failed', 'Canceling', 'Canceled', ], ], 'WorldExportJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorldExportJobSummary', ], 'max' => 100, 'min' => 0, ], 'WorldExportJobSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'WorldExportJobStatus', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'worlds' => [ 'shape' => 'Arns', ], ], ], 'WorldFailure' => [ 'type' => 'structure', 'members' => [ 'failureCode' => [ 'shape' => 'WorldGenerationJobErrorCode', ], 'sampleFailureReason' => [ 'shape' => 'GenericString', ], 'failureCount' => [ 'shape' => 'Integer', ], ], ], 'WorldFailures' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorldFailure', ], 'max' => 100, 'min' => 0, ], 'WorldGenerationJobErrorCode' => [ 'type' => 'string', 'enum' => [ 'InternalServiceError', 'LimitExceeded', 'ResourceNotFound', 'RequestThrottled', 'InvalidInput', 'AllWorldGenerationFailed', ], ], 'WorldGenerationJobStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'Running', 'Completed', 'Failed', 'PartialFailed', 'Canceling', 'Canceled', ], ], 'WorldGenerationJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorldGenerationJobSummary', ], 'max' => 100, 'min' => 0, ], 'WorldGenerationJobSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'template' => [ 'shape' => 'Arn', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'status' => [ 'shape' => 'WorldGenerationJobStatus', ], 'worldCount' => [ 'shape' => 'WorldCount', ], 'succeededWorldCount' => [ 'shape' => 'Integer', ], 'failedWorldCount' => [ 'shape' => 'Integer', ], ], ], 'WorldSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorldSummary', ], ], 'WorldSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'generationJob' => [ 'shape' => 'Arn', ], 'template' => [ 'shape' => 'Arn', ], ], ], 'errorMessage' => [ 'type' => 'string', ], ],];
