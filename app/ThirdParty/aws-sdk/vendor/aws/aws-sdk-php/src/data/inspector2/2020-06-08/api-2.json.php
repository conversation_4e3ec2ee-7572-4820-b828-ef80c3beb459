<?php
// This file was auto-generated from sdk-root/src/data/inspector2/2020-06-08/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-06-08', 'endpointPrefix' => 'inspector2', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'Inspector2', 'serviceFullName' => 'Inspector2', 'serviceId' => 'Inspector2', 'signatureVersion' => 'v4', 'signingName' => 'inspector2', 'uid' => 'inspector2-2020-06-08', ], 'operations' => [ 'AssociateMember' => [ 'name' => 'AssociateMember', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/associate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateMemberRequest', ], 'output' => [ 'shape' => 'AssociateMemberResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchGetAccountStatus' => [ 'name' => 'BatchGetAccountStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/status/batch/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetAccountStatusRequest', ], 'output' => [ 'shape' => 'BatchGetAccountStatusResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchGetFreeTrialInfo' => [ 'name' => 'BatchGetFreeTrialInfo', 'http' => [ 'method' => 'POST', 'requestUri' => '/freetrialinfo/batchget', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetFreeTrialInfoRequest', ], 'output' => [ 'shape' => 'BatchGetFreeTrialInfoResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CancelFindingsReport' => [ 'name' => 'CancelFindingsReport', 'http' => [ 'method' => 'POST', 'requestUri' => '/reporting/cancel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelFindingsReportRequest', ], 'output' => [ 'shape' => 'CancelFindingsReportResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateFilter' => [ 'name' => 'CreateFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/filters/create', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateFilterRequest', ], 'output' => [ 'shape' => 'CreateFilterResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateFindingsReport' => [ 'name' => 'CreateFindingsReport', 'http' => [ 'method' => 'POST', 'requestUri' => '/reporting/create', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateFindingsReportRequest', ], 'output' => [ 'shape' => 'CreateFindingsReportResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteFilter' => [ 'name' => 'DeleteFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/filters/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteFilterRequest', ], 'output' => [ 'shape' => 'DeleteFilterResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeOrganizationConfiguration' => [ 'name' => 'DescribeOrganizationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/organizationconfiguration/describe', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeOrganizationConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeOrganizationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'Disable' => [ 'name' => 'Disable', 'http' => [ 'method' => 'POST', 'requestUri' => '/disable', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisableRequest', ], 'output' => [ 'shape' => 'DisableResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisableDelegatedAdminAccount' => [ 'name' => 'DisableDelegatedAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/delegatedadminaccounts/disable', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisableDelegatedAdminAccountRequest', ], 'output' => [ 'shape' => 'DisableDelegatedAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisassociateMember' => [ 'name' => 'DisassociateMember', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/disassociate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateMemberRequest', ], 'output' => [ 'shape' => 'DisassociateMemberResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'Enable' => [ 'name' => 'Enable', 'http' => [ 'method' => 'POST', 'requestUri' => '/enable', 'responseCode' => 200, ], 'input' => [ 'shape' => 'EnableRequest', ], 'output' => [ 'shape' => 'EnableResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'EnableDelegatedAdminAccount' => [ 'name' => 'EnableDelegatedAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/delegatedadminaccounts/enable', 'responseCode' => 200, ], 'input' => [ 'shape' => 'EnableDelegatedAdminAccountRequest', ], 'output' => [ 'shape' => 'EnableDelegatedAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetDelegatedAdminAccount' => [ 'name' => 'GetDelegatedAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/delegatedadminaccounts/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDelegatedAdminAccountRequest', ], 'output' => [ 'shape' => 'GetDelegatedAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetFindingsReportStatus' => [ 'name' => 'GetFindingsReportStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/reporting/status/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFindingsReportStatusRequest', ], 'output' => [ 'shape' => 'GetFindingsReportStatusResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetMember' => [ 'name' => 'GetMember', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMemberRequest', ], 'output' => [ 'shape' => 'GetMemberResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAccountPermissions' => [ 'name' => 'ListAccountPermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/accountpermissions/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAccountPermissionsRequest', ], 'output' => [ 'shape' => 'ListAccountPermissionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCoverage' => [ 'name' => 'ListCoverage', 'http' => [ 'method' => 'POST', 'requestUri' => '/coverage/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCoverageRequest', ], 'output' => [ 'shape' => 'ListCoverageResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCoverageStatistics' => [ 'name' => 'ListCoverageStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/coverage/statistics/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCoverageStatisticsRequest', ], 'output' => [ 'shape' => 'ListCoverageStatisticsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDelegatedAdminAccounts' => [ 'name' => 'ListDelegatedAdminAccounts', 'http' => [ 'method' => 'POST', 'requestUri' => '/delegatedadminaccounts/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDelegatedAdminAccountsRequest', ], 'output' => [ 'shape' => 'ListDelegatedAdminAccountsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListFilters' => [ 'name' => 'ListFilters', 'http' => [ 'method' => 'POST', 'requestUri' => '/filters/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFiltersRequest', ], 'output' => [ 'shape' => 'ListFiltersResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListFindingAggregations' => [ 'name' => 'ListFindingAggregations', 'http' => [ 'method' => 'POST', 'requestUri' => '/findings/aggregation/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFindingAggregationsRequest', ], 'output' => [ 'shape' => 'ListFindingAggregationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListFindings' => [ 'name' => 'ListFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/findings/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFindingsRequest', ], 'output' => [ 'shape' => 'ListFindingsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListMembers' => [ 'name' => 'ListMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMembersRequest', ], 'output' => [ 'shape' => 'ListMembersResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListUsageTotals' => [ 'name' => 'ListUsageTotals', 'http' => [ 'method' => 'POST', 'requestUri' => '/usage/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListUsageTotalsRequest', ], 'output' => [ 'shape' => 'ListUsageTotalsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateFilter' => [ 'name' => 'UpdateFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/filters/update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFilterRequest', ], 'output' => [ 'shape' => 'UpdateFilterResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateOrganizationConfiguration' => [ 'name' => 'UpdateOrganizationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/organizationconfiguration/update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateOrganizationConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateOrganizationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Account' => [ 'type' => 'structure', 'required' => [ 'accountId', 'resourceStatus', 'status', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'resourceStatus' => [ 'shape' => 'ResourceStatus', ], 'status' => [ 'shape' => 'Status', ], ], ], 'AccountAggregation' => [ 'type' => 'structure', 'members' => [ 'findingType' => [ 'shape' => 'AggregationFindingType', ], 'resourceType' => [ 'shape' => 'AggregationResourceType', ], 'sortBy' => [ 'shape' => 'AccountSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'AccountAggregationResponse' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d{12}$', ], 'AccountIdSet' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'max' => 100, 'min' => 0, ], 'AccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Account', ], ], 'AccountSortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', ], ], 'AccountState' => [ 'type' => 'structure', 'required' => [ 'accountId', 'resourceState', 'state', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'resourceState' => [ 'shape' => 'ResourceState', ], 'state' => [ 'shape' => 'State', ], ], ], 'AccountStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountState', ], 'max' => 100, 'min' => 0, ], 'AggCounts' => [ 'type' => 'long', ], 'AggregationFindingType' => [ 'type' => 'string', 'enum' => [ 'NETWORK_REACHABILITY', 'PACKAGE_VULNERABILITY', ], ], 'AggregationRequest' => [ 'type' => 'structure', 'members' => [ 'accountAggregation' => [ 'shape' => 'AccountAggregation', ], 'amiAggregation' => [ 'shape' => 'AmiAggregation', ], 'awsEcrContainerAggregation' => [ 'shape' => 'AwsEcrContainerAggregation', ], 'ec2InstanceAggregation' => [ 'shape' => 'Ec2InstanceAggregation', ], 'findingTypeAggregation' => [ 'shape' => 'FindingTypeAggregation', ], 'imageLayerAggregation' => [ 'shape' => 'ImageLayerAggregation', ], 'packageAggregation' => [ 'shape' => 'PackageAggregation', ], 'repositoryAggregation' => [ 'shape' => 'RepositoryAggregation', ], 'titleAggregation' => [ 'shape' => 'TitleAggregation', ], ], 'union' => true, ], 'AggregationResourceType' => [ 'type' => 'string', 'enum' => [ 'AWS_EC2_INSTANCE', 'AWS_ECR_CONTAINER_IMAGE', ], ], 'AggregationResponse' => [ 'type' => 'structure', 'members' => [ 'accountAggregation' => [ 'shape' => 'AccountAggregationResponse', ], 'amiAggregation' => [ 'shape' => 'AmiAggregationResponse', ], 'awsEcrContainerAggregation' => [ 'shape' => 'AwsEcrContainerAggregationResponse', ], 'ec2InstanceAggregation' => [ 'shape' => 'Ec2InstanceAggregationResponse', ], 'findingTypeAggregation' => [ 'shape' => 'FindingTypeAggregationResponse', ], 'imageLayerAggregation' => [ 'shape' => 'ImageLayerAggregationResponse', ], 'packageAggregation' => [ 'shape' => 'PackageAggregationResponse', ], 'repositoryAggregation' => [ 'shape' => 'RepositoryAggregationResponse', ], 'titleAggregation' => [ 'shape' => 'TitleAggregationResponse', ], ], 'union' => true, ], 'AggregationResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregationResponse', ], ], 'AggregationType' => [ 'type' => 'string', 'enum' => [ 'FINDING_TYPE', 'PACKAGE', 'TITLE', 'REPOSITORY', 'AMI', 'AWS_EC2_INSTANCE', 'AWS_ECR_CONTAINER', 'IMAGE_LAYER', 'ACCOUNT', ], ], 'AmiAggregation' => [ 'type' => 'structure', 'members' => [ 'amis' => [ 'shape' => 'StringFilterList', ], 'sortBy' => [ 'shape' => 'AmiSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'AmiAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'ami', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'affectedInstances' => [ 'shape' => 'Long', ], 'ami' => [ 'shape' => 'AmiId', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'AmiId' => [ 'type' => 'string', 'pattern' => '^ami-([a-z0-9]{8}|[a-z0-9]{17}|\\*)$', ], 'AmiSortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', 'AFFECTED_INSTANCES', ], ], 'Arn' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'AssociateMemberRequest' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'AssociateMemberResponse' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'AutoEnable' => [ 'type' => 'structure', 'required' => [ 'ec2', 'ecr', ], 'members' => [ 'ec2' => [ 'shape' => 'Boolean', ], 'ecr' => [ 'shape' => 'Boolean', ], ], ], 'AwsEc2InstanceDetails' => [ 'type' => 'structure', 'members' => [ 'iamInstanceProfileArn' => [ 'shape' => 'NonEmptyString', ], 'imageId' => [ 'shape' => 'NonEmptyString', ], 'ipV4Addresses' => [ 'shape' => 'IpV4AddressList', ], 'ipV6Addresses' => [ 'shape' => 'IpV6AddressList', ], 'keyName' => [ 'shape' => 'NonEmptyString', ], 'launchedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'platform' => [ 'shape' => 'Platform', ], 'subnetId' => [ 'shape' => 'NonEmptyString', ], 'type' => [ 'shape' => 'NonEmptyString', ], 'vpcId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcrContainerAggregation' => [ 'type' => 'structure', 'members' => [ 'architectures' => [ 'shape' => 'StringFilterList', ], 'imageShas' => [ 'shape' => 'StringFilterList', ], 'imageTags' => [ 'shape' => 'StringFilterList', ], 'repositories' => [ 'shape' => 'StringFilterList', ], 'resourceIds' => [ 'shape' => 'StringFilterList', ], 'sortBy' => [ 'shape' => 'AwsEcrContainerSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'AwsEcrContainerAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'resourceId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'architecture' => [ 'shape' => 'String', ], 'imageSha' => [ 'shape' => 'String', ], 'imageTags' => [ 'shape' => 'StringList', ], 'repository' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'NonEmptyString', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'AwsEcrContainerImageDetails' => [ 'type' => 'structure', 'required' => [ 'imageHash', 'registry', 'repositoryName', ], 'members' => [ 'architecture' => [ 'shape' => 'NonEmptyString', ], 'author' => [ 'shape' => 'String', ], 'imageHash' => [ 'shape' => 'ImageHash', ], 'imageTags' => [ 'shape' => 'ImageTagList', ], 'platform' => [ 'shape' => 'Platform', ], 'pushedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'registry' => [ 'shape' => 'NonEmptyString', ], 'repositoryName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcrContainerSortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', ], ], 'BadRequestException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'BatchGetAccountStatusRequest' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'AccountIdSet', ], ], ], 'BatchGetAccountStatusResponse' => [ 'type' => 'structure', 'required' => [ 'accounts', ], 'members' => [ 'accounts' => [ 'shape' => 'AccountStateList', ], 'failedAccounts' => [ 'shape' => 'FailedAccountList', ], ], ], 'BatchGetFreeTrialInfoRequest' => [ 'type' => 'structure', 'required' => [ 'accountIds', ], 'members' => [ 'accountIds' => [ 'shape' => 'BatchGetFreeTrialInfoRequestAccountIdsList', ], ], ], 'BatchGetFreeTrialInfoRequestAccountIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MeteringAccountId', ], 'max' => 100, 'min' => 1, ], 'BatchGetFreeTrialInfoResponse' => [ 'type' => 'structure', 'required' => [ 'accounts', 'failedAccounts', ], 'members' => [ 'accounts' => [ 'shape' => 'FreeTrialAccountInfoList', ], 'failedAccounts' => [ 'shape' => 'FreeTrialInfoErrorList', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CancelFindingsReportRequest' => [ 'type' => 'structure', 'required' => [ 'reportId', ], 'members' => [ 'reportId' => [ 'shape' => 'ReportId', ], ], ], 'CancelFindingsReportResponse' => [ 'type' => 'structure', 'required' => [ 'reportId', ], 'members' => [ 'reportId' => [ 'shape' => 'ReportId', ], ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'Component' => [ 'type' => 'string', ], 'ComponentType' => [ 'type' => 'string', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'Counts' => [ 'type' => 'structure', 'members' => [ 'count' => [ 'shape' => 'AggCounts', ], 'groupKey' => [ 'shape' => 'GroupKey', ], ], ], 'CountsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Counts', ], 'max' => 5, 'min' => 1, ], 'CoverageFilterCriteria' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'CoverageStringFilterList', ], 'ec2InstanceTags' => [ 'shape' => 'CoverageMapFilterList', ], 'ecrImageTags' => [ 'shape' => 'CoverageStringFilterList', ], 'ecrRepositoryName' => [ 'shape' => 'CoverageStringFilterList', ], 'resourceId' => [ 'shape' => 'CoverageStringFilterList', ], 'resourceType' => [ 'shape' => 'CoverageStringFilterList', ], 'scanStatusCode' => [ 'shape' => 'CoverageStringFilterList', ], 'scanStatusReason' => [ 'shape' => 'CoverageStringFilterList', ], 'scanType' => [ 'shape' => 'CoverageStringFilterList', ], ], ], 'CoverageMapComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'CoverageMapFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'key', ], 'members' => [ 'comparison' => [ 'shape' => 'CoverageMapComparison', ], 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], ], 'CoverageMapFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoverageMapFilter', ], 'max' => 10, 'min' => 1, ], 'CoverageResourceType' => [ 'type' => 'string', 'enum' => [ 'AWS_EC2_INSTANCE', 'AWS_ECR_CONTAINER_IMAGE', 'AWS_ECR_REPOSITORY', ], ], 'CoverageStringComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'NOT_EQUALS', ], ], 'CoverageStringFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'value', ], 'members' => [ 'comparison' => [ 'shape' => 'CoverageStringComparison', ], 'value' => [ 'shape' => 'CoverageStringInput', ], ], ], 'CoverageStringFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoverageStringFilter', ], 'max' => 10, 'min' => 1, ], 'CoverageStringInput' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'CoveredResource' => [ 'type' => 'structure', 'required' => [ 'accountId', 'resourceId', 'resourceType', 'scanType', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceMetadata' => [ 'shape' => 'ResourceScanMetadata', ], 'resourceType' => [ 'shape' => 'CoverageResourceType', ], 'scanStatus' => [ 'shape' => 'ScanStatus', ], 'scanType' => [ 'shape' => 'ScanType', ], ], ], 'CoveredResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoveredResource', ], ], 'CreateFilterRequest' => [ 'type' => 'structure', 'required' => [ 'action', 'filterCriteria', 'name', ], 'members' => [ 'action' => [ 'shape' => 'FilterAction', ], 'description' => [ 'shape' => 'FilterDescription', ], 'filterCriteria' => [ 'shape' => 'FilterCriteria', ], 'name' => [ 'shape' => 'FilterName', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateFilterResponse' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'FilterArn', ], ], ], 'CreateFindingsReportRequest' => [ 'type' => 'structure', 'required' => [ 'reportFormat', 's3Destination', ], 'members' => [ 'filterCriteria' => [ 'shape' => 'FilterCriteria', ], 'reportFormat' => [ 'shape' => 'ReportFormat', ], 's3Destination' => [ 'shape' => 'Destination', ], ], ], 'CreateFindingsReportResponse' => [ 'type' => 'structure', 'members' => [ 'reportId' => [ 'shape' => 'ReportId', ], ], ], 'Currency' => [ 'type' => 'string', 'enum' => [ 'USD', ], ], 'CvssScore' => [ 'type' => 'structure', 'required' => [ 'baseScore', 'scoringVector', 'source', 'version', ], 'members' => [ 'baseScore' => [ 'shape' => 'Double', ], 'scoringVector' => [ 'shape' => 'NonEmptyString', ], 'source' => [ 'shape' => 'NonEmptyString', ], 'version' => [ 'shape' => 'NonEmptyString', ], ], ], 'CvssScoreAdjustment' => [ 'type' => 'structure', 'required' => [ 'metric', 'reason', ], 'members' => [ 'metric' => [ 'shape' => 'NonEmptyString', ], 'reason' => [ 'shape' => 'NonEmptyString', ], ], ], 'CvssScoreAdjustmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CvssScoreAdjustment', ], ], 'CvssScoreDetails' => [ 'type' => 'structure', 'required' => [ 'score', 'scoreSource', 'scoringVector', 'version', ], 'members' => [ 'adjustments' => [ 'shape' => 'CvssScoreAdjustmentList', ], 'cvssSource' => [ 'shape' => 'NonEmptyString', ], 'score' => [ 'shape' => 'Double', ], 'scoreSource' => [ 'shape' => 'NonEmptyString', ], 'scoringVector' => [ 'shape' => 'NonEmptyString', ], 'version' => [ 'shape' => 'NonEmptyString', ], ], ], 'CvssScoreList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CvssScore', ], ], 'DateFilter' => [ 'type' => 'structure', 'members' => [ 'endInclusive' => [ 'shape' => 'Timestamp', ], 'startInclusive' => [ 'shape' => 'Timestamp', ], ], ], 'DateFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DateFilter', ], 'max' => 10, 'min' => 1, ], 'DateTimeTimestamp' => [ 'type' => 'timestamp', ], 'DelegatedAdmin' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'relationshipStatus' => [ 'shape' => 'RelationshipStatus', ], ], ], 'DelegatedAdminAccount' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'status' => [ 'shape' => 'DelegatedAdminStatus', ], ], ], 'DelegatedAdminAccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DelegatedAdminAccount', ], 'max' => 5, 'min' => 0, ], 'DelegatedAdminStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLE_IN_PROGRESS', ], ], 'DeleteFilterRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'FilterArn', ], ], ], 'DeleteFilterResponse' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'FilterArn', ], ], ], 'DescribeOrganizationConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeOrganizationConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'autoEnable' => [ 'shape' => 'AutoEnable', ], 'maxAccountLimitReached' => [ 'shape' => 'Boolean', ], ], ], 'Destination' => [ 'type' => 'structure', 'required' => [ 'bucketName', 'kmsKeyArn', ], 'members' => [ 'bucketName' => [ 'shape' => 'String', ], 'keyPrefix' => [ 'shape' => 'String', ], 'kmsKeyArn' => [ 'shape' => 'String', ], ], ], 'DisableDelegatedAdminAccountRequest' => [ 'type' => 'structure', 'required' => [ 'delegatedAdminAccountId', ], 'members' => [ 'delegatedAdminAccountId' => [ 'shape' => 'AccountId', ], ], ], 'DisableDelegatedAdminAccountResponse' => [ 'type' => 'structure', 'required' => [ 'delegatedAdminAccountId', ], 'members' => [ 'delegatedAdminAccountId' => [ 'shape' => 'AccountId', ], ], ], 'DisableRequest' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'AccountIdSet', ], 'resourceTypes' => [ 'shape' => 'DisableResourceTypeList', ], ], ], 'DisableResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceScanType', ], 'max' => 2, 'min' => 0, ], 'DisableResponse' => [ 'type' => 'structure', 'required' => [ 'accounts', ], 'members' => [ 'accounts' => [ 'shape' => 'AccountList', ], 'failedAccounts' => [ 'shape' => 'FailedAccountList', ], ], ], 'DisassociateMemberRequest' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'DisassociateMemberResponse' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'Ec2InstanceAggregation' => [ 'type' => 'structure', 'members' => [ 'amis' => [ 'shape' => 'StringFilterList', ], 'instanceIds' => [ 'shape' => 'StringFilterList', ], 'instanceTags' => [ 'shape' => 'MapFilterList', ], 'operatingSystems' => [ 'shape' => 'StringFilterList', ], 'sortBy' => [ 'shape' => 'Ec2InstanceSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'Ec2InstanceAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'instanceId', ], 'members' => [ 'accountId' => [ 'shape' => 'String', ], 'ami' => [ 'shape' => 'AmiId', ], 'instanceId' => [ 'shape' => 'NonEmptyString', ], 'instanceTags' => [ 'shape' => 'TagMap', ], 'networkFindings' => [ 'shape' => 'Long', ], 'operatingSystem' => [ 'shape' => 'String', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'Ec2InstanceSortBy' => [ 'type' => 'string', 'enum' => [ 'NETWORK_FINDINGS', 'CRITICAL', 'HIGH', 'ALL', ], ], 'Ec2Metadata' => [ 'type' => 'structure', 'members' => [ 'amiId' => [ 'shape' => 'AmiId', ], 'platform' => [ 'shape' => 'Ec2Platform', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'Ec2Platform' => [ 'type' => 'string', 'enum' => [ 'WINDOWS', 'LINUX', 'UNKNOWN', ], ], 'EcrContainerImageMetadata' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagList', ], ], ], 'EcrRepositoryMetadata' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'scanFrequency' => [ 'shape' => 'EcrScanFrequency', ], ], ], 'EcrScanFrequency' => [ 'type' => 'string', 'enum' => [ 'MANUAL', 'SCAN_ON_PUSH', 'CONTINUOUS_SCAN', ], ], 'EnableDelegatedAdminAccountRequest' => [ 'type' => 'structure', 'required' => [ 'delegatedAdminAccountId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'delegatedAdminAccountId' => [ 'shape' => 'AccountId', ], ], ], 'EnableDelegatedAdminAccountResponse' => [ 'type' => 'structure', 'required' => [ 'delegatedAdminAccountId', ], 'members' => [ 'delegatedAdminAccountId' => [ 'shape' => 'AccountId', ], ], ], 'EnableRequest' => [ 'type' => 'structure', 'required' => [ 'resourceTypes', ], 'members' => [ 'accountIds' => [ 'shape' => 'AccountIdSet', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'resourceTypes' => [ 'shape' => 'EnableResourceTypeList', ], ], ], 'EnableResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceScanType', ], 'max' => 2, 'min' => 1, ], 'EnableResponse' => [ 'type' => 'structure', 'required' => [ 'accounts', ], 'members' => [ 'accounts' => [ 'shape' => 'AccountList', ], 'failedAccounts' => [ 'shape' => 'FailedAccountList', ], ], ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'ALREADY_ENABLED', 'ENABLE_IN_PROGRESS', 'DISABLE_IN_PROGRESS', 'SUSPEND_IN_PROGRESS', 'RESOURCE_NOT_FOUND', 'ACCESS_DENIED', 'INTERNAL_ERROR', 'SSM_UNAVAILABLE', 'SSM_THROTTLED', 'EVENTBRIDGE_UNAVAILABLE', 'EVENTBRIDGE_THROTTLED', 'RESOURCE_SCAN_NOT_DISABLED', 'DISASSOCIATE_ALL_MEMBERS', ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'ExternalReportStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'IN_PROGRESS', 'CANCELLED', 'FAILED', ], ], 'FailedAccount' => [ 'type' => 'structure', 'required' => [ 'accountId', 'errorCode', 'errorMessage', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'errorCode' => [ 'shape' => 'ErrorCode', ], 'errorMessage' => [ 'shape' => 'NonEmptyString', ], 'resourceStatus' => [ 'shape' => 'ResourceStatus', ], 'status' => [ 'shape' => 'Status', ], ], ], 'FailedAccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedAccount', ], 'max' => 100, 'min' => 0, ], 'FilePath' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'action', 'arn', 'createdAt', 'criteria', 'name', 'ownerId', 'updatedAt', ], 'members' => [ 'action' => [ 'shape' => 'FilterAction', ], 'arn' => [ 'shape' => 'FilterArn', ], 'createdAt' => [ 'shape' => 'DateTimeTimestamp', ], 'criteria' => [ 'shape' => 'FilterCriteria', ], 'description' => [ 'shape' => 'FilterDescription', ], 'name' => [ 'shape' => 'FilterName', ], 'ownerId' => [ 'shape' => 'OwnerId', ], 'reason' => [ 'shape' => 'FilterReason', ], 'tags' => [ 'shape' => 'TagMap', ], 'updatedAt' => [ 'shape' => 'DateTimeTimestamp', ], ], ], 'FilterAction' => [ 'type' => 'string', 'enum' => [ 'NONE', 'SUPPRESS', ], ], 'FilterArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'FilterArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterArn', ], ], 'FilterCriteria' => [ 'type' => 'structure', 'members' => [ 'awsAccountId' => [ 'shape' => 'StringFilterList', ], 'componentId' => [ 'shape' => 'StringFilterList', ], 'componentType' => [ 'shape' => 'StringFilterList', ], 'ec2InstanceImageId' => [ 'shape' => 'StringFilterList', ], 'ec2InstanceSubnetId' => [ 'shape' => 'StringFilterList', ], 'ec2InstanceVpcId' => [ 'shape' => 'StringFilterList', ], 'ecrImageArchitecture' => [ 'shape' => 'StringFilterList', ], 'ecrImageHash' => [ 'shape' => 'StringFilterList', ], 'ecrImagePushedAt' => [ 'shape' => 'DateFilterList', ], 'ecrImageRegistry' => [ 'shape' => 'StringFilterList', ], 'ecrImageRepositoryName' => [ 'shape' => 'StringFilterList', ], 'ecrImageTags' => [ 'shape' => 'StringFilterList', ], 'findingArn' => [ 'shape' => 'StringFilterList', ], 'findingStatus' => [ 'shape' => 'StringFilterList', ], 'findingType' => [ 'shape' => 'StringFilterList', ], 'firstObservedAt' => [ 'shape' => 'DateFilterList', ], 'inspectorScore' => [ 'shape' => 'NumberFilterList', ], 'lastObservedAt' => [ 'shape' => 'DateFilterList', ], 'networkProtocol' => [ 'shape' => 'StringFilterList', ], 'portRange' => [ 'shape' => 'PortRangeFilterList', ], 'relatedVulnerabilities' => [ 'shape' => 'StringFilterList', ], 'resourceId' => [ 'shape' => 'StringFilterList', ], 'resourceTags' => [ 'shape' => 'MapFilterList', ], 'resourceType' => [ 'shape' => 'StringFilterList', ], 'severity' => [ 'shape' => 'StringFilterList', ], 'title' => [ 'shape' => 'StringFilterList', ], 'updatedAt' => [ 'shape' => 'DateFilterList', ], 'vendorSeverity' => [ 'shape' => 'StringFilterList', ], 'vulnerabilityId' => [ 'shape' => 'StringFilterList', ], 'vulnerabilitySource' => [ 'shape' => 'StringFilterList', ], 'vulnerablePackages' => [ 'shape' => 'PackageFilterList', ], ], ], 'FilterDescription' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'FilterName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'FilterReason' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'Finding' => [ 'type' => 'structure', 'required' => [ 'awsAccountId', 'description', 'findingArn', 'firstObservedAt', 'lastObservedAt', 'remediation', 'resources', 'severity', 'status', 'type', ], 'members' => [ 'awsAccountId' => [ 'shape' => 'AccountId', ], 'description' => [ 'shape' => 'FindingDescription', ], 'findingArn' => [ 'shape' => 'FindingArn', ], 'firstObservedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'inspectorScore' => [ 'shape' => 'Double', ], 'inspectorScoreDetails' => [ 'shape' => 'InspectorScoreDetails', ], 'lastObservedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'networkReachabilityDetails' => [ 'shape' => 'NetworkReachabilityDetails', ], 'packageVulnerabilityDetails' => [ 'shape' => 'PackageVulnerabilityDetails', ], 'remediation' => [ 'shape' => 'Remediation', ], 'resources' => [ 'shape' => 'ResourceList', ], 'severity' => [ 'shape' => 'Severity', ], 'status' => [ 'shape' => 'FindingStatus', ], 'title' => [ 'shape' => 'FindingTitle', ], 'type' => [ 'shape' => 'FindingType', ], 'updatedAt' => [ 'shape' => 'DateTimeTimestamp', ], ], ], 'FindingArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'FindingDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'FindingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Finding', ], 'max' => 25, 'min' => 0, ], 'FindingStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'SUPPRESSED', 'CLOSED', ], ], 'FindingTitle' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'FindingType' => [ 'type' => 'string', 'enum' => [ 'NETWORK_REACHABILITY', 'PACKAGE_VULNERABILITY', ], ], 'FindingTypeAggregation' => [ 'type' => 'structure', 'members' => [ 'findingType' => [ 'shape' => 'AggregationFindingType', ], 'resourceType' => [ 'shape' => 'AggregationResourceType', ], 'sortBy' => [ 'shape' => 'FindingTypeSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'FindingTypeAggregationResponse' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'FindingTypeSortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', ], ], 'FreeTrialAccountInfo' => [ 'type' => 'structure', 'required' => [ 'accountId', 'freeTrialInfo', ], 'members' => [ 'accountId' => [ 'shape' => 'MeteringAccountId', ], 'freeTrialInfo' => [ 'shape' => 'FreeTrialInfoList', ], ], ], 'FreeTrialAccountInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FreeTrialAccountInfo', ], ], 'FreeTrialInfo' => [ 'type' => 'structure', 'required' => [ 'end', 'start', 'status', 'type', ], 'members' => [ 'end' => [ 'shape' => 'Timestamp', ], 'start' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'FreeTrialStatus', ], 'type' => [ 'shape' => 'FreeTrialType', ], ], ], 'FreeTrialInfoError' => [ 'type' => 'structure', 'required' => [ 'accountId', 'code', 'message', ], 'members' => [ 'accountId' => [ 'shape' => 'MeteringAccountId', ], 'code' => [ 'shape' => 'FreeTrialInfoErrorCode', ], 'message' => [ 'shape' => 'String', ], ], ], 'FreeTrialInfoErrorCode' => [ 'type' => 'string', 'enum' => [ 'ACCESS_DENIED', 'INTERNAL_ERROR', ], ], 'FreeTrialInfoErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FreeTrialInfoError', ], ], 'FreeTrialInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FreeTrialInfo', ], ], 'FreeTrialStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'FreeTrialType' => [ 'type' => 'string', 'enum' => [ 'EC2', 'ECR', ], ], 'GetDelegatedAdminAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetDelegatedAdminAccountResponse' => [ 'type' => 'structure', 'members' => [ 'delegatedAdmin' => [ 'shape' => 'DelegatedAdmin', ], ], ], 'GetFindingsReportStatusRequest' => [ 'type' => 'structure', 'members' => [ 'reportId' => [ 'shape' => 'ReportId', ], ], ], 'GetFindingsReportStatusResponse' => [ 'type' => 'structure', 'members' => [ 'destination' => [ 'shape' => 'Destination', ], 'errorCode' => [ 'shape' => 'ReportingErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'filterCriteria' => [ 'shape' => 'FilterCriteria', ], 'reportId' => [ 'shape' => 'ReportId', ], 'status' => [ 'shape' => 'ExternalReportStatus', ], ], ], 'GetMemberRequest' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'GetMemberResponse' => [ 'type' => 'structure', 'members' => [ 'member' => [ 'shape' => 'Member', ], ], ], 'GroupKey' => [ 'type' => 'string', 'enum' => [ 'SCAN_STATUS_CODE', 'SCAN_STATUS_REASON', 'ACCOUNT_ID', 'RESOURCE_TYPE', 'ECR_REPOSITORY_NAME', ], ], 'ImageHash' => [ 'type' => 'string', 'max' => 71, 'min' => 71, 'pattern' => '^sha256:[a-z0-9]{64}$', ], 'ImageLayerAggregation' => [ 'type' => 'structure', 'members' => [ 'layerHashes' => [ 'shape' => 'StringFilterList', ], 'repositories' => [ 'shape' => 'StringFilterList', ], 'resourceIds' => [ 'shape' => 'StringFilterList', ], 'sortBy' => [ 'shape' => 'ImageLayerSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ImageLayerAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'accountId', 'layerHash', 'repository', 'resourceId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'layerHash' => [ 'shape' => 'NonEmptyString', ], 'repository' => [ 'shape' => 'NonEmptyString', ], 'resourceId' => [ 'shape' => 'NonEmptyString', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'ImageLayerSortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', ], ], 'ImageTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'InspectorScoreDetails' => [ 'type' => 'structure', 'members' => [ 'adjustedCvss' => [ 'shape' => 'CvssScoreDetails', ], ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'IpV4Address' => [ 'type' => 'string', 'max' => 15, 'min' => 7, 'pattern' => '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$', ], 'IpV4AddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpV4Address', ], ], 'IpV6Address' => [ 'type' => 'string', 'max' => 47, 'min' => 1, ], 'IpV6AddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpV6Address', ], ], 'ListAccountPermissionsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1024, 'min' => 1, ], 'ListAccountPermissionsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListAccountPermissionsMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'service' => [ 'shape' => 'Service', ], ], ], 'ListAccountPermissionsResponse' => [ 'type' => 'structure', 'required' => [ 'permissions', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'permissions' => [ 'shape' => 'Permissions', ], ], ], 'ListCoverageMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 200, 'min' => 1, ], 'ListCoverageRequest' => [ 'type' => 'structure', 'members' => [ 'filterCriteria' => [ 'shape' => 'CoverageFilterCriteria', ], 'maxResults' => [ 'shape' => 'ListCoverageMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCoverageResponse' => [ 'type' => 'structure', 'members' => [ 'coveredResources' => [ 'shape' => 'CoveredResources', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCoverageStatisticsRequest' => [ 'type' => 'structure', 'members' => [ 'filterCriteria' => [ 'shape' => 'CoverageFilterCriteria', ], 'groupBy' => [ 'shape' => 'GroupKey', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCoverageStatisticsResponse' => [ 'type' => 'structure', 'required' => [ 'totalCounts', ], 'members' => [ 'countsByGroup' => [ 'shape' => 'CountsList', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'totalCounts' => [ 'shape' => 'Long', ], ], ], 'ListDelegatedAdminAccountsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListDelegatedAdminMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDelegatedAdminAccountsResponse' => [ 'type' => 'structure', 'members' => [ 'delegatedAdminAccounts' => [ 'shape' => 'DelegatedAdminAccountList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDelegatedAdminMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 5, 'min' => 1, ], 'ListFilterMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListFiltersRequest' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'FilterAction', ], 'arns' => [ 'shape' => 'FilterArnList', ], 'maxResults' => [ 'shape' => 'ListFilterMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFiltersResponse' => [ 'type' => 'structure', 'required' => [ 'filters', ], 'members' => [ 'filters' => [ 'shape' => 'FilterList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFindingAggregationsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListFindingAggregationsRequest' => [ 'type' => 'structure', 'required' => [ 'aggregationType', ], 'members' => [ 'accountIds' => [ 'shape' => 'StringFilterList', ], 'aggregationRequest' => [ 'shape' => 'AggregationRequest', ], 'aggregationType' => [ 'shape' => 'AggregationType', ], 'maxResults' => [ 'shape' => 'ListFindingAggregationsMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFindingAggregationsResponse' => [ 'type' => 'structure', 'required' => [ 'aggregationType', ], 'members' => [ 'aggregationType' => [ 'shape' => 'AggregationType', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'responses' => [ 'shape' => 'AggregationResponseList', ], ], ], 'ListFindingsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListFindingsRequest' => [ 'type' => 'structure', 'members' => [ 'filterCriteria' => [ 'shape' => 'FilterCriteria', ], 'maxResults' => [ 'shape' => 'ListFindingsMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'sortCriteria' => [ 'shape' => 'SortCriteria', ], ], ], 'ListFindingsResponse' => [ 'type' => 'structure', 'members' => [ 'findings' => [ 'shape' => 'FindingList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMembersMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'ListMembersRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListMembersMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'onlyAssociated' => [ 'shape' => 'Boolean', ], ], ], 'ListMembersResponse' => [ 'type' => 'structure', 'members' => [ 'members' => [ 'shape' => 'MemberList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListUsageTotalsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 500, 'min' => 1, ], 'ListUsageTotalsNextToken' => [ 'type' => 'string', 'min' => 1, ], 'ListUsageTotalsRequest' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'UsageAccountIdList', ], 'maxResults' => [ 'shape' => 'ListUsageTotalsMaxResults', ], 'nextToken' => [ 'shape' => 'ListUsageTotalsNextToken', ], ], ], 'ListUsageTotalsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'ListUsageTotalsNextToken', ], 'totals' => [ 'shape' => 'UsageTotalList', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MapComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'MapFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'key', ], 'members' => [ 'comparison' => [ 'shape' => 'MapComparison', ], 'key' => [ 'shape' => 'MapKey', ], 'value' => [ 'shape' => 'MapValue', ], ], ], 'MapFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MapFilter', ], 'max' => 10, 'min' => 1, ], 'MapKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'MapValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Member' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'delegatedAdminAccountId' => [ 'shape' => 'AccountId', ], 'relationshipStatus' => [ 'shape' => 'RelationshipStatus', ], 'updatedAt' => [ 'shape' => 'DateTimeTimestamp', ], ], ], 'MemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Member', ], 'max' => 50, 'min' => 0, ], 'MeteringAccountId' => [ 'type' => 'string', 'pattern' => '[0-9]{12}', ], 'MonthlyCostEstimate' => [ 'type' => 'double', 'min' => 0, ], 'NetworkPath' => [ 'type' => 'structure', 'members' => [ 'steps' => [ 'shape' => 'StepList', ], ], ], 'NetworkProtocol' => [ 'type' => 'string', 'enum' => [ 'TCP', 'UDP', ], ], 'NetworkReachabilityDetails' => [ 'type' => 'structure', 'required' => [ 'networkPath', 'openPortRange', 'protocol', ], 'members' => [ 'networkPath' => [ 'shape' => 'NetworkPath', ], 'openPortRange' => [ 'shape' => 'PortRange', ], 'protocol' => [ 'shape' => 'NetworkProtocol', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 1000000, 'min' => 0, ], 'NonEmptyString' => [ 'type' => 'string', 'min' => 1, ], 'NonEmptyStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'NumberFilter' => [ 'type' => 'structure', 'members' => [ 'lowerInclusive' => [ 'shape' => 'Double', ], 'upperInclusive' => [ 'shape' => 'Double', ], ], ], 'NumberFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NumberFilter', ], 'max' => 10, 'min' => 1, ], 'Operation' => [ 'type' => 'string', 'enum' => [ 'ENABLE_SCANNING', 'DISABLE_SCANNING', 'ENABLE_REPOSITORY', 'DISABLE_REPOSITORY', ], ], 'OwnerId' => [ 'type' => 'string', 'max' => 34, 'min' => 12, 'pattern' => '(^\\d{12}$)|(^o-[a-z0-9]{10,32}$)', ], 'PackageAggregation' => [ 'type' => 'structure', 'members' => [ 'packageNames' => [ 'shape' => 'StringFilterList', ], 'sortBy' => [ 'shape' => 'PackageSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'PackageAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'packageName', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'packageName' => [ 'shape' => 'NonEmptyString', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'PackageArchitecture' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'PackageEpoch' => [ 'type' => 'integer', ], 'PackageFilter' => [ 'type' => 'structure', 'members' => [ 'architecture' => [ 'shape' => 'StringFilter', ], 'epoch' => [ 'shape' => 'NumberFilter', ], 'name' => [ 'shape' => 'StringFilter', ], 'release' => [ 'shape' => 'StringFilter', ], 'sourceLayerHash' => [ 'shape' => 'StringFilter', ], 'version' => [ 'shape' => 'StringFilter', ], ], ], 'PackageFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageFilter', ], 'max' => 10, 'min' => 1, ], 'PackageManager' => [ 'type' => 'string', 'enum' => [ 'BUNDLER', 'CARGO', 'COMPOSER', 'NPM', 'NUGET', 'PIPENV', 'POETRY', 'YARN', 'GOBINARY', 'GOMOD', 'JAR', 'OS', ], ], 'PackageName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'PackageRelease' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'PackageSortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', ], ], 'PackageVersion' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'PackageVulnerabilityDetails' => [ 'type' => 'structure', 'required' => [ 'source', 'vulnerabilityId', 'vulnerablePackages', ], 'members' => [ 'cvss' => [ 'shape' => 'CvssScoreList', ], 'referenceUrls' => [ 'shape' => 'NonEmptyStringList', ], 'relatedVulnerabilities' => [ 'shape' => 'VulnerabilityIdList', ], 'source' => [ 'shape' => 'NonEmptyString', ], 'sourceUrl' => [ 'shape' => 'NonEmptyString', ], 'vendorCreatedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'vendorSeverity' => [ 'shape' => 'NonEmptyString', ], 'vendorUpdatedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'vulnerabilityId' => [ 'shape' => 'VulnerabilityId', ], 'vulnerablePackages' => [ 'shape' => 'VulnerablePackageList', ], ], ], 'Permission' => [ 'type' => 'structure', 'required' => [ 'operation', 'service', ], 'members' => [ 'operation' => [ 'shape' => 'Operation', ], 'service' => [ 'shape' => 'Service', ], ], ], 'Permissions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Permission', ], 'max' => 1024, 'min' => 0, ], 'Platform' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'Port' => [ 'type' => 'integer', 'box' => true, 'max' => 65535, 'min' => 0, ], 'PortRange' => [ 'type' => 'structure', 'required' => [ 'begin', 'end', ], 'members' => [ 'begin' => [ 'shape' => 'Port', ], 'end' => [ 'shape' => 'Port', ], ], ], 'PortRangeFilter' => [ 'type' => 'structure', 'members' => [ 'beginInclusive' => [ 'shape' => 'Port', ], 'endInclusive' => [ 'shape' => 'Port', ], ], ], 'PortRangeFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortRangeFilter', ], 'max' => 10, 'min' => 1, ], 'Recommendation' => [ 'type' => 'structure', 'members' => [ 'Url' => [ 'shape' => 'NonEmptyString', ], 'text' => [ 'shape' => 'NonEmptyString', ], ], ], 'RelationshipStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'INVITED', 'DISABLED', 'ENABLED', 'REMOVED', 'RESIGNED', 'DELETED', 'EMAIL_VERIFICATION_IN_PROGRESS', 'EMAIL_VERIFICATION_FAILED', 'REGION_DISABLED', 'ACCOUNT_SUSPENDED', 'CANNOT_CREATE_DETECTOR_IN_ORG_MASTER', ], ], 'Remediation' => [ 'type' => 'structure', 'members' => [ 'recommendation' => [ 'shape' => 'Recommendation', ], ], ], 'ReportFormat' => [ 'type' => 'string', 'enum' => [ 'CSV', 'JSON', ], ], 'ReportId' => [ 'type' => 'string', 'pattern' => '\\b[a-f0-9]{8}\\b-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-\\b[a-f0-9]{12}\\b', ], 'ReportingErrorCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_ERROR', 'INVALID_PERMISSIONS', ], ], 'RepositoryAggregation' => [ 'type' => 'structure', 'members' => [ 'repositories' => [ 'shape' => 'StringFilterList', ], 'sortBy' => [ 'shape' => 'RepositorySortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'RepositoryAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'repository', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'affectedImages' => [ 'shape' => 'Long', ], 'repository' => [ 'shape' => 'NonEmptyString', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'RepositorySortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', 'AFFECTED_IMAGES', ], ], 'Resource' => [ 'type' => 'structure', 'required' => [ 'id', 'type', ], 'members' => [ 'details' => [ 'shape' => 'ResourceDetails', ], 'id' => [ 'shape' => 'NonEmptyString', ], 'partition' => [ 'shape' => 'NonEmptyString', ], 'region' => [ 'shape' => 'NonEmptyString', ], 'tags' => [ 'shape' => 'TagMap', ], 'type' => [ 'shape' => 'ResourceType', ], ], ], 'ResourceDetails' => [ 'type' => 'structure', 'members' => [ 'awsEc2Instance' => [ 'shape' => 'AwsEc2InstanceDetails', ], 'awsEcrContainerImage' => [ 'shape' => 'AwsEcrContainerImageDetails', ], ], ], 'ResourceId' => [ 'type' => 'string', 'max' => 341, 'min' => 10, 'pattern' => '(^arn:.*:ecr:.*:\\d{12}:repository\\/(?:[a-z0-9]+(?:[._-][a-z0-9]+)*\\/)*[a-z0-9]+(?:[._-][a-z0-9]+)*(\\/sha256:[a-z0-9]{64})?$)|(^i-([a-z0-9]{8}|[a-z0-9]{17}|\\\\*)$)', ], 'ResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resource', ], 'max' => 10, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceScanMetadata' => [ 'type' => 'structure', 'members' => [ 'ec2' => [ 'shape' => 'Ec2Metadata', ], 'ecrImage' => [ 'shape' => 'EcrContainerImageMetadata', ], 'ecrRepository' => [ 'shape' => 'EcrRepositoryMetadata', ], ], ], 'ResourceScanType' => [ 'type' => 'string', 'enum' => [ 'EC2', 'ECR', ], ], 'ResourceState' => [ 'type' => 'structure', 'required' => [ 'ec2', 'ecr', ], 'members' => [ 'ec2' => [ 'shape' => 'State', ], 'ecr' => [ 'shape' => 'State', ], ], ], 'ResourceStatus' => [ 'type' => 'structure', 'required' => [ 'ec2', 'ecr', ], 'members' => [ 'ec2' => [ 'shape' => 'Status', ], 'ecr' => [ 'shape' => 'Status', ], ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'AWS_EC2_INSTANCE', 'AWS_ECR_CONTAINER_IMAGE', 'AWS_ECR_REPOSITORY', ], ], 'ScanStatus' => [ 'type' => 'structure', 'required' => [ 'reason', 'statusCode', ], 'members' => [ 'reason' => [ 'shape' => 'ScanStatusReason', ], 'statusCode' => [ 'shape' => 'ScanStatusCode', ], ], ], 'ScanStatusCode' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'ScanStatusReason' => [ 'type' => 'string', 'enum' => [ 'PENDING_INITIAL_SCAN', 'ACCESS_DENIED', 'INTERNAL_ERROR', 'UNMANAGED_EC2_INSTANCE', 'UNSUPPORTED_OS', 'SCAN_ELIGIBILITY_EXPIRED', 'RESOURCE_TERMINATED', 'SUCCESSFUL', 'NO_RESOURCES_FOUND', 'IMAGE_SIZE_EXCEEDED', 'SCAN_FREQUENCY_MANUAL', 'SCAN_FREQUENCY_SCAN_ON_PUSH', 'EC2_INSTANCE_STOPPED', ], ], 'ScanType' => [ 'type' => 'string', 'enum' => [ 'NETWORK', 'PACKAGE', ], ], 'Service' => [ 'type' => 'string', 'enum' => [ 'EC2', 'ECR', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'Severity' => [ 'type' => 'string', 'enum' => [ 'INFORMATIONAL', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL', 'UNTRIAGED', ], ], 'SeverityCounts' => [ 'type' => 'structure', 'members' => [ 'all' => [ 'shape' => 'Long', ], 'critical' => [ 'shape' => 'Long', ], 'high' => [ 'shape' => 'Long', ], 'medium' => [ 'shape' => 'Long', ], ], ], 'SortCriteria' => [ 'type' => 'structure', 'required' => [ 'field', 'sortOrder', ], 'members' => [ 'field' => [ 'shape' => 'SortField', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'SortField' => [ 'type' => 'string', 'enum' => [ 'AWS_ACCOUNT_ID', 'FINDING_TYPE', 'SEVERITY', 'FIRST_OBSERVED_AT', 'LAST_OBSERVED_AT', 'FINDING_STATUS', 'RESOURCE_TYPE', 'ECR_IMAGE_PUSHED_AT', 'ECR_IMAGE_REPOSITORY_NAME', 'ECR_IMAGE_REGISTRY', 'NETWORK_PROTOCOL', 'COMPONENT_TYPE', 'VULNERABILITY_ID', 'VULNERABILITY_SOURCE', 'INSPECTOR_SCORE', 'VENDOR_SEVERITY', ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'SourceLayerHash' => [ 'type' => 'string', 'max' => 71, 'min' => 71, 'pattern' => '^sha256:[a-z0-9]{64}$', ], 'State' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorMessage', 'status', ], 'members' => [ 'errorCode' => [ 'shape' => 'ErrorCode', ], 'errorMessage' => [ 'shape' => 'NonEmptyString', ], 'status' => [ 'shape' => 'Status', ], ], ], 'Status' => [ 'type' => 'string', 'enum' => [ 'ENABLING', 'ENABLED', 'DISABLING', 'DISABLED', 'SUSPENDING', 'SUSPENDED', ], ], 'Step' => [ 'type' => 'structure', 'required' => [ 'componentId', 'componentType', ], 'members' => [ 'componentId' => [ 'shape' => 'Component', ], 'componentType' => [ 'shape' => 'ComponentType', ], ], ], 'StepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Step', ], 'max' => 30, 'min' => 1, ], 'String' => [ 'type' => 'string', ], 'StringComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'PREFIX', 'NOT_EQUALS', ], ], 'StringFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'value', ], 'members' => [ 'comparison' => [ 'shape' => 'StringComparison', ], 'value' => [ 'shape' => 'StringInput', ], ], ], 'StringFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringFilter', ], 'max' => 10, 'min' => 1, ], 'StringInput' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'MapKey', ], 'value' => [ 'shape' => 'MapValue', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TitleAggregation' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'AggregationResourceType', ], 'sortBy' => [ 'shape' => 'TitleSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], 'titles' => [ 'shape' => 'StringFilterList', ], 'vulnerabilityIds' => [ 'shape' => 'StringFilterList', ], ], ], 'TitleAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'title', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], 'title' => [ 'shape' => 'NonEmptyString', ], 'vulnerabilityId' => [ 'shape' => 'String', ], ], ], 'TitleSortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateFilterRequest' => [ 'type' => 'structure', 'required' => [ 'filterArn', ], 'members' => [ 'action' => [ 'shape' => 'FilterAction', ], 'description' => [ 'shape' => 'FilterDescription', ], 'filterArn' => [ 'shape' => 'FilterArn', ], 'filterCriteria' => [ 'shape' => 'FilterCriteria', ], 'name' => [ 'shape' => 'FilterName', ], ], ], 'UpdateFilterResponse' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'FilterArn', ], ], ], 'UpdateOrganizationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'autoEnable', ], 'members' => [ 'autoEnable' => [ 'shape' => 'AutoEnable', ], ], ], 'UpdateOrganizationConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'autoEnable', ], 'members' => [ 'autoEnable' => [ 'shape' => 'AutoEnable', ], ], ], 'Usage' => [ 'type' => 'structure', 'members' => [ 'currency' => [ 'shape' => 'Currency', ], 'estimatedMonthlyCost' => [ 'shape' => 'MonthlyCostEstimate', ], 'total' => [ 'shape' => 'UsageValue', ], 'type' => [ 'shape' => 'UsageType', ], ], ], 'UsageAccountId' => [ 'type' => 'string', 'pattern' => '[0-9]{12}', ], 'UsageAccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageAccountId', ], 'max' => 5000, 'min' => 1, ], 'UsageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Usage', ], ], 'UsageTotal' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'MeteringAccountId', ], 'usage' => [ 'shape' => 'UsageList', ], ], ], 'UsageTotalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageTotal', ], ], 'UsageType' => [ 'type' => 'string', 'enum' => [ 'EC2_INSTANCE_HOURS', 'ECR_INITIAL_SCAN', 'ECR_RESCAN', ], ], 'UsageValue' => [ 'type' => 'double', 'min' => 0, ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'fields' => [ 'shape' => 'ValidationExceptionFields', ], 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'message', 'name', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'CANNOT_PARSE', 'FIELD_VALIDATION_FAILED', 'OTHER', ], ], 'VulnerabilityId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'VulnerabilityIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VulnerabilityId', ], ], 'VulnerablePackage' => [ 'type' => 'structure', 'required' => [ 'name', 'version', ], 'members' => [ 'arch' => [ 'shape' => 'PackageArchitecture', ], 'epoch' => [ 'shape' => 'PackageEpoch', ], 'filePath' => [ 'shape' => 'FilePath', ], 'fixedInVersion' => [ 'shape' => 'PackageVersion', ], 'name' => [ 'shape' => 'PackageName', ], 'packageManager' => [ 'shape' => 'PackageManager', ], 'release' => [ 'shape' => 'PackageRelease', ], 'sourceLayerHash' => [ 'shape' => 'SourceLayerHash', ], 'version' => [ 'shape' => 'PackageVersion', ], ], ], 'VulnerablePackageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VulnerablePackage', ], ], ],];
