<?php
// This file was auto-generated from sdk-root/src/data/drs/2020-02-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-02-26', 'endpointPrefix' => 'drs', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'drs', 'serviceFullName' => 'Elastic Disaster Recovery Service', 'serviceId' => 'drs', 'signatureVersion' => 'v4', 'signingName' => 'drs', 'uid' => 'drs-2020-02-26', ], 'operations' => [ 'CreateReplicationConfigurationTemplate' => [ 'name' => 'CreateReplicationConfigurationTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateReplicationConfigurationTemplate', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateReplicationConfigurationTemplateRequest', ], 'output' => [ 'shape' => 'ReplicationConfigurationTemplate', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DeleteJob' => [ 'name' => 'DeleteJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteJob', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteJobRequest', ], 'output' => [ 'shape' => 'DeleteJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], 'idempotent' => true, ], 'DeleteRecoveryInstance' => [ 'name' => 'DeleteRecoveryInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteRecoveryInstance', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteRecoveryInstanceRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DeleteReplicationConfigurationTemplate' => [ 'name' => 'DeleteReplicationConfigurationTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteReplicationConfigurationTemplate', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteReplicationConfigurationTemplateRequest', ], 'output' => [ 'shape' => 'DeleteReplicationConfigurationTemplateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], 'idempotent' => true, ], 'DeleteSourceServer' => [ 'name' => 'DeleteSourceServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteSourceServer', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSourceServerRequest', ], 'output' => [ 'shape' => 'DeleteSourceServerResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], 'idempotent' => true, ], 'DescribeJobLogItems' => [ 'name' => 'DescribeJobLogItems', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeJobLogItems', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeJobLogItemsRequest', ], 'output' => [ 'shape' => 'DescribeJobLogItemsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DescribeJobs' => [ 'name' => 'DescribeJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeJobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeJobsRequest', ], 'output' => [ 'shape' => 'DescribeJobsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DescribeRecoveryInstances' => [ 'name' => 'DescribeRecoveryInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeRecoveryInstances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeRecoveryInstancesRequest', ], 'output' => [ 'shape' => 'DescribeRecoveryInstancesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DescribeRecoverySnapshots' => [ 'name' => 'DescribeRecoverySnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeRecoverySnapshots', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeRecoverySnapshotsRequest', ], 'output' => [ 'shape' => 'DescribeRecoverySnapshotsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DescribeReplicationConfigurationTemplates' => [ 'name' => 'DescribeReplicationConfigurationTemplates', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeReplicationConfigurationTemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeReplicationConfigurationTemplatesRequest', ], 'output' => [ 'shape' => 'DescribeReplicationConfigurationTemplatesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DescribeSourceServers' => [ 'name' => 'DescribeSourceServers', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeSourceServers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSourceServersRequest', ], 'output' => [ 'shape' => 'DescribeSourceServersResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DisconnectRecoveryInstance' => [ 'name' => 'DisconnectRecoveryInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/DisconnectRecoveryInstance', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisconnectRecoveryInstanceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DisconnectSourceServer' => [ 'name' => 'DisconnectSourceServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/DisconnectSourceServer', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisconnectSourceServerRequest', ], 'output' => [ 'shape' => 'SourceServer', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'GetFailbackReplicationConfiguration' => [ 'name' => 'GetFailbackReplicationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetFailbackReplicationConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFailbackReplicationConfigurationRequest', ], 'output' => [ 'shape' => 'GetFailbackReplicationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'GetLaunchConfiguration' => [ 'name' => 'GetLaunchConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetLaunchConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLaunchConfigurationRequest', ], 'output' => [ 'shape' => 'LaunchConfiguration', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'GetReplicationConfiguration' => [ 'name' => 'GetReplicationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetReplicationConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetReplicationConfigurationRequest', ], 'output' => [ 'shape' => 'ReplicationConfiguration', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'InitializeService' => [ 'name' => 'InitializeService', 'http' => [ 'method' => 'POST', 'requestUri' => '/InitializeService', 'responseCode' => 204, ], 'input' => [ 'shape' => 'InitializeServiceRequest', ], 'output' => [ 'shape' => 'InitializeServiceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'RetryDataReplication' => [ 'name' => 'RetryDataReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/RetryDataReplication', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RetryDataReplicationRequest', ], 'output' => [ 'shape' => 'SourceServer', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'StartFailbackLaunch' => [ 'name' => 'StartFailbackLaunch', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartFailbackLaunch', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartFailbackLaunchRequest', ], 'output' => [ 'shape' => 'StartFailbackLaunchResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'StartRecovery' => [ 'name' => 'StartRecovery', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartRecovery', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartRecoveryRequest', ], 'output' => [ 'shape' => 'StartRecoveryResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'StopFailback' => [ 'name' => 'StopFailback', 'http' => [ 'method' => 'POST', 'requestUri' => '/StopFailback', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopFailbackRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'TerminateRecoveryInstances' => [ 'name' => 'TerminateRecoveryInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/TerminateRecoveryInstances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TerminateRecoveryInstancesRequest', ], 'output' => [ 'shape' => 'TerminateRecoveryInstancesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateFailbackReplicationConfiguration' => [ 'name' => 'UpdateFailbackReplicationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateFailbackReplicationConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFailbackReplicationConfigurationRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'UpdateLaunchConfiguration' => [ 'name' => 'UpdateLaunchConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateLaunchConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateLaunchConfigurationRequest', ], 'output' => [ 'shape' => 'LaunchConfiguration', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], 'idempotent' => true, ], 'UpdateReplicationConfiguration' => [ 'name' => 'UpdateReplicationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateReplicationConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateReplicationConfigurationRequest', ], 'output' => [ 'shape' => 'ReplicationConfiguration', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], 'idempotent' => true, ], 'UpdateReplicationConfigurationTemplate' => [ 'name' => 'UpdateReplicationConfigurationTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateReplicationConfigurationTemplate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateReplicationConfigurationTemplateRequest', ], 'output' => [ 'shape' => 'ReplicationConfigurationTemplate', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], ], 'shapes' => [ 'ARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:.{16,2044}$', ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'message' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BoundedString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'CPU' => [ 'type' => 'structure', 'members' => [ 'cores' => [ 'shape' => 'PositiveInteger', ], 'modelName' => [ 'shape' => 'BoundedString', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'message' => [ 'shape' => 'LargeBoundedString', ], 'resourceId' => [ 'shape' => 'LargeBoundedString', ], 'resourceType' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'Cpus' => [ 'type' => 'list', 'member' => [ 'shape' => 'CPU', ], 'max' => 256, 'min' => 0, ], 'CreateReplicationConfigurationTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'associateDefaultSecurityGroup', 'bandwidthThrottling', 'createPublicIP', 'dataPlaneRouting', 'defaultLargeStagingDiskType', 'ebsEncryption', 'pitPolicy', 'replicationServerInstanceType', 'replicationServersSecurityGroupsIDs', 'stagingAreaSubnetId', 'stagingAreaTags', 'useDedicatedReplicationServer', ], 'members' => [ 'associateDefaultSecurityGroup' => [ 'shape' => 'Boolean', ], 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'createPublicIP' => [ 'shape' => 'Boolean', ], 'dataPlaneRouting' => [ 'shape' => 'ReplicationConfigurationDataPlaneRouting', ], 'defaultLargeStagingDiskType' => [ 'shape' => 'ReplicationConfigurationDefaultLargeStagingDiskType', ], 'ebsEncryption' => [ 'shape' => 'ReplicationConfigurationEbsEncryption', ], 'ebsEncryptionKeyArn' => [ 'shape' => 'ARN', ], 'pitPolicy' => [ 'shape' => 'PITPolicy', ], 'replicationServerInstanceType' => [ 'shape' => 'EC2InstanceType', ], 'replicationServersSecurityGroupsIDs' => [ 'shape' => 'ReplicationServersSecurityGroupsIDs', ], 'stagingAreaSubnetId' => [ 'shape' => 'SubnetID', ], 'stagingAreaTags' => [ 'shape' => 'TagsMap', ], 'tags' => [ 'shape' => 'TagsMap', ], 'useDedicatedReplicationServer' => [ 'shape' => 'Boolean', ], ], ], 'DataReplicationError' => [ 'type' => 'structure', 'members' => [ 'error' => [ 'shape' => 'DataReplicationErrorString', ], 'rawError' => [ 'shape' => 'LargeBoundedString', ], ], ], 'DataReplicationErrorString' => [ 'type' => 'string', 'enum' => [ 'AGENT_NOT_SEEN', 'SNAPSHOTS_FAILURE', 'NOT_CONVERGING', 'UNSTABLE_NETWORK', 'FAILED_TO_CREATE_SECURITY_GROUP', 'FAILED_TO_LAUNCH_REPLICATION_SERVER', 'FAILED_TO_BOOT_REPLICATION_SERVER', 'FAILED_TO_AUTHENTICATE_WITH_SERVICE', 'FAILED_TO_DOWNLOAD_REPLICATION_SOFTWARE', 'FAILED_TO_CREATE_STAGING_DISKS', 'FAILED_TO_ATTACH_STAGING_DISKS', 'FAILED_TO_PAIR_REPLICATION_SERVER_WITH_AGENT', 'FAILED_TO_CONNECT_AGENT_TO_REPLICATION_SERVER', 'FAILED_TO_START_DATA_TRANSFER', ], ], 'DataReplicationInfo' => [ 'type' => 'structure', 'members' => [ 'dataReplicationError' => [ 'shape' => 'DataReplicationError', ], 'dataReplicationInitiation' => [ 'shape' => 'DataReplicationInitiation', ], 'dataReplicationState' => [ 'shape' => 'DataReplicationState', ], 'etaDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'lagDuration' => [ 'shape' => 'ISO8601DatetimeString', ], 'replicatedDisks' => [ 'shape' => 'DataReplicationInfoReplicatedDisks', ], ], ], 'DataReplicationInfoReplicatedDisk' => [ 'type' => 'structure', 'members' => [ 'backloggedStorageBytes' => [ 'shape' => 'PositiveInteger', ], 'deviceName' => [ 'shape' => 'BoundedString', ], 'replicatedStorageBytes' => [ 'shape' => 'PositiveInteger', ], 'rescannedStorageBytes' => [ 'shape' => 'PositiveInteger', ], 'totalStorageBytes' => [ 'shape' => 'PositiveInteger', ], ], ], 'DataReplicationInfoReplicatedDisks' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataReplicationInfoReplicatedDisk', ], 'max' => 60, 'min' => 0, ], 'DataReplicationInitiation' => [ 'type' => 'structure', 'members' => [ 'nextAttemptDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'startDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'steps' => [ 'shape' => 'DataReplicationInitiationSteps', ], ], ], 'DataReplicationInitiationStep' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'DataReplicationInitiationStepName', ], 'status' => [ 'shape' => 'DataReplicationInitiationStepStatus', ], ], ], 'DataReplicationInitiationStepName' => [ 'type' => 'string', 'enum' => [ 'WAIT', 'CREATE_SECURITY_GROUP', 'LAUNCH_REPLICATION_SERVER', 'BOOT_REPLICATION_SERVER', 'AUTHENTICATE_WITH_SERVICE', 'DOWNLOAD_REPLICATION_SOFTWARE', 'CREATE_STAGING_DISKS', 'ATTACH_STAGING_DISKS', 'PAIR_REPLICATION_SERVER_WITH_AGENT', 'CONNECT_AGENT_TO_REPLICATION_SERVER', 'START_DATA_TRANSFER', ], ], 'DataReplicationInitiationStepStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_STARTED', 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', 'SKIPPED', ], ], 'DataReplicationInitiationSteps' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataReplicationInitiationStep', ], ], 'DataReplicationState' => [ 'type' => 'string', 'enum' => [ 'STOPPED', 'INITIATING', 'INITIAL_SYNC', 'BACKLOG', 'CREATING_SNAPSHOT', 'CONTINUOUS', 'PAUSED', 'RESCAN', 'STALLED', 'DISCONNECTED', ], ], 'DeleteJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobID', ], 'members' => [ 'jobID' => [ 'shape' => 'JobID', ], ], ], 'DeleteJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRecoveryInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceID', ], 'members' => [ 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], ], ], 'DeleteReplicationConfigurationTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'replicationConfigurationTemplateID', ], 'members' => [ 'replicationConfigurationTemplateID' => [ 'shape' => 'ReplicationConfigurationTemplateID', ], ], ], 'DeleteReplicationConfigurationTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSourceServerRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'DeleteSourceServerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeJobLogItemsRequest' => [ 'type' => 'structure', 'required' => [ 'jobID', ], 'members' => [ 'jobID' => [ 'shape' => 'JobID', ], 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeJobLogItemsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'JobLogs', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeJobsRequest' => [ 'type' => 'structure', 'required' => [ 'filters', ], 'members' => [ 'filters' => [ 'shape' => 'DescribeJobsRequestFilters', ], 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeJobsRequestFilters' => [ 'type' => 'structure', 'members' => [ 'fromDate' => [ 'shape' => 'ISO8601DatetimeString', ], 'jobIDs' => [ 'shape' => 'DescribeJobsRequestFiltersJobIDs', ], 'toDate' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'DescribeJobsRequestFiltersJobIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobID', ], 'max' => 1000, 'min' => 0, ], 'DescribeJobsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'JobsList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeRecoveryInstancesItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryInstance', ], ], 'DescribeRecoveryInstancesRequest' => [ 'type' => 'structure', 'required' => [ 'filters', ], 'members' => [ 'filters' => [ 'shape' => 'DescribeRecoveryInstancesRequestFilters', ], 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeRecoveryInstancesRequestFilters' => [ 'type' => 'structure', 'members' => [ 'recoveryInstanceIDs' => [ 'shape' => 'RecoveryInstanceIDs', ], 'sourceServerIDs' => [ 'shape' => 'SourceServerIDs', ], ], ], 'DescribeRecoveryInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'DescribeRecoveryInstancesItems', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeRecoverySnapshotsRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'filters' => [ 'shape' => 'DescribeRecoverySnapshotsRequestFilters', ], 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'order' => [ 'shape' => 'RecoverySnapshotsOrder', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'DescribeRecoverySnapshotsRequestFilters' => [ 'type' => 'structure', 'members' => [ 'fromDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'toDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'DescribeRecoverySnapshotsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'RecoverySnapshotsList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeReplicationConfigurationTemplatesRequest' => [ 'type' => 'structure', 'required' => [ 'replicationConfigurationTemplateIDs', ], 'members' => [ 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'replicationConfigurationTemplateIDs' => [ 'shape' => 'ReplicationConfigurationTemplateIDs', ], ], ], 'DescribeReplicationConfigurationTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'ReplicationConfigurationTemplates', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeSourceServersRequest' => [ 'type' => 'structure', 'required' => [ 'filters', ], 'members' => [ 'filters' => [ 'shape' => 'DescribeSourceServersRequestFilters', ], 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeSourceServersRequestFilters' => [ 'type' => 'structure', 'members' => [ 'hardwareId' => [ 'shape' => 'BoundedString', ], 'sourceServerIDs' => [ 'shape' => 'DescribeSourceServersRequestFiltersIDs', ], ], ], 'DescribeSourceServersRequestFiltersIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceServerID', ], 'max' => 200, 'min' => 0, ], 'DescribeSourceServersResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'SourceServersList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DisconnectRecoveryInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceID', ], 'members' => [ 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], ], ], 'DisconnectSourceServerRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'Disk' => [ 'type' => 'structure', 'members' => [ 'bytes' => [ 'shape' => 'PositiveInteger', ], 'deviceName' => [ 'shape' => 'BoundedString', ], ], ], 'Disks' => [ 'type' => 'list', 'member' => [ 'shape' => 'Disk', ], 'max' => 1000, 'min' => 0, ], 'EC2InstanceID' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^i-[0-9a-fA-F]{8,}$', ], 'EC2InstanceState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'RUNNING', 'STOPPING', 'STOPPED', 'SHUTTING-DOWN', 'TERMINATED', 'NOT_FOUND', ], ], 'EC2InstanceType' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'EbsSnapshotsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ebsSnapshot', ], ], 'EbsVolumeID' => [ 'type' => 'string', 'max' => 19, 'min' => 10, 'pattern' => '^vol-([0-9a-fA-F]{8}|[0-9a-fA-F]{17})$', ], 'FailbackReplicationError' => [ 'type' => 'string', 'enum' => [ 'AGENT_NOT_SEEN', 'FAILBACK_CLIENT_NOT_SEEN', 'NOT_CONVERGING', 'UNSTABLE_NETWORK', 'FAILED_TO_ESTABLISH_RECOVERY_INSTANCE_COMMUNICATION', 'FAILED_TO_DOWNLOAD_REPLICATION_SOFTWARE_TO_FAILBACK_CLIENT', 'FAILED_TO_CONFIGURE_REPLICATION_SOFTWARE', 'FAILED_TO_PAIR_AGENT_WITH_REPLICATION_SOFTWARE', 'FAILED_TO_ESTABLISH_AGENT_REPLICATOR_SOFTWARE_COMMUNICATION', ], ], 'FailbackState' => [ 'type' => 'string', 'enum' => [ 'FAILBACK_NOT_STARTED', 'FAILBACK_IN_PROGRESS', 'FAILBACK_READY_FOR_LAUNCH', 'FAILBACK_COMPLETED', 'FAILBACK_ERROR', ], ], 'GetFailbackReplicationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceID', ], 'members' => [ 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], ], ], 'GetFailbackReplicationConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceID', ], 'members' => [ 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'name' => [ 'shape' => 'BoundedString', ], 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], 'usePrivateIP' => [ 'shape' => 'Boolean', ], ], ], 'GetLaunchConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'GetReplicationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'IPsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BoundedString', ], ], 'ISO8601DatetimeString' => [ 'type' => 'string', 'max' => 32, 'min' => 19, 'pattern' => '^[1-9][0-9]*-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9](\\.[0-9]+)?Z$', ], 'IdentificationHints' => [ 'type' => 'structure', 'members' => [ 'awsInstanceID' => [ 'shape' => 'EC2InstanceID', ], 'fqdn' => [ 'shape' => 'BoundedString', ], 'hostname' => [ 'shape' => 'BoundedString', ], 'vmWareUuid' => [ 'shape' => 'BoundedString', ], ], ], 'InitializeServiceRequest' => [ 'type' => 'structure', 'members' => [], ], 'InitializeServiceResponse' => [ 'type' => 'structure', 'members' => [], ], 'InitiatedBy' => [ 'type' => 'string', 'enum' => [ 'START_RECOVERY', 'START_DRILL', 'FAILBACK', 'DIAGNOSTIC', 'TERMINATE_RECOVERY_INSTANCES', ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'LargeBoundedString', ], 'retryAfterSeconds' => [ 'shape' => 'PositiveInteger', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'Job' => [ 'type' => 'structure', 'required' => [ 'jobID', ], 'members' => [ 'arn' => [ 'shape' => 'ARN', ], 'creationDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'endDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'initiatedBy' => [ 'shape' => 'InitiatedBy', ], 'jobID' => [ 'shape' => 'JobID', ], 'participatingServers' => [ 'shape' => 'ParticipatingServers', ], 'status' => [ 'shape' => 'JobStatus', ], 'tags' => [ 'shape' => 'TagsMap', ], 'type' => [ 'shape' => 'JobType', ], ], ], 'JobID' => [ 'type' => 'string', 'max' => 24, 'min' => 24, 'pattern' => '^drsjob-[0-9a-zA-Z]{17}$', ], 'JobLog' => [ 'type' => 'structure', 'members' => [ 'event' => [ 'shape' => 'JobLogEvent', ], 'eventData' => [ 'shape' => 'JobLogEventData', ], 'logDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'JobLogEvent' => [ 'type' => 'string', 'enum' => [ 'JOB_START', 'SERVER_SKIPPED', 'CLEANUP_START', 'CLEANUP_END', 'CLEANUP_FAIL', 'SNAPSHOT_START', 'SNAPSHOT_END', 'SNAPSHOT_FAIL', 'USING_PREVIOUS_SNAPSHOT', 'USING_PREVIOUS_SNAPSHOT_FAILED', 'CONVERSION_START', 'CONVERSION_END', 'CONVERSION_FAIL', 'LAUNCH_START', 'LAUNCH_FAILED', 'JOB_CANCEL', 'JOB_END', ], ], 'JobLogEventData' => [ 'type' => 'structure', 'members' => [ 'conversionServerID' => [ 'shape' => 'EC2InstanceID', ], 'rawError' => [ 'shape' => 'LargeBoundedString', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'targetInstanceID' => [ 'shape' => 'EC2InstanceID', ], ], ], 'JobLogs' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobLog', ], ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'STARTED', 'COMPLETED', ], ], 'JobType' => [ 'type' => 'string', 'enum' => [ 'LAUNCH', 'TERMINATE', ], ], 'JobsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Job', ], ], 'LargeBoundedString' => [ 'type' => 'string', 'max' => 65536, 'min' => 0, ], 'LastLaunchResult' => [ 'type' => 'string', 'enum' => [ 'NOT_STARTED', 'PENDING', 'SUCCEEDED', 'FAILED', ], ], 'LastLaunchType' => [ 'type' => 'string', 'enum' => [ 'RECOVERY', 'DRILL', ], ], 'LaunchConfiguration' => [ 'type' => 'structure', 'members' => [ 'copyPrivateIp' => [ 'shape' => 'Boolean', ], 'copyTags' => [ 'shape' => 'Boolean', ], 'ec2LaunchTemplateID' => [ 'shape' => 'BoundedString', ], 'launchDisposition' => [ 'shape' => 'LaunchDisposition', ], 'licensing' => [ 'shape' => 'Licensing', ], 'name' => [ 'shape' => 'SmallBoundedString', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'targetInstanceTypeRightSizingMethod' => [ 'shape' => 'TargetInstanceTypeRightSizingMethod', ], ], ], 'LaunchDisposition' => [ 'type' => 'string', 'enum' => [ 'STOPPED', 'STARTED', ], ], 'LaunchStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_PROGRESS', 'LAUNCHED', 'FAILED', 'TERMINATED', ], ], 'Licensing' => [ 'type' => 'structure', 'members' => [ 'osByol' => [ 'shape' => 'Boolean', ], ], ], 'LifeCycle' => [ 'type' => 'structure', 'members' => [ 'addedToServiceDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'elapsedReplicationDuration' => [ 'shape' => 'ISO8601DatetimeString', ], 'firstByteDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'lastLaunch' => [ 'shape' => 'LifeCycleLastLaunch', ], 'lastSeenByServiceDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'LifeCycleLastLaunch' => [ 'type' => 'structure', 'members' => [ 'initiated' => [ 'shape' => 'LifeCycleLastLaunchInitiated', ], ], ], 'LifeCycleLastLaunchInitiated' => [ 'type' => 'structure', 'members' => [ 'apiCallDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'jobID' => [ 'shape' => 'JobID', ], 'type' => [ 'shape' => 'LastLaunchType', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'NetworkInterface' => [ 'type' => 'structure', 'members' => [ 'ips' => [ 'shape' => 'IPsList', ], 'isPrimary' => [ 'shape' => 'Boolean', ], 'macAddress' => [ 'shape' => 'BoundedString', ], ], ], 'NetworkInterfaces' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkInterface', ], 'max' => 32, 'min' => 0, ], 'OS' => [ 'type' => 'structure', 'members' => [ 'fullString' => [ 'shape' => 'BoundedString', ], ], ], 'PITPolicy' => [ 'type' => 'list', 'member' => [ 'shape' => 'PITPolicyRule', ], 'max' => 10, 'min' => 1, ], 'PITPolicyRule' => [ 'type' => 'structure', 'required' => [ 'interval', 'retentionDuration', 'units', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'interval' => [ 'shape' => 'StrictlyPositiveInteger', ], 'retentionDuration' => [ 'shape' => 'StrictlyPositiveInteger', ], 'ruleID' => [ 'shape' => 'PositiveInteger', ], 'units' => [ 'shape' => 'PITPolicyRuleUnits', ], ], ], 'PITPolicyRuleUnits' => [ 'type' => 'string', 'enum' => [ 'MINUTE', 'HOUR', 'DAY', ], ], 'PaginationToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'ParticipatingServer' => [ 'type' => 'structure', 'members' => [ 'launchStatus' => [ 'shape' => 'LaunchStatus', ], 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'ParticipatingServers' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParticipatingServer', ], ], 'PositiveInteger' => [ 'type' => 'long', 'min' => 0, ], 'RecoveryInstance' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ARN', ], 'dataReplicationInfo' => [ 'shape' => 'RecoveryInstanceDataReplicationInfo', ], 'ec2InstanceID' => [ 'shape' => 'EC2InstanceID', ], 'ec2InstanceState' => [ 'shape' => 'EC2InstanceState', ], 'failback' => [ 'shape' => 'RecoveryInstanceFailback', ], 'isDrill' => [ 'shape' => 'Boolean', ], 'jobID' => [ 'shape' => 'JobID', ], 'pointInTimeSnapshotDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], 'recoveryInstanceProperties' => [ 'shape' => 'RecoveryInstanceProperties', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'RecoveryInstanceDataReplicationError' => [ 'type' => 'structure', 'members' => [ 'error' => [ 'shape' => 'FailbackReplicationError', ], 'rawError' => [ 'shape' => 'LargeBoundedString', ], ], ], 'RecoveryInstanceDataReplicationInfo' => [ 'type' => 'structure', 'members' => [ 'dataReplicationError' => [ 'shape' => 'RecoveryInstanceDataReplicationError', ], 'dataReplicationInitiation' => [ 'shape' => 'RecoveryInstanceDataReplicationInitiation', ], 'dataReplicationState' => [ 'shape' => 'RecoveryInstanceDataReplicationState', ], 'etaDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'lagDuration' => [ 'shape' => 'ISO8601DatetimeString', ], 'replicatedDisks' => [ 'shape' => 'RecoveryInstanceDataReplicationInfoReplicatedDisks', ], ], ], 'RecoveryInstanceDataReplicationInfoReplicatedDisk' => [ 'type' => 'structure', 'members' => [ 'backloggedStorageBytes' => [ 'shape' => 'PositiveInteger', ], 'deviceName' => [ 'shape' => 'BoundedString', ], 'replicatedStorageBytes' => [ 'shape' => 'PositiveInteger', ], 'rescannedStorageBytes' => [ 'shape' => 'PositiveInteger', ], 'totalStorageBytes' => [ 'shape' => 'PositiveInteger', ], ], ], 'RecoveryInstanceDataReplicationInfoReplicatedDisks' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryInstanceDataReplicationInfoReplicatedDisk', ], 'max' => 60, 'min' => 0, ], 'RecoveryInstanceDataReplicationInitiation' => [ 'type' => 'structure', 'members' => [ 'startDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'steps' => [ 'shape' => 'RecoveryInstanceDataReplicationInitiationSteps', ], ], ], 'RecoveryInstanceDataReplicationInitiationStep' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'RecoveryInstanceDataReplicationInitiationStepName', ], 'status' => [ 'shape' => 'RecoveryInstanceDataReplicationInitiationStepStatus', ], ], ], 'RecoveryInstanceDataReplicationInitiationStepName' => [ 'type' => 'string', 'enum' => [ 'LINK_FAILBACK_CLIENT_WITH_RECOVERY_INSTANCE', 'COMPLETE_VOLUME_MAPPING', 'ESTABLISH_RECOVERY_INSTANCE_COMMUNICATION', 'DOWNLOAD_REPLICATION_SOFTWARE_TO_FAILBACK_CLIENT', 'CONFIGURE_REPLICATION_SOFTWARE', 'PAIR_AGENT_WITH_REPLICATION_SOFTWARE', 'ESTABLISH_AGENT_REPLICATOR_SOFTWARE_COMMUNICATION', ], ], 'RecoveryInstanceDataReplicationInitiationStepStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_STARTED', 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', 'SKIPPED', ], ], 'RecoveryInstanceDataReplicationInitiationSteps' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryInstanceDataReplicationInitiationStep', ], ], 'RecoveryInstanceDataReplicationState' => [ 'type' => 'string', 'enum' => [ 'STOPPED', 'INITIATING', 'INITIAL_SYNC', 'BACKLOG', 'CREATING_SNAPSHOT', 'CONTINUOUS', 'PAUSED', 'RESCAN', 'STALLED', 'DISCONNECTED', ], ], 'RecoveryInstanceDisk' => [ 'type' => 'structure', 'members' => [ 'bytes' => [ 'shape' => 'PositiveInteger', ], 'ebsVolumeID' => [ 'shape' => 'EbsVolumeID', ], 'internalDeviceName' => [ 'shape' => 'BoundedString', ], ], ], 'RecoveryInstanceDisks' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryInstanceDisk', ], 'max' => 1000, 'min' => 0, ], 'RecoveryInstanceFailback' => [ 'type' => 'structure', 'members' => [ 'agentLastSeenByServiceDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'elapsedReplicationDuration' => [ 'shape' => 'ISO8601DatetimeString', ], 'failbackClientID' => [ 'shape' => 'BoundedString', ], 'failbackClientLastSeenByServiceDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'failbackInitiationTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'failbackJobID' => [ 'shape' => 'JobID', ], 'failbackToOriginalServer' => [ 'shape' => 'Boolean', ], 'firstByteDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'state' => [ 'shape' => 'FailbackState', ], ], ], 'RecoveryInstanceID' => [ 'type' => 'string', 'max' => 19, 'min' => 10, 'pattern' => '^i-[0-9a-fA-F]{8,}$', ], 'RecoveryInstanceIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryInstanceID', ], 'max' => 200, 'min' => 0, ], 'RecoveryInstanceProperties' => [ 'type' => 'structure', 'members' => [ 'cpus' => [ 'shape' => 'Cpus', ], 'disks' => [ 'shape' => 'RecoveryInstanceDisks', ], 'identificationHints' => [ 'shape' => 'IdentificationHints', ], 'lastUpdatedDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'networkInterfaces' => [ 'shape' => 'NetworkInterfaces', ], 'os' => [ 'shape' => 'OS', ], 'ramBytes' => [ 'shape' => 'PositiveInteger', ], ], ], 'RecoveryInstancesForTerminationRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryInstanceID', ], 'max' => 200, 'min' => 1, ], 'RecoverySnapshot' => [ 'type' => 'structure', 'required' => [ 'expectedTimestamp', 'snapshotID', 'sourceServerID', ], 'members' => [ 'ebsSnapshots' => [ 'shape' => 'EbsSnapshotsList', ], 'expectedTimestamp' => [ 'shape' => 'ISO8601DatetimeString', ], 'snapshotID' => [ 'shape' => 'RecoverySnapshotID', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'timestamp' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'RecoverySnapshotID' => [ 'type' => 'string', 'max' => 21, 'min' => 21, 'pattern' => '^pit-[0-9a-zA-Z]{17}$', ], 'RecoverySnapshotsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoverySnapshot', ], ], 'RecoverySnapshotsOrder' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'ReplicationConfiguration' => [ 'type' => 'structure', 'members' => [ 'associateDefaultSecurityGroup' => [ 'shape' => 'Boolean', ], 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'createPublicIP' => [ 'shape' => 'Boolean', ], 'dataPlaneRouting' => [ 'shape' => 'ReplicationConfigurationDataPlaneRouting', ], 'defaultLargeStagingDiskType' => [ 'shape' => 'ReplicationConfigurationDefaultLargeStagingDiskType', ], 'ebsEncryption' => [ 'shape' => 'ReplicationConfigurationEbsEncryption', ], 'ebsEncryptionKeyArn' => [ 'shape' => 'ARN', ], 'name' => [ 'shape' => 'SmallBoundedString', ], 'pitPolicy' => [ 'shape' => 'PITPolicy', ], 'replicatedDisks' => [ 'shape' => 'ReplicationConfigurationReplicatedDisks', ], 'replicationServerInstanceType' => [ 'shape' => 'EC2InstanceType', ], 'replicationServersSecurityGroupsIDs' => [ 'shape' => 'ReplicationServersSecurityGroupsIDs', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'stagingAreaSubnetId' => [ 'shape' => 'SubnetID', ], 'stagingAreaTags' => [ 'shape' => 'TagsMap', ], 'useDedicatedReplicationServer' => [ 'shape' => 'Boolean', ], ], ], 'ReplicationConfigurationDataPlaneRouting' => [ 'type' => 'string', 'enum' => [ 'PRIVATE_IP', 'PUBLIC_IP', ], ], 'ReplicationConfigurationDefaultLargeStagingDiskType' => [ 'type' => 'string', 'enum' => [ 'GP2', 'GP3', 'ST1', ], ], 'ReplicationConfigurationEbsEncryption' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'CUSTOM', ], ], 'ReplicationConfigurationReplicatedDisk' => [ 'type' => 'structure', 'members' => [ 'deviceName' => [ 'shape' => 'BoundedString', ], 'iops' => [ 'shape' => 'PositiveInteger', ], 'isBootDisk' => [ 'shape' => 'Boolean', ], 'stagingDiskType' => [ 'shape' => 'ReplicationConfigurationReplicatedDiskStagingDiskType', ], 'throughput' => [ 'shape' => 'PositiveInteger', ], ], ], 'ReplicationConfigurationReplicatedDiskStagingDiskType' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'GP2', 'GP3', 'IO1', 'SC1', 'ST1', 'STANDARD', ], ], 'ReplicationConfigurationReplicatedDisks' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationConfigurationReplicatedDisk', ], 'max' => 60, 'min' => 0, ], 'ReplicationConfigurationTemplate' => [ 'type' => 'structure', 'required' => [ 'replicationConfigurationTemplateID', ], 'members' => [ 'arn' => [ 'shape' => 'ARN', ], 'associateDefaultSecurityGroup' => [ 'shape' => 'Boolean', ], 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'createPublicIP' => [ 'shape' => 'Boolean', ], 'dataPlaneRouting' => [ 'shape' => 'ReplicationConfigurationDataPlaneRouting', ], 'defaultLargeStagingDiskType' => [ 'shape' => 'ReplicationConfigurationDefaultLargeStagingDiskType', ], 'ebsEncryption' => [ 'shape' => 'ReplicationConfigurationEbsEncryption', ], 'ebsEncryptionKeyArn' => [ 'shape' => 'ARN', ], 'pitPolicy' => [ 'shape' => 'PITPolicy', ], 'replicationConfigurationTemplateID' => [ 'shape' => 'ReplicationConfigurationTemplateID', ], 'replicationServerInstanceType' => [ 'shape' => 'EC2InstanceType', ], 'replicationServersSecurityGroupsIDs' => [ 'shape' => 'ReplicationServersSecurityGroupsIDs', ], 'stagingAreaSubnetId' => [ 'shape' => 'SubnetID', ], 'stagingAreaTags' => [ 'shape' => 'TagsMap', ], 'tags' => [ 'shape' => 'TagsMap', ], 'useDedicatedReplicationServer' => [ 'shape' => 'Boolean', ], ], ], 'ReplicationConfigurationTemplateID' => [ 'type' => 'string', 'max' => 21, 'min' => 21, 'pattern' => '^rct-[0-9a-zA-Z]{17}$', ], 'ReplicationConfigurationTemplateIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationConfigurationTemplateID', ], 'max' => 200, 'min' => 0, ], 'ReplicationConfigurationTemplates' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationConfigurationTemplate', ], ], 'ReplicationServersSecurityGroupsIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupID', ], 'max' => 32, 'min' => 0, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'message' => [ 'shape' => 'LargeBoundedString', ], 'resourceId' => [ 'shape' => 'LargeBoundedString', ], 'resourceType' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RetryDataReplicationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'SecurityGroupID' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^sg-[0-9a-fA-F]{8,}$', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'message' => [ 'shape' => 'LargeBoundedString', ], 'quotaCode' => [ 'shape' => 'LargeBoundedString', ], 'resourceId' => [ 'shape' => 'LargeBoundedString', ], 'resourceType' => [ 'shape' => 'LargeBoundedString', ], 'serviceCode' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SmallBoundedString' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'SourceProperties' => [ 'type' => 'structure', 'members' => [ 'cpus' => [ 'shape' => 'Cpus', ], 'disks' => [ 'shape' => 'Disks', ], 'identificationHints' => [ 'shape' => 'IdentificationHints', ], 'lastUpdatedDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'networkInterfaces' => [ 'shape' => 'NetworkInterfaces', ], 'os' => [ 'shape' => 'OS', ], 'ramBytes' => [ 'shape' => 'PositiveInteger', ], 'recommendedInstanceType' => [ 'shape' => 'EC2InstanceType', ], ], ], 'SourceServer' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ARN', ], 'dataReplicationInfo' => [ 'shape' => 'DataReplicationInfo', ], 'lastLaunchResult' => [ 'shape' => 'LastLaunchResult', ], 'lifeCycle' => [ 'shape' => 'LifeCycle', ], 'recoveryInstanceId' => [ 'shape' => 'RecoveryInstanceID', ], 'sourceProperties' => [ 'shape' => 'SourceProperties', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'SourceServerID' => [ 'type' => 'string', 'max' => 19, 'min' => 19, 'pattern' => '^s-[0-9a-zA-Z]{17}$', ], 'SourceServerIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceServerID', ], ], 'SourceServersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceServer', ], ], 'StartFailbackLaunchRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceIDs', ], 'members' => [ 'recoveryInstanceIDs' => [ 'shape' => 'StartFailbackRequestRecoveryInstanceIDs', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'StartFailbackLaunchResponse' => [ 'type' => 'structure', 'members' => [ 'job' => [ 'shape' => 'Job', ], ], ], 'StartFailbackRequestRecoveryInstanceIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryInstanceID', ], 'max' => 200, 'min' => 1, ], 'StartRecoveryRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServers', ], 'members' => [ 'isDrill' => [ 'shape' => 'Boolean', ], 'sourceServers' => [ 'shape' => 'StartRecoveryRequestSourceServers', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'StartRecoveryRequestSourceServer' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'recoverySnapshotID' => [ 'shape' => 'RecoverySnapshotID', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'StartRecoveryRequestSourceServers' => [ 'type' => 'list', 'member' => [ 'shape' => 'StartRecoveryRequestSourceServer', ], 'max' => 200, 'min' => 1, ], 'StartRecoveryResponse' => [ 'type' => 'structure', 'members' => [ 'job' => [ 'shape' => 'Job', ], ], ], 'StopFailbackRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceID', ], 'members' => [ 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], ], ], 'StrictlyPositiveInteger' => [ 'type' => 'integer', 'min' => 1, ], 'SubnetID' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^subnet-[0-9a-fA-F]{8,}$', ], 'TagKey' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'sensitive' => true, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TagsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'sensitive' => true, ], 'TargetInstanceTypeRightSizingMethod' => [ 'type' => 'string', 'enum' => [ 'NONE', 'BASIC', ], ], 'TerminateRecoveryInstancesRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceIDs', ], 'members' => [ 'recoveryInstanceIDs' => [ 'shape' => 'RecoveryInstancesForTerminationRequest', ], ], ], 'TerminateRecoveryInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'job' => [ 'shape' => 'Job', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'LargeBoundedString', ], 'quotaCode' => [ 'shape' => 'LargeBoundedString', ], 'retryAfterSeconds' => [ 'shape' => 'LargeBoundedString', 'location' => 'header', 'locationName' => 'Retry-After', ], 'serviceCode' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'UninitializedAccountException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'message' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UpdateFailbackReplicationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceID', ], 'members' => [ 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'name' => [ 'shape' => 'BoundedString', ], 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], 'usePrivateIP' => [ 'shape' => 'Boolean', ], ], ], 'UpdateLaunchConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'copyPrivateIp' => [ 'shape' => 'Boolean', ], 'copyTags' => [ 'shape' => 'Boolean', ], 'launchDisposition' => [ 'shape' => 'LaunchDisposition', ], 'licensing' => [ 'shape' => 'Licensing', ], 'name' => [ 'shape' => 'SmallBoundedString', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'targetInstanceTypeRightSizingMethod' => [ 'shape' => 'TargetInstanceTypeRightSizingMethod', ], ], ], 'UpdateReplicationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'associateDefaultSecurityGroup' => [ 'shape' => 'Boolean', ], 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'createPublicIP' => [ 'shape' => 'Boolean', ], 'dataPlaneRouting' => [ 'shape' => 'ReplicationConfigurationDataPlaneRouting', ], 'defaultLargeStagingDiskType' => [ 'shape' => 'ReplicationConfigurationDefaultLargeStagingDiskType', ], 'ebsEncryption' => [ 'shape' => 'ReplicationConfigurationEbsEncryption', ], 'ebsEncryptionKeyArn' => [ 'shape' => 'ARN', ], 'name' => [ 'shape' => 'SmallBoundedString', ], 'pitPolicy' => [ 'shape' => 'PITPolicy', ], 'replicatedDisks' => [ 'shape' => 'ReplicationConfigurationReplicatedDisks', ], 'replicationServerInstanceType' => [ 'shape' => 'EC2InstanceType', ], 'replicationServersSecurityGroupsIDs' => [ 'shape' => 'ReplicationServersSecurityGroupsIDs', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'stagingAreaSubnetId' => [ 'shape' => 'SubnetID', ], 'stagingAreaTags' => [ 'shape' => 'TagsMap', ], 'useDedicatedReplicationServer' => [ 'shape' => 'Boolean', ], ], ], 'UpdateReplicationConfigurationTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'replicationConfigurationTemplateID', ], 'members' => [ 'arn' => [ 'shape' => 'ARN', ], 'associateDefaultSecurityGroup' => [ 'shape' => 'Boolean', ], 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'createPublicIP' => [ 'shape' => 'Boolean', ], 'dataPlaneRouting' => [ 'shape' => 'ReplicationConfigurationDataPlaneRouting', ], 'defaultLargeStagingDiskType' => [ 'shape' => 'ReplicationConfigurationDefaultLargeStagingDiskType', ], 'ebsEncryption' => [ 'shape' => 'ReplicationConfigurationEbsEncryption', ], 'ebsEncryptionKeyArn' => [ 'shape' => 'ARN', ], 'pitPolicy' => [ 'shape' => 'PITPolicy', ], 'replicationConfigurationTemplateID' => [ 'shape' => 'ReplicationConfigurationTemplateID', ], 'replicationServerInstanceType' => [ 'shape' => 'EC2InstanceType', ], 'replicationServersSecurityGroupsIDs' => [ 'shape' => 'ReplicationServersSecurityGroupsIDs', ], 'stagingAreaSubnetId' => [ 'shape' => 'SubnetID', ], 'stagingAreaTags' => [ 'shape' => 'TagsMap', ], 'useDedicatedReplicationServer' => [ 'shape' => 'Boolean', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'LargeBoundedString', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'LargeBoundedString', ], 'name' => [ 'shape' => 'LargeBoundedString', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'other', ], ], 'ebsSnapshot' => [ 'type' => 'string', 'pattern' => '^snap-[0-9a-zA-Z]{17}$', ], ],];
