<?php
// This file was auto-generated from sdk-root/src/data/storagegateway/2013-06-30/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2013-06-30', 'endpointPrefix' => 'storagegateway', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'AWS Storage Gateway', 'serviceId' => 'Storage Gateway', 'signatureVersion' => 'v4', 'targetPrefix' => 'StorageGateway_20130630', 'uid' => 'storagegateway-2013-06-30', ], 'operations' => [ 'ActivateGateway' => [ 'name' => 'ActivateGateway', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ActivateGatewayInput', ], 'output' => [ 'shape' => 'ActivateGatewayOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'AddCache' => [ 'name' => 'AddCache', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddCacheInput', ], 'output' => [ 'shape' => 'AddCacheOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'AddTagsToResource' => [ 'name' => 'AddTagsToResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddTagsToResourceInput', ], 'output' => [ 'shape' => 'AddTagsToResourceOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'AddUploadBuffer' => [ 'name' => 'AddUploadBuffer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddUploadBufferInput', ], 'output' => [ 'shape' => 'AddUploadBufferOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'AddWorkingStorage' => [ 'name' => 'AddWorkingStorage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddWorkingStorageInput', ], 'output' => [ 'shape' => 'AddWorkingStorageOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'AssignTapePool' => [ 'name' => 'AssignTapePool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssignTapePoolInput', ], 'output' => [ 'shape' => 'AssignTapePoolOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'AssociateFileSystem' => [ 'name' => 'AssociateFileSystem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateFileSystemInput', ], 'output' => [ 'shape' => 'AssociateFileSystemOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'AttachVolume' => [ 'name' => 'AttachVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachVolumeInput', ], 'output' => [ 'shape' => 'AttachVolumeOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'CancelArchival' => [ 'name' => 'CancelArchival', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelArchivalInput', ], 'output' => [ 'shape' => 'CancelArchivalOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'CancelRetrieval' => [ 'name' => 'CancelRetrieval', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelRetrievalInput', ], 'output' => [ 'shape' => 'CancelRetrievalOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'CreateCachediSCSIVolume' => [ 'name' => 'CreateCachediSCSIVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCachediSCSIVolumeInput', ], 'output' => [ 'shape' => 'CreateCachediSCSIVolumeOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'CreateNFSFileShare' => [ 'name' => 'CreateNFSFileShare', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateNFSFileShareInput', ], 'output' => [ 'shape' => 'CreateNFSFileShareOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'CreateSMBFileShare' => [ 'name' => 'CreateSMBFileShare', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSMBFileShareInput', ], 'output' => [ 'shape' => 'CreateSMBFileShareOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'CreateSnapshot' => [ 'name' => 'CreateSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSnapshotInput', ], 'output' => [ 'shape' => 'CreateSnapshotOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ServiceUnavailableError', ], ], ], 'CreateSnapshotFromVolumeRecoveryPoint' => [ 'name' => 'CreateSnapshotFromVolumeRecoveryPoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSnapshotFromVolumeRecoveryPointInput', ], 'output' => [ 'shape' => 'CreateSnapshotFromVolumeRecoveryPointOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ServiceUnavailableError', ], ], ], 'CreateStorediSCSIVolume' => [ 'name' => 'CreateStorediSCSIVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStorediSCSIVolumeInput', ], 'output' => [ 'shape' => 'CreateStorediSCSIVolumeOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'CreateTapePool' => [ 'name' => 'CreateTapePool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTapePoolInput', ], 'output' => [ 'shape' => 'CreateTapePoolOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'CreateTapeWithBarcode' => [ 'name' => 'CreateTapeWithBarcode', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTapeWithBarcodeInput', ], 'output' => [ 'shape' => 'CreateTapeWithBarcodeOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'CreateTapes' => [ 'name' => 'CreateTapes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTapesInput', ], 'output' => [ 'shape' => 'CreateTapesOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DeleteAutomaticTapeCreationPolicy' => [ 'name' => 'DeleteAutomaticTapeCreationPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAutomaticTapeCreationPolicyInput', ], 'output' => [ 'shape' => 'DeleteAutomaticTapeCreationPolicyOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DeleteBandwidthRateLimit' => [ 'name' => 'DeleteBandwidthRateLimit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBandwidthRateLimitInput', ], 'output' => [ 'shape' => 'DeleteBandwidthRateLimitOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DeleteChapCredentials' => [ 'name' => 'DeleteChapCredentials', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteChapCredentialsInput', ], 'output' => [ 'shape' => 'DeleteChapCredentialsOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DeleteFileShare' => [ 'name' => 'DeleteFileShare', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFileShareInput', ], 'output' => [ 'shape' => 'DeleteFileShareOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DeleteGateway' => [ 'name' => 'DeleteGateway', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteGatewayInput', ], 'output' => [ 'shape' => 'DeleteGatewayOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DeleteSnapshotSchedule' => [ 'name' => 'DeleteSnapshotSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSnapshotScheduleInput', ], 'output' => [ 'shape' => 'DeleteSnapshotScheduleOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DeleteTape' => [ 'name' => 'DeleteTape', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTapeInput', ], 'output' => [ 'shape' => 'DeleteTapeOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DeleteTapeArchive' => [ 'name' => 'DeleteTapeArchive', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTapeArchiveInput', ], 'output' => [ 'shape' => 'DeleteTapeArchiveOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DeleteTapePool' => [ 'name' => 'DeleteTapePool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTapePoolInput', ], 'output' => [ 'shape' => 'DeleteTapePoolOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DeleteVolume' => [ 'name' => 'DeleteVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteVolumeInput', ], 'output' => [ 'shape' => 'DeleteVolumeOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeAvailabilityMonitorTest' => [ 'name' => 'DescribeAvailabilityMonitorTest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAvailabilityMonitorTestInput', ], 'output' => [ 'shape' => 'DescribeAvailabilityMonitorTestOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeBandwidthRateLimit' => [ 'name' => 'DescribeBandwidthRateLimit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeBandwidthRateLimitInput', ], 'output' => [ 'shape' => 'DescribeBandwidthRateLimitOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeBandwidthRateLimitSchedule' => [ 'name' => 'DescribeBandwidthRateLimitSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeBandwidthRateLimitScheduleInput', ], 'output' => [ 'shape' => 'DescribeBandwidthRateLimitScheduleOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeCache' => [ 'name' => 'DescribeCache', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCacheInput', ], 'output' => [ 'shape' => 'DescribeCacheOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeCachediSCSIVolumes' => [ 'name' => 'DescribeCachediSCSIVolumes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCachediSCSIVolumesInput', ], 'output' => [ 'shape' => 'DescribeCachediSCSIVolumesOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeChapCredentials' => [ 'name' => 'DescribeChapCredentials', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeChapCredentialsInput', ], 'output' => [ 'shape' => 'DescribeChapCredentialsOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeFileSystemAssociations' => [ 'name' => 'DescribeFileSystemAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFileSystemAssociationsInput', ], 'output' => [ 'shape' => 'DescribeFileSystemAssociationsOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeGatewayInformation' => [ 'name' => 'DescribeGatewayInformation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeGatewayInformationInput', ], 'output' => [ 'shape' => 'DescribeGatewayInformationOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeMaintenanceStartTime' => [ 'name' => 'DescribeMaintenanceStartTime', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMaintenanceStartTimeInput', ], 'output' => [ 'shape' => 'DescribeMaintenanceStartTimeOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeNFSFileShares' => [ 'name' => 'DescribeNFSFileShares', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeNFSFileSharesInput', ], 'output' => [ 'shape' => 'DescribeNFSFileSharesOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeSMBFileShares' => [ 'name' => 'DescribeSMBFileShares', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSMBFileSharesInput', ], 'output' => [ 'shape' => 'DescribeSMBFileSharesOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeSMBSettings' => [ 'name' => 'DescribeSMBSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSMBSettingsInput', ], 'output' => [ 'shape' => 'DescribeSMBSettingsOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeSnapshotSchedule' => [ 'name' => 'DescribeSnapshotSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSnapshotScheduleInput', ], 'output' => [ 'shape' => 'DescribeSnapshotScheduleOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeStorediSCSIVolumes' => [ 'name' => 'DescribeStorediSCSIVolumes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStorediSCSIVolumesInput', ], 'output' => [ 'shape' => 'DescribeStorediSCSIVolumesOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeTapeArchives' => [ 'name' => 'DescribeTapeArchives', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTapeArchivesInput', ], 'output' => [ 'shape' => 'DescribeTapeArchivesOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeTapeRecoveryPoints' => [ 'name' => 'DescribeTapeRecoveryPoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTapeRecoveryPointsInput', ], 'output' => [ 'shape' => 'DescribeTapeRecoveryPointsOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeTapes' => [ 'name' => 'DescribeTapes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTapesInput', ], 'output' => [ 'shape' => 'DescribeTapesOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeUploadBuffer' => [ 'name' => 'DescribeUploadBuffer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUploadBufferInput', ], 'output' => [ 'shape' => 'DescribeUploadBufferOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeVTLDevices' => [ 'name' => 'DescribeVTLDevices', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeVTLDevicesInput', ], 'output' => [ 'shape' => 'DescribeVTLDevicesOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeWorkingStorage' => [ 'name' => 'DescribeWorkingStorage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeWorkingStorageInput', ], 'output' => [ 'shape' => 'DescribeWorkingStorageOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DetachVolume' => [ 'name' => 'DetachVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachVolumeInput', ], 'output' => [ 'shape' => 'DetachVolumeOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DisableGateway' => [ 'name' => 'DisableGateway', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableGatewayInput', ], 'output' => [ 'shape' => 'DisableGatewayOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DisassociateFileSystem' => [ 'name' => 'DisassociateFileSystem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateFileSystemInput', ], 'output' => [ 'shape' => 'DisassociateFileSystemOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'JoinDomain' => [ 'name' => 'JoinDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'JoinDomainInput', ], 'output' => [ 'shape' => 'JoinDomainOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListAutomaticTapeCreationPolicies' => [ 'name' => 'ListAutomaticTapeCreationPolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAutomaticTapeCreationPoliciesInput', ], 'output' => [ 'shape' => 'ListAutomaticTapeCreationPoliciesOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListFileShares' => [ 'name' => 'ListFileShares', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFileSharesInput', ], 'output' => [ 'shape' => 'ListFileSharesOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListFileSystemAssociations' => [ 'name' => 'ListFileSystemAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFileSystemAssociationsInput', ], 'output' => [ 'shape' => 'ListFileSystemAssociationsOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListGateways' => [ 'name' => 'ListGateways', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListGatewaysInput', ], 'output' => [ 'shape' => 'ListGatewaysOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListLocalDisks' => [ 'name' => 'ListLocalDisks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLocalDisksInput', ], 'output' => [ 'shape' => 'ListLocalDisksOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListTapePools' => [ 'name' => 'ListTapePools', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTapePoolsInput', ], 'output' => [ 'shape' => 'ListTapePoolsOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListTapes' => [ 'name' => 'ListTapes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTapesInput', ], 'output' => [ 'shape' => 'ListTapesOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListVolumeInitiators' => [ 'name' => 'ListVolumeInitiators', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListVolumeInitiatorsInput', ], 'output' => [ 'shape' => 'ListVolumeInitiatorsOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListVolumeRecoveryPoints' => [ 'name' => 'ListVolumeRecoveryPoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListVolumeRecoveryPointsInput', ], 'output' => [ 'shape' => 'ListVolumeRecoveryPointsOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListVolumes' => [ 'name' => 'ListVolumes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListVolumesInput', ], 'output' => [ 'shape' => 'ListVolumesOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'NotifyWhenUploaded' => [ 'name' => 'NotifyWhenUploaded', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'NotifyWhenUploadedInput', ], 'output' => [ 'shape' => 'NotifyWhenUploadedOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'RefreshCache' => [ 'name' => 'RefreshCache', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RefreshCacheInput', ], 'output' => [ 'shape' => 'RefreshCacheOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'RemoveTagsFromResource' => [ 'name' => 'RemoveTagsFromResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveTagsFromResourceInput', ], 'output' => [ 'shape' => 'RemoveTagsFromResourceOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ResetCache' => [ 'name' => 'ResetCache', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResetCacheInput', ], 'output' => [ 'shape' => 'ResetCacheOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'RetrieveTapeArchive' => [ 'name' => 'RetrieveTapeArchive', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RetrieveTapeArchiveInput', ], 'output' => [ 'shape' => 'RetrieveTapeArchiveOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'RetrieveTapeRecoveryPoint' => [ 'name' => 'RetrieveTapeRecoveryPoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RetrieveTapeRecoveryPointInput', ], 'output' => [ 'shape' => 'RetrieveTapeRecoveryPointOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'SetLocalConsolePassword' => [ 'name' => 'SetLocalConsolePassword', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetLocalConsolePasswordInput', ], 'output' => [ 'shape' => 'SetLocalConsolePasswordOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'SetSMBGuestPassword' => [ 'name' => 'SetSMBGuestPassword', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetSMBGuestPasswordInput', ], 'output' => [ 'shape' => 'SetSMBGuestPasswordOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ShutdownGateway' => [ 'name' => 'ShutdownGateway', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ShutdownGatewayInput', ], 'output' => [ 'shape' => 'ShutdownGatewayOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'StartAvailabilityMonitorTest' => [ 'name' => 'StartAvailabilityMonitorTest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartAvailabilityMonitorTestInput', ], 'output' => [ 'shape' => 'StartAvailabilityMonitorTestOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'StartGateway' => [ 'name' => 'StartGateway', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartGatewayInput', ], 'output' => [ 'shape' => 'StartGatewayOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateAutomaticTapeCreationPolicy' => [ 'name' => 'UpdateAutomaticTapeCreationPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAutomaticTapeCreationPolicyInput', ], 'output' => [ 'shape' => 'UpdateAutomaticTapeCreationPolicyOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateBandwidthRateLimit' => [ 'name' => 'UpdateBandwidthRateLimit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateBandwidthRateLimitInput', ], 'output' => [ 'shape' => 'UpdateBandwidthRateLimitOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateBandwidthRateLimitSchedule' => [ 'name' => 'UpdateBandwidthRateLimitSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateBandwidthRateLimitScheduleInput', ], 'output' => [ 'shape' => 'UpdateBandwidthRateLimitScheduleOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateChapCredentials' => [ 'name' => 'UpdateChapCredentials', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateChapCredentialsInput', ], 'output' => [ 'shape' => 'UpdateChapCredentialsOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateFileSystemAssociation' => [ 'name' => 'UpdateFileSystemAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFileSystemAssociationInput', ], 'output' => [ 'shape' => 'UpdateFileSystemAssociationOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateGatewayInformation' => [ 'name' => 'UpdateGatewayInformation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateGatewayInformationInput', ], 'output' => [ 'shape' => 'UpdateGatewayInformationOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateGatewaySoftwareNow' => [ 'name' => 'UpdateGatewaySoftwareNow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateGatewaySoftwareNowInput', ], 'output' => [ 'shape' => 'UpdateGatewaySoftwareNowOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateMaintenanceStartTime' => [ 'name' => 'UpdateMaintenanceStartTime', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateMaintenanceStartTimeInput', ], 'output' => [ 'shape' => 'UpdateMaintenanceStartTimeOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateNFSFileShare' => [ 'name' => 'UpdateNFSFileShare', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateNFSFileShareInput', ], 'output' => [ 'shape' => 'UpdateNFSFileShareOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateSMBFileShare' => [ 'name' => 'UpdateSMBFileShare', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSMBFileShareInput', ], 'output' => [ 'shape' => 'UpdateSMBFileShareOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateSMBFileShareVisibility' => [ 'name' => 'UpdateSMBFileShareVisibility', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSMBFileShareVisibilityInput', ], 'output' => [ 'shape' => 'UpdateSMBFileShareVisibilityOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateSMBLocalGroups' => [ 'name' => 'UpdateSMBLocalGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSMBLocalGroupsInput', ], 'output' => [ 'shape' => 'UpdateSMBLocalGroupsOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateSMBSecurityStrategy' => [ 'name' => 'UpdateSMBSecurityStrategy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSMBSecurityStrategyInput', ], 'output' => [ 'shape' => 'UpdateSMBSecurityStrategyOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateSnapshotSchedule' => [ 'name' => 'UpdateSnapshotSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSnapshotScheduleInput', ], 'output' => [ 'shape' => 'UpdateSnapshotScheduleOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateVTLDeviceType' => [ 'name' => 'UpdateVTLDeviceType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateVTLDeviceTypeInput', ], 'output' => [ 'shape' => 'UpdateVTLDeviceTypeOutput', ], 'errors' => [ [ 'shape' => 'InvalidGatewayRequestException', ], [ 'shape' => 'InternalServerError', ], ], ], ], 'shapes' => [ 'ActivateGatewayInput' => [ 'type' => 'structure', 'required' => [ 'ActivationKey', 'GatewayName', 'GatewayTimezone', 'GatewayRegion', ], 'members' => [ 'ActivationKey' => [ 'shape' => 'ActivationKey', ], 'GatewayName' => [ 'shape' => 'GatewayName', ], 'GatewayTimezone' => [ 'shape' => 'GatewayTimezone', ], 'GatewayRegion' => [ 'shape' => 'RegionId', ], 'GatewayType' => [ 'shape' => 'GatewayType', ], 'TapeDriveType' => [ 'shape' => 'TapeDriveType', ], 'MediumChangerType' => [ 'shape' => 'MediumChangerType', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'ActivateGatewayOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'ActivationKey' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'ActiveDirectoryStatus' => [ 'type' => 'string', 'enum' => [ 'ACCESS_DENIED', 'DETACHED', 'JOINED', 'JOINING', 'NETWORK_ERROR', 'TIMEOUT', 'UNKNOWN_ERROR', ], ], 'AddCacheInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'DiskIds', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'DiskIds' => [ 'shape' => 'DiskIds', ], ], ], 'AddCacheOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'AddTagsToResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'AddTagsToResourceOutput' => [ 'type' => 'structure', 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceARN', ], ], ], 'AddUploadBufferInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'DiskIds', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'DiskIds' => [ 'shape' => 'DiskIds', ], ], ], 'AddUploadBufferOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'AddWorkingStorageInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'DiskIds', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'DiskIds' => [ 'shape' => 'DiskIds', ], ], ], 'AddWorkingStorageOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'AssignTapePoolInput' => [ 'type' => 'structure', 'required' => [ 'TapeARN', 'PoolId', ], 'members' => [ 'TapeARN' => [ 'shape' => 'TapeARN', ], 'PoolId' => [ 'shape' => 'PoolId', ], 'BypassGovernanceRetention' => [ 'shape' => 'boolean', ], ], ], 'AssignTapePoolOutput' => [ 'type' => 'structure', 'members' => [ 'TapeARN' => [ 'shape' => 'TapeARN', ], ], ], 'AssociateFileSystemInput' => [ 'type' => 'structure', 'required' => [ 'UserName', 'Password', 'ClientToken', 'GatewayARN', 'LocationARN', ], 'members' => [ 'UserName' => [ 'shape' => 'DomainUserName', ], 'Password' => [ 'shape' => 'DomainUserPassword', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'LocationARN' => [ 'shape' => 'FileSystemLocationARN', ], 'Tags' => [ 'shape' => 'Tags', ], 'AuditDestinationARN' => [ 'shape' => 'AuditDestinationARN', ], 'CacheAttributes' => [ 'shape' => 'CacheAttributes', ], 'EndpointNetworkConfiguration' => [ 'shape' => 'EndpointNetworkConfiguration', ], ], ], 'AssociateFileSystemOutput' => [ 'type' => 'structure', 'members' => [ 'FileSystemAssociationARN' => [ 'shape' => 'FileSystemAssociationARN', ], ], ], 'AttachVolumeInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'VolumeARN', 'NetworkInterfaceId', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'TargetName' => [ 'shape' => 'TargetName', ], 'VolumeARN' => [ 'shape' => 'VolumeARN', ], 'NetworkInterfaceId' => [ 'shape' => 'NetworkInterfaceId', ], 'DiskId' => [ 'shape' => 'DiskId', ], ], ], 'AttachVolumeOutput' => [ 'type' => 'structure', 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], 'TargetARN' => [ 'shape' => 'TargetARN', ], ], ], 'AuditDestinationARN' => [ 'type' => 'string', 'max' => 1024, ], 'Authentication' => [ 'type' => 'string', 'max' => 15, 'min' => 5, ], 'AutomaticTapeCreationPolicyInfo' => [ 'type' => 'structure', 'members' => [ 'AutomaticTapeCreationRules' => [ 'shape' => 'AutomaticTapeCreationRules', ], 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'AutomaticTapeCreationPolicyInfos' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutomaticTapeCreationPolicyInfo', ], ], 'AutomaticTapeCreationRule' => [ 'type' => 'structure', 'required' => [ 'TapeBarcodePrefix', 'PoolId', 'TapeSizeInBytes', 'MinimumNumTapes', ], 'members' => [ 'TapeBarcodePrefix' => [ 'shape' => 'TapeBarcodePrefix', ], 'PoolId' => [ 'shape' => 'PoolId', ], 'TapeSizeInBytes' => [ 'shape' => 'TapeSize', ], 'MinimumNumTapes' => [ 'shape' => 'MinimumNumTapes', ], 'Worm' => [ 'shape' => 'boolean', ], ], ], 'AutomaticTapeCreationRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutomaticTapeCreationRule', ], 'max' => 10, 'min' => 1, ], 'AvailabilityMonitorTestStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETE', 'FAILED', 'PENDING', ], ], 'BandwidthDownloadRateLimit' => [ 'type' => 'long', 'min' => 102400, ], 'BandwidthRateLimitInterval' => [ 'type' => 'structure', 'required' => [ 'StartHourOfDay', 'StartMinuteOfHour', 'EndHourOfDay', 'EndMinuteOfHour', 'DaysOfWeek', ], 'members' => [ 'StartHourOfDay' => [ 'shape' => 'HourOfDay', ], 'StartMinuteOfHour' => [ 'shape' => 'MinuteOfHour', ], 'EndHourOfDay' => [ 'shape' => 'HourOfDay', ], 'EndMinuteOfHour' => [ 'shape' => 'MinuteOfHour', ], 'DaysOfWeek' => [ 'shape' => 'DaysOfWeek', ], 'AverageUploadRateLimitInBitsPerSec' => [ 'shape' => 'BandwidthUploadRateLimit', ], 'AverageDownloadRateLimitInBitsPerSec' => [ 'shape' => 'BandwidthDownloadRateLimit', ], ], ], 'BandwidthRateLimitIntervals' => [ 'type' => 'list', 'member' => [ 'shape' => 'BandwidthRateLimitInterval', ], 'max' => 20, 'min' => 0, ], 'BandwidthType' => [ 'type' => 'string', 'max' => 25, 'min' => 3, ], 'BandwidthUploadRateLimit' => [ 'type' => 'long', 'min' => 51200, ], 'Boolean' => [ 'type' => 'boolean', ], 'CacheAttributes' => [ 'type' => 'structure', 'members' => [ 'CacheStaleTimeoutInSeconds' => [ 'shape' => 'CacheStaleTimeoutInSeconds', ], ], ], 'CacheStaleTimeoutInSeconds' => [ 'type' => 'integer', ], 'CachediSCSIVolume' => [ 'type' => 'structure', 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], 'VolumeId' => [ 'shape' => 'VolumeId', ], 'VolumeType' => [ 'shape' => 'VolumeType', ], 'VolumeStatus' => [ 'shape' => 'VolumeStatus', ], 'VolumeAttachmentStatus' => [ 'shape' => 'VolumeAttachmentStatus', ], 'VolumeSizeInBytes' => [ 'shape' => 'long', ], 'VolumeProgress' => [ 'shape' => 'DoubleObject', ], 'SourceSnapshotId' => [ 'shape' => 'SnapshotId', ], 'VolumeiSCSIAttributes' => [ 'shape' => 'VolumeiSCSIAttributes', ], 'CreatedDate' => [ 'shape' => 'CreatedDate', ], 'VolumeUsedInBytes' => [ 'shape' => 'VolumeUsedInBytes', ], 'KMSKey' => [ 'shape' => 'KMSKey', ], 'TargetName' => [ 'shape' => 'TargetName', ], ], ], 'CachediSCSIVolumes' => [ 'type' => 'list', 'member' => [ 'shape' => 'CachediSCSIVolume', ], ], 'CancelArchivalInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'TapeARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'TapeARN' => [ 'shape' => 'TapeARN', ], ], ], 'CancelArchivalOutput' => [ 'type' => 'structure', 'members' => [ 'TapeARN' => [ 'shape' => 'TapeARN', ], ], ], 'CancelRetrievalInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'TapeARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'TapeARN' => [ 'shape' => 'TapeARN', ], ], ], 'CancelRetrievalOutput' => [ 'type' => 'structure', 'members' => [ 'TapeARN' => [ 'shape' => 'TapeARN', ], ], ], 'CaseSensitivity' => [ 'type' => 'string', 'enum' => [ 'ClientSpecified', 'CaseSensitive', ], ], 'ChapCredentials' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChapInfo', ], ], 'ChapInfo' => [ 'type' => 'structure', 'members' => [ 'TargetARN' => [ 'shape' => 'TargetARN', ], 'SecretToAuthenticateInitiator' => [ 'shape' => 'ChapSecret', ], 'InitiatorName' => [ 'shape' => 'IqnName', ], 'SecretToAuthenticateTarget' => [ 'shape' => 'ChapSecret', ], ], ], 'ChapSecret' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'ClientToken' => [ 'type' => 'string', 'max' => 100, 'min' => 5, ], 'CloudWatchLogGroupARN' => [ 'type' => 'string', 'max' => 562, ], 'CreateCachediSCSIVolumeInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'VolumeSizeInBytes', 'TargetName', 'NetworkInterfaceId', 'ClientToken', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'VolumeSizeInBytes' => [ 'shape' => 'long', ], 'SnapshotId' => [ 'shape' => 'SnapshotId', ], 'TargetName' => [ 'shape' => 'TargetName', ], 'SourceVolumeARN' => [ 'shape' => 'VolumeARN', ], 'NetworkInterfaceId' => [ 'shape' => 'NetworkInterfaceId', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], 'KMSEncrypted' => [ 'shape' => 'Boolean', ], 'KMSKey' => [ 'shape' => 'KMSKey', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateCachediSCSIVolumeOutput' => [ 'type' => 'structure', 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], 'TargetARN' => [ 'shape' => 'TargetARN', ], ], ], 'CreateNFSFileShareInput' => [ 'type' => 'structure', 'required' => [ 'ClientToken', 'GatewayARN', 'Role', 'LocationARN', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', ], 'NFSFileShareDefaults' => [ 'shape' => 'NFSFileShareDefaults', ], 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'KMSEncrypted' => [ 'shape' => 'Boolean', ], 'KMSKey' => [ 'shape' => 'KMSKey', ], 'Role' => [ 'shape' => 'Role', ], 'LocationARN' => [ 'shape' => 'LocationARN', ], 'DefaultStorageClass' => [ 'shape' => 'StorageClass', ], 'ObjectACL' => [ 'shape' => 'ObjectACL', ], 'ClientList' => [ 'shape' => 'FileShareClientList', ], 'Squash' => [ 'shape' => 'Squash', ], 'ReadOnly' => [ 'shape' => 'Boolean', ], 'GuessMIMETypeEnabled' => [ 'shape' => 'Boolean', ], 'RequesterPays' => [ 'shape' => 'Boolean', ], 'Tags' => [ 'shape' => 'Tags', ], 'FileShareName' => [ 'shape' => 'FileShareName', ], 'CacheAttributes' => [ 'shape' => 'CacheAttributes', ], 'NotificationPolicy' => [ 'shape' => 'NotificationPolicy', ], 'VPCEndpointDNSName' => [ 'shape' => 'DNSHostName', ], 'BucketRegion' => [ 'shape' => 'RegionId', ], 'AuditDestinationARN' => [ 'shape' => 'AuditDestinationARN', ], ], ], 'CreateNFSFileShareOutput' => [ 'type' => 'structure', 'members' => [ 'FileShareARN' => [ 'shape' => 'FileShareARN', ], ], ], 'CreateSMBFileShareInput' => [ 'type' => 'structure', 'required' => [ 'ClientToken', 'GatewayARN', 'Role', 'LocationARN', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', ], 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'KMSEncrypted' => [ 'shape' => 'Boolean', ], 'KMSKey' => [ 'shape' => 'KMSKey', ], 'Role' => [ 'shape' => 'Role', ], 'LocationARN' => [ 'shape' => 'LocationARN', ], 'DefaultStorageClass' => [ 'shape' => 'StorageClass', ], 'ObjectACL' => [ 'shape' => 'ObjectACL', ], 'ReadOnly' => [ 'shape' => 'Boolean', ], 'GuessMIMETypeEnabled' => [ 'shape' => 'Boolean', ], 'RequesterPays' => [ 'shape' => 'Boolean', ], 'SMBACLEnabled' => [ 'shape' => 'Boolean', ], 'AccessBasedEnumeration' => [ 'shape' => 'Boolean', ], 'AdminUserList' => [ 'shape' => 'UserList', ], 'ValidUserList' => [ 'shape' => 'UserList', ], 'InvalidUserList' => [ 'shape' => 'UserList', ], 'AuditDestinationARN' => [ 'shape' => 'AuditDestinationARN', ], 'Authentication' => [ 'shape' => 'Authentication', ], 'CaseSensitivity' => [ 'shape' => 'CaseSensitivity', ], 'Tags' => [ 'shape' => 'Tags', ], 'FileShareName' => [ 'shape' => 'FileShareName', ], 'CacheAttributes' => [ 'shape' => 'CacheAttributes', ], 'NotificationPolicy' => [ 'shape' => 'NotificationPolicy', ], 'VPCEndpointDNSName' => [ 'shape' => 'DNSHostName', ], 'BucketRegion' => [ 'shape' => 'RegionId', ], 'OplocksEnabled' => [ 'shape' => 'Boolean', ], ], ], 'CreateSMBFileShareOutput' => [ 'type' => 'structure', 'members' => [ 'FileShareARN' => [ 'shape' => 'FileShareARN', ], ], ], 'CreateSnapshotFromVolumeRecoveryPointInput' => [ 'type' => 'structure', 'required' => [ 'VolumeARN', 'SnapshotDescription', ], 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], 'SnapshotDescription' => [ 'shape' => 'SnapshotDescription', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateSnapshotFromVolumeRecoveryPointOutput' => [ 'type' => 'structure', 'members' => [ 'SnapshotId' => [ 'shape' => 'SnapshotId', ], 'VolumeARN' => [ 'shape' => 'VolumeARN', ], 'VolumeRecoveryPointTime' => [ 'shape' => 'string', ], ], ], 'CreateSnapshotInput' => [ 'type' => 'structure', 'required' => [ 'VolumeARN', 'SnapshotDescription', ], 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], 'SnapshotDescription' => [ 'shape' => 'SnapshotDescription', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateSnapshotOutput' => [ 'type' => 'structure', 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], 'SnapshotId' => [ 'shape' => 'SnapshotId', ], ], ], 'CreateStorediSCSIVolumeInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'DiskId', 'PreserveExistingData', 'TargetName', 'NetworkInterfaceId', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'DiskId' => [ 'shape' => 'DiskId', ], 'SnapshotId' => [ 'shape' => 'SnapshotId', ], 'PreserveExistingData' => [ 'shape' => 'boolean', ], 'TargetName' => [ 'shape' => 'TargetName', ], 'NetworkInterfaceId' => [ 'shape' => 'NetworkInterfaceId', ], 'KMSEncrypted' => [ 'shape' => 'Boolean', ], 'KMSKey' => [ 'shape' => 'KMSKey', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateStorediSCSIVolumeOutput' => [ 'type' => 'structure', 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], 'VolumeSizeInBytes' => [ 'shape' => 'long', ], 'TargetARN' => [ 'shape' => 'TargetARN', ], ], ], 'CreateTapePoolInput' => [ 'type' => 'structure', 'required' => [ 'PoolName', 'StorageClass', ], 'members' => [ 'PoolName' => [ 'shape' => 'PoolName', ], 'StorageClass' => [ 'shape' => 'TapeStorageClass', ], 'RetentionLockType' => [ 'shape' => 'RetentionLockType', ], 'RetentionLockTimeInDays' => [ 'shape' => 'RetentionLockTimeInDays', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateTapePoolOutput' => [ 'type' => 'structure', 'members' => [ 'PoolARN' => [ 'shape' => 'PoolARN', ], ], ], 'CreateTapeWithBarcodeInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'TapeSizeInBytes', 'TapeBarcode', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'TapeSizeInBytes' => [ 'shape' => 'TapeSize', ], 'TapeBarcode' => [ 'shape' => 'TapeBarcode', ], 'KMSEncrypted' => [ 'shape' => 'Boolean', ], 'KMSKey' => [ 'shape' => 'KMSKey', ], 'PoolId' => [ 'shape' => 'PoolId', ], 'Worm' => [ 'shape' => 'boolean', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateTapeWithBarcodeOutput' => [ 'type' => 'structure', 'members' => [ 'TapeARN' => [ 'shape' => 'TapeARN', ], ], ], 'CreateTapesInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'TapeSizeInBytes', 'ClientToken', 'NumTapesToCreate', 'TapeBarcodePrefix', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'TapeSizeInBytes' => [ 'shape' => 'TapeSize', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], 'NumTapesToCreate' => [ 'shape' => 'NumTapesToCreate', ], 'TapeBarcodePrefix' => [ 'shape' => 'TapeBarcodePrefix', ], 'KMSEncrypted' => [ 'shape' => 'Boolean', ], 'KMSKey' => [ 'shape' => 'KMSKey', ], 'PoolId' => [ 'shape' => 'PoolId', ], 'Worm' => [ 'shape' => 'boolean', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateTapesOutput' => [ 'type' => 'structure', 'members' => [ 'TapeARNs' => [ 'shape' => 'TapeARNs', ], ], ], 'CreatedDate' => [ 'type' => 'timestamp', ], 'DNSHostName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(([a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9\\-]*[A-Za-z0-9])$', ], 'DayOfMonth' => [ 'type' => 'integer', 'max' => 28, 'min' => 1, ], 'DayOfWeek' => [ 'type' => 'integer', 'max' => 6, 'min' => 0, ], 'DaysOfWeek' => [ 'type' => 'list', 'member' => [ 'shape' => 'DayOfWeek', ], 'max' => 7, 'min' => 1, ], 'DeleteAutomaticTapeCreationPolicyInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'DeleteAutomaticTapeCreationPolicyOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'DeleteBandwidthRateLimitInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'BandwidthType', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'BandwidthType' => [ 'shape' => 'BandwidthType', ], ], ], 'DeleteBandwidthRateLimitOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'DeleteChapCredentialsInput' => [ 'type' => 'structure', 'required' => [ 'TargetARN', 'InitiatorName', ], 'members' => [ 'TargetARN' => [ 'shape' => 'TargetARN', ], 'InitiatorName' => [ 'shape' => 'IqnName', ], ], ], 'DeleteChapCredentialsOutput' => [ 'type' => 'structure', 'members' => [ 'TargetARN' => [ 'shape' => 'TargetARN', ], 'InitiatorName' => [ 'shape' => 'IqnName', ], ], ], 'DeleteFileShareInput' => [ 'type' => 'structure', 'required' => [ 'FileShareARN', ], 'members' => [ 'FileShareARN' => [ 'shape' => 'FileShareARN', ], 'ForceDelete' => [ 'shape' => 'boolean', ], ], ], 'DeleteFileShareOutput' => [ 'type' => 'structure', 'members' => [ 'FileShareARN' => [ 'shape' => 'FileShareARN', ], ], ], 'DeleteGatewayInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'DeleteGatewayOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'DeleteSnapshotScheduleInput' => [ 'type' => 'structure', 'required' => [ 'VolumeARN', ], 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], ], ], 'DeleteSnapshotScheduleOutput' => [ 'type' => 'structure', 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], ], ], 'DeleteTapeArchiveInput' => [ 'type' => 'structure', 'required' => [ 'TapeARN', ], 'members' => [ 'TapeARN' => [ 'shape' => 'TapeARN', ], 'BypassGovernanceRetention' => [ 'shape' => 'boolean', ], ], ], 'DeleteTapeArchiveOutput' => [ 'type' => 'structure', 'members' => [ 'TapeARN' => [ 'shape' => 'TapeARN', ], ], ], 'DeleteTapeInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'TapeARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'TapeARN' => [ 'shape' => 'TapeARN', ], 'BypassGovernanceRetention' => [ 'shape' => 'boolean', ], ], ], 'DeleteTapeOutput' => [ 'type' => 'structure', 'members' => [ 'TapeARN' => [ 'shape' => 'TapeARN', ], ], ], 'DeleteTapePoolInput' => [ 'type' => 'structure', 'required' => [ 'PoolARN', ], 'members' => [ 'PoolARN' => [ 'shape' => 'PoolARN', ], ], ], 'DeleteTapePoolOutput' => [ 'type' => 'structure', 'members' => [ 'PoolARN' => [ 'shape' => 'PoolARN', ], ], ], 'DeleteVolumeInput' => [ 'type' => 'structure', 'required' => [ 'VolumeARN', ], 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], ], ], 'DeleteVolumeOutput' => [ 'type' => 'structure', 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], ], ], 'DeprecationDate' => [ 'type' => 'string', 'max' => 25, 'min' => 1, ], 'DescribeAvailabilityMonitorTestInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'DescribeAvailabilityMonitorTestOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'Status' => [ 'shape' => 'AvailabilityMonitorTestStatus', ], 'StartTime' => [ 'shape' => 'Time', ], ], ], 'DescribeBandwidthRateLimitInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'DescribeBandwidthRateLimitOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'AverageUploadRateLimitInBitsPerSec' => [ 'shape' => 'BandwidthUploadRateLimit', ], 'AverageDownloadRateLimitInBitsPerSec' => [ 'shape' => 'BandwidthDownloadRateLimit', ], ], ], 'DescribeBandwidthRateLimitScheduleInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'DescribeBandwidthRateLimitScheduleOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'BandwidthRateLimitIntervals' => [ 'shape' => 'BandwidthRateLimitIntervals', ], ], ], 'DescribeCacheInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'DescribeCacheOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'DiskIds' => [ 'shape' => 'DiskIds', ], 'CacheAllocatedInBytes' => [ 'shape' => 'long', ], 'CacheUsedPercentage' => [ 'shape' => 'double', ], 'CacheDirtyPercentage' => [ 'shape' => 'double', ], 'CacheHitPercentage' => [ 'shape' => 'double', ], 'CacheMissPercentage' => [ 'shape' => 'double', ], ], ], 'DescribeCachediSCSIVolumesInput' => [ 'type' => 'structure', 'required' => [ 'VolumeARNs', ], 'members' => [ 'VolumeARNs' => [ 'shape' => 'VolumeARNs', ], ], ], 'DescribeCachediSCSIVolumesOutput' => [ 'type' => 'structure', 'members' => [ 'CachediSCSIVolumes' => [ 'shape' => 'CachediSCSIVolumes', ], ], ], 'DescribeChapCredentialsInput' => [ 'type' => 'structure', 'required' => [ 'TargetARN', ], 'members' => [ 'TargetARN' => [ 'shape' => 'TargetARN', ], ], ], 'DescribeChapCredentialsOutput' => [ 'type' => 'structure', 'members' => [ 'ChapCredentials' => [ 'shape' => 'ChapCredentials', ], ], ], 'DescribeFileSystemAssociationsInput' => [ 'type' => 'structure', 'required' => [ 'FileSystemAssociationARNList', ], 'members' => [ 'FileSystemAssociationARNList' => [ 'shape' => 'FileSystemAssociationARNList', ], ], ], 'DescribeFileSystemAssociationsOutput' => [ 'type' => 'structure', 'members' => [ 'FileSystemAssociationInfoList' => [ 'shape' => 'FileSystemAssociationInfoList', ], ], ], 'DescribeGatewayInformationInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'DescribeGatewayInformationOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'GatewayId' => [ 'shape' => 'GatewayId', ], 'GatewayName' => [ 'shape' => 'string', ], 'GatewayTimezone' => [ 'shape' => 'GatewayTimezone', ], 'GatewayState' => [ 'shape' => 'GatewayState', ], 'GatewayNetworkInterfaces' => [ 'shape' => 'GatewayNetworkInterfaces', ], 'GatewayType' => [ 'shape' => 'GatewayType', ], 'NextUpdateAvailabilityDate' => [ 'shape' => 'NextUpdateAvailabilityDate', ], 'LastSoftwareUpdate' => [ 'shape' => 'LastSoftwareUpdate', ], 'Ec2InstanceId' => [ 'shape' => 'Ec2InstanceId', ], 'Ec2InstanceRegion' => [ 'shape' => 'Ec2InstanceRegion', ], 'Tags' => [ 'shape' => 'Tags', ], 'VPCEndpoint' => [ 'shape' => 'string', ], 'CloudWatchLogGroupARN' => [ 'shape' => 'CloudWatchLogGroupARN', ], 'HostEnvironment' => [ 'shape' => 'HostEnvironment', ], 'EndpointType' => [ 'shape' => 'EndpointType', ], 'SoftwareUpdatesEndDate' => [ 'shape' => 'SoftwareUpdatesEndDate', ], 'DeprecationDate' => [ 'shape' => 'DeprecationDate', ], 'GatewayCapacity' => [ 'shape' => 'GatewayCapacity', ], 'SupportedGatewayCapacities' => [ 'shape' => 'SupportedGatewayCapacities', ], 'HostEnvironmentId' => [ 'shape' => 'HostEnvironmentId', ], ], ], 'DescribeMaintenanceStartTimeInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'DescribeMaintenanceStartTimeOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'HourOfDay' => [ 'shape' => 'HourOfDay', ], 'MinuteOfHour' => [ 'shape' => 'MinuteOfHour', ], 'DayOfWeek' => [ 'shape' => 'DayOfWeek', ], 'DayOfMonth' => [ 'shape' => 'DayOfMonth', ], 'Timezone' => [ 'shape' => 'GatewayTimezone', ], ], ], 'DescribeNFSFileSharesInput' => [ 'type' => 'structure', 'required' => [ 'FileShareARNList', ], 'members' => [ 'FileShareARNList' => [ 'shape' => 'FileShareARNList', ], ], ], 'DescribeNFSFileSharesOutput' => [ 'type' => 'structure', 'members' => [ 'NFSFileShareInfoList' => [ 'shape' => 'NFSFileShareInfoList', ], ], ], 'DescribeSMBFileSharesInput' => [ 'type' => 'structure', 'required' => [ 'FileShareARNList', ], 'members' => [ 'FileShareARNList' => [ 'shape' => 'FileShareARNList', ], ], ], 'DescribeSMBFileSharesOutput' => [ 'type' => 'structure', 'members' => [ 'SMBFileShareInfoList' => [ 'shape' => 'SMBFileShareInfoList', ], ], ], 'DescribeSMBSettingsInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'DescribeSMBSettingsOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'ActiveDirectoryStatus' => [ 'shape' => 'ActiveDirectoryStatus', ], 'SMBGuestPasswordSet' => [ 'shape' => 'Boolean', ], 'SMBSecurityStrategy' => [ 'shape' => 'SMBSecurityStrategy', ], 'FileSharesVisible' => [ 'shape' => 'Boolean', ], 'SMBLocalGroups' => [ 'shape' => 'SMBLocalGroups', ], ], ], 'DescribeSnapshotScheduleInput' => [ 'type' => 'structure', 'required' => [ 'VolumeARN', ], 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], ], ], 'DescribeSnapshotScheduleOutput' => [ 'type' => 'structure', 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], 'StartAt' => [ 'shape' => 'HourOfDay', ], 'RecurrenceInHours' => [ 'shape' => 'RecurrenceInHours', ], 'Description' => [ 'shape' => 'Description', ], 'Timezone' => [ 'shape' => 'GatewayTimezone', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'DescribeStorediSCSIVolumesInput' => [ 'type' => 'structure', 'required' => [ 'VolumeARNs', ], 'members' => [ 'VolumeARNs' => [ 'shape' => 'VolumeARNs', ], ], ], 'DescribeStorediSCSIVolumesOutput' => [ 'type' => 'structure', 'members' => [ 'StorediSCSIVolumes' => [ 'shape' => 'StorediSCSIVolumes', ], ], ], 'DescribeTapeArchivesInput' => [ 'type' => 'structure', 'members' => [ 'TapeARNs' => [ 'shape' => 'TapeARNs', ], 'Marker' => [ 'shape' => 'Marker', ], 'Limit' => [ 'shape' => 'PositiveIntObject', ], ], ], 'DescribeTapeArchivesOutput' => [ 'type' => 'structure', 'members' => [ 'TapeArchives' => [ 'shape' => 'TapeArchives', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'DescribeTapeRecoveryPointsInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'Marker' => [ 'shape' => 'Marker', ], 'Limit' => [ 'shape' => 'PositiveIntObject', ], ], ], 'DescribeTapeRecoveryPointsOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'TapeRecoveryPointInfos' => [ 'shape' => 'TapeRecoveryPointInfos', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'DescribeTapesInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'TapeARNs' => [ 'shape' => 'TapeARNs', ], 'Marker' => [ 'shape' => 'Marker', ], 'Limit' => [ 'shape' => 'PositiveIntObject', ], ], ], 'DescribeTapesOutput' => [ 'type' => 'structure', 'members' => [ 'Tapes' => [ 'shape' => 'Tapes', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'DescribeUploadBufferInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'DescribeUploadBufferOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'DiskIds' => [ 'shape' => 'DiskIds', ], 'UploadBufferUsedInBytes' => [ 'shape' => 'long', ], 'UploadBufferAllocatedInBytes' => [ 'shape' => 'long', ], ], ], 'DescribeVTLDevicesInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'VTLDeviceARNs' => [ 'shape' => 'VTLDeviceARNs', ], 'Marker' => [ 'shape' => 'Marker', ], 'Limit' => [ 'shape' => 'PositiveIntObject', ], ], ], 'DescribeVTLDevicesOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'VTLDevices' => [ 'shape' => 'VTLDevices', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'DescribeWorkingStorageInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'DescribeWorkingStorageOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'DiskIds' => [ 'shape' => 'DiskIds', ], 'WorkingStorageUsedInBytes' => [ 'shape' => 'long', ], 'WorkingStorageAllocatedInBytes' => [ 'shape' => 'long', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DetachVolumeInput' => [ 'type' => 'structure', 'required' => [ 'VolumeARN', ], 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], 'ForceDetach' => [ 'shape' => 'Boolean', ], ], ], 'DetachVolumeOutput' => [ 'type' => 'structure', 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], ], ], 'DeviceType' => [ 'type' => 'string', 'max' => 50, 'min' => 2, ], 'DeviceiSCSIAttributes' => [ 'type' => 'structure', 'members' => [ 'TargetARN' => [ 'shape' => 'TargetARN', ], 'NetworkInterfaceId' => [ 'shape' => 'NetworkInterfaceId', ], 'NetworkInterfacePort' => [ 'shape' => 'integer', ], 'ChapEnabled' => [ 'shape' => 'boolean', ], ], ], 'DisableGatewayInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'DisableGatewayOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'DisassociateFileSystemInput' => [ 'type' => 'structure', 'required' => [ 'FileSystemAssociationARN', ], 'members' => [ 'FileSystemAssociationARN' => [ 'shape' => 'FileSystemAssociationARN', ], 'ForceDelete' => [ 'shape' => 'boolean', ], ], ], 'DisassociateFileSystemOutput' => [ 'type' => 'structure', 'members' => [ 'FileSystemAssociationARN' => [ 'shape' => 'FileSystemAssociationARN', ], ], ], 'Disk' => [ 'type' => 'structure', 'members' => [ 'DiskId' => [ 'shape' => 'DiskId', ], 'DiskPath' => [ 'shape' => 'string', ], 'DiskNode' => [ 'shape' => 'string', ], 'DiskStatus' => [ 'shape' => 'string', ], 'DiskSizeInBytes' => [ 'shape' => 'long', ], 'DiskAllocationType' => [ 'shape' => 'DiskAllocationType', ], 'DiskAllocationResource' => [ 'shape' => 'string', ], 'DiskAttributeList' => [ 'shape' => 'DiskAttributeList', ], ], ], 'DiskAllocationType' => [ 'type' => 'string', 'max' => 100, 'min' => 3, ], 'DiskAttribute' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'DiskAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DiskAttribute', ], 'max' => 10, 'min' => 0, ], 'DiskId' => [ 'type' => 'string', 'max' => 300, 'min' => 1, ], 'DiskIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'DiskId', ], ], 'Disks' => [ 'type' => 'list', 'member' => [ 'shape' => 'Disk', ], ], 'DomainName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^([a-zA-Z0-9]+[\\\\.-])+([a-zA-Z0-9])+$', ], 'DomainUserName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^\\w[\\w\\.\\- ]*$', ], 'DomainUserPassword' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[ -~]+$', 'sensitive' => true, ], 'DoubleObject' => [ 'type' => 'double', ], 'Ec2InstanceId' => [ 'type' => 'string', ], 'Ec2InstanceRegion' => [ 'type' => 'string', ], 'EndpointNetworkConfiguration' => [ 'type' => 'structure', 'members' => [ 'IpAddresses' => [ 'shape' => 'IpAddressList', ], ], ], 'EndpointType' => [ 'type' => 'string', 'max' => 8, 'min' => 4, ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'ActivationKeyExpired', 'ActivationKeyInvalid', 'ActivationKeyNotFound', 'GatewayInternalError', 'GatewayNotConnected', 'GatewayNotFound', 'GatewayProxyNetworkConnectionBusy', 'AuthenticationFailure', 'BandwidthThrottleScheduleNotFound', 'Blocked', 'CannotExportSnapshot', 'ChapCredentialNotFound', 'DiskAlreadyAllocated', 'DiskDoesNotExist', 'DiskSizeGreaterThanVolumeMaxSize', 'DiskSizeLessThanVolumeSize', 'DiskSizeNotGigAligned', 'DuplicateCertificateInfo', 'DuplicateSchedule', 'EndpointNotFound', 'IAMNotSupported', 'InitiatorInvalid', 'InitiatorNotFound', 'InternalError', 'InvalidGateway', 'InvalidEndpoint', 'InvalidParameters', 'InvalidSchedule', 'LocalStorageLimitExceeded', 'LunAlreadyAllocated ', 'LunInvalid', 'JoinDomainInProgress', 'MaximumContentLengthExceeded', 'MaximumTapeCartridgeCountExceeded', 'MaximumVolumeCountExceeded', 'NetworkConfigurationChanged', 'NoDisksAvailable', 'NotImplemented', 'NotSupported', 'OperationAborted', 'OutdatedGateway', 'ParametersNotImplemented', 'RegionInvalid', 'RequestTimeout', 'ServiceUnavailable', 'SnapshotDeleted', 'SnapshotIdInvalid', 'SnapshotInProgress', 'SnapshotNotFound', 'SnapshotScheduleNotFound', 'StagingAreaFull', 'StorageFailure', 'TapeCartridgeNotFound', 'TargetAlreadyExists', 'TargetInvalid', 'TargetNotFound', 'UnauthorizedOperation', 'VolumeAlreadyExists', 'VolumeIdInvalid', 'VolumeInUse', 'VolumeNotFound', 'VolumeNotReady', ], ], 'FileShareARN' => [ 'type' => 'string', 'max' => 500, 'min' => 50, ], 'FileShareARNList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileShareARN', ], 'max' => 10, 'min' => 1, ], 'FileShareClientList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IPV4AddressCIDR', ], 'max' => 100, 'min' => 1, ], 'FileShareId' => [ 'type' => 'string', 'max' => 30, 'min' => 12, ], 'FileShareInfo' => [ 'type' => 'structure', 'members' => [ 'FileShareType' => [ 'shape' => 'FileShareType', ], 'FileShareARN' => [ 'shape' => 'FileShareARN', ], 'FileShareId' => [ 'shape' => 'FileShareId', ], 'FileShareStatus' => [ 'shape' => 'FileShareStatus', ], 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'FileShareInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileShareInfo', ], ], 'FileShareName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'FileShareStatus' => [ 'type' => 'string', 'max' => 50, 'min' => 3, ], 'FileShareType' => [ 'type' => 'string', 'enum' => [ 'NFS', 'SMB', ], ], 'FileSystemAssociationARN' => [ 'type' => 'string', 'max' => 500, 'min' => 50, ], 'FileSystemAssociationARNList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileSystemAssociationARN', ], 'max' => 10, 'min' => 1, ], 'FileSystemAssociationId' => [ 'type' => 'string', 'max' => 30, 'min' => 10, ], 'FileSystemAssociationInfo' => [ 'type' => 'structure', 'members' => [ 'FileSystemAssociationARN' => [ 'shape' => 'FileSystemAssociationARN', ], 'LocationARN' => [ 'shape' => 'FileSystemLocationARN', ], 'FileSystemAssociationStatus' => [ 'shape' => 'FileSystemAssociationStatus', ], 'AuditDestinationARN' => [ 'shape' => 'AuditDestinationARN', ], 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'Tags' => [ 'shape' => 'Tags', ], 'CacheAttributes' => [ 'shape' => 'CacheAttributes', ], 'EndpointNetworkConfiguration' => [ 'shape' => 'EndpointNetworkConfiguration', ], 'FileSystemAssociationStatusDetails' => [ 'shape' => 'FileSystemAssociationStatusDetails', ], ], ], 'FileSystemAssociationInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileSystemAssociationInfo', ], ], 'FileSystemAssociationStatus' => [ 'type' => 'string', 'max' => 50, 'min' => 3, ], 'FileSystemAssociationStatusDetail' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'FileSystemAssociationSyncErrorCode', ], ], ], 'FileSystemAssociationStatusDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileSystemAssociationStatusDetail', ], ], 'FileSystemAssociationSummary' => [ 'type' => 'structure', 'members' => [ 'FileSystemAssociationId' => [ 'shape' => 'FileSystemAssociationId', ], 'FileSystemAssociationARN' => [ 'shape' => 'FileSystemAssociationARN', ], 'FileSystemAssociationStatus' => [ 'shape' => 'FileSystemAssociationStatus', ], 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'FileSystemAssociationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileSystemAssociationSummary', ], ], 'FileSystemAssociationSyncErrorCode' => [ 'type' => 'string', ], 'FileSystemLocationARN' => [ 'type' => 'string', 'max' => 512, 'min' => 8, ], 'Folder' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'FolderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Folder', ], 'max' => 50, 'min' => 1, ], 'GatewayARN' => [ 'type' => 'string', 'max' => 500, 'min' => 50, ], 'GatewayCapacity' => [ 'type' => 'string', 'enum' => [ 'Small', 'Medium', 'Large', ], ], 'GatewayId' => [ 'type' => 'string', 'max' => 30, 'min' => 12, ], 'GatewayInfo' => [ 'type' => 'structure', 'members' => [ 'GatewayId' => [ 'shape' => 'GatewayId', ], 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'GatewayType' => [ 'shape' => 'GatewayType', ], 'GatewayOperationalState' => [ 'shape' => 'GatewayOperationalState', ], 'GatewayName' => [ 'shape' => 'string', ], 'Ec2InstanceId' => [ 'shape' => 'Ec2InstanceId', ], 'Ec2InstanceRegion' => [ 'shape' => 'Ec2InstanceRegion', ], 'HostEnvironment' => [ 'shape' => 'HostEnvironment', ], 'HostEnvironmentId' => [ 'shape' => 'HostEnvironmentId', ], ], ], 'GatewayName' => [ 'type' => 'string', 'max' => 255, 'min' => 2, 'pattern' => '^[ -\\.0-\\[\\]-~]*[!-\\.0-\\[\\]-~][ -\\.0-\\[\\]-~]*$', ], 'GatewayNetworkInterfaces' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkInterface', ], ], 'GatewayOperationalState' => [ 'type' => 'string', 'max' => 25, 'min' => 2, ], 'GatewayState' => [ 'type' => 'string', 'max' => 25, 'min' => 2, ], 'GatewayTimezone' => [ 'type' => 'string', 'max' => 10, 'min' => 3, ], 'GatewayType' => [ 'type' => 'string', 'max' => 20, 'min' => 2, ], 'Gateways' => [ 'type' => 'list', 'member' => [ 'shape' => 'GatewayInfo', ], ], 'Host' => [ 'type' => 'string', 'max' => 1024, 'min' => 6, 'pattern' => '^(([a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9\\-]*[A-Za-z0-9])(:(\\d+))?$', ], 'HostEnvironment' => [ 'type' => 'string', 'enum' => [ 'VMWARE', 'HYPER-V', 'EC2', 'KVM', 'OTHER', 'SNOWBALL', ], ], 'HostEnvironmentId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'Hosts' => [ 'type' => 'list', 'member' => [ 'shape' => 'Host', ], ], 'HourOfDay' => [ 'type' => 'integer', 'max' => 23, 'min' => 0, ], 'IPV4Address' => [ 'type' => 'string', 'max' => 15, 'min' => 7, 'pattern' => '^((25[0-5]|(2[0-4]|1[0-9]|[1-9]|)[0-9])(\\.(?!$)|$)){4}', ], 'IPV4AddressCIDR' => [ 'type' => 'string', 'pattern' => '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\/([0-9]|[1-2][0-9]|3[0-2]))?$', ], 'Initiator' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'Initiators' => [ 'type' => 'list', 'member' => [ 'shape' => 'Initiator', ], ], 'InternalServerError' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], 'error' => [ 'shape' => 'StorageGatewayError', ], ], 'exception' => true, ], 'InvalidGatewayRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], 'error' => [ 'shape' => 'StorageGatewayError', ], ], 'exception' => true, ], 'IpAddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IPV4Address', ], 'max' => 1, 'min' => 0, ], 'IqnName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[0-9a-z:.-]+', ], 'JoinDomainInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'DomainName', 'UserName', 'Password', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'OrganizationalUnit' => [ 'shape' => 'OrganizationalUnit', ], 'DomainControllers' => [ 'shape' => 'Hosts', ], 'TimeoutInSeconds' => [ 'shape' => 'TimeoutInSeconds', ], 'UserName' => [ 'shape' => 'DomainUserName', ], 'Password' => [ 'shape' => 'DomainUserPassword', ], ], ], 'JoinDomainOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'ActiveDirectoryStatus' => [ 'shape' => 'ActiveDirectoryStatus', ], ], ], 'KMSKey' => [ 'type' => 'string', 'max' => 2048, 'min' => 7, 'pattern' => '(^arn:(aws|aws-cn|aws-us-gov):kms:([a-zA-Z0-9-]+):([0-9]+):(key|alias)/(\\S+)$)|(^alias/(\\S+)$)', ], 'LastSoftwareUpdate' => [ 'type' => 'string', 'max' => 25, 'min' => 1, ], 'ListAutomaticTapeCreationPoliciesInput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'ListAutomaticTapeCreationPoliciesOutput' => [ 'type' => 'structure', 'members' => [ 'AutomaticTapeCreationPolicyInfos' => [ 'shape' => 'AutomaticTapeCreationPolicyInfos', ], ], ], 'ListFileSharesInput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'Limit' => [ 'shape' => 'PositiveIntObject', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListFileSharesOutput' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'Marker', ], 'NextMarker' => [ 'shape' => 'Marker', ], 'FileShareInfoList' => [ 'shape' => 'FileShareInfoList', ], ], ], 'ListFileSystemAssociationsInput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'Limit' => [ 'shape' => 'PositiveIntObject', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListFileSystemAssociationsOutput' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'Marker', ], 'NextMarker' => [ 'shape' => 'Marker', ], 'FileSystemAssociationSummaryList' => [ 'shape' => 'FileSystemAssociationSummaryList', ], ], ], 'ListGatewaysInput' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'Marker', ], 'Limit' => [ 'shape' => 'PositiveIntObject', ], ], ], 'ListGatewaysOutput' => [ 'type' => 'structure', 'members' => [ 'Gateways' => [ 'shape' => 'Gateways', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListLocalDisksInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'ListLocalDisksOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'Disks' => [ 'shape' => 'Disks', ], ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'Marker' => [ 'shape' => 'Marker', ], 'Limit' => [ 'shape' => 'PositiveIntObject', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'Marker' => [ 'shape' => 'Marker', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'ListTapePoolsInput' => [ 'type' => 'structure', 'members' => [ 'PoolARNs' => [ 'shape' => 'PoolARNs', ], 'Marker' => [ 'shape' => 'Marker', ], 'Limit' => [ 'shape' => 'PositiveIntObject', ], ], ], 'ListTapePoolsOutput' => [ 'type' => 'structure', 'members' => [ 'PoolInfos' => [ 'shape' => 'PoolInfos', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListTapesInput' => [ 'type' => 'structure', 'members' => [ 'TapeARNs' => [ 'shape' => 'TapeARNs', ], 'Marker' => [ 'shape' => 'Marker', ], 'Limit' => [ 'shape' => 'PositiveIntObject', ], ], ], 'ListTapesOutput' => [ 'type' => 'structure', 'members' => [ 'TapeInfos' => [ 'shape' => 'TapeInfos', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListVolumeInitiatorsInput' => [ 'type' => 'structure', 'required' => [ 'VolumeARN', ], 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], ], ], 'ListVolumeInitiatorsOutput' => [ 'type' => 'structure', 'members' => [ 'Initiators' => [ 'shape' => 'Initiators', ], ], ], 'ListVolumeRecoveryPointsInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'ListVolumeRecoveryPointsOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'VolumeRecoveryPointInfos' => [ 'shape' => 'VolumeRecoveryPointInfos', ], ], ], 'ListVolumesInput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'Marker' => [ 'shape' => 'Marker', ], 'Limit' => [ 'shape' => 'PositiveIntObject', ], ], ], 'ListVolumesOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'Marker' => [ 'shape' => 'Marker', ], 'VolumeInfos' => [ 'shape' => 'VolumeInfos', ], ], ], 'LocalConsolePassword' => [ 'type' => 'string', 'max' => 512, 'min' => 6, 'pattern' => '^[ -~]+$', 'sensitive' => true, ], 'LocationARN' => [ 'type' => 'string', 'max' => 1400, 'min' => 16, ], 'Marker' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'MediumChangerType' => [ 'type' => 'string', 'max' => 50, 'min' => 2, ], 'MinimumNumTapes' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'MinuteOfHour' => [ 'type' => 'integer', 'max' => 59, 'min' => 0, ], 'NFSFileShareDefaults' => [ 'type' => 'structure', 'members' => [ 'FileMode' => [ 'shape' => 'PermissionMode', ], 'DirectoryMode' => [ 'shape' => 'PermissionMode', ], 'GroupId' => [ 'shape' => 'PermissionId', ], 'OwnerId' => [ 'shape' => 'PermissionId', ], ], ], 'NFSFileShareInfo' => [ 'type' => 'structure', 'members' => [ 'NFSFileShareDefaults' => [ 'shape' => 'NFSFileShareDefaults', ], 'FileShareARN' => [ 'shape' => 'FileShareARN', ], 'FileShareId' => [ 'shape' => 'FileShareId', ], 'FileShareStatus' => [ 'shape' => 'FileShareStatus', ], 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'KMSEncrypted' => [ 'shape' => 'boolean', ], 'KMSKey' => [ 'shape' => 'KMSKey', ], 'Path' => [ 'shape' => 'Path', ], 'Role' => [ 'shape' => 'Role', ], 'LocationARN' => [ 'shape' => 'LocationARN', ], 'DefaultStorageClass' => [ 'shape' => 'StorageClass', ], 'ObjectACL' => [ 'shape' => 'ObjectACL', ], 'ClientList' => [ 'shape' => 'FileShareClientList', ], 'Squash' => [ 'shape' => 'Squash', ], 'ReadOnly' => [ 'shape' => 'Boolean', ], 'GuessMIMETypeEnabled' => [ 'shape' => 'Boolean', ], 'RequesterPays' => [ 'shape' => 'Boolean', ], 'Tags' => [ 'shape' => 'Tags', ], 'FileShareName' => [ 'shape' => 'FileShareName', ], 'CacheAttributes' => [ 'shape' => 'CacheAttributes', ], 'NotificationPolicy' => [ 'shape' => 'NotificationPolicy', ], 'VPCEndpointDNSName' => [ 'shape' => 'DNSHostName', ], 'BucketRegion' => [ 'shape' => 'RegionId', ], 'AuditDestinationARN' => [ 'shape' => 'AuditDestinationARN', ], ], ], 'NFSFileShareInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NFSFileShareInfo', ], ], 'NetworkInterface' => [ 'type' => 'structure', 'members' => [ 'Ipv4Address' => [ 'shape' => 'string', ], 'MacAddress' => [ 'shape' => 'string', ], 'Ipv6Address' => [ 'shape' => 'string', ], ], ], 'NetworkInterfaceId' => [ 'type' => 'string', 'pattern' => '\\A(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}\\z', ], 'NextUpdateAvailabilityDate' => [ 'type' => 'string', 'max' => 25, 'min' => 1, ], 'NotificationId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'NotificationPolicy' => [ 'type' => 'string', 'max' => 100, 'min' => 2, 'pattern' => '^\\{[\\w\\s:\\{\\}\\[\\]"]*}$', ], 'NotifyWhenUploadedInput' => [ 'type' => 'structure', 'required' => [ 'FileShareARN', ], 'members' => [ 'FileShareARN' => [ 'shape' => 'FileShareARN', ], ], ], 'NotifyWhenUploadedOutput' => [ 'type' => 'structure', 'members' => [ 'FileShareARN' => [ 'shape' => 'FileShareARN', ], 'NotificationId' => [ 'shape' => 'NotificationId', ], ], ], 'NumTapesToCreate' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'ObjectACL' => [ 'type' => 'string', 'enum' => [ 'private', 'public-read', 'public-read-write', 'authenticated-read', 'bucket-owner-read', 'bucket-owner-full-control', 'aws-exec-read', ], ], 'OrganizationalUnit' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'Path' => [ 'type' => 'string', ], 'PermissionId' => [ 'type' => 'long', 'max' => 4294967294, 'min' => 0, ], 'PermissionMode' => [ 'type' => 'string', 'max' => 4, 'min' => 1, 'pattern' => '^[0-7]{4}$', ], 'PoolARN' => [ 'type' => 'string', 'max' => 500, 'min' => 50, ], 'PoolARNs' => [ 'type' => 'list', 'member' => [ 'shape' => 'PoolARN', ], ], 'PoolId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'PoolInfo' => [ 'type' => 'structure', 'members' => [ 'PoolARN' => [ 'shape' => 'PoolARN', ], 'PoolName' => [ 'shape' => 'PoolName', ], 'StorageClass' => [ 'shape' => 'TapeStorageClass', ], 'RetentionLockType' => [ 'shape' => 'RetentionLockType', ], 'RetentionLockTimeInDays' => [ 'shape' => 'RetentionLockTimeInDays', ], 'PoolStatus' => [ 'shape' => 'PoolStatus', ], ], ], 'PoolInfos' => [ 'type' => 'list', 'member' => [ 'shape' => 'PoolInfo', ], ], 'PoolName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[ -\\.0-\\[\\]-~]*[!-\\.0-\\[\\]-~][ -\\.0-\\[\\]-~]*$', ], 'PoolStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DELETED', ], ], 'PositiveIntObject' => [ 'type' => 'integer', 'min' => 1, ], 'RecurrenceInHours' => [ 'type' => 'integer', 'max' => 24, 'min' => 1, ], 'RefreshCacheInput' => [ 'type' => 'structure', 'required' => [ 'FileShareARN', ], 'members' => [ 'FileShareARN' => [ 'shape' => 'FileShareARN', ], 'FolderList' => [ 'shape' => 'FolderList', ], 'Recursive' => [ 'shape' => 'Boolean', ], ], ], 'RefreshCacheOutput' => [ 'type' => 'structure', 'members' => [ 'FileShareARN' => [ 'shape' => 'FileShareARN', ], 'NotificationId' => [ 'shape' => 'NotificationId', ], ], ], 'RegionId' => [ 'type' => 'string', 'max' => 25, 'min' => 1, ], 'RemoveTagsFromResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'TagKeys' => [ 'shape' => 'TagKeys', ], ], ], 'RemoveTagsFromResourceOutput' => [ 'type' => 'structure', 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceARN', ], ], ], 'ResetCacheInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'ResetCacheOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'ResourceARN' => [ 'type' => 'string', 'max' => 500, 'min' => 50, ], 'RetentionLockTimeInDays' => [ 'type' => 'integer', 'max' => 36500, 'min' => 0, ], 'RetentionLockType' => [ 'type' => 'string', 'enum' => [ 'COMPLIANCE', 'GOVERNANCE', 'NONE', ], ], 'RetrieveTapeArchiveInput' => [ 'type' => 'structure', 'required' => [ 'TapeARN', 'GatewayARN', ], 'members' => [ 'TapeARN' => [ 'shape' => 'TapeARN', ], 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'RetrieveTapeArchiveOutput' => [ 'type' => 'structure', 'members' => [ 'TapeARN' => [ 'shape' => 'TapeARN', ], ], ], 'RetrieveTapeRecoveryPointInput' => [ 'type' => 'structure', 'required' => [ 'TapeARN', 'GatewayARN', ], 'members' => [ 'TapeARN' => [ 'shape' => 'TapeARN', ], 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'RetrieveTapeRecoveryPointOutput' => [ 'type' => 'structure', 'members' => [ 'TapeARN' => [ 'shape' => 'TapeARN', ], ], ], 'Role' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov):iam::([0-9]+):role/(\\S+)$', ], 'SMBFileShareInfo' => [ 'type' => 'structure', 'members' => [ 'FileShareARN' => [ 'shape' => 'FileShareARN', ], 'FileShareId' => [ 'shape' => 'FileShareId', ], 'FileShareStatus' => [ 'shape' => 'FileShareStatus', ], 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'KMSEncrypted' => [ 'shape' => 'boolean', ], 'KMSKey' => [ 'shape' => 'KMSKey', ], 'Path' => [ 'shape' => 'Path', ], 'Role' => [ 'shape' => 'Role', ], 'LocationARN' => [ 'shape' => 'LocationARN', ], 'DefaultStorageClass' => [ 'shape' => 'StorageClass', ], 'ObjectACL' => [ 'shape' => 'ObjectACL', ], 'ReadOnly' => [ 'shape' => 'Boolean', ], 'GuessMIMETypeEnabled' => [ 'shape' => 'Boolean', ], 'RequesterPays' => [ 'shape' => 'Boolean', ], 'SMBACLEnabled' => [ 'shape' => 'Boolean', ], 'AccessBasedEnumeration' => [ 'shape' => 'Boolean', ], 'AdminUserList' => [ 'shape' => 'UserList', ], 'ValidUserList' => [ 'shape' => 'UserList', ], 'InvalidUserList' => [ 'shape' => 'UserList', ], 'AuditDestinationARN' => [ 'shape' => 'AuditDestinationARN', ], 'Authentication' => [ 'shape' => 'Authentication', ], 'CaseSensitivity' => [ 'shape' => 'CaseSensitivity', ], 'Tags' => [ 'shape' => 'Tags', ], 'FileShareName' => [ 'shape' => 'FileShareName', ], 'CacheAttributes' => [ 'shape' => 'CacheAttributes', ], 'NotificationPolicy' => [ 'shape' => 'NotificationPolicy', ], 'VPCEndpointDNSName' => [ 'shape' => 'DNSHostName', ], 'BucketRegion' => [ 'shape' => 'RegionId', ], 'OplocksEnabled' => [ 'shape' => 'Boolean', ], ], ], 'SMBFileShareInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SMBFileShareInfo', ], ], 'SMBGuestPassword' => [ 'type' => 'string', 'max' => 512, 'min' => 6, 'pattern' => '^[ -~]+$', 'sensitive' => true, ], 'SMBLocalGroups' => [ 'type' => 'structure', 'members' => [ 'GatewayAdmins' => [ 'shape' => 'UserList', ], ], ], 'SMBSecurityStrategy' => [ 'type' => 'string', 'enum' => [ 'ClientSpecified', 'MandatorySigning', 'MandatoryEncryption', ], ], 'ServiceUnavailableError' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], 'error' => [ 'shape' => 'StorageGatewayError', ], ], 'exception' => true, ], 'SetLocalConsolePasswordInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'LocalConsolePassword', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'LocalConsolePassword' => [ 'shape' => 'LocalConsolePassword', ], ], ], 'SetLocalConsolePasswordOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'SetSMBGuestPasswordInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'Password', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'Password' => [ 'shape' => 'SMBGuestPassword', ], ], ], 'SetSMBGuestPasswordOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'ShutdownGatewayInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'ShutdownGatewayOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'SnapshotDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'SnapshotId' => [ 'type' => 'string', 'pattern' => '\\Asnap-([0-9A-Fa-f]{8}|[0-9A-Fa-f]{17})\\z', ], 'SoftwareUpdatesEndDate' => [ 'type' => 'string', 'max' => 25, 'min' => 1, ], 'Squash' => [ 'type' => 'string', 'max' => 15, 'min' => 5, ], 'StartAvailabilityMonitorTestInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'StartAvailabilityMonitorTestOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'StartGatewayInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'StartGatewayOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'StorageClass' => [ 'type' => 'string', 'max' => 50, 'min' => 5, ], 'StorageGatewayError' => [ 'type' => 'structure', 'members' => [ 'errorCode' => [ 'shape' => 'ErrorCode', ], 'errorDetails' => [ 'shape' => 'errorDetails', ], ], ], 'StorediSCSIVolume' => [ 'type' => 'structure', 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], 'VolumeId' => [ 'shape' => 'VolumeId', ], 'VolumeType' => [ 'shape' => 'VolumeType', ], 'VolumeStatus' => [ 'shape' => 'VolumeStatus', ], 'VolumeAttachmentStatus' => [ 'shape' => 'VolumeAttachmentStatus', ], 'VolumeSizeInBytes' => [ 'shape' => 'long', ], 'VolumeProgress' => [ 'shape' => 'DoubleObject', ], 'VolumeDiskId' => [ 'shape' => 'DiskId', ], 'SourceSnapshotId' => [ 'shape' => 'SnapshotId', ], 'PreservedExistingData' => [ 'shape' => 'boolean', ], 'VolumeiSCSIAttributes' => [ 'shape' => 'VolumeiSCSIAttributes', ], 'CreatedDate' => [ 'shape' => 'CreatedDate', ], 'VolumeUsedInBytes' => [ 'shape' => 'VolumeUsedInBytes', ], 'KMSKey' => [ 'shape' => 'KMSKey', ], 'TargetName' => [ 'shape' => 'TargetName', ], ], ], 'StorediSCSIVolumes' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorediSCSIVolume', ], ], 'SupportedGatewayCapacities' => [ 'type' => 'list', 'member' => [ 'shape' => 'GatewayCapacity', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'Tape' => [ 'type' => 'structure', 'members' => [ 'TapeARN' => [ 'shape' => 'TapeARN', ], 'TapeBarcode' => [ 'shape' => 'TapeBarcode', ], 'TapeCreatedDate' => [ 'shape' => 'Time', ], 'TapeSizeInBytes' => [ 'shape' => 'TapeSize', ], 'TapeStatus' => [ 'shape' => 'TapeStatus', ], 'VTLDevice' => [ 'shape' => 'VTLDeviceARN', ], 'Progress' => [ 'shape' => 'DoubleObject', ], 'TapeUsedInBytes' => [ 'shape' => 'TapeUsage', ], 'KMSKey' => [ 'shape' => 'KMSKey', ], 'PoolId' => [ 'shape' => 'PoolId', ], 'Worm' => [ 'shape' => 'boolean', ], 'RetentionStartDate' => [ 'shape' => 'Time', ], 'PoolEntryDate' => [ 'shape' => 'Time', ], ], ], 'TapeARN' => [ 'type' => 'string', 'max' => 500, 'min' => 50, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov):storagegateway:[a-z\\-0-9]+:[0-9]+:tape\\/[0-9A-Z]{7,16}$', ], 'TapeARNs' => [ 'type' => 'list', 'member' => [ 'shape' => 'TapeARN', ], ], 'TapeArchive' => [ 'type' => 'structure', 'members' => [ 'TapeARN' => [ 'shape' => 'TapeARN', ], 'TapeBarcode' => [ 'shape' => 'TapeBarcode', ], 'TapeCreatedDate' => [ 'shape' => 'Time', ], 'TapeSizeInBytes' => [ 'shape' => 'TapeSize', ], 'CompletionTime' => [ 'shape' => 'Time', ], 'RetrievedTo' => [ 'shape' => 'GatewayARN', ], 'TapeStatus' => [ 'shape' => 'TapeArchiveStatus', ], 'TapeUsedInBytes' => [ 'shape' => 'TapeUsage', ], 'KMSKey' => [ 'shape' => 'KMSKey', ], 'PoolId' => [ 'shape' => 'PoolId', ], 'Worm' => [ 'shape' => 'boolean', ], 'RetentionStartDate' => [ 'shape' => 'Time', ], 'PoolEntryDate' => [ 'shape' => 'Time', ], ], ], 'TapeArchiveStatus' => [ 'type' => 'string', ], 'TapeArchives' => [ 'type' => 'list', 'member' => [ 'shape' => 'TapeArchive', ], ], 'TapeBarcode' => [ 'type' => 'string', 'max' => 16, 'min' => 7, 'pattern' => '^[A-Z0-9]*$', ], 'TapeBarcodePrefix' => [ 'type' => 'string', 'max' => 4, 'min' => 1, 'pattern' => '^[A-Z]*$', ], 'TapeDriveType' => [ 'type' => 'string', 'max' => 50, 'min' => 2, ], 'TapeInfo' => [ 'type' => 'structure', 'members' => [ 'TapeARN' => [ 'shape' => 'TapeARN', ], 'TapeBarcode' => [ 'shape' => 'TapeBarcode', ], 'TapeSizeInBytes' => [ 'shape' => 'TapeSize', ], 'TapeStatus' => [ 'shape' => 'TapeStatus', ], 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'PoolId' => [ 'shape' => 'PoolId', ], 'RetentionStartDate' => [ 'shape' => 'Time', ], 'PoolEntryDate' => [ 'shape' => 'Time', ], ], ], 'TapeInfos' => [ 'type' => 'list', 'member' => [ 'shape' => 'TapeInfo', ], ], 'TapeRecoveryPointInfo' => [ 'type' => 'structure', 'members' => [ 'TapeARN' => [ 'shape' => 'TapeARN', ], 'TapeRecoveryPointTime' => [ 'shape' => 'Time', ], 'TapeSizeInBytes' => [ 'shape' => 'TapeSize', ], 'TapeStatus' => [ 'shape' => 'TapeRecoveryPointStatus', ], ], ], 'TapeRecoveryPointInfos' => [ 'type' => 'list', 'member' => [ 'shape' => 'TapeRecoveryPointInfo', ], ], 'TapeRecoveryPointStatus' => [ 'type' => 'string', ], 'TapeSize' => [ 'type' => 'long', ], 'TapeStatus' => [ 'type' => 'string', ], 'TapeStorageClass' => [ 'type' => 'string', 'enum' => [ 'DEEP_ARCHIVE', 'GLACIER', ], ], 'TapeUsage' => [ 'type' => 'long', ], 'Tapes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tape', ], ], 'TargetARN' => [ 'type' => 'string', 'max' => 800, 'min' => 50, ], 'TargetName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[-\\.;a-z0-9]+$', ], 'Time' => [ 'type' => 'timestamp', ], 'TimeoutInSeconds' => [ 'type' => 'integer', 'max' => 3600, 'min' => 0, ], 'UpdateAutomaticTapeCreationPolicyInput' => [ 'type' => 'structure', 'required' => [ 'AutomaticTapeCreationRules', 'GatewayARN', ], 'members' => [ 'AutomaticTapeCreationRules' => [ 'shape' => 'AutomaticTapeCreationRules', ], 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'UpdateAutomaticTapeCreationPolicyOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'UpdateBandwidthRateLimitInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'AverageUploadRateLimitInBitsPerSec' => [ 'shape' => 'BandwidthUploadRateLimit', ], 'AverageDownloadRateLimitInBitsPerSec' => [ 'shape' => 'BandwidthDownloadRateLimit', ], ], ], 'UpdateBandwidthRateLimitOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'UpdateBandwidthRateLimitScheduleInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'BandwidthRateLimitIntervals', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'BandwidthRateLimitIntervals' => [ 'shape' => 'BandwidthRateLimitIntervals', ], ], ], 'UpdateBandwidthRateLimitScheduleOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'UpdateChapCredentialsInput' => [ 'type' => 'structure', 'required' => [ 'TargetARN', 'SecretToAuthenticateInitiator', 'InitiatorName', ], 'members' => [ 'TargetARN' => [ 'shape' => 'TargetARN', ], 'SecretToAuthenticateInitiator' => [ 'shape' => 'ChapSecret', ], 'InitiatorName' => [ 'shape' => 'IqnName', ], 'SecretToAuthenticateTarget' => [ 'shape' => 'ChapSecret', ], ], ], 'UpdateChapCredentialsOutput' => [ 'type' => 'structure', 'members' => [ 'TargetARN' => [ 'shape' => 'TargetARN', ], 'InitiatorName' => [ 'shape' => 'IqnName', ], ], ], 'UpdateFileSystemAssociationInput' => [ 'type' => 'structure', 'required' => [ 'FileSystemAssociationARN', ], 'members' => [ 'FileSystemAssociationARN' => [ 'shape' => 'FileSystemAssociationARN', ], 'UserName' => [ 'shape' => 'DomainUserName', ], 'Password' => [ 'shape' => 'DomainUserPassword', ], 'AuditDestinationARN' => [ 'shape' => 'AuditDestinationARN', ], 'CacheAttributes' => [ 'shape' => 'CacheAttributes', ], ], ], 'UpdateFileSystemAssociationOutput' => [ 'type' => 'structure', 'members' => [ 'FileSystemAssociationARN' => [ 'shape' => 'FileSystemAssociationARN', ], ], ], 'UpdateGatewayInformationInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'GatewayName' => [ 'shape' => 'GatewayName', ], 'GatewayTimezone' => [ 'shape' => 'GatewayTimezone', ], 'CloudWatchLogGroupARN' => [ 'shape' => 'CloudWatchLogGroupARN', ], 'GatewayCapacity' => [ 'shape' => 'GatewayCapacity', ], ], ], 'UpdateGatewayInformationOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'GatewayName' => [ 'shape' => 'string', ], ], ], 'UpdateGatewaySoftwareNowInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'UpdateGatewaySoftwareNowOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'UpdateMaintenanceStartTimeInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'HourOfDay', 'MinuteOfHour', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'HourOfDay' => [ 'shape' => 'HourOfDay', ], 'MinuteOfHour' => [ 'shape' => 'MinuteOfHour', ], 'DayOfWeek' => [ 'shape' => 'DayOfWeek', ], 'DayOfMonth' => [ 'shape' => 'DayOfMonth', ], ], ], 'UpdateMaintenanceStartTimeOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'UpdateNFSFileShareInput' => [ 'type' => 'structure', 'required' => [ 'FileShareARN', ], 'members' => [ 'FileShareARN' => [ 'shape' => 'FileShareARN', ], 'KMSEncrypted' => [ 'shape' => 'Boolean', ], 'KMSKey' => [ 'shape' => 'KMSKey', ], 'NFSFileShareDefaults' => [ 'shape' => 'NFSFileShareDefaults', ], 'DefaultStorageClass' => [ 'shape' => 'StorageClass', ], 'ObjectACL' => [ 'shape' => 'ObjectACL', ], 'ClientList' => [ 'shape' => 'FileShareClientList', ], 'Squash' => [ 'shape' => 'Squash', ], 'ReadOnly' => [ 'shape' => 'Boolean', ], 'GuessMIMETypeEnabled' => [ 'shape' => 'Boolean', ], 'RequesterPays' => [ 'shape' => 'Boolean', ], 'FileShareName' => [ 'shape' => 'FileShareName', ], 'CacheAttributes' => [ 'shape' => 'CacheAttributes', ], 'NotificationPolicy' => [ 'shape' => 'NotificationPolicy', ], 'AuditDestinationARN' => [ 'shape' => 'AuditDestinationARN', ], ], ], 'UpdateNFSFileShareOutput' => [ 'type' => 'structure', 'members' => [ 'FileShareARN' => [ 'shape' => 'FileShareARN', ], ], ], 'UpdateSMBFileShareInput' => [ 'type' => 'structure', 'required' => [ 'FileShareARN', ], 'members' => [ 'FileShareARN' => [ 'shape' => 'FileShareARN', ], 'KMSEncrypted' => [ 'shape' => 'Boolean', ], 'KMSKey' => [ 'shape' => 'KMSKey', ], 'DefaultStorageClass' => [ 'shape' => 'StorageClass', ], 'ObjectACL' => [ 'shape' => 'ObjectACL', ], 'ReadOnly' => [ 'shape' => 'Boolean', ], 'GuessMIMETypeEnabled' => [ 'shape' => 'Boolean', ], 'RequesterPays' => [ 'shape' => 'Boolean', ], 'SMBACLEnabled' => [ 'shape' => 'Boolean', ], 'AccessBasedEnumeration' => [ 'shape' => 'Boolean', ], 'AdminUserList' => [ 'shape' => 'UserList', ], 'ValidUserList' => [ 'shape' => 'UserList', ], 'InvalidUserList' => [ 'shape' => 'UserList', ], 'AuditDestinationARN' => [ 'shape' => 'AuditDestinationARN', ], 'CaseSensitivity' => [ 'shape' => 'CaseSensitivity', ], 'FileShareName' => [ 'shape' => 'FileShareName', ], 'CacheAttributes' => [ 'shape' => 'CacheAttributes', ], 'NotificationPolicy' => [ 'shape' => 'NotificationPolicy', ], 'OplocksEnabled' => [ 'shape' => 'Boolean', ], ], ], 'UpdateSMBFileShareOutput' => [ 'type' => 'structure', 'members' => [ 'FileShareARN' => [ 'shape' => 'FileShareARN', ], ], ], 'UpdateSMBFileShareVisibilityInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'FileSharesVisible', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'FileSharesVisible' => [ 'shape' => 'Boolean', ], ], ], 'UpdateSMBFileShareVisibilityOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'UpdateSMBLocalGroupsInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'SMBLocalGroups', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'SMBLocalGroups' => [ 'shape' => 'SMBLocalGroups', ], ], ], 'UpdateSMBLocalGroupsOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'UpdateSMBSecurityStrategyInput' => [ 'type' => 'structure', 'required' => [ 'GatewayARN', 'SMBSecurityStrategy', ], 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'SMBSecurityStrategy' => [ 'shape' => 'SMBSecurityStrategy', ], ], ], 'UpdateSMBSecurityStrategyOutput' => [ 'type' => 'structure', 'members' => [ 'GatewayARN' => [ 'shape' => 'GatewayARN', ], ], ], 'UpdateSnapshotScheduleInput' => [ 'type' => 'structure', 'required' => [ 'VolumeARN', 'StartAt', 'RecurrenceInHours', ], 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], 'StartAt' => [ 'shape' => 'HourOfDay', ], 'RecurrenceInHours' => [ 'shape' => 'RecurrenceInHours', ], 'Description' => [ 'shape' => 'Description', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'UpdateSnapshotScheduleOutput' => [ 'type' => 'structure', 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], ], ], 'UpdateVTLDeviceTypeInput' => [ 'type' => 'structure', 'required' => [ 'VTLDeviceARN', 'DeviceType', ], 'members' => [ 'VTLDeviceARN' => [ 'shape' => 'VTLDeviceARN', ], 'DeviceType' => [ 'shape' => 'DeviceType', ], ], ], 'UpdateVTLDeviceTypeOutput' => [ 'type' => 'structure', 'members' => [ 'VTLDeviceARN' => [ 'shape' => 'VTLDeviceARN', ], ], ], 'UserList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserListUser', ], 'max' => 100, 'min' => 0, ], 'UserListUser' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'VTLDevice' => [ 'type' => 'structure', 'members' => [ 'VTLDeviceARN' => [ 'shape' => 'VTLDeviceARN', ], 'VTLDeviceType' => [ 'shape' => 'VTLDeviceType', ], 'VTLDeviceVendor' => [ 'shape' => 'VTLDeviceVendor', ], 'VTLDeviceProductIdentifier' => [ 'shape' => 'VTLDeviceProductIdentifier', ], 'DeviceiSCSIAttributes' => [ 'shape' => 'DeviceiSCSIAttributes', ], ], ], 'VTLDeviceARN' => [ 'type' => 'string', 'max' => 500, 'min' => 50, ], 'VTLDeviceARNs' => [ 'type' => 'list', 'member' => [ 'shape' => 'VTLDeviceARN', ], ], 'VTLDeviceProductIdentifier' => [ 'type' => 'string', ], 'VTLDeviceType' => [ 'type' => 'string', ], 'VTLDeviceVendor' => [ 'type' => 'string', ], 'VTLDevices' => [ 'type' => 'list', 'member' => [ 'shape' => 'VTLDevice', ], ], 'VolumeARN' => [ 'type' => 'string', 'max' => 500, 'min' => 50, ], 'VolumeARNs' => [ 'type' => 'list', 'member' => [ 'shape' => 'VolumeARN', ], ], 'VolumeAttachmentStatus' => [ 'type' => 'string', 'max' => 50, 'min' => 3, ], 'VolumeId' => [ 'type' => 'string', 'max' => 30, 'min' => 12, ], 'VolumeInfo' => [ 'type' => 'structure', 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], 'VolumeId' => [ 'shape' => 'VolumeId', ], 'GatewayARN' => [ 'shape' => 'GatewayARN', ], 'GatewayId' => [ 'shape' => 'GatewayId', ], 'VolumeType' => [ 'shape' => 'VolumeType', ], 'VolumeSizeInBytes' => [ 'shape' => 'long', ], 'VolumeAttachmentStatus' => [ 'shape' => 'VolumeAttachmentStatus', ], ], ], 'VolumeInfos' => [ 'type' => 'list', 'member' => [ 'shape' => 'VolumeInfo', ], ], 'VolumeRecoveryPointInfo' => [ 'type' => 'structure', 'members' => [ 'VolumeARN' => [ 'shape' => 'VolumeARN', ], 'VolumeSizeInBytes' => [ 'shape' => 'long', ], 'VolumeUsageInBytes' => [ 'shape' => 'long', ], 'VolumeRecoveryPointTime' => [ 'shape' => 'string', ], ], ], 'VolumeRecoveryPointInfos' => [ 'type' => 'list', 'member' => [ 'shape' => 'VolumeRecoveryPointInfo', ], ], 'VolumeStatus' => [ 'type' => 'string', 'max' => 50, 'min' => 3, ], 'VolumeType' => [ 'type' => 'string', 'max' => 100, 'min' => 3, ], 'VolumeUsedInBytes' => [ 'type' => 'long', ], 'VolumeiSCSIAttributes' => [ 'type' => 'structure', 'members' => [ 'TargetARN' => [ 'shape' => 'TargetARN', ], 'NetworkInterfaceId' => [ 'shape' => 'NetworkInterfaceId', ], 'NetworkInterfacePort' => [ 'shape' => 'integer', ], 'LunNumber' => [ 'shape' => 'PositiveIntObject', ], 'ChapEnabled' => [ 'shape' => 'boolean', ], ], ], 'boolean' => [ 'type' => 'boolean', ], 'double' => [ 'type' => 'double', ], 'errorDetails' => [ 'type' => 'map', 'key' => [ 'shape' => 'string', ], 'value' => [ 'shape' => 'string', ], ], 'integer' => [ 'type' => 'integer', ], 'long' => [ 'type' => 'long', ], 'string' => [ 'type' => 'string', ], ],];
