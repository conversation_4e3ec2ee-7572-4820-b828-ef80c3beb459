<?php
// This file was auto-generated from sdk-root/src/data/connect/2017-08-08/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-08-08', 'endpointPrefix' => 'connect', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'Amazon Connect', 'serviceFullName' => 'Amazon Connect Service', 'serviceId' => 'Connect', 'signatureVersion' => 'v4', 'signingName' => 'connect', 'uid' => 'connect-2017-08-08', ], 'operations' => [ 'AssociateApprovedOrigin' => [ 'name' => 'AssociateApprovedOrigin', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/approved-origin', ], 'input' => [ 'shape' => 'AssociateApprovedOriginRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociateBot' => [ 'name' => 'AssociateBot', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/bot', ], 'input' => [ 'shape' => 'AssociateBotRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociateInstanceStorageConfig' => [ 'name' => 'AssociateInstanceStorageConfig', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/storage-config', ], 'input' => [ 'shape' => 'AssociateInstanceStorageConfigRequest', ], 'output' => [ 'shape' => 'AssociateInstanceStorageConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociateLambdaFunction' => [ 'name' => 'AssociateLambdaFunction', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/lambda-function', ], 'input' => [ 'shape' => 'AssociateLambdaFunctionRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociateLexBot' => [ 'name' => 'AssociateLexBot', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/lex-bot', ], 'input' => [ 'shape' => 'AssociateLexBotRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociateQueueQuickConnects' => [ 'name' => 'AssociateQueueQuickConnects', 'http' => [ 'method' => 'POST', 'requestUri' => '/queues/{InstanceId}/{QueueId}/associate-quick-connects', ], 'input' => [ 'shape' => 'AssociateQueueQuickConnectsRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'AssociateRoutingProfileQueues' => [ 'name' => 'AssociateRoutingProfileQueues', 'http' => [ 'method' => 'POST', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}/associate-queues', ], 'input' => [ 'shape' => 'AssociateRoutingProfileQueuesRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'AssociateSecurityKey' => [ 'name' => 'AssociateSecurityKey', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/security-key', ], 'input' => [ 'shape' => 'AssociateSecurityKeyRequest', ], 'output' => [ 'shape' => 'AssociateSecurityKeyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateAgentStatus' => [ 'name' => 'CreateAgentStatus', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agent-status/{InstanceId}', ], 'input' => [ 'shape' => 'CreateAgentStatusRequest', ], 'output' => [ 'shape' => 'CreateAgentStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateContactFlow' => [ 'name' => 'CreateContactFlow', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact-flows/{InstanceId}', ], 'input' => [ 'shape' => 'CreateContactFlowRequest', ], 'output' => [ 'shape' => 'CreateContactFlowResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidContactFlowException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateContactFlowModule' => [ 'name' => 'CreateContactFlowModule', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact-flow-modules/{InstanceId}', ], 'input' => [ 'shape' => 'CreateContactFlowModuleRequest', ], 'output' => [ 'shape' => 'CreateContactFlowModuleResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidContactFlowModuleException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'IdempotencyException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateHoursOfOperation' => [ 'name' => 'CreateHoursOfOperation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/hours-of-operations/{InstanceId}', ], 'input' => [ 'shape' => 'CreateHoursOfOperationRequest', ], 'output' => [ 'shape' => 'CreateHoursOfOperationResponse', ], 'errors' => [ [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateInstance' => [ 'name' => 'CreateInstance', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance', ], 'input' => [ 'shape' => 'CreateInstanceRequest', ], 'output' => [ 'shape' => 'CreateInstanceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateIntegrationAssociation' => [ 'name' => 'CreateIntegrationAssociation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/integration-associations', ], 'input' => [ 'shape' => 'CreateIntegrationAssociationRequest', ], 'output' => [ 'shape' => 'CreateIntegrationAssociationResponse', ], 'errors' => [ [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateQueue' => [ 'name' => 'CreateQueue', 'http' => [ 'method' => 'PUT', 'requestUri' => '/queues/{InstanceId}', ], 'input' => [ 'shape' => 'CreateQueueRequest', ], 'output' => [ 'shape' => 'CreateQueueResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateQuickConnect' => [ 'name' => 'CreateQuickConnect', 'http' => [ 'method' => 'PUT', 'requestUri' => '/quick-connects/{InstanceId}', ], 'input' => [ 'shape' => 'CreateQuickConnectRequest', ], 'output' => [ 'shape' => 'CreateQuickConnectResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateRoutingProfile' => [ 'name' => 'CreateRoutingProfile', 'http' => [ 'method' => 'PUT', 'requestUri' => '/routing-profiles/{InstanceId}', ], 'input' => [ 'shape' => 'CreateRoutingProfileRequest', ], 'output' => [ 'shape' => 'CreateRoutingProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateSecurityProfile' => [ 'name' => 'CreateSecurityProfile', 'http' => [ 'method' => 'PUT', 'requestUri' => '/security-profiles/{InstanceId}', ], 'input' => [ 'shape' => 'CreateSecurityProfileRequest', ], 'output' => [ 'shape' => 'CreateSecurityProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateUseCase' => [ 'name' => 'CreateUseCase', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/integration-associations/{IntegrationAssociationId}/use-cases', ], 'input' => [ 'shape' => 'CreateUseCaseRequest', ], 'output' => [ 'shape' => 'CreateUseCaseResponse', ], 'errors' => [ [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateUser' => [ 'name' => 'CreateUser', 'http' => [ 'method' => 'PUT', 'requestUri' => '/users/{InstanceId}', ], 'input' => [ 'shape' => 'CreateUserRequest', ], 'output' => [ 'shape' => 'CreateUserResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateUserHierarchyGroup' => [ 'name' => 'CreateUserHierarchyGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/user-hierarchy-groups/{InstanceId}', ], 'input' => [ 'shape' => 'CreateUserHierarchyGroupRequest', ], 'output' => [ 'shape' => 'CreateUserHierarchyGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteContactFlow' => [ 'name' => 'DeleteContactFlow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/contact-flows/{InstanceId}/{ContactFlowId}', ], 'input' => [ 'shape' => 'DeleteContactFlowRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteContactFlowModule' => [ 'name' => 'DeleteContactFlowModule', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/contact-flow-modules/{InstanceId}/{ContactFlowModuleId}', ], 'input' => [ 'shape' => 'DeleteContactFlowModuleRequest', ], 'output' => [ 'shape' => 'DeleteContactFlowModuleResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteHoursOfOperation' => [ 'name' => 'DeleteHoursOfOperation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/hours-of-operations/{InstanceId}/{HoursOfOperationId}', ], 'input' => [ 'shape' => 'DeleteHoursOfOperationRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteInstance' => [ 'name' => 'DeleteInstance', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}', ], 'input' => [ 'shape' => 'DeleteInstanceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DeleteIntegrationAssociation' => [ 'name' => 'DeleteIntegrationAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}/integration-associations/{IntegrationAssociationId}', ], 'input' => [ 'shape' => 'DeleteIntegrationAssociationRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteQuickConnect' => [ 'name' => 'DeleteQuickConnect', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/quick-connects/{InstanceId}/{QuickConnectId}', ], 'input' => [ 'shape' => 'DeleteQuickConnectRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteSecurityProfile' => [ 'name' => 'DeleteSecurityProfile', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/security-profiles/{InstanceId}/{SecurityProfileId}', ], 'input' => [ 'shape' => 'DeleteSecurityProfileRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteUseCase' => [ 'name' => 'DeleteUseCase', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}/integration-associations/{IntegrationAssociationId}/use-cases/{UseCaseId}', ], 'input' => [ 'shape' => 'DeleteUseCaseRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteUser' => [ 'name' => 'DeleteUser', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/users/{InstanceId}/{UserId}', ], 'input' => [ 'shape' => 'DeleteUserRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteUserHierarchyGroup' => [ 'name' => 'DeleteUserHierarchyGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/user-hierarchy-groups/{InstanceId}/{HierarchyGroupId}', ], 'input' => [ 'shape' => 'DeleteUserHierarchyGroupRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeAgentStatus' => [ 'name' => 'DescribeAgentStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/agent-status/{InstanceId}/{AgentStatusId}', ], 'input' => [ 'shape' => 'DescribeAgentStatusRequest', ], 'output' => [ 'shape' => 'DescribeAgentStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeContact' => [ 'name' => 'DescribeContact', 'http' => [ 'method' => 'GET', 'requestUri' => '/contacts/{InstanceId}/{ContactId}', ], 'input' => [ 'shape' => 'DescribeContactRequest', ], 'output' => [ 'shape' => 'DescribeContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeContactFlow' => [ 'name' => 'DescribeContactFlow', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact-flows/{InstanceId}/{ContactFlowId}', ], 'input' => [ 'shape' => 'DescribeContactFlowRequest', ], 'output' => [ 'shape' => 'DescribeContactFlowResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ContactFlowNotPublishedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeContactFlowModule' => [ 'name' => 'DescribeContactFlowModule', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact-flow-modules/{InstanceId}/{ContactFlowModuleId}', ], 'input' => [ 'shape' => 'DescribeContactFlowModuleRequest', ], 'output' => [ 'shape' => 'DescribeContactFlowModuleResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeHoursOfOperation' => [ 'name' => 'DescribeHoursOfOperation', 'http' => [ 'method' => 'GET', 'requestUri' => '/hours-of-operations/{InstanceId}/{HoursOfOperationId}', ], 'input' => [ 'shape' => 'DescribeHoursOfOperationRequest', ], 'output' => [ 'shape' => 'DescribeHoursOfOperationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeInstance' => [ 'name' => 'DescribeInstance', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}', ], 'input' => [ 'shape' => 'DescribeInstanceRequest', ], 'output' => [ 'shape' => 'DescribeInstanceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeInstanceAttribute' => [ 'name' => 'DescribeInstanceAttribute', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/attribute/{AttributeType}', ], 'input' => [ 'shape' => 'DescribeInstanceAttributeRequest', ], 'output' => [ 'shape' => 'DescribeInstanceAttributeResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeInstanceStorageConfig' => [ 'name' => 'DescribeInstanceStorageConfig', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/storage-config/{AssociationId}', ], 'input' => [ 'shape' => 'DescribeInstanceStorageConfigRequest', ], 'output' => [ 'shape' => 'DescribeInstanceStorageConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeQueue' => [ 'name' => 'DescribeQueue', 'http' => [ 'method' => 'GET', 'requestUri' => '/queues/{InstanceId}/{QueueId}', ], 'input' => [ 'shape' => 'DescribeQueueRequest', ], 'output' => [ 'shape' => 'DescribeQueueResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeQuickConnect' => [ 'name' => 'DescribeQuickConnect', 'http' => [ 'method' => 'GET', 'requestUri' => '/quick-connects/{InstanceId}/{QuickConnectId}', ], 'input' => [ 'shape' => 'DescribeQuickConnectRequest', ], 'output' => [ 'shape' => 'DescribeQuickConnectResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeRoutingProfile' => [ 'name' => 'DescribeRoutingProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}', ], 'input' => [ 'shape' => 'DescribeRoutingProfileRequest', ], 'output' => [ 'shape' => 'DescribeRoutingProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeSecurityProfile' => [ 'name' => 'DescribeSecurityProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/security-profiles/{InstanceId}/{SecurityProfileId}', ], 'input' => [ 'shape' => 'DescribeSecurityProfileRequest', ], 'output' => [ 'shape' => 'DescribeSecurityProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeUser' => [ 'name' => 'DescribeUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/users/{InstanceId}/{UserId}', ], 'input' => [ 'shape' => 'DescribeUserRequest', ], 'output' => [ 'shape' => 'DescribeUserResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeUserHierarchyGroup' => [ 'name' => 'DescribeUserHierarchyGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/user-hierarchy-groups/{InstanceId}/{HierarchyGroupId}', ], 'input' => [ 'shape' => 'DescribeUserHierarchyGroupRequest', ], 'output' => [ 'shape' => 'DescribeUserHierarchyGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeUserHierarchyStructure' => [ 'name' => 'DescribeUserHierarchyStructure', 'http' => [ 'method' => 'GET', 'requestUri' => '/user-hierarchy-structure/{InstanceId}', ], 'input' => [ 'shape' => 'DescribeUserHierarchyStructureRequest', ], 'output' => [ 'shape' => 'DescribeUserHierarchyStructureResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DisassociateApprovedOrigin' => [ 'name' => 'DisassociateApprovedOrigin', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}/approved-origin', ], 'input' => [ 'shape' => 'DisassociateApprovedOriginRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateBot' => [ 'name' => 'DisassociateBot', 'http' => [ 'method' => 'POST', 'requestUri' => '/instance/{InstanceId}/bot', ], 'input' => [ 'shape' => 'DisassociateBotRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateInstanceStorageConfig' => [ 'name' => 'DisassociateInstanceStorageConfig', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}/storage-config/{AssociationId}', ], 'input' => [ 'shape' => 'DisassociateInstanceStorageConfigRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateLambdaFunction' => [ 'name' => 'DisassociateLambdaFunction', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}/lambda-function', ], 'input' => [ 'shape' => 'DisassociateLambdaFunctionRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateLexBot' => [ 'name' => 'DisassociateLexBot', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}/lex-bot', ], 'input' => [ 'shape' => 'DisassociateLexBotRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateQueueQuickConnects' => [ 'name' => 'DisassociateQueueQuickConnects', 'http' => [ 'method' => 'POST', 'requestUri' => '/queues/{InstanceId}/{QueueId}/disassociate-quick-connects', ], 'input' => [ 'shape' => 'DisassociateQueueQuickConnectsRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DisassociateRoutingProfileQueues' => [ 'name' => 'DisassociateRoutingProfileQueues', 'http' => [ 'method' => 'POST', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}/disassociate-queues', ], 'input' => [ 'shape' => 'DisassociateRoutingProfileQueuesRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DisassociateSecurityKey' => [ 'name' => 'DisassociateSecurityKey', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}/security-key/{AssociationId}', ], 'input' => [ 'shape' => 'DisassociateSecurityKeyRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetContactAttributes' => [ 'name' => 'GetContactAttributes', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact/attributes/{InstanceId}/{InitialContactId}', ], 'input' => [ 'shape' => 'GetContactAttributesRequest', ], 'output' => [ 'shape' => 'GetContactAttributesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetCurrentMetricData' => [ 'name' => 'GetCurrentMetricData', 'http' => [ 'method' => 'POST', 'requestUri' => '/metrics/current/{InstanceId}', ], 'input' => [ 'shape' => 'GetCurrentMetricDataRequest', ], 'output' => [ 'shape' => 'GetCurrentMetricDataResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetFederationToken' => [ 'name' => 'GetFederationToken', 'http' => [ 'method' => 'GET', 'requestUri' => '/user/federate/{InstanceId}', ], 'input' => [ 'shape' => 'GetFederationTokenRequest', ], 'output' => [ 'shape' => 'GetFederationTokenResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'DuplicateResourceException', ], ], ], 'GetMetricData' => [ 'name' => 'GetMetricData', 'http' => [ 'method' => 'POST', 'requestUri' => '/metrics/historical/{InstanceId}', ], 'input' => [ 'shape' => 'GetMetricDataRequest', ], 'output' => [ 'shape' => 'GetMetricDataResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAgentStatuses' => [ 'name' => 'ListAgentStatuses', 'http' => [ 'method' => 'GET', 'requestUri' => '/agent-status/{InstanceId}', ], 'input' => [ 'shape' => 'ListAgentStatusRequest', ], 'output' => [ 'shape' => 'ListAgentStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListApprovedOrigins' => [ 'name' => 'ListApprovedOrigins', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/approved-origins', ], 'input' => [ 'shape' => 'ListApprovedOriginsRequest', ], 'output' => [ 'shape' => 'ListApprovedOriginsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListBots' => [ 'name' => 'ListBots', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/bots', ], 'input' => [ 'shape' => 'ListBotsRequest', ], 'output' => [ 'shape' => 'ListBotsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListContactFlowModules' => [ 'name' => 'ListContactFlowModules', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact-flow-modules-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListContactFlowModulesRequest', ], 'output' => [ 'shape' => 'ListContactFlowModulesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListContactFlows' => [ 'name' => 'ListContactFlows', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact-flows-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListContactFlowsRequest', ], 'output' => [ 'shape' => 'ListContactFlowsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListContactReferences' => [ 'name' => 'ListContactReferences', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact/references/{InstanceId}/{ContactId}', ], 'input' => [ 'shape' => 'ListContactReferencesRequest', ], 'output' => [ 'shape' => 'ListContactReferencesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListHoursOfOperations' => [ 'name' => 'ListHoursOfOperations', 'http' => [ 'method' => 'GET', 'requestUri' => '/hours-of-operations-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListHoursOfOperationsRequest', ], 'output' => [ 'shape' => 'ListHoursOfOperationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListInstanceAttributes' => [ 'name' => 'ListInstanceAttributes', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/attributes', ], 'input' => [ 'shape' => 'ListInstanceAttributesRequest', ], 'output' => [ 'shape' => 'ListInstanceAttributesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListInstanceStorageConfigs' => [ 'name' => 'ListInstanceStorageConfigs', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/storage-configs', ], 'input' => [ 'shape' => 'ListInstanceStorageConfigsRequest', ], 'output' => [ 'shape' => 'ListInstanceStorageConfigsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListInstances' => [ 'name' => 'ListInstances', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance', ], 'input' => [ 'shape' => 'ListInstancesRequest', ], 'output' => [ 'shape' => 'ListInstancesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListIntegrationAssociations' => [ 'name' => 'ListIntegrationAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/integration-associations', ], 'input' => [ 'shape' => 'ListIntegrationAssociationsRequest', ], 'output' => [ 'shape' => 'ListIntegrationAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListLambdaFunctions' => [ 'name' => 'ListLambdaFunctions', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/lambda-functions', ], 'input' => [ 'shape' => 'ListLambdaFunctionsRequest', ], 'output' => [ 'shape' => 'ListLambdaFunctionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListLexBots' => [ 'name' => 'ListLexBots', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/lex-bots', ], 'input' => [ 'shape' => 'ListLexBotsRequest', ], 'output' => [ 'shape' => 'ListLexBotsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListPhoneNumbers' => [ 'name' => 'ListPhoneNumbers', 'http' => [ 'method' => 'GET', 'requestUri' => '/phone-numbers-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListPhoneNumbersRequest', ], 'output' => [ 'shape' => 'ListPhoneNumbersResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListPrompts' => [ 'name' => 'ListPrompts', 'http' => [ 'method' => 'GET', 'requestUri' => '/prompts-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListPromptsRequest', ], 'output' => [ 'shape' => 'ListPromptsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListQueueQuickConnects' => [ 'name' => 'ListQueueQuickConnects', 'http' => [ 'method' => 'GET', 'requestUri' => '/queues/{InstanceId}/{QueueId}/quick-connects', ], 'input' => [ 'shape' => 'ListQueueQuickConnectsRequest', ], 'output' => [ 'shape' => 'ListQueueQuickConnectsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListQueues' => [ 'name' => 'ListQueues', 'http' => [ 'method' => 'GET', 'requestUri' => '/queues-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListQueuesRequest', ], 'output' => [ 'shape' => 'ListQueuesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListQuickConnects' => [ 'name' => 'ListQuickConnects', 'http' => [ 'method' => 'GET', 'requestUri' => '/quick-connects/{InstanceId}', ], 'input' => [ 'shape' => 'ListQuickConnectsRequest', ], 'output' => [ 'shape' => 'ListQuickConnectsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListRoutingProfileQueues' => [ 'name' => 'ListRoutingProfileQueues', 'http' => [ 'method' => 'GET', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}/queues', ], 'input' => [ 'shape' => 'ListRoutingProfileQueuesRequest', ], 'output' => [ 'shape' => 'ListRoutingProfileQueuesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListRoutingProfiles' => [ 'name' => 'ListRoutingProfiles', 'http' => [ 'method' => 'GET', 'requestUri' => '/routing-profiles-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListRoutingProfilesRequest', ], 'output' => [ 'shape' => 'ListRoutingProfilesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListSecurityKeys' => [ 'name' => 'ListSecurityKeys', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/security-keys', ], 'input' => [ 'shape' => 'ListSecurityKeysRequest', ], 'output' => [ 'shape' => 'ListSecurityKeysResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListSecurityProfilePermissions' => [ 'name' => 'ListSecurityProfilePermissions', 'http' => [ 'method' => 'GET', 'requestUri' => '/security-profiles-permissions/{InstanceId}/{SecurityProfileId}', ], 'input' => [ 'shape' => 'ListSecurityProfilePermissionsRequest', ], 'output' => [ 'shape' => 'ListSecurityProfilePermissionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListSecurityProfiles' => [ 'name' => 'ListSecurityProfiles', 'http' => [ 'method' => 'GET', 'requestUri' => '/security-profiles-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListSecurityProfilesRequest', ], 'output' => [ 'shape' => 'ListSecurityProfilesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListUseCases' => [ 'name' => 'ListUseCases', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/integration-associations/{IntegrationAssociationId}/use-cases', ], 'input' => [ 'shape' => 'ListUseCasesRequest', ], 'output' => [ 'shape' => 'ListUseCasesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListUserHierarchyGroups' => [ 'name' => 'ListUserHierarchyGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/user-hierarchy-groups-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListUserHierarchyGroupsRequest', ], 'output' => [ 'shape' => 'ListUserHierarchyGroupsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListUsers' => [ 'name' => 'ListUsers', 'http' => [ 'method' => 'GET', 'requestUri' => '/users-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListUsersRequest', ], 'output' => [ 'shape' => 'ListUsersResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ResumeContactRecording' => [ 'name' => 'ResumeContactRecording', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/resume-recording', ], 'input' => [ 'shape' => 'ResumeContactRecordingRequest', ], 'output' => [ 'shape' => 'ResumeContactRecordingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'StartChatContact' => [ 'name' => 'StartChatContact', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact/chat', ], 'input' => [ 'shape' => 'StartChatContactRequest', ], 'output' => [ 'shape' => 'StartChatContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'StartContactRecording' => [ 'name' => 'StartContactRecording', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/start-recording', ], 'input' => [ 'shape' => 'StartContactRecordingRequest', ], 'output' => [ 'shape' => 'StartContactRecordingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'StartContactStreaming' => [ 'name' => 'StartContactStreaming', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/start-streaming', ], 'input' => [ 'shape' => 'StartContactStreamingRequest', ], 'output' => [ 'shape' => 'StartContactStreamingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'StartOutboundVoiceContact' => [ 'name' => 'StartOutboundVoiceContact', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact/outbound-voice', ], 'input' => [ 'shape' => 'StartOutboundVoiceContactRequest', ], 'output' => [ 'shape' => 'StartOutboundVoiceContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DestinationNotAllowedException', ], [ 'shape' => 'OutboundContactNotPermittedException', ], ], ], 'StartTaskContact' => [ 'name' => 'StartTaskContact', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact/task', ], 'input' => [ 'shape' => 'StartTaskContactRequest', ], 'output' => [ 'shape' => 'StartTaskContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'StopContact' => [ 'name' => 'StopContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/stop', ], 'input' => [ 'shape' => 'StopContactRequest', ], 'output' => [ 'shape' => 'StopContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ContactNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'StopContactRecording' => [ 'name' => 'StopContactRecording', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/stop-recording', ], 'input' => [ 'shape' => 'StopContactRecordingRequest', ], 'output' => [ 'shape' => 'StopContactRecordingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'StopContactStreaming' => [ 'name' => 'StopContactStreaming', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/stop-streaming', ], 'input' => [ 'shape' => 'StopContactStreamingRequest', ], 'output' => [ 'shape' => 'StopContactStreamingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SuspendContactRecording' => [ 'name' => 'SuspendContactRecording', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/suspend-recording', ], 'input' => [ 'shape' => 'SuspendContactRecordingRequest', ], 'output' => [ 'shape' => 'SuspendContactRecordingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateAgentStatus' => [ 'name' => 'UpdateAgentStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/agent-status/{InstanceId}/{AgentStatusId}', ], 'input' => [ 'shape' => 'UpdateAgentStatusRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateContact' => [ 'name' => 'UpdateContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/contacts/{InstanceId}/{ContactId}', ], 'input' => [ 'shape' => 'UpdateContactRequest', ], 'output' => [ 'shape' => 'UpdateContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateContactAttributes' => [ 'name' => 'UpdateContactAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/attributes', ], 'input' => [ 'shape' => 'UpdateContactAttributesRequest', ], 'output' => [ 'shape' => 'UpdateContactAttributesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateContactFlowContent' => [ 'name' => 'UpdateContactFlowContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact-flows/{InstanceId}/{ContactFlowId}/content', ], 'input' => [ 'shape' => 'UpdateContactFlowContentRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidContactFlowException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateContactFlowMetadata' => [ 'name' => 'UpdateContactFlowMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact-flows/{InstanceId}/{ContactFlowId}/metadata', ], 'input' => [ 'shape' => 'UpdateContactFlowMetadataRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateContactFlowModuleContent' => [ 'name' => 'UpdateContactFlowModuleContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact-flow-modules/{InstanceId}/{ContactFlowModuleId}/content', ], 'input' => [ 'shape' => 'UpdateContactFlowModuleContentRequest', ], 'output' => [ 'shape' => 'UpdateContactFlowModuleContentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidContactFlowModuleException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateContactFlowModuleMetadata' => [ 'name' => 'UpdateContactFlowModuleMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact-flow-modules/{InstanceId}/{ContactFlowModuleId}/metadata', ], 'input' => [ 'shape' => 'UpdateContactFlowModuleMetadataRequest', ], 'output' => [ 'shape' => 'UpdateContactFlowModuleMetadataResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateContactFlowName' => [ 'name' => 'UpdateContactFlowName', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact-flows/{InstanceId}/{ContactFlowId}/name', ], 'input' => [ 'shape' => 'UpdateContactFlowNameRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateContactSchedule' => [ 'name' => 'UpdateContactSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/schedule', ], 'input' => [ 'shape' => 'UpdateContactScheduleRequest', ], 'output' => [ 'shape' => 'UpdateContactScheduleResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateHoursOfOperation' => [ 'name' => 'UpdateHoursOfOperation', 'http' => [ 'method' => 'POST', 'requestUri' => '/hours-of-operations/{InstanceId}/{HoursOfOperationId}', ], 'input' => [ 'shape' => 'UpdateHoursOfOperationRequest', ], 'errors' => [ [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateInstanceAttribute' => [ 'name' => 'UpdateInstanceAttribute', 'http' => [ 'method' => 'POST', 'requestUri' => '/instance/{InstanceId}/attribute/{AttributeType}', ], 'input' => [ 'shape' => 'UpdateInstanceAttributeRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateInstanceStorageConfig' => [ 'name' => 'UpdateInstanceStorageConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/instance/{InstanceId}/storage-config/{AssociationId}', ], 'input' => [ 'shape' => 'UpdateInstanceStorageConfigRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateQueueHoursOfOperation' => [ 'name' => 'UpdateQueueHoursOfOperation', 'http' => [ 'method' => 'POST', 'requestUri' => '/queues/{InstanceId}/{QueueId}/hours-of-operation', ], 'input' => [ 'shape' => 'UpdateQueueHoursOfOperationRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateQueueMaxContacts' => [ 'name' => 'UpdateQueueMaxContacts', 'http' => [ 'method' => 'POST', 'requestUri' => '/queues/{InstanceId}/{QueueId}/max-contacts', ], 'input' => [ 'shape' => 'UpdateQueueMaxContactsRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateQueueName' => [ 'name' => 'UpdateQueueName', 'http' => [ 'method' => 'POST', 'requestUri' => '/queues/{InstanceId}/{QueueId}/name', ], 'input' => [ 'shape' => 'UpdateQueueNameRequest', ], 'errors' => [ [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateQueueOutboundCallerConfig' => [ 'name' => 'UpdateQueueOutboundCallerConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/queues/{InstanceId}/{QueueId}/outbound-caller-config', ], 'input' => [ 'shape' => 'UpdateQueueOutboundCallerConfigRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateQueueStatus' => [ 'name' => 'UpdateQueueStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/queues/{InstanceId}/{QueueId}/status', ], 'input' => [ 'shape' => 'UpdateQueueStatusRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateQuickConnectConfig' => [ 'name' => 'UpdateQuickConnectConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/quick-connects/{InstanceId}/{QuickConnectId}/config', ], 'input' => [ 'shape' => 'UpdateQuickConnectConfigRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateQuickConnectName' => [ 'name' => 'UpdateQuickConnectName', 'http' => [ 'method' => 'POST', 'requestUri' => '/quick-connects/{InstanceId}/{QuickConnectId}/name', ], 'input' => [ 'shape' => 'UpdateQuickConnectNameRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateRoutingProfileConcurrency' => [ 'name' => 'UpdateRoutingProfileConcurrency', 'http' => [ 'method' => 'POST', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}/concurrency', ], 'input' => [ 'shape' => 'UpdateRoutingProfileConcurrencyRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateRoutingProfileDefaultOutboundQueue' => [ 'name' => 'UpdateRoutingProfileDefaultOutboundQueue', 'http' => [ 'method' => 'POST', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}/default-outbound-queue', ], 'input' => [ 'shape' => 'UpdateRoutingProfileDefaultOutboundQueueRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateRoutingProfileName' => [ 'name' => 'UpdateRoutingProfileName', 'http' => [ 'method' => 'POST', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}/name', ], 'input' => [ 'shape' => 'UpdateRoutingProfileNameRequest', ], 'errors' => [ [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateRoutingProfileQueues' => [ 'name' => 'UpdateRoutingProfileQueues', 'http' => [ 'method' => 'POST', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}/queues', ], 'input' => [ 'shape' => 'UpdateRoutingProfileQueuesRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateSecurityProfile' => [ 'name' => 'UpdateSecurityProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/security-profiles/{InstanceId}/{SecurityProfileId}', ], 'input' => [ 'shape' => 'UpdateSecurityProfileRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateUserHierarchy' => [ 'name' => 'UpdateUserHierarchy', 'http' => [ 'method' => 'POST', 'requestUri' => '/users/{InstanceId}/{UserId}/hierarchy', ], 'input' => [ 'shape' => 'UpdateUserHierarchyRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateUserHierarchyGroupName' => [ 'name' => 'UpdateUserHierarchyGroupName', 'http' => [ 'method' => 'POST', 'requestUri' => '/user-hierarchy-groups/{InstanceId}/{HierarchyGroupId}/name', ], 'input' => [ 'shape' => 'UpdateUserHierarchyGroupNameRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateUserHierarchyStructure' => [ 'name' => 'UpdateUserHierarchyStructure', 'http' => [ 'method' => 'POST', 'requestUri' => '/user-hierarchy-structure/{InstanceId}', ], 'input' => [ 'shape' => 'UpdateUserHierarchyStructureRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateUserIdentityInfo' => [ 'name' => 'UpdateUserIdentityInfo', 'http' => [ 'method' => 'POST', 'requestUri' => '/users/{InstanceId}/{UserId}/identity-info', ], 'input' => [ 'shape' => 'UpdateUserIdentityInfoRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateUserPhoneConfig' => [ 'name' => 'UpdateUserPhoneConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/users/{InstanceId}/{UserId}/phone-config', ], 'input' => [ 'shape' => 'UpdateUserPhoneConfigRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateUserRoutingProfile' => [ 'name' => 'UpdateUserRoutingProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/users/{InstanceId}/{UserId}/routing-profile', ], 'input' => [ 'shape' => 'UpdateUserRoutingProfileRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateUserSecurityProfiles' => [ 'name' => 'UpdateUserSecurityProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/users/{InstanceId}/{UserId}/security-profiles', ], 'input' => [ 'shape' => 'UpdateUserSecurityProfilesRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], ], 'shapes' => [ 'ARN' => [ 'type' => 'string', ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AfterContactWorkTimeLimit' => [ 'type' => 'integer', 'min' => 0, ], 'AgentFirstName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'AgentInfo' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'AgentResourceId', ], 'ConnectedToAgentTimestamp' => [ 'shape' => 'timestamp', ], ], ], 'AgentLastName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'AgentResourceId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'AgentStatus' => [ 'type' => 'structure', 'members' => [ 'AgentStatusARN' => [ 'shape' => 'ARN', ], 'AgentStatusId' => [ 'shape' => 'AgentStatusId', ], 'Name' => [ 'shape' => 'AgentStatusName', ], 'Description' => [ 'shape' => 'AgentStatusDescription', ], 'Type' => [ 'shape' => 'AgentStatusType', ], 'DisplayOrder' => [ 'shape' => 'AgentStatusOrderNumber', ], 'State' => [ 'shape' => 'AgentStatusState', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'AgentStatusDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'AgentStatusId' => [ 'type' => 'string', ], 'AgentStatusName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, ], 'AgentStatusOrderNumber' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'AgentStatusState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'AgentStatusSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'AgentStatusId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'AgentStatusName', ], 'Type' => [ 'shape' => 'AgentStatusType', ], ], ], 'AgentStatusSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentStatusSummary', ], ], 'AgentStatusType' => [ 'type' => 'string', 'enum' => [ 'ROUTABLE', 'CUSTOM', 'OFFLINE', ], ], 'AgentStatusTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentStatusType', ], 'max' => 3, ], 'AgentUsername' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'AliasArn' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'AnswerMachineDetectionConfig' => [ 'type' => 'structure', 'members' => [ 'EnableAnswerMachineDetection' => [ 'shape' => 'Boolean', ], 'AwaitAnswerMachinePrompt' => [ 'shape' => 'Boolean', ], ], ], 'AssociateApprovedOriginRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Origin', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Origin' => [ 'shape' => 'Origin', ], ], ], 'AssociateBotRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'LexBot' => [ 'shape' => 'LexBot', ], 'LexV2Bot' => [ 'shape' => 'LexV2Bot', ], ], ], 'AssociateInstanceStorageConfigRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ResourceType', 'StorageConfig', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ResourceType' => [ 'shape' => 'InstanceStorageResourceType', ], 'StorageConfig' => [ 'shape' => 'InstanceStorageConfig', ], ], ], 'AssociateInstanceStorageConfigResponse' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], ], ], 'AssociateLambdaFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'FunctionArn', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'FunctionArn' => [ 'shape' => 'FunctionArn', ], ], ], 'AssociateLexBotRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'LexBot', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'LexBot' => [ 'shape' => 'LexBot', ], ], ], 'AssociateQueueQuickConnectsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', 'QuickConnectIds', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'QuickConnectIds' => [ 'shape' => 'QuickConnectsList', ], ], ], 'AssociateRoutingProfileQueuesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', 'QueueConfigs', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], 'QueueConfigs' => [ 'shape' => 'RoutingProfileQueueConfigList', ], ], ], 'AssociateSecurityKeyRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Key', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Key' => [ 'shape' => 'PEM', ], ], ], 'AssociateSecurityKeyResponse' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], ], ], 'AssociationId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'AttachmentReference' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ReferenceKey', ], 'Value' => [ 'shape' => 'ReferenceValue', ], 'Status' => [ 'shape' => 'ReferenceStatus', ], ], ], 'Attribute' => [ 'type' => 'structure', 'members' => [ 'AttributeType' => [ 'shape' => 'InstanceAttributeType', ], 'Value' => [ 'shape' => 'InstanceAttributeValue', ], ], ], 'AttributeName' => [ 'type' => 'string', 'max' => 32767, 'min' => 1, ], 'AttributeValue' => [ 'type' => 'string', 'max' => 32767, 'min' => 0, ], 'Attributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValue', ], ], 'AttributesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Attribute', ], ], 'AutoAccept' => [ 'type' => 'boolean', ], 'Boolean' => [ 'type' => 'boolean', ], 'BotName' => [ 'type' => 'string', 'max' => 50, ], 'BucketName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'CampaignId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'Channel' => [ 'type' => 'string', 'enum' => [ 'VOICE', 'CHAT', 'TASK', ], ], 'Channels' => [ 'type' => 'list', 'member' => [ 'shape' => 'Channel', ], 'max' => 3, ], 'ChatContent' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ChatContentType' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ChatMessage' => [ 'type' => 'structure', 'required' => [ 'ContentType', 'Content', ], 'members' => [ 'ContentType' => [ 'shape' => 'ChatContentType', ], 'Content' => [ 'shape' => 'ChatContent', ], ], ], 'ChatStreamingConfiguration' => [ 'type' => 'structure', 'required' => [ 'StreamingEndpointArn', ], 'members' => [ 'StreamingEndpointArn' => [ 'shape' => 'ChatStreamingEndpointARN', ], ], ], 'ChatStreamingEndpointARN' => [ 'type' => 'string', 'max' => 350, 'min' => 1, ], 'ClientToken' => [ 'type' => 'string', 'max' => 500, ], 'CommonNameLength127' => [ 'type' => 'string', 'max' => 127, 'min' => 1, ], 'Comparison' => [ 'type' => 'string', 'enum' => [ 'LT', ], ], 'Concurrency' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'Contact' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ARN', ], 'Id' => [ 'shape' => 'ContactId', ], 'InitialContactId' => [ 'shape' => 'ContactId', ], 'PreviousContactId' => [ 'shape' => 'ContactId', ], 'InitiationMethod' => [ 'shape' => 'ContactInitiationMethod', ], 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'Channel' => [ 'shape' => 'Channel', ], 'QueueInfo' => [ 'shape' => 'QueueInfo', ], 'AgentInfo' => [ 'shape' => 'AgentInfo', ], 'InitiationTimestamp' => [ 'shape' => 'timestamp', ], 'DisconnectTimestamp' => [ 'shape' => 'timestamp', ], 'LastUpdateTimestamp' => [ 'shape' => 'timestamp', ], 'ScheduledTimestamp' => [ 'shape' => 'timestamp', ], ], ], 'ContactFlow' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ARN', ], 'Id' => [ 'shape' => 'ContactFlowId', ], 'Name' => [ 'shape' => 'ContactFlowName', ], 'Type' => [ 'shape' => 'ContactFlowType', ], 'State' => [ 'shape' => 'ContactFlowState', ], 'Description' => [ 'shape' => 'ContactFlowDescription', ], 'Content' => [ 'shape' => 'ContactFlowContent', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ContactFlowContent' => [ 'type' => 'string', ], 'ContactFlowDescription' => [ 'type' => 'string', ], 'ContactFlowId' => [ 'type' => 'string', 'max' => 500, ], 'ContactFlowModule' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ARN', ], 'Id' => [ 'shape' => 'ContactFlowModuleId', ], 'Name' => [ 'shape' => 'ContactFlowModuleName', ], 'Content' => [ 'shape' => 'ContactFlowModuleContent', ], 'Description' => [ 'shape' => 'ContactFlowModuleDescription', ], 'State' => [ 'shape' => 'ContactFlowModuleState', ], 'Status' => [ 'shape' => 'ContactFlowModuleStatus', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ContactFlowModuleContent' => [ 'type' => 'string', 'max' => 256000, 'min' => 1, ], 'ContactFlowModuleDescription' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '.*\\S.*', ], 'ContactFlowModuleId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ContactFlowModuleName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '.*\\S.*', ], 'ContactFlowModuleState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ARCHIVED', ], ], 'ContactFlowModuleStatus' => [ 'type' => 'string', 'enum' => [ 'PUBLISHED', 'SAVED', ], ], 'ContactFlowModuleSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ContactFlowModuleId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'ContactFlowModuleName', ], 'State' => [ 'shape' => 'ContactFlowModuleState', ], ], ], 'ContactFlowModulesSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactFlowModuleSummary', ], ], 'ContactFlowName' => [ 'type' => 'string', 'min' => 1, ], 'ContactFlowNotPublishedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ContactFlowState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ARCHIVED', ], ], 'ContactFlowSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ContactFlowId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'ContactFlowName', ], 'ContactFlowType' => [ 'shape' => 'ContactFlowType', ], 'ContactFlowState' => [ 'shape' => 'ContactFlowState', ], ], ], 'ContactFlowSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactFlowSummary', ], ], 'ContactFlowType' => [ 'type' => 'string', 'enum' => [ 'CONTACT_FLOW', 'CUSTOMER_QUEUE', 'CUSTOMER_HOLD', 'CUSTOMER_WHISPER', 'AGENT_HOLD', 'AGENT_WHISPER', 'OUTBOUND_WHISPER', 'AGENT_TRANSFER', 'QUEUE_TRANSFER', ], ], 'ContactFlowTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactFlowType', ], 'max' => 10, ], 'ContactId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ContactInitiationMethod' => [ 'type' => 'string', 'enum' => [ 'INBOUND', 'OUTBOUND', 'TRANSFER', 'QUEUE_TRANSFER', 'CALLBACK', 'API', ], ], 'ContactNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], 'ContactReferences' => [ 'type' => 'map', 'key' => [ 'shape' => 'ReferenceKey', ], 'value' => [ 'shape' => 'Reference', ], ], 'CreateAgentStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'State', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'AgentStatusName', ], 'Description' => [ 'shape' => 'AgentStatusDescription', ], 'State' => [ 'shape' => 'AgentStatusState', ], 'DisplayOrder' => [ 'shape' => 'AgentStatusOrderNumber', 'box' => true, ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateAgentStatusResponse' => [ 'type' => 'structure', 'members' => [ 'AgentStatusARN' => [ 'shape' => 'ARN', ], 'AgentStatusId' => [ 'shape' => 'AgentStatusId', ], ], ], 'CreateContactFlowModuleRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'Content', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'ContactFlowModuleName', ], 'Description' => [ 'shape' => 'ContactFlowModuleDescription', ], 'Content' => [ 'shape' => 'ContactFlowModuleContent', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateContactFlowModuleResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ContactFlowModuleId', ], 'Arn' => [ 'shape' => 'ARN', ], ], ], 'CreateContactFlowRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'Type', 'Content', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'ContactFlowName', ], 'Type' => [ 'shape' => 'ContactFlowType', ], 'Description' => [ 'shape' => 'ContactFlowDescription', ], 'Content' => [ 'shape' => 'ContactFlowContent', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateContactFlowResponse' => [ 'type' => 'structure', 'members' => [ 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'ContactFlowArn' => [ 'shape' => 'ARN', ], ], ], 'CreateHoursOfOperationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'TimeZone', 'Config', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'CommonNameLength127', ], 'Description' => [ 'shape' => 'HoursOfOperationDescription', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], 'Config' => [ 'shape' => 'HoursOfOperationConfigList', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateHoursOfOperationResponse' => [ 'type' => 'structure', 'members' => [ 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', ], 'HoursOfOperationArn' => [ 'shape' => 'ARN', ], ], ], 'CreateInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'IdentityManagementType', 'InboundCallsEnabled', 'OutboundCallsEnabled', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', ], 'IdentityManagementType' => [ 'shape' => 'DirectoryType', ], 'InstanceAlias' => [ 'shape' => 'DirectoryAlias', ], 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'InboundCallsEnabled' => [ 'shape' => 'InboundCallsEnabled', ], 'OutboundCallsEnabled' => [ 'shape' => 'OutboundCallsEnabled', ], ], ], 'CreateInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InstanceId', ], 'Arn' => [ 'shape' => 'ARN', ], ], ], 'CreateIntegrationAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'IntegrationType', 'IntegrationArn', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'IntegrationType' => [ 'shape' => 'IntegrationType', ], 'IntegrationArn' => [ 'shape' => 'ARN', ], 'SourceApplicationUrl' => [ 'shape' => 'URI', ], 'SourceApplicationName' => [ 'shape' => 'SourceApplicationName', ], 'SourceType' => [ 'shape' => 'SourceType', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateIntegrationAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'IntegrationAssociationId' => [ 'shape' => 'IntegrationAssociationId', ], 'IntegrationAssociationArn' => [ 'shape' => 'ARN', ], ], ], 'CreateQueueRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'HoursOfOperationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'CommonNameLength127', ], 'Description' => [ 'shape' => 'QueueDescription', ], 'OutboundCallerConfig' => [ 'shape' => 'OutboundCallerConfig', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', ], 'MaxContacts' => [ 'shape' => 'QueueMaxContacts', 'box' => true, ], 'QuickConnectIds' => [ 'shape' => 'QuickConnectsList', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateQueueResponse' => [ 'type' => 'structure', 'members' => [ 'QueueArn' => [ 'shape' => 'ARN', ], 'QueueId' => [ 'shape' => 'QueueId', ], ], ], 'CreateQuickConnectRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'QuickConnectConfig', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'QuickConnectName', ], 'Description' => [ 'shape' => 'QuickConnectDescription', ], 'QuickConnectConfig' => [ 'shape' => 'QuickConnectConfig', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateQuickConnectResponse' => [ 'type' => 'structure', 'members' => [ 'QuickConnectARN' => [ 'shape' => 'ARN', ], 'QuickConnectId' => [ 'shape' => 'QuickConnectId', ], ], ], 'CreateRoutingProfileRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'Description', 'DefaultOutboundQueueId', 'MediaConcurrencies', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'RoutingProfileName', ], 'Description' => [ 'shape' => 'RoutingProfileDescription', ], 'DefaultOutboundQueueId' => [ 'shape' => 'QueueId', ], 'QueueConfigs' => [ 'shape' => 'RoutingProfileQueueConfigList', ], 'MediaConcurrencies' => [ 'shape' => 'MediaConcurrencies', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateRoutingProfileResponse' => [ 'type' => 'structure', 'members' => [ 'RoutingProfileArn' => [ 'shape' => 'ARN', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', ], ], ], 'CreateSecurityProfileRequest' => [ 'type' => 'structure', 'required' => [ 'SecurityProfileName', 'InstanceId', ], 'members' => [ 'SecurityProfileName' => [ 'shape' => 'SecurityProfileName', ], 'Description' => [ 'shape' => 'SecurityProfileDescription', ], 'Permissions' => [ 'shape' => 'PermissionsList', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateSecurityProfileResponse' => [ 'type' => 'structure', 'members' => [ 'SecurityProfileId' => [ 'shape' => 'SecurityProfileId', ], 'SecurityProfileArn' => [ 'shape' => 'ARN', ], ], ], 'CreateUseCaseRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'IntegrationAssociationId', 'UseCaseType', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'IntegrationAssociationId' => [ 'shape' => 'IntegrationAssociationId', 'location' => 'uri', 'locationName' => 'IntegrationAssociationId', ], 'UseCaseType' => [ 'shape' => 'UseCaseType', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateUseCaseResponse' => [ 'type' => 'structure', 'members' => [ 'UseCaseId' => [ 'shape' => 'UseCaseId', ], 'UseCaseArn' => [ 'shape' => 'ARN', ], ], ], 'CreateUserHierarchyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'InstanceId', ], 'members' => [ 'Name' => [ 'shape' => 'HierarchyGroupName', ], 'ParentGroupId' => [ 'shape' => 'HierarchyGroupId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'CreateUserHierarchyGroupResponse' => [ 'type' => 'structure', 'members' => [ 'HierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', ], 'HierarchyGroupArn' => [ 'shape' => 'ARN', ], ], ], 'CreateUserRequest' => [ 'type' => 'structure', 'required' => [ 'Username', 'PhoneConfig', 'SecurityProfileIds', 'RoutingProfileId', 'InstanceId', ], 'members' => [ 'Username' => [ 'shape' => 'AgentUsername', ], 'Password' => [ 'shape' => 'Password', ], 'IdentityInfo' => [ 'shape' => 'UserIdentityInfo', ], 'PhoneConfig' => [ 'shape' => 'UserPhoneConfig', ], 'DirectoryUserId' => [ 'shape' => 'DirectoryUserId', ], 'SecurityProfileIds' => [ 'shape' => 'SecurityProfileIds', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', ], 'HierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateUserResponse' => [ 'type' => 'structure', 'members' => [ 'UserId' => [ 'shape' => 'UserId', ], 'UserArn' => [ 'shape' => 'ARN', ], ], ], 'Credentials' => [ 'type' => 'structure', 'members' => [ 'AccessToken' => [ 'shape' => 'SecurityToken', ], 'AccessTokenExpiration' => [ 'shape' => 'timestamp', ], 'RefreshToken' => [ 'shape' => 'SecurityToken', ], 'RefreshTokenExpiration' => [ 'shape' => 'timestamp', ], ], ], 'CurrentMetric' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'CurrentMetricName', ], 'Unit' => [ 'shape' => 'Unit', ], ], ], 'CurrentMetricData' => [ 'type' => 'structure', 'members' => [ 'Metric' => [ 'shape' => 'CurrentMetric', ], 'Value' => [ 'shape' => 'Value', 'box' => true, ], ], ], 'CurrentMetricDataCollections' => [ 'type' => 'list', 'member' => [ 'shape' => 'CurrentMetricData', ], ], 'CurrentMetricName' => [ 'type' => 'string', 'enum' => [ 'AGENTS_ONLINE', 'AGENTS_AVAILABLE', 'AGENTS_ON_CALL', 'AGENTS_NON_PRODUCTIVE', 'AGENTS_AFTER_CONTACT_WORK', 'AGENTS_ERROR', 'AGENTS_STAFFED', 'CONTACTS_IN_QUEUE', 'OLDEST_CONTACT_AGE', 'CONTACTS_SCHEDULED', 'AGENTS_ON_CONTACT', 'SLOTS_ACTIVE', 'SLOTS_AVAILABLE', ], ], 'CurrentMetricResult' => [ 'type' => 'structure', 'members' => [ 'Dimensions' => [ 'shape' => 'Dimensions', ], 'Collections' => [ 'shape' => 'CurrentMetricDataCollections', ], ], ], 'CurrentMetricResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'CurrentMetricResult', ], ], 'CurrentMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'CurrentMetric', ], ], 'Delay' => [ 'type' => 'integer', 'max' => 9999, 'min' => 0, ], 'DeleteContactFlowModuleRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowModuleId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowModuleId' => [ 'shape' => 'ContactFlowModuleId', 'location' => 'uri', 'locationName' => 'ContactFlowModuleId', ], ], ], 'DeleteContactFlowModuleResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteContactFlowRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', 'location' => 'uri', 'locationName' => 'ContactFlowId', ], ], ], 'DeleteHoursOfOperationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'HoursOfOperationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', 'location' => 'uri', 'locationName' => 'HoursOfOperationId', ], ], ], 'DeleteInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'DeleteIntegrationAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'IntegrationAssociationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'IntegrationAssociationId' => [ 'shape' => 'IntegrationAssociationId', 'location' => 'uri', 'locationName' => 'IntegrationAssociationId', ], ], ], 'DeleteQuickConnectRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QuickConnectId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QuickConnectId' => [ 'shape' => 'QuickConnectId', 'location' => 'uri', 'locationName' => 'QuickConnectId', ], ], ], 'DeleteSecurityProfileRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'SecurityProfileId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'SecurityProfileId' => [ 'shape' => 'SecurityProfileId', 'location' => 'uri', 'locationName' => 'SecurityProfileId', ], ], ], 'DeleteUseCaseRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'IntegrationAssociationId', 'UseCaseId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'IntegrationAssociationId' => [ 'shape' => 'IntegrationAssociationId', 'location' => 'uri', 'locationName' => 'IntegrationAssociationId', ], 'UseCaseId' => [ 'shape' => 'UseCaseId', 'location' => 'uri', 'locationName' => 'UseCaseId', ], ], ], 'DeleteUserHierarchyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'HierarchyGroupId', 'InstanceId', ], 'members' => [ 'HierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', 'location' => 'uri', 'locationName' => 'HierarchyGroupId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'DeleteUserRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'UserId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], ], ], 'DescribeAgentStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AgentStatusId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AgentStatusId' => [ 'shape' => 'AgentStatusId', 'location' => 'uri', 'locationName' => 'AgentStatusId', ], ], ], 'DescribeAgentStatusResponse' => [ 'type' => 'structure', 'members' => [ 'AgentStatus' => [ 'shape' => 'AgentStatus', ], ], ], 'DescribeContactFlowModuleRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowModuleId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowModuleId' => [ 'shape' => 'ContactFlowModuleId', 'location' => 'uri', 'locationName' => 'ContactFlowModuleId', ], ], ], 'DescribeContactFlowModuleResponse' => [ 'type' => 'structure', 'members' => [ 'ContactFlowModule' => [ 'shape' => 'ContactFlowModule', ], ], ], 'DescribeContactFlowRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', 'location' => 'uri', 'locationName' => 'ContactFlowId', ], ], ], 'DescribeContactFlowResponse' => [ 'type' => 'structure', 'members' => [ 'ContactFlow' => [ 'shape' => 'ContactFlow', ], ], ], 'DescribeContactRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', 'location' => 'uri', 'locationName' => 'ContactId', ], ], ], 'DescribeContactResponse' => [ 'type' => 'structure', 'members' => [ 'Contact' => [ 'shape' => 'Contact', ], ], ], 'DescribeHoursOfOperationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'HoursOfOperationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', 'location' => 'uri', 'locationName' => 'HoursOfOperationId', ], ], ], 'DescribeHoursOfOperationResponse' => [ 'type' => 'structure', 'members' => [ 'HoursOfOperation' => [ 'shape' => 'HoursOfOperation', ], ], ], 'DescribeInstanceAttributeRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AttributeType', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AttributeType' => [ 'shape' => 'InstanceAttributeType', 'location' => 'uri', 'locationName' => 'AttributeType', ], ], ], 'DescribeInstanceAttributeResponse' => [ 'type' => 'structure', 'members' => [ 'Attribute' => [ 'shape' => 'Attribute', ], ], ], 'DescribeInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'DescribeInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'Instance' => [ 'shape' => 'Instance', ], ], ], 'DescribeInstanceStorageConfigRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AssociationId', 'ResourceType', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AssociationId' => [ 'shape' => 'AssociationId', 'location' => 'uri', 'locationName' => 'AssociationId', ], 'ResourceType' => [ 'shape' => 'InstanceStorageResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], ], ], 'DescribeInstanceStorageConfigResponse' => [ 'type' => 'structure', 'members' => [ 'StorageConfig' => [ 'shape' => 'InstanceStorageConfig', ], ], ], 'DescribeQueueRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], ], ], 'DescribeQueueResponse' => [ 'type' => 'structure', 'members' => [ 'Queue' => [ 'shape' => 'Queue', ], ], ], 'DescribeQuickConnectRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QuickConnectId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QuickConnectId' => [ 'shape' => 'QuickConnectId', 'location' => 'uri', 'locationName' => 'QuickConnectId', ], ], ], 'DescribeQuickConnectResponse' => [ 'type' => 'structure', 'members' => [ 'QuickConnect' => [ 'shape' => 'QuickConnect', ], ], ], 'DescribeRoutingProfileRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], ], ], 'DescribeRoutingProfileResponse' => [ 'type' => 'structure', 'members' => [ 'RoutingProfile' => [ 'shape' => 'RoutingProfile', ], ], ], 'DescribeSecurityProfileRequest' => [ 'type' => 'structure', 'required' => [ 'SecurityProfileId', 'InstanceId', ], 'members' => [ 'SecurityProfileId' => [ 'shape' => 'SecurityProfileId', 'location' => 'uri', 'locationName' => 'SecurityProfileId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'DescribeSecurityProfileResponse' => [ 'type' => 'structure', 'members' => [ 'SecurityProfile' => [ 'shape' => 'SecurityProfile', ], ], ], 'DescribeUserHierarchyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'HierarchyGroupId', 'InstanceId', ], 'members' => [ 'HierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', 'location' => 'uri', 'locationName' => 'HierarchyGroupId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'DescribeUserHierarchyGroupResponse' => [ 'type' => 'structure', 'members' => [ 'HierarchyGroup' => [ 'shape' => 'HierarchyGroup', ], ], ], 'DescribeUserHierarchyStructureRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'DescribeUserHierarchyStructureResponse' => [ 'type' => 'structure', 'members' => [ 'HierarchyStructure' => [ 'shape' => 'HierarchyStructure', ], ], ], 'DescribeUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserId', 'InstanceId', ], 'members' => [ 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'DescribeUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, ], 'DestinationNotAllowedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'Dimensions' => [ 'type' => 'structure', 'members' => [ 'Queue' => [ 'shape' => 'QueueReference', ], 'Channel' => [ 'shape' => 'Channel', ], ], ], 'DirectoryAlias' => [ 'type' => 'string', 'max' => 62, 'min' => 1, 'pattern' => '^(?!d-)([\\da-zA-Z]+)([-]*[\\da-zA-Z])*$', 'sensitive' => true, ], 'DirectoryId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^d-[0-9a-f]{10}$', ], 'DirectoryType' => [ 'type' => 'string', 'enum' => [ 'SAML', 'CONNECT_MANAGED', 'EXISTING_DIRECTORY', ], ], 'DirectoryUserId' => [ 'type' => 'string', ], 'DisassociateApprovedOriginRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Origin', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Origin' => [ 'shape' => 'Origin', 'location' => 'querystring', 'locationName' => 'origin', ], ], ], 'DisassociateBotRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'LexBot' => [ 'shape' => 'LexBot', ], 'LexV2Bot' => [ 'shape' => 'LexV2Bot', ], ], ], 'DisassociateInstanceStorageConfigRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AssociationId', 'ResourceType', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AssociationId' => [ 'shape' => 'AssociationId', 'location' => 'uri', 'locationName' => 'AssociationId', ], 'ResourceType' => [ 'shape' => 'InstanceStorageResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], ], ], 'DisassociateLambdaFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'FunctionArn', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'FunctionArn' => [ 'shape' => 'FunctionArn', 'location' => 'querystring', 'locationName' => 'functionArn', ], ], ], 'DisassociateLexBotRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'BotName', 'LexRegion', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'BotName' => [ 'shape' => 'BotName', 'location' => 'querystring', 'locationName' => 'botName', ], 'LexRegion' => [ 'shape' => 'LexRegion', 'location' => 'querystring', 'locationName' => 'lexRegion', ], ], ], 'DisassociateQueueQuickConnectsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', 'QuickConnectIds', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'QuickConnectIds' => [ 'shape' => 'QuickConnectsList', ], ], ], 'DisassociateRoutingProfileQueuesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', 'QueueReferences', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], 'QueueReferences' => [ 'shape' => 'RoutingProfileQueueReferenceList', ], ], ], 'DisassociateSecurityKeyRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AssociationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AssociationId' => [ 'shape' => 'AssociationId', 'location' => 'uri', 'locationName' => 'AssociationId', ], ], ], 'DisplayName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'DuplicateResourceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'Email' => [ 'type' => 'string', ], 'EncryptionConfig' => [ 'type' => 'structure', 'required' => [ 'EncryptionType', 'KeyId', ], 'members' => [ 'EncryptionType' => [ 'shape' => 'EncryptionType', ], 'KeyId' => [ 'shape' => 'KeyId', ], ], ], 'EncryptionType' => [ 'type' => 'string', 'enum' => [ 'KMS', ], ], 'Filters' => [ 'type' => 'structure', 'members' => [ 'Queues' => [ 'shape' => 'Queues', ], 'Channels' => [ 'shape' => 'Channels', ], ], ], 'FunctionArn' => [ 'type' => 'string', 'max' => 140, 'min' => 1, ], 'FunctionArnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FunctionArn', ], ], 'GetContactAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'InitialContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'InitialContactId' => [ 'shape' => 'ContactId', 'location' => 'uri', 'locationName' => 'InitialContactId', ], ], ], 'GetContactAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'Attributes', ], ], ], 'GetCurrentMetricDataRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Filters', 'CurrentMetrics', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Filters' => [ 'shape' => 'Filters', ], 'Groupings' => [ 'shape' => 'Groupings', ], 'CurrentMetrics' => [ 'shape' => 'CurrentMetrics', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], ], ], 'GetCurrentMetricDataResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MetricResults' => [ 'shape' => 'CurrentMetricResults', ], 'DataSnapshotTime' => [ 'shape' => 'timestamp', ], ], ], 'GetFederationTokenRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'GetFederationTokenResponse' => [ 'type' => 'structure', 'members' => [ 'Credentials' => [ 'shape' => 'Credentials', ], ], ], 'GetMetricDataRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'StartTime', 'EndTime', 'Filters', 'HistoricalMetrics', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'StartTime' => [ 'shape' => 'timestamp', ], 'EndTime' => [ 'shape' => 'timestamp', ], 'Filters' => [ 'shape' => 'Filters', ], 'Groupings' => [ 'shape' => 'Groupings', ], 'HistoricalMetrics' => [ 'shape' => 'HistoricalMetrics', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], ], ], 'GetMetricDataResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MetricResults' => [ 'shape' => 'HistoricalMetricResults', ], ], ], 'Grouping' => [ 'type' => 'string', 'enum' => [ 'QUEUE', 'CHANNEL', ], ], 'Groupings' => [ 'type' => 'list', 'member' => [ 'shape' => 'Grouping', ], 'max' => 2, ], 'HierarchyGroup' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'HierarchyGroupId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'HierarchyGroupName', ], 'LevelId' => [ 'shape' => 'HierarchyLevelId', ], 'HierarchyPath' => [ 'shape' => 'HierarchyPath', ], ], ], 'HierarchyGroupId' => [ 'type' => 'string', ], 'HierarchyGroupName' => [ 'type' => 'string', ], 'HierarchyGroupSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'HierarchyGroupId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'HierarchyGroupName', ], ], ], 'HierarchyGroupSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HierarchyGroupSummary', ], ], 'HierarchyLevel' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'HierarchyLevelId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'HierarchyLevelName', ], ], ], 'HierarchyLevelId' => [ 'type' => 'string', ], 'HierarchyLevelName' => [ 'type' => 'string', ], 'HierarchyLevelUpdate' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'HierarchyLevelName', ], ], ], 'HierarchyPath' => [ 'type' => 'structure', 'members' => [ 'LevelOne' => [ 'shape' => 'HierarchyGroupSummary', ], 'LevelTwo' => [ 'shape' => 'HierarchyGroupSummary', ], 'LevelThree' => [ 'shape' => 'HierarchyGroupSummary', ], 'LevelFour' => [ 'shape' => 'HierarchyGroupSummary', ], 'LevelFive' => [ 'shape' => 'HierarchyGroupSummary', ], ], ], 'HierarchyStructure' => [ 'type' => 'structure', 'members' => [ 'LevelOne' => [ 'shape' => 'HierarchyLevel', ], 'LevelTwo' => [ 'shape' => 'HierarchyLevel', ], 'LevelThree' => [ 'shape' => 'HierarchyLevel', ], 'LevelFour' => [ 'shape' => 'HierarchyLevel', ], 'LevelFive' => [ 'shape' => 'HierarchyLevel', ], ], ], 'HierarchyStructureUpdate' => [ 'type' => 'structure', 'members' => [ 'LevelOne' => [ 'shape' => 'HierarchyLevelUpdate', ], 'LevelTwo' => [ 'shape' => 'HierarchyLevelUpdate', ], 'LevelThree' => [ 'shape' => 'HierarchyLevelUpdate', ], 'LevelFour' => [ 'shape' => 'HierarchyLevelUpdate', ], 'LevelFive' => [ 'shape' => 'HierarchyLevelUpdate', ], ], ], 'HistoricalMetric' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'HistoricalMetricName', ], 'Threshold' => [ 'shape' => 'Threshold', 'box' => true, ], 'Statistic' => [ 'shape' => 'Statistic', ], 'Unit' => [ 'shape' => 'Unit', ], ], ], 'HistoricalMetricData' => [ 'type' => 'structure', 'members' => [ 'Metric' => [ 'shape' => 'HistoricalMetric', ], 'Value' => [ 'shape' => 'Value', 'box' => true, ], ], ], 'HistoricalMetricDataCollections' => [ 'type' => 'list', 'member' => [ 'shape' => 'HistoricalMetricData', ], ], 'HistoricalMetricName' => [ 'type' => 'string', 'enum' => [ 'CONTACTS_QUEUED', 'CONTACTS_HANDLED', 'CONTACTS_ABANDONED', 'CONTACTS_CONSULTED', 'CONTACTS_AGENT_HUNG_UP_FIRST', 'CONTACTS_HANDLED_INCOMING', 'CONTACTS_HANDLED_OUTBOUND', 'CONTACTS_HOLD_ABANDONS', 'CONTACTS_TRANSFERRED_IN', 'CONTACTS_TRANSFERRED_OUT', 'CONTACTS_TRANSFERRED_IN_FROM_QUEUE', 'CONTACTS_TRANSFERRED_OUT_FROM_QUEUE', 'CONTACTS_MISSED', 'CALLBACK_CONTACTS_HANDLED', 'API_CONTACTS_HANDLED', 'OCCUPANCY', 'HANDLE_TIME', 'AFTER_CONTACT_WORK_TIME', 'QUEUED_TIME', 'ABANDON_TIME', 'QUEUE_ANSWER_TIME', 'HOLD_TIME', 'INTERACTION_TIME', 'INTERACTION_AND_HOLD_TIME', 'SERVICE_LEVEL', ], ], 'HistoricalMetricResult' => [ 'type' => 'structure', 'members' => [ 'Dimensions' => [ 'shape' => 'Dimensions', ], 'Collections' => [ 'shape' => 'HistoricalMetricDataCollections', ], ], ], 'HistoricalMetricResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'HistoricalMetricResult', ], ], 'HistoricalMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'HistoricalMetric', ], ], 'Hours' => [ 'type' => 'integer', 'max' => 87600, 'min' => 0, ], 'Hours24Format' => [ 'type' => 'integer', 'max' => 23, 'min' => 0, ], 'HoursOfOperation' => [ 'type' => 'structure', 'members' => [ 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', ], 'HoursOfOperationArn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'CommonNameLength127', ], 'Description' => [ 'shape' => 'HoursOfOperationDescription', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], 'Config' => [ 'shape' => 'HoursOfOperationConfigList', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'HoursOfOperationConfig' => [ 'type' => 'structure', 'required' => [ 'Day', 'StartTime', 'EndTime', ], 'members' => [ 'Day' => [ 'shape' => 'HoursOfOperationDays', ], 'StartTime' => [ 'shape' => 'HoursOfOperationTimeSlice', ], 'EndTime' => [ 'shape' => 'HoursOfOperationTimeSlice', ], ], ], 'HoursOfOperationConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HoursOfOperationConfig', ], 'max' => 100, 'min' => 0, ], 'HoursOfOperationDays' => [ 'type' => 'string', 'enum' => [ 'SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', ], ], 'HoursOfOperationDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'HoursOfOperationId' => [ 'type' => 'string', ], 'HoursOfOperationName' => [ 'type' => 'string', ], 'HoursOfOperationSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'HoursOfOperationId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'HoursOfOperationName', ], ], ], 'HoursOfOperationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HoursOfOperationSummary', ], ], 'HoursOfOperationTimeSlice' => [ 'type' => 'structure', 'required' => [ 'Hours', 'Minutes', ], 'members' => [ 'Hours' => [ 'shape' => 'Hours24Format', 'box' => true, ], 'Minutes' => [ 'shape' => 'MinutesLimit60', 'box' => true, ], ], ], 'IdempotencyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'InboundCallsEnabled' => [ 'type' => 'boolean', ], 'Instance' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InstanceId', ], 'Arn' => [ 'shape' => 'ARN', ], 'IdentityManagementType' => [ 'shape' => 'DirectoryType', ], 'InstanceAlias' => [ 'shape' => 'DirectoryAlias', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'ServiceRole' => [ 'shape' => 'ARN', ], 'InstanceStatus' => [ 'shape' => 'InstanceStatus', ], 'StatusReason' => [ 'shape' => 'InstanceStatusReason', ], 'InboundCallsEnabled' => [ 'shape' => 'InboundCallsEnabled', ], 'OutboundCallsEnabled' => [ 'shape' => 'OutboundCallsEnabled', ], ], ], 'InstanceAttributeType' => [ 'type' => 'string', 'enum' => [ 'INBOUND_CALLS', 'OUTBOUND_CALLS', 'CONTACTFLOW_LOGS', 'CONTACT_LENS', 'AUTO_RESOLVE_BEST_VOICES', 'USE_CUSTOM_TTS_VOICES', 'EARLY_MEDIA', ], ], 'InstanceAttributeValue' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'InstanceId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'InstanceStatus' => [ 'type' => 'string', 'enum' => [ 'CREATION_IN_PROGRESS', 'ACTIVE', 'CREATION_FAILED', ], ], 'InstanceStatusReason' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], ], 'InstanceStorageConfig' => [ 'type' => 'structure', 'required' => [ 'StorageType', ], 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], 'StorageType' => [ 'shape' => 'StorageType', ], 'S3Config' => [ 'shape' => 'S3Config', ], 'KinesisVideoStreamConfig' => [ 'shape' => 'KinesisVideoStreamConfig', ], 'KinesisStreamConfig' => [ 'shape' => 'KinesisStreamConfig', ], 'KinesisFirehoseConfig' => [ 'shape' => 'KinesisFirehoseConfig', ], ], ], 'InstanceStorageConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceStorageConfig', ], ], 'InstanceStorageResourceType' => [ 'type' => 'string', 'enum' => [ 'CHAT_TRANSCRIPTS', 'CALL_RECORDINGS', 'SCHEDULED_REPORTS', 'MEDIA_STREAMS', 'CONTACT_TRACE_RECORDS', 'AGENT_EVENTS', ], ], 'InstanceSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InstanceId', ], 'Arn' => [ 'shape' => 'ARN', ], 'IdentityManagementType' => [ 'shape' => 'DirectoryType', ], 'InstanceAlias' => [ 'shape' => 'DirectoryAlias', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'ServiceRole' => [ 'shape' => 'ARN', ], 'InstanceStatus' => [ 'shape' => 'InstanceStatus', ], 'InboundCallsEnabled' => [ 'shape' => 'InboundCallsEnabled', ], 'OutboundCallsEnabled' => [ 'shape' => 'OutboundCallsEnabled', ], ], ], 'InstanceSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceSummary', ], ], 'IntegrationAssociationId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'IntegrationAssociationSummary' => [ 'type' => 'structure', 'members' => [ 'IntegrationAssociationId' => [ 'shape' => 'IntegrationAssociationId', ], 'IntegrationAssociationArn' => [ 'shape' => 'ARN', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'IntegrationType' => [ 'shape' => 'IntegrationType', ], 'IntegrationArn' => [ 'shape' => 'ARN', ], 'SourceApplicationUrl' => [ 'shape' => 'URI', ], 'SourceApplicationName' => [ 'shape' => 'SourceApplicationName', ], 'SourceType' => [ 'shape' => 'SourceType', ], ], ], 'IntegrationAssociationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntegrationAssociationSummary', ], ], 'IntegrationType' => [ 'type' => 'string', 'enum' => [ 'EVENT', 'VOICE_ID', 'PINPOINT_APP', 'WISDOM_ASSISTANT', 'WISDOM_KNOWLEDGE_BASE', ], ], 'InternalServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'InvalidContactFlowException' => [ 'type' => 'structure', 'members' => [ 'problems' => [ 'shape' => 'Problems', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidContactFlowModuleException' => [ 'type' => 'structure', 'members' => [ 'Problems' => [ 'shape' => 'Problems', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'KeyId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'KinesisFirehoseConfig' => [ 'type' => 'structure', 'required' => [ 'FirehoseArn', ], 'members' => [ 'FirehoseArn' => [ 'shape' => 'ARN', ], ], ], 'KinesisStreamConfig' => [ 'type' => 'structure', 'required' => [ 'StreamArn', ], 'members' => [ 'StreamArn' => [ 'shape' => 'ARN', ], ], ], 'KinesisVideoStreamConfig' => [ 'type' => 'structure', 'required' => [ 'Prefix', 'RetentionPeriodHours', 'EncryptionConfig', ], 'members' => [ 'Prefix' => [ 'shape' => 'Prefix', ], 'RetentionPeriodHours' => [ 'shape' => 'Hours', ], 'EncryptionConfig' => [ 'shape' => 'EncryptionConfig', ], ], ], 'LexBot' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'BotName', ], 'LexRegion' => [ 'shape' => 'LexRegion', ], ], ], 'LexBotConfig' => [ 'type' => 'structure', 'members' => [ 'LexBot' => [ 'shape' => 'LexBot', ], 'LexV2Bot' => [ 'shape' => 'LexV2Bot', ], ], ], 'LexBotConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LexBotConfig', ], ], 'LexBotsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LexBot', ], ], 'LexRegion' => [ 'type' => 'string', 'max' => 60, ], 'LexV2Bot' => [ 'type' => 'structure', 'members' => [ 'AliasArn' => [ 'shape' => 'AliasArn', ], ], ], 'LexVersion' => [ 'type' => 'string', 'enum' => [ 'V1', 'V2', ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'ListAgentStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'AgentStatusTypes' => [ 'shape' => 'AgentStatusTypes', 'location' => 'querystring', 'locationName' => 'AgentStatusTypes', ], ], ], 'ListAgentStatusResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'AgentStatusSummaryList' => [ 'shape' => 'AgentStatusSummaryList', ], ], ], 'ListApprovedOriginsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult25', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListApprovedOriginsResponse' => [ 'type' => 'structure', 'members' => [ 'Origins' => [ 'shape' => 'OriginsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'LexVersion', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult25', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'LexVersion' => [ 'shape' => 'LexVersion', 'location' => 'querystring', 'locationName' => 'lexVersion', ], ], ], 'ListBotsResponse' => [ 'type' => 'structure', 'members' => [ 'LexBots' => [ 'shape' => 'LexBotConfigList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListContactFlowModulesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'ContactFlowModuleState' => [ 'shape' => 'ContactFlowModuleState', 'location' => 'querystring', 'locationName' => 'state', ], ], ], 'ListContactFlowModulesResponse' => [ 'type' => 'structure', 'members' => [ 'ContactFlowModulesSummaryList' => [ 'shape' => 'ContactFlowModulesSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListContactFlowsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowTypes' => [ 'shape' => 'ContactFlowTypes', 'location' => 'querystring', 'locationName' => 'contactFlowTypes', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListContactFlowsResponse' => [ 'type' => 'structure', 'members' => [ 'ContactFlowSummaryList' => [ 'shape' => 'ContactFlowSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListContactReferencesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'ReferenceTypes', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', 'location' => 'uri', 'locationName' => 'ContactId', ], 'ReferenceTypes' => [ 'shape' => 'ReferenceTypes', 'location' => 'querystring', 'locationName' => 'referenceTypes', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListContactReferencesResponse' => [ 'type' => 'structure', 'members' => [ 'ReferenceSummaryList' => [ 'shape' => 'ReferenceSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListHoursOfOperationsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListHoursOfOperationsResponse' => [ 'type' => 'structure', 'members' => [ 'HoursOfOperationSummaryList' => [ 'shape' => 'HoursOfOperationSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListInstanceAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult7', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListInstanceAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'AttributesList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListInstanceStorageConfigsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ResourceType', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ResourceType' => [ 'shape' => 'InstanceStorageResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult10', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListInstanceStorageConfigsResponse' => [ 'type' => 'structure', 'members' => [ 'StorageConfigs' => [ 'shape' => 'InstanceStorageConfigs', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult10', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'InstanceSummaryList' => [ 'shape' => 'InstanceSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIntegrationAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'IntegrationType' => [ 'shape' => 'IntegrationType', 'location' => 'querystring', 'locationName' => 'integrationType', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListIntegrationAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'IntegrationAssociationSummaryList' => [ 'shape' => 'IntegrationAssociationSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListLambdaFunctionsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult25', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListLambdaFunctionsResponse' => [ 'type' => 'structure', 'members' => [ 'LambdaFunctions' => [ 'shape' => 'FunctionArnsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListLexBotsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult25', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListLexBotsResponse' => [ 'type' => 'structure', 'members' => [ 'LexBots' => [ 'shape' => 'LexBotsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPhoneNumbersRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'PhoneNumberTypes' => [ 'shape' => 'PhoneNumberTypes', 'location' => 'querystring', 'locationName' => 'phoneNumberTypes', ], 'PhoneNumberCountryCodes' => [ 'shape' => 'PhoneNumberCountryCodes', 'location' => 'querystring', 'locationName' => 'phoneNumberCountryCodes', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListPhoneNumbersResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberSummaryList' => [ 'shape' => 'PhoneNumberSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPromptsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListPromptsResponse' => [ 'type' => 'structure', 'members' => [ 'PromptSummaryList' => [ 'shape' => 'PromptSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListQueueQuickConnectsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListQueueQuickConnectsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'QuickConnectSummaryList' => [ 'shape' => 'QuickConnectSummaryList', ], ], ], 'ListQueuesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueTypes' => [ 'shape' => 'QueueTypes', 'location' => 'querystring', 'locationName' => 'queueTypes', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListQueuesResponse' => [ 'type' => 'structure', 'members' => [ 'QueueSummaryList' => [ 'shape' => 'QueueSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListQuickConnectsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'QuickConnectTypes' => [ 'shape' => 'QuickConnectTypes', 'location' => 'querystring', 'locationName' => 'QuickConnectTypes', ], ], ], 'ListQuickConnectsResponse' => [ 'type' => 'structure', 'members' => [ 'QuickConnectSummaryList' => [ 'shape' => 'QuickConnectSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRoutingProfileQueuesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListRoutingProfileQueuesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'RoutingProfileQueueConfigSummaryList' => [ 'shape' => 'RoutingProfileQueueConfigSummaryList', ], ], ], 'ListRoutingProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListRoutingProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'RoutingProfileSummaryList' => [ 'shape' => 'RoutingProfileSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSecurityKeysRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult2', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListSecurityKeysResponse' => [ 'type' => 'structure', 'members' => [ 'SecurityKeys' => [ 'shape' => 'SecurityKeysList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSecurityProfilePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'SecurityProfileId', 'InstanceId', ], 'members' => [ 'SecurityProfileId' => [ 'shape' => 'SecurityProfileId', 'location' => 'uri', 'locationName' => 'SecurityProfileId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListSecurityProfilePermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'Permissions' => [ 'shape' => 'PermissionsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSecurityProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListSecurityProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'SecurityProfileSummaryList' => [ 'shape' => 'SecurityProfileSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListUseCasesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'IntegrationAssociationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'IntegrationAssociationId' => [ 'shape' => 'IntegrationAssociationId', 'location' => 'uri', 'locationName' => 'IntegrationAssociationId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListUseCasesResponse' => [ 'type' => 'structure', 'members' => [ 'UseCaseSummaryList' => [ 'shape' => 'UseCaseSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListUserHierarchyGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListUserHierarchyGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'UserHierarchyGroupSummaryList' => [ 'shape' => 'HierarchyGroupSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListUsersRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListUsersResponse' => [ 'type' => 'structure', 'members' => [ 'UserSummaryList' => [ 'shape' => 'UserSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'MaxResult10' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'MaxResult100' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxResult1000' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'MaxResult2' => [ 'type' => 'integer', 'max' => 2, 'min' => 1, ], 'MaxResult25' => [ 'type' => 'integer', 'max' => 25, 'min' => 1, ], 'MaxResult7' => [ 'type' => 'integer', 'max' => 7, 'min' => 1, ], 'MediaConcurrencies' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaConcurrency', ], ], 'MediaConcurrency' => [ 'type' => 'structure', 'required' => [ 'Channel', 'Concurrency', ], 'members' => [ 'Channel' => [ 'shape' => 'Channel', ], 'Concurrency' => [ 'shape' => 'Concurrency', ], ], ], 'Message' => [ 'type' => 'string', ], 'MinutesLimit60' => [ 'type' => 'integer', 'max' => 59, 'min' => 0, ], 'Name' => [ 'type' => 'string', 'max' => 512, 'min' => 0, ], 'NextToken' => [ 'type' => 'string', ], 'Origin' => [ 'type' => 'string', 'max' => 267, ], 'OriginsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Origin', ], ], 'OutboundCallerConfig' => [ 'type' => 'structure', 'members' => [ 'OutboundCallerIdName' => [ 'shape' => 'OutboundCallerIdName', ], 'OutboundCallerIdNumberId' => [ 'shape' => 'PhoneNumberId', ], 'OutboundFlowId' => [ 'shape' => 'ContactFlowId', ], ], ], 'OutboundCallerIdName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'OutboundCallsEnabled' => [ 'type' => 'boolean', ], 'OutboundContactNotPermittedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'PEM' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ParticipantDetails' => [ 'type' => 'structure', 'required' => [ 'DisplayName', ], 'members' => [ 'DisplayName' => [ 'shape' => 'DisplayName', ], ], ], 'ParticipantId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ParticipantToken' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'Password' => [ 'type' => 'string', 'pattern' => '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d\\S]{8,64}$/', ], 'PermissionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityProfilePermission', ], 'max' => 500, ], 'PhoneNumber' => [ 'type' => 'string', ], 'PhoneNumberCountryCode' => [ 'type' => 'string', 'enum' => [ 'AF', 'AL', 'DZ', 'AS', 'AD', 'AO', 'AI', 'AQ', 'AG', 'AR', 'AM', 'AW', 'AU', 'AT', 'AZ', 'BS', 'BH', 'BD', 'BB', 'BY', 'BE', 'BZ', 'BJ', 'BM', 'BT', 'BO', 'BA', 'BW', 'BR', 'IO', 'VG', 'BN', 'BG', 'BF', 'BI', 'KH', 'CM', 'CA', 'CV', 'KY', 'CF', 'TD', 'CL', 'CN', 'CX', 'CC', 'CO', 'KM', 'CK', 'CR', 'HR', 'CU', 'CW', 'CY', 'CZ', 'CD', 'DK', 'DJ', 'DM', 'DO', 'TL', 'EC', 'EG', 'SV', 'GQ', 'ER', 'EE', 'ET', 'FK', 'FO', 'FJ', 'FI', 'FR', 'PF', 'GA', 'GM', 'GE', 'DE', 'GH', 'GI', 'GR', 'GL', 'GD', 'GU', 'GT', 'GG', 'GN', 'GW', 'GY', 'HT', 'HN', 'HK', 'HU', 'IS', 'IN', 'ID', 'IR', 'IQ', 'IE', 'IM', 'IL', 'IT', 'CI', 'JM', 'JP', 'JE', 'JO', 'KZ', 'KE', 'KI', 'KW', 'KG', 'LA', 'LV', 'LB', 'LS', 'LR', 'LY', 'LI', 'LT', 'LU', 'MO', 'MK', 'MG', 'MW', 'MY', 'MV', 'ML', 'MT', 'MH', 'MR', 'MU', 'YT', 'MX', 'FM', 'MD', 'MC', 'MN', 'ME', 'MS', 'MA', 'MZ', 'MM', 'NA', 'NR', 'NP', 'NL', 'AN', 'NC', 'NZ', 'NI', 'NE', 'NG', 'NU', 'KP', 'MP', 'NO', 'OM', 'PK', 'PW', 'PA', 'PG', 'PY', 'PE', 'PH', 'PN', 'PL', 'PT', 'PR', 'QA', 'CG', 'RE', 'RO', 'RU', 'RW', 'BL', 'SH', 'KN', 'LC', 'MF', 'PM', 'VC', 'WS', 'SM', 'ST', 'SA', 'SN', 'RS', 'SC', 'SL', 'SG', 'SX', 'SK', 'SI', 'SB', 'SO', 'ZA', 'KR', 'ES', 'LK', 'SD', 'SR', 'SJ', 'SZ', 'SE', 'CH', 'SY', 'TW', 'TJ', 'TZ', 'TH', 'TG', 'TK', 'TO', 'TT', 'TN', 'TR', 'TM', 'TC', 'TV', 'VI', 'UG', 'UA', 'AE', 'GB', 'US', 'UY', 'UZ', 'VU', 'VA', 'VE', 'VN', 'WF', 'EH', 'YE', 'ZM', 'ZW', ], ], 'PhoneNumberCountryCodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberCountryCode', ], 'max' => 10, ], 'PhoneNumberId' => [ 'type' => 'string', ], 'PhoneNumberQuickConnectConfig' => [ 'type' => 'structure', 'required' => [ 'PhoneNumber', ], 'members' => [ 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'PhoneNumberSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'PhoneNumberId', ], 'Arn' => [ 'shape' => 'ARN', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'PhoneNumberType' => [ 'shape' => 'PhoneNumberType', ], 'PhoneNumberCountryCode' => [ 'shape' => 'PhoneNumberCountryCode', ], ], ], 'PhoneNumberSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberSummary', ], ], 'PhoneNumberType' => [ 'type' => 'string', 'enum' => [ 'TOLL_FREE', 'DID', ], ], 'PhoneNumberTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberType', ], 'max' => 2, ], 'PhoneType' => [ 'type' => 'string', 'enum' => [ 'SOFT_PHONE', 'DESK_PHONE', ], ], 'Prefix' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'Priority' => [ 'type' => 'integer', 'max' => 99, 'min' => 1, ], 'ProblemDetail' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ProblemMessageString', ], ], ], 'ProblemMessageString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Problems' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProblemDetail', ], 'max' => 50, 'min' => 1, ], 'PromptId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'PromptName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'PromptSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'PromptId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'PromptName', ], ], ], 'PromptSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PromptSummary', ], ], 'Queue' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'CommonNameLength127', ], 'QueueArn' => [ 'shape' => 'ARN', ], 'QueueId' => [ 'shape' => 'QueueId', ], 'Description' => [ 'shape' => 'QueueDescription', ], 'OutboundCallerConfig' => [ 'shape' => 'OutboundCallerConfig', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', ], 'MaxContacts' => [ 'shape' => 'QueueMaxContacts', 'box' => true, ], 'Status' => [ 'shape' => 'QueueStatus', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'QueueDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'QueueId' => [ 'type' => 'string', ], 'QueueInfo' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'QueueId', ], 'EnqueueTimestamp' => [ 'shape' => 'timestamp', ], ], ], 'QueueMaxContacts' => [ 'type' => 'integer', 'min' => 0, ], 'QueueName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'QueueQuickConnectConfig' => [ 'type' => 'structure', 'required' => [ 'QueueId', 'ContactFlowId', ], 'members' => [ 'QueueId' => [ 'shape' => 'QueueId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], ], ], 'QueueReference' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'QueueId', ], 'Arn' => [ 'shape' => 'ARN', ], ], ], 'QueueStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'QueueSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'QueueId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'QueueName', ], 'QueueType' => [ 'shape' => 'QueueType', ], ], ], 'QueueSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueSummary', ], ], 'QueueType' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'AGENT', ], ], 'QueueTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueType', ], 'max' => 2, ], 'Queues' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueId', ], 'max' => 100, 'min' => 1, ], 'QuickConnect' => [ 'type' => 'structure', 'members' => [ 'QuickConnectARN' => [ 'shape' => 'ARN', ], 'QuickConnectId' => [ 'shape' => 'QuickConnectId', ], 'Name' => [ 'shape' => 'QuickConnectName', ], 'Description' => [ 'shape' => 'QuickConnectDescription', ], 'QuickConnectConfig' => [ 'shape' => 'QuickConnectConfig', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'QuickConnectConfig' => [ 'type' => 'structure', 'required' => [ 'QuickConnectType', ], 'members' => [ 'QuickConnectType' => [ 'shape' => 'QuickConnectType', ], 'UserConfig' => [ 'shape' => 'UserQuickConnectConfig', ], 'QueueConfig' => [ 'shape' => 'QueueQuickConnectConfig', ], 'PhoneConfig' => [ 'shape' => 'PhoneNumberQuickConnectConfig', ], ], ], 'QuickConnectDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'QuickConnectId' => [ 'type' => 'string', ], 'QuickConnectName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, ], 'QuickConnectSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'QuickConnectId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'QuickConnectName', ], 'QuickConnectType' => [ 'shape' => 'QuickConnectType', ], ], ], 'QuickConnectSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickConnectSummary', ], ], 'QuickConnectType' => [ 'type' => 'string', 'enum' => [ 'USER', 'QUEUE', 'PHONE_NUMBER', ], ], 'QuickConnectTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickConnectType', ], 'max' => 3, ], 'QuickConnectsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickConnectId', ], 'max' => 50, 'min' => 1, ], 'Reference' => [ 'type' => 'structure', 'required' => [ 'Value', 'Type', ], 'members' => [ 'Value' => [ 'shape' => 'ReferenceValue', ], 'Type' => [ 'shape' => 'ReferenceType', ], ], ], 'ReferenceKey' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'ReferenceStatus' => [ 'type' => 'string', 'enum' => [ 'APPROVED', 'REJECTED', ], ], 'ReferenceSummary' => [ 'type' => 'structure', 'members' => [ 'Url' => [ 'shape' => 'UrlReference', ], 'Attachment' => [ 'shape' => 'AttachmentReference', ], ], 'union' => true, ], 'ReferenceSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReferenceSummary', ], ], 'ReferenceType' => [ 'type' => 'string', 'enum' => [ 'URL', 'ATTACHMENT', ], ], 'ReferenceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReferenceType', ], 'max' => 2, ], 'ReferenceValue' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, ], 'ResourceConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ResourceId' => [ 'shape' => 'ARN', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'CONTACT', 'CONTACT_FLOW', 'INSTANCE', 'PARTICIPANT', 'HIERARCHY_LEVEL', 'HIERARCHY_GROUP', 'USER', ], ], 'ResumeContactRecordingRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'InitialContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'InitialContactId' => [ 'shape' => 'ContactId', ], ], ], 'ResumeContactRecordingResponse' => [ 'type' => 'structure', 'members' => [], ], 'RoutingProfile' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Name' => [ 'shape' => 'RoutingProfileName', ], 'RoutingProfileArn' => [ 'shape' => 'ARN', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', ], 'Description' => [ 'shape' => 'RoutingProfileDescription', ], 'MediaConcurrencies' => [ 'shape' => 'MediaConcurrencies', ], 'DefaultOutboundQueueId' => [ 'shape' => 'QueueId', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'RoutingProfileDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'RoutingProfileId' => [ 'type' => 'string', ], 'RoutingProfileName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, ], 'RoutingProfileQueueConfig' => [ 'type' => 'structure', 'required' => [ 'QueueReference', 'Priority', 'Delay', ], 'members' => [ 'QueueReference' => [ 'shape' => 'RoutingProfileQueueReference', ], 'Priority' => [ 'shape' => 'Priority', 'box' => true, ], 'Delay' => [ 'shape' => 'Delay', 'box' => true, ], ], ], 'RoutingProfileQueueConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutingProfileQueueConfig', ], 'max' => 10, 'min' => 1, ], 'RoutingProfileQueueConfigSummary' => [ 'type' => 'structure', 'required' => [ 'QueueId', 'QueueArn', 'QueueName', 'Priority', 'Delay', 'Channel', ], 'members' => [ 'QueueId' => [ 'shape' => 'QueueId', ], 'QueueArn' => [ 'shape' => 'ARN', ], 'QueueName' => [ 'shape' => 'QueueName', ], 'Priority' => [ 'shape' => 'Priority', ], 'Delay' => [ 'shape' => 'Delay', ], 'Channel' => [ 'shape' => 'Channel', ], ], ], 'RoutingProfileQueueConfigSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutingProfileQueueConfigSummary', ], ], 'RoutingProfileQueueReference' => [ 'type' => 'structure', 'required' => [ 'QueueId', 'Channel', ], 'members' => [ 'QueueId' => [ 'shape' => 'QueueId', ], 'Channel' => [ 'shape' => 'Channel', ], ], ], 'RoutingProfileQueueReferenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutingProfileQueueReference', ], ], 'RoutingProfileSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'RoutingProfileId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'RoutingProfileName', ], ], ], 'RoutingProfileSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutingProfileSummary', ], ], 'S3Config' => [ 'type' => 'structure', 'required' => [ 'BucketName', 'BucketPrefix', ], 'members' => [ 'BucketName' => [ 'shape' => 'BucketName', ], 'BucketPrefix' => [ 'shape' => 'Prefix', ], 'EncryptionConfig' => [ 'shape' => 'EncryptionConfig', ], ], ], 'SecurityKey' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], 'Key' => [ 'shape' => 'PEM', ], 'CreationTime' => [ 'shape' => 'timestamp', ], ], ], 'SecurityKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityKey', ], ], 'SecurityProfile' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'SecurityProfileId', ], 'OrganizationResourceId' => [ 'shape' => 'InstanceId', ], 'Arn' => [ 'shape' => 'ARN', ], 'SecurityProfileName' => [ 'shape' => 'SecurityProfileName', ], 'Description' => [ 'shape' => 'SecurityProfileDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'SecurityProfileDescription' => [ 'type' => 'string', 'max' => 250, ], 'SecurityProfileId' => [ 'type' => 'string', ], 'SecurityProfileIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityProfileId', ], 'max' => 10, 'min' => 1, ], 'SecurityProfileName' => [ 'type' => 'string', ], 'SecurityProfilePermission' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'SecurityProfileSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'SecurityProfileId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'SecurityProfileName', ], ], ], 'SecurityProfileSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityProfileSummary', ], ], 'SecurityToken' => [ 'type' => 'string', 'sensitive' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'SourceApplicationName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_ -]+$', ], 'SourceType' => [ 'type' => 'string', 'enum' => [ 'SALESFORCE', 'ZENDESK', ], ], 'StartChatContactRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowId', 'ParticipantDetails', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'Attributes' => [ 'shape' => 'Attributes', ], 'ParticipantDetails' => [ 'shape' => 'ParticipantDetails', ], 'InitialMessage' => [ 'shape' => 'ChatMessage', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'StartChatContactResponse' => [ 'type' => 'structure', 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], 'ParticipantId' => [ 'shape' => 'ParticipantId', ], 'ParticipantToken' => [ 'shape' => 'ParticipantToken', ], ], ], 'StartContactRecordingRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'InitialContactId', 'VoiceRecordingConfiguration', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'InitialContactId' => [ 'shape' => 'ContactId', ], 'VoiceRecordingConfiguration' => [ 'shape' => 'VoiceRecordingConfiguration', ], ], ], 'StartContactRecordingResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartContactStreamingRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'ChatStreamingConfiguration', 'ClientToken', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'ChatStreamingConfiguration' => [ 'shape' => 'ChatStreamingConfiguration', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'StartContactStreamingResponse' => [ 'type' => 'structure', 'required' => [ 'StreamingId', ], 'members' => [ 'StreamingId' => [ 'shape' => 'StreamingId', ], ], ], 'StartOutboundVoiceContactRequest' => [ 'type' => 'structure', 'required' => [ 'DestinationPhoneNumber', 'ContactFlowId', 'InstanceId', ], 'members' => [ 'DestinationPhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'SourcePhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'QueueId' => [ 'shape' => 'QueueId', ], 'Attributes' => [ 'shape' => 'Attributes', ], 'AnswerMachineDetectionConfig' => [ 'shape' => 'AnswerMachineDetectionConfig', ], 'CampaignId' => [ 'shape' => 'CampaignId', ], 'TrafficType' => [ 'shape' => 'TrafficType', ], ], ], 'StartOutboundVoiceContactResponse' => [ 'type' => 'structure', 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], ], ], 'StartTaskContactRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowId', 'Name', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'PreviousContactId' => [ 'shape' => 'ContactId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'Attributes' => [ 'shape' => 'Attributes', ], 'Name' => [ 'shape' => 'Name', ], 'References' => [ 'shape' => 'ContactReferences', ], 'Description' => [ 'shape' => 'Description', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ScheduledTime' => [ 'shape' => 'Timestamp', ], ], ], 'StartTaskContactResponse' => [ 'type' => 'structure', 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], ], ], 'Statistic' => [ 'type' => 'string', 'enum' => [ 'SUM', 'MAX', 'AVG', ], ], 'StopContactRecordingRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'InitialContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'InitialContactId' => [ 'shape' => 'ContactId', ], ], ], 'StopContactRecordingResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactId', 'InstanceId', ], 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], ], ], 'StopContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopContactStreamingRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'StreamingId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'StreamingId' => [ 'shape' => 'StreamingId', ], ], ], 'StopContactStreamingResponse' => [ 'type' => 'structure', 'members' => [], ], 'StorageType' => [ 'type' => 'string', 'enum' => [ 'S3', 'KINESIS_VIDEO_STREAM', 'KINESIS_STREAM', 'KINESIS_FIREHOSE', ], ], 'StreamingId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'String' => [ 'type' => 'string', ], 'SuspendContactRecordingRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'InitialContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'InitialContactId' => [ 'shape' => 'ContactId', ], ], ], 'SuspendContactRecordingResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, ], 'Threshold' => [ 'type' => 'structure', 'members' => [ 'Comparison' => [ 'shape' => 'Comparison', ], 'ThresholdValue' => [ 'shape' => 'ThresholdValue', 'box' => true, ], ], ], 'ThresholdValue' => [ 'type' => 'double', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TimeZone' => [ 'type' => 'string', ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TrafficType' => [ 'type' => 'string', 'enum' => [ 'GENERAL', 'CAMPAIGN', ], ], 'URI' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'Unit' => [ 'type' => 'string', 'enum' => [ 'SECONDS', 'COUNT', 'PERCENT', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UpdateAgentStatusDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 0, ], 'UpdateAgentStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AgentStatusId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AgentStatusId' => [ 'shape' => 'AgentStatusId', 'location' => 'uri', 'locationName' => 'AgentStatusId', ], 'Name' => [ 'shape' => 'AgentStatusName', ], 'Description' => [ 'shape' => 'UpdateAgentStatusDescription', ], 'State' => [ 'shape' => 'AgentStatusState', ], 'DisplayOrder' => [ 'shape' => 'AgentStatusOrderNumber', 'box' => true, ], 'ResetOrderNumber' => [ 'shape' => 'Boolean', ], ], ], 'UpdateContactAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'InitialContactId', 'InstanceId', 'Attributes', ], 'members' => [ 'InitialContactId' => [ 'shape' => 'ContactId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Attributes' => [ 'shape' => 'Attributes', ], ], ], 'UpdateContactAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContactFlowContentRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowId', 'Content', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', 'location' => 'uri', 'locationName' => 'ContactFlowId', ], 'Content' => [ 'shape' => 'ContactFlowContent', ], ], ], 'UpdateContactFlowMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', 'location' => 'uri', 'locationName' => 'ContactFlowId', ], 'Name' => [ 'shape' => 'ContactFlowName', ], 'Description' => [ 'shape' => 'ContactFlowDescription', ], 'ContactFlowState' => [ 'shape' => 'ContactFlowState', ], ], ], 'UpdateContactFlowModuleContentRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowModuleId', 'Content', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowModuleId' => [ 'shape' => 'ContactFlowModuleId', 'location' => 'uri', 'locationName' => 'ContactFlowModuleId', ], 'Content' => [ 'shape' => 'ContactFlowModuleContent', ], ], ], 'UpdateContactFlowModuleContentResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContactFlowModuleMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowModuleId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowModuleId' => [ 'shape' => 'ContactFlowModuleId', 'location' => 'uri', 'locationName' => 'ContactFlowModuleId', ], 'Name' => [ 'shape' => 'ContactFlowModuleName', ], 'Description' => [ 'shape' => 'ContactFlowModuleDescription', ], 'State' => [ 'shape' => 'ContactFlowModuleState', ], ], ], 'UpdateContactFlowModuleMetadataResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContactFlowNameRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', 'location' => 'uri', 'locationName' => 'ContactFlowId', ], 'Name' => [ 'shape' => 'ContactFlowName', ], 'Description' => [ 'shape' => 'ContactFlowDescription', ], ], ], 'UpdateContactRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', 'location' => 'uri', 'locationName' => 'ContactId', ], 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'References' => [ 'shape' => 'ContactReferences', ], ], ], 'UpdateContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContactScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'ScheduledTime', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'ScheduledTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateContactScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateHoursOfOperationDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 0, ], 'UpdateHoursOfOperationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'HoursOfOperationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', 'location' => 'uri', 'locationName' => 'HoursOfOperationId', ], 'Name' => [ 'shape' => 'CommonNameLength127', ], 'Description' => [ 'shape' => 'UpdateHoursOfOperationDescription', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], 'Config' => [ 'shape' => 'HoursOfOperationConfigList', ], ], ], 'UpdateInstanceAttributeRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AttributeType', 'Value', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AttributeType' => [ 'shape' => 'InstanceAttributeType', 'location' => 'uri', 'locationName' => 'AttributeType', ], 'Value' => [ 'shape' => 'InstanceAttributeValue', ], ], ], 'UpdateInstanceStorageConfigRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AssociationId', 'ResourceType', 'StorageConfig', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AssociationId' => [ 'shape' => 'AssociationId', 'location' => 'uri', 'locationName' => 'AssociationId', ], 'ResourceType' => [ 'shape' => 'InstanceStorageResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'StorageConfig' => [ 'shape' => 'InstanceStorageConfig', ], ], ], 'UpdateQueueHoursOfOperationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', 'HoursOfOperationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', ], ], ], 'UpdateQueueMaxContactsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'MaxContacts' => [ 'shape' => 'QueueMaxContacts', 'box' => true, ], ], ], 'UpdateQueueNameRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'Name' => [ 'shape' => 'CommonNameLength127', ], 'Description' => [ 'shape' => 'QueueDescription', ], ], ], 'UpdateQueueOutboundCallerConfigRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', 'OutboundCallerConfig', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'OutboundCallerConfig' => [ 'shape' => 'OutboundCallerConfig', ], ], ], 'UpdateQueueStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', 'Status', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'Status' => [ 'shape' => 'QueueStatus', ], ], ], 'UpdateQuickConnectConfigRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QuickConnectId', 'QuickConnectConfig', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QuickConnectId' => [ 'shape' => 'QuickConnectId', 'location' => 'uri', 'locationName' => 'QuickConnectId', ], 'QuickConnectConfig' => [ 'shape' => 'QuickConnectConfig', ], ], ], 'UpdateQuickConnectDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 0, ], 'UpdateQuickConnectNameRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QuickConnectId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QuickConnectId' => [ 'shape' => 'QuickConnectId', 'location' => 'uri', 'locationName' => 'QuickConnectId', ], 'Name' => [ 'shape' => 'QuickConnectName', ], 'Description' => [ 'shape' => 'UpdateQuickConnectDescription', ], ], ], 'UpdateRoutingProfileConcurrencyRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', 'MediaConcurrencies', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], 'MediaConcurrencies' => [ 'shape' => 'MediaConcurrencies', ], ], ], 'UpdateRoutingProfileDefaultOutboundQueueRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', 'DefaultOutboundQueueId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], 'DefaultOutboundQueueId' => [ 'shape' => 'QueueId', ], ], ], 'UpdateRoutingProfileNameRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], 'Name' => [ 'shape' => 'RoutingProfileName', ], 'Description' => [ 'shape' => 'RoutingProfileDescription', ], ], ], 'UpdateRoutingProfileQueuesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', 'QueueConfigs', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], 'QueueConfigs' => [ 'shape' => 'RoutingProfileQueueConfigList', ], ], ], 'UpdateSecurityProfileRequest' => [ 'type' => 'structure', 'required' => [ 'SecurityProfileId', 'InstanceId', ], 'members' => [ 'Description' => [ 'shape' => 'SecurityProfileDescription', ], 'Permissions' => [ 'shape' => 'PermissionsList', ], 'SecurityProfileId' => [ 'shape' => 'SecurityProfileId', 'location' => 'uri', 'locationName' => 'SecurityProfileId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'UpdateUserHierarchyGroupNameRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'HierarchyGroupId', 'InstanceId', ], 'members' => [ 'Name' => [ 'shape' => 'HierarchyGroupName', ], 'HierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', 'location' => 'uri', 'locationName' => 'HierarchyGroupId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'UpdateUserHierarchyRequest' => [ 'type' => 'structure', 'required' => [ 'UserId', 'InstanceId', ], 'members' => [ 'HierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'UpdateUserHierarchyStructureRequest' => [ 'type' => 'structure', 'required' => [ 'HierarchyStructure', 'InstanceId', ], 'members' => [ 'HierarchyStructure' => [ 'shape' => 'HierarchyStructureUpdate', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'UpdateUserIdentityInfoRequest' => [ 'type' => 'structure', 'required' => [ 'IdentityInfo', 'UserId', 'InstanceId', ], 'members' => [ 'IdentityInfo' => [ 'shape' => 'UserIdentityInfo', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'UpdateUserPhoneConfigRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneConfig', 'UserId', 'InstanceId', ], 'members' => [ 'PhoneConfig' => [ 'shape' => 'UserPhoneConfig', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'UpdateUserRoutingProfileRequest' => [ 'type' => 'structure', 'required' => [ 'RoutingProfileId', 'UserId', 'InstanceId', ], 'members' => [ 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'UpdateUserSecurityProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'SecurityProfileIds', 'UserId', 'InstanceId', ], 'members' => [ 'SecurityProfileIds' => [ 'shape' => 'SecurityProfileIds', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'UrlReference' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ReferenceKey', ], 'Value' => [ 'shape' => 'ReferenceValue', ], ], ], 'UseCase' => [ 'type' => 'structure', 'members' => [ 'UseCaseId' => [ 'shape' => 'UseCaseId', ], 'UseCaseArn' => [ 'shape' => 'ARN', ], 'UseCaseType' => [ 'shape' => 'UseCaseType', ], ], ], 'UseCaseId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'UseCaseSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UseCase', ], ], 'UseCaseType' => [ 'type' => 'string', 'enum' => [ 'RULES_EVALUATION', 'CONNECT_CAMPAIGNS', ], ], 'User' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'UserId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Username' => [ 'shape' => 'AgentUsername', ], 'IdentityInfo' => [ 'shape' => 'UserIdentityInfo', ], 'PhoneConfig' => [ 'shape' => 'UserPhoneConfig', ], 'DirectoryUserId' => [ 'shape' => 'DirectoryUserId', ], 'SecurityProfileIds' => [ 'shape' => 'SecurityProfileIds', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', ], 'HierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'UserId' => [ 'type' => 'string', ], 'UserIdentityInfo' => [ 'type' => 'structure', 'members' => [ 'FirstName' => [ 'shape' => 'AgentFirstName', ], 'LastName' => [ 'shape' => 'AgentLastName', ], 'Email' => [ 'shape' => 'Email', ], ], ], 'UserNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'UserPhoneConfig' => [ 'type' => 'structure', 'required' => [ 'PhoneType', ], 'members' => [ 'PhoneType' => [ 'shape' => 'PhoneType', ], 'AutoAccept' => [ 'shape' => 'AutoAccept', ], 'AfterContactWorkTimeLimit' => [ 'shape' => 'AfterContactWorkTimeLimit', ], 'DeskPhoneNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'UserQuickConnectConfig' => [ 'type' => 'structure', 'required' => [ 'UserId', 'ContactFlowId', ], 'members' => [ 'UserId' => [ 'shape' => 'UserId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], ], ], 'UserSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'UserId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Username' => [ 'shape' => 'AgentUsername', ], ], ], 'UserSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserSummary', ], ], 'Value' => [ 'type' => 'double', ], 'VoiceRecordingConfiguration' => [ 'type' => 'structure', 'members' => [ 'VoiceRecordingTrack' => [ 'shape' => 'VoiceRecordingTrack', ], ], ], 'VoiceRecordingTrack' => [ 'type' => 'string', 'enum' => [ 'FROM_AGENT', 'TO_AGENT', 'ALL', ], ], 'timestamp' => [ 'type' => 'timestamp', ], ],];
