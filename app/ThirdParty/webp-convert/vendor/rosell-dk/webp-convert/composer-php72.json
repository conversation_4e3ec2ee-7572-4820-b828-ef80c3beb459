{"name": "rosell-dk/webp-convert", "description": "Convert JPEG & PNG to WebP with PHP", "type": "library", "license": "MIT", "keywords": ["webp", "images", "cwebp", "imagick", "gd", "jpg2webp", "png2webp", "jpg", "png", "image conversion"], "scripts": {"ci": ["@test", "@phpcs-all", "@composer validate --no-check-all --strict", "@phpstan-global"], "test": "phpunit --coverage-text", "phpunit": "phpunit --coverage-text", "test-no-cov": "phpunit --no-coverage", "cs-fix-all": ["php-cs-fixer fix src"], "cs-fix": "php-cs-fixer fix", "cs-dry": "php-cs-fixer fix --dry-run --diff", "phpcs": "phpcs --standard=PSR2", "phpcs-all": "phpcs --standard=PSR2 src", "phpcbf": "phpcbf --standard=PSR2", "phpstan": "vendor/bin/phpstan analyse src --level=4", "phpstan-global-old": "~/.composer/vendor/bin/phpstan analyse src --level=4", "phpstan-global": "~/.config/composer/vendor/bin/phpstan analyse src --level=4"}, "extra": {"scripts-descriptions": {"ci": "Run tests before CI", "phpcs": "Checks coding styles (PSR2) of file/dir, which you must supply. To check all, supply 'src'", "phpcbf": "Fix coding styles (PSR2) of file/dir, which you must supply. To fix all, supply 'src'", "cs-fix-all": "Fix the coding style of all the source files, to comply with the PSR-2 coding standard", "cs-fix": "Fix the coding style of a PHP file or directory, which you must specify.", "test": "Launches the preconfigured PHPUnit"}}, "autoload": {"psr-4": {"WebPConvert\\": "src/"}}, "autoload-dev": {"psr-4": {"WebPConvert\\Tests\\": "tests/"}}, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.bitwise-it.dk/contact", "role": "Project Author"}, {"name": "<PERSON>", "homepage": "https://twobrain.io", "role": "Collaborator"}], "require": {"php": "^7.2", "rosell-dk/image-mime-type-guesser": "^0.3"}, "suggest": {"ext-gd": "to use GD extension for converting. Note: Gd must be compiled with webp support", "ext-imagick": "to use Imagick extension for converting. Note: Gd must be compiled with webp support", "ext-vips": "to use Vips extension for converting.", "php-stan/php-stan": "Suggested for dev, in order to analyse code before committing"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.11", "phpunit/phpunit": "^8.0", "squizlabs/php_codesniffer": "3.*"}, "config": {"sort-packages": true}}