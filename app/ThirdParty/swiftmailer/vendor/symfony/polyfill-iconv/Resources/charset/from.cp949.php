<?php

static $data = array (
  ' ' => ' ',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '	' => '	',
  '
' => '
',
  '' => '',
  '' => '',
  '
' => '
',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  ' ' => ' ',
  '!' => '!',
  '"' => '"',
  '#' => '#',
  '$' => '$',
  '%' => '%',
  '&' => '&',
  '\'' => '\'',
  '(' => '(',
  ')' => ')',
  '*' => '*',
  '+' => '+',
  ',' => ',',
  '-' => '-',
  '.' => '.',
  '/' => '/',
  0 => '0',
  1 => '1',
  2 => '2',
  3 => '3',
  4 => '4',
  5 => '5',
  6 => '6',
  7 => '7',
  8 => '8',
  9 => '9',
  ':' => ':',
  ';' => ';',
  '<' => '<',
  '=' => '=',
  '>' => '>',
  '?' => '?',
  '@' => '@',
  'A' => 'A',
  'B' => 'B',
  'C' => 'C',
  'D' => 'D',
  'E' => 'E',
  'F' => 'F',
  'G' => 'G',
  'H' => 'H',
  'I' => 'I',
  'J' => 'J',
  'K' => 'K',
  'L' => 'L',
  'M' => 'M',
  'N' => 'N',
  'O' => 'O',
  'P' => 'P',
  'Q' => 'Q',
  'R' => 'R',
  'S' => 'S',
  'T' => 'T',
  'U' => 'U',
  'V' => 'V',
  'W' => 'W',
  'X' => 'X',
  'Y' => 'Y',
  'Z' => 'Z',
  '[' => '[',
  '\\' => '\\',
  ']' => ']',
  '^' => '^',
  '_' => '_',
  '`' => '`',
  'a' => 'a',
  'b' => 'b',
  'c' => 'c',
  'd' => 'd',
  'e' => 'e',
  'f' => 'f',
  'g' => 'g',
  'h' => 'h',
  'i' => 'i',
  'j' => 'j',
  'k' => 'k',
  'l' => 'l',
  'm' => 'm',
  'n' => 'n',
  'o' => 'o',
  'p' => 'p',
  'q' => 'q',
  'r' => 'r',
  's' => 's',
  't' => 't',
  'u' => 'u',
  'v' => 'v',
  'w' => 'w',
  'x' => 'x',
  'y' => 'y',
  'z' => 'z',
  '{' => '{',
  '|' => '|',
  '}' => '}',
  '~' => '~',
  '' => '',
  '�A' => '갂',
  '�B' => '갃',
  '�C' => '갅',
  '�D' => '갆',
  '�E' => '갋',
  '�F' => '갌',
  '�G' => '갍',
  '�H' => '갎',
  '�I' => '갏',
  '�J' => '갘',
  '�K' => '갞',
  '�L' => '갟',
  '�M' => '갡',
  '�N' => '갢',
  '�O' => '갣',
  '�P' => '갥',
  '�Q' => '갦',
  '�R' => '갧',
  '�S' => '갨',
  '�T' => '갩',
  '�U' => '갪',
  '�V' => '갫',
  '�W' => '갮',
  '�X' => '갲',
  '�Y' => '갳',
  '�Z' => '갴',
  '�a' => '갵',
  '�b' => '갶',
  '�c' => '갷',
  '�d' => '갺',
  '�e' => '갻',
  '�f' => '갽',
  '�g' => '갾',
  '�h' => '갿',
  '�i' => '걁',
  '�j' => '걂',
  '�k' => '걃',
  '�l' => '걄',
  '�m' => '걅',
  '�n' => '걆',
  '�o' => '걇',
  '�p' => '걈',
  '�q' => '걉',
  '�r' => '걊',
  '�s' => '걌',
  '�t' => '걎',
  '�u' => '걏',
  '�v' => '걐',
  '�w' => '걑',
  '�x' => '걒',
  '�y' => '걓',
  '�z' => '걕',
  '��' => '걖',
  '��' => '걗',
  '��' => '걙',
  '��' => '걚',
  '��' => '걛',
  '��' => '걝',
  '��' => '걞',
  '��' => '걟',
  '��' => '걠',
  '��' => '걡',
  '��' => '걢',
  '��' => '걣',
  '��' => '걤',
  '��' => '걥',
  '��' => '걦',
  '��' => '걧',
  '��' => '걨',
  '��' => '걩',
  '��' => '걪',
  '��' => '걫',
  '��' => '걬',
  '��' => '걭',
  '��' => '걮',
  '��' => '걯',
  '��' => '걲',
  '��' => '걳',
  '��' => '걵',
  '��' => '걶',
  '��' => '걹',
  '��' => '걻',
  '��' => '걼',
  '��' => '걽',
  '��' => '걾',
  '��' => '걿',
  '��' => '겂',
  '��' => '겇',
  '��' => '겈',
  '��' => '겍',
  '��' => '겎',
  '��' => '겏',
  '��' => '겑',
  '��' => '겒',
  '��' => '겓',
  '��' => '겕',
  '��' => '겖',
  '��' => '겗',
  '��' => '겘',
  '��' => '겙',
  '��' => '겚',
  '��' => '겛',
  '��' => '겞',
  '��' => '겢',
  '��' => '겣',
  '��' => '겤',
  '��' => '겥',
  '��' => '겦',
  '��' => '겧',
  '��' => '겫',
  '��' => '겭',
  '��' => '겮',
  '��' => '겱',
  '��' => '겲',
  '��' => '겳',
  '��' => '겴',
  '��' => '겵',
  '��' => '겶',
  '��' => '겷',
  '��' => '겺',
  '��' => '겾',
  '��' => '겿',
  '��' => '곀',
  '��' => '곂',
  '��' => '곃',
  '��' => '곅',
  '��' => '곆',
  '��' => '곇',
  '��' => '곉',
  '��' => '곊',
  '��' => '곋',
  '��' => '곍',
  '��' => '곎',
  '��' => '곏',
  '��' => '곐',
  '��' => '곑',
  '��' => '곒',
  '��' => '곓',
  '��' => '곔',
  '��' => '곖',
  '��' => '곘',
  '��' => '곙',
  '��' => '곚',
  '��' => '곛',
  '��' => '곜',
  '��' => '곝',
  '��' => '곞',
  '��' => '곟',
  '��' => '곢',
  '��' => '곣',
  '��' => '곥',
  '��' => '곦',
  '��' => '곩',
  '��' => '곫',
  '��' => '곭',
  '��' => '곮',
  '��' => '곲',
  '��' => '곴',
  '��' => '곷',
  '��' => '곸',
  '��' => '곹',
  '��' => '곺',
  '��' => '곻',
  '��' => '곾',
  '��' => '곿',
  '��' => '괁',
  '��' => '괂',
  '��' => '괃',
  '��' => '괅',
  '��' => '괇',
  '��' => '괈',
  '��' => '괉',
  '��' => '괊',
  '��' => '괋',
  '��' => '괎',
  '��' => '괐',
  '��' => '괒',
  '��' => '괓',
  '�A' => '괔',
  '�B' => '괕',
  '�C' => '괖',
  '�D' => '괗',
  '�E' => '괙',
  '�F' => '괚',
  '�G' => '괛',
  '�H' => '괝',
  '�I' => '괞',
  '�J' => '괟',
  '�K' => '괡',
  '�L' => '괢',
  '�M' => '괣',
  '�N' => '괤',
  '�O' => '괥',
  '�P' => '괦',
  '�Q' => '괧',
  '�R' => '괨',
  '�S' => '괪',
  '�T' => '괫',
  '�U' => '괮',
  '�V' => '괯',
  '�W' => '괰',
  '�X' => '괱',
  '�Y' => '괲',
  '�Z' => '괳',
  '�a' => '괶',
  '�b' => '괷',
  '�c' => '괹',
  '�d' => '괺',
  '�e' => '괻',
  '�f' => '괽',
  '�g' => '괾',
  '�h' => '괿',
  '�i' => '굀',
  '�j' => '굁',
  '�k' => '굂',
  '�l' => '굃',
  '�m' => '굆',
  '�n' => '굈',
  '�o' => '굊',
  '�p' => '굋',
  '�q' => '굌',
  '�r' => '굍',
  '�s' => '굎',
  '�t' => '굏',
  '�u' => '굑',
  '�v' => '굒',
  '�w' => '굓',
  '�x' => '굕',
  '�y' => '굖',
  '�z' => '굗',
  '��' => '굙',
  '��' => '굚',
  '��' => '굛',
  '��' => '굜',
  '��' => '굝',
  '��' => '굞',
  '��' => '굟',
  '��' => '굠',
  '��' => '굢',
  '��' => '굤',
  '��' => '굥',
  '��' => '굦',
  '��' => '굧',
  '��' => '굨',
  '��' => '굩',
  '��' => '굪',
  '��' => '굫',
  '��' => '굮',
  '��' => '굯',
  '��' => '굱',
  '��' => '굲',
  '��' => '굷',
  '��' => '굸',
  '��' => '굹',
  '��' => '굺',
  '��' => '굾',
  '��' => '궀',
  '��' => '궃',
  '��' => '궄',
  '��' => '궅',
  '��' => '궆',
  '��' => '궇',
  '��' => '궊',
  '��' => '궋',
  '��' => '궍',
  '��' => '궎',
  '��' => '궏',
  '��' => '궑',
  '��' => '궒',
  '��' => '궓',
  '��' => '궔',
  '��' => '궕',
  '��' => '궖',
  '��' => '궗',
  '��' => '궘',
  '��' => '궙',
  '��' => '궚',
  '��' => '궛',
  '��' => '궞',
  '��' => '궟',
  '��' => '궠',
  '��' => '궡',
  '��' => '궢',
  '��' => '궣',
  '��' => '궥',
  '��' => '궦',
  '��' => '궧',
  '��' => '궨',
  '��' => '궩',
  '��' => '궪',
  '��' => '궫',
  '��' => '궬',
  '��' => '궭',
  '��' => '궮',
  '��' => '궯',
  '��' => '궰',
  '��' => '궱',
  '��' => '궲',
  '��' => '궳',
  '��' => '궴',
  '��' => '궵',
  '��' => '궶',
  '��' => '궸',
  '��' => '궹',
  '��' => '궺',
  '��' => '궻',
  '��' => '궼',
  '��' => '궽',
  '��' => '궾',
  '��' => '궿',
  '��' => '귂',
  '��' => '귃',
  '��' => '귅',
  '��' => '귆',
  '��' => '귇',
  '��' => '귉',
  '��' => '귊',
  '��' => '귋',
  '��' => '귌',
  '��' => '귍',
  '��' => '귎',
  '��' => '귏',
  '��' => '귒',
  '��' => '귔',
  '��' => '귕',
  '��' => '귖',
  '��' => '귗',
  '��' => '귘',
  '��' => '귙',
  '��' => '귚',
  '��' => '귛',
  '��' => '귝',
  '��' => '귞',
  '��' => '귟',
  '��' => '귡',
  '��' => '귢',
  '��' => '귣',
  '��' => '귥',
  '��' => '귦',
  '��' => '귧',
  '��' => '귨',
  '��' => '귩',
  '��' => '귪',
  '��' => '귫',
  '��' => '귬',
  '��' => '귭',
  '��' => '귮',
  '��' => '귯',
  '��' => '귰',
  '��' => '귱',
  '��' => '귲',
  '��' => '귳',
  '��' => '귴',
  '��' => '귵',
  '��' => '귶',
  '��' => '귷',
  '�A' => '귺',
  '�B' => '귻',
  '�C' => '귽',
  '�D' => '귾',
  '�E' => '긂',
  '�F' => '긃',
  '�G' => '긄',
  '�H' => '긅',
  '�I' => '긆',
  '�J' => '긇',
  '�K' => '긊',
  '�L' => '긌',
  '�M' => '긎',
  '�N' => '긏',
  '�O' => '긐',
  '�P' => '긑',
  '�Q' => '긒',
  '�R' => '긓',
  '�S' => '긕',
  '�T' => '긖',
  '�U' => '긗',
  '�V' => '긘',
  '�W' => '긙',
  '�X' => '긚',
  '�Y' => '긛',
  '�Z' => '긜',
  '�a' => '긝',
  '�b' => '긞',
  '�c' => '긟',
  '�d' => '긠',
  '�e' => '긡',
  '�f' => '긢',
  '�g' => '긣',
  '�h' => '긤',
  '�i' => '긥',
  '�j' => '긦',
  '�k' => '긧',
  '�l' => '긨',
  '�m' => '긩',
  '�n' => '긪',
  '�o' => '긫',
  '�p' => '긬',
  '�q' => '긭',
  '�r' => '긮',
  '�s' => '긯',
  '�t' => '긲',
  '�u' => '긳',
  '�v' => '긵',
  '�w' => '긶',
  '�x' => '긹',
  '�y' => '긻',
  '�z' => '긼',
  '��' => '긽',
  '��' => '긾',
  '��' => '긿',
  '��' => '깂',
  '��' => '깄',
  '��' => '깇',
  '��' => '깈',
  '��' => '깉',
  '��' => '깋',
  '��' => '깏',
  '��' => '깑',
  '��' => '깒',
  '��' => '깓',
  '��' => '깕',
  '��' => '깗',
  '��' => '깘',
  '��' => '깙',
  '��' => '깚',
  '��' => '깛',
  '��' => '깞',
  '��' => '깢',
  '��' => '깣',
  '��' => '깤',
  '��' => '깦',
  '��' => '깧',
  '��' => '깪',
  '��' => '깫',
  '��' => '깭',
  '��' => '깮',
  '��' => '깯',
  '��' => '깱',
  '��' => '깲',
  '��' => '깳',
  '��' => '깴',
  '��' => '깵',
  '��' => '깶',
  '��' => '깷',
  '��' => '깺',
  '��' => '깾',
  '��' => '깿',
  '��' => '꺀',
  '��' => '꺁',
  '��' => '꺂',
  '��' => '꺃',
  '��' => '꺆',
  '��' => '꺇',
  '��' => '꺈',
  '��' => '꺉',
  '��' => '꺊',
  '��' => '꺋',
  '��' => '꺍',
  '��' => '꺎',
  '��' => '꺏',
  '��' => '꺐',
  '��' => '꺑',
  '��' => '꺒',
  '��' => '꺓',
  '��' => '꺔',
  '��' => '꺕',
  '��' => '꺖',
  '��' => '꺗',
  '��' => '꺘',
  '��' => '꺙',
  '��' => '꺚',
  '��' => '꺛',
  '��' => '꺜',
  '��' => '꺝',
  '��' => '꺞',
  '��' => '꺟',
  '��' => '꺠',
  '��' => '꺡',
  '��' => '꺢',
  '��' => '꺣',
  '��' => '꺤',
  '��' => '꺥',
  '��' => '꺦',
  '��' => '꺧',
  '��' => '꺨',
  '��' => '꺩',
  '��' => '꺪',
  '��' => '꺫',
  '��' => '꺬',
  '��' => '꺭',
  '��' => '꺮',
  '��' => '꺯',
  '��' => '꺰',
  '��' => '꺱',
  '��' => '꺲',
  '��' => '꺳',
  '��' => '꺴',
  '��' => '꺵',
  '��' => '꺶',
  '��' => '꺷',
  '��' => '꺸',
  '��' => '꺹',
  '��' => '꺺',
  '��' => '꺻',
  '��' => '꺿',
  '��' => '껁',
  '��' => '껂',
  '��' => '껃',
  '��' => '껅',
  '��' => '껆',
  '��' => '껇',
  '��' => '껈',
  '��' => '껉',
  '��' => '껊',
  '��' => '껋',
  '��' => '껎',
  '��' => '껒',
  '��' => '껓',
  '��' => '껔',
  '��' => '껕',
  '��' => '껖',
  '��' => '껗',
  '��' => '껚',
  '��' => '껛',
  '��' => '껝',
  '��' => '껞',
  '��' => '껟',
  '��' => '껠',
  '��' => '껡',
  '��' => '껢',
  '��' => '껣',
  '��' => '껤',
  '��' => '껥',
  '�A' => '껦',
  '�B' => '껧',
  '�C' => '껩',
  '�D' => '껪',
  '�E' => '껬',
  '�F' => '껮',
  '�G' => '껯',
  '�H' => '껰',
  '�I' => '껱',
  '�J' => '껲',
  '�K' => '껳',
  '�L' => '껵',
  '�M' => '껶',
  '�N' => '껷',
  '�O' => '껹',
  '�P' => '껺',
  '�Q' => '껻',
  '�R' => '껽',
  '�S' => '껾',
  '�T' => '껿',
  '�U' => '꼀',
  '�V' => '꼁',
  '�W' => '꼂',
  '�X' => '꼃',
  '�Y' => '꼄',
  '�Z' => '꼅',
  '�a' => '꼆',
  '�b' => '꼉',
  '�c' => '꼊',
  '�d' => '꼋',
  '�e' => '꼌',
  '�f' => '꼎',
  '�g' => '꼏',
  '�h' => '꼑',
  '�i' => '꼒',
  '�j' => '꼓',
  '�k' => '꼔',
  '�l' => '꼕',
  '�m' => '꼖',
  '�n' => '꼗',
  '�o' => '꼘',
  '�p' => '꼙',
  '�q' => '꼚',
  '�r' => '꼛',
  '�s' => '꼜',
  '�t' => '꼝',
  '�u' => '꼞',
  '�v' => '꼟',
  '�w' => '꼠',
  '�x' => '꼡',
  '�y' => '꼢',
  '�z' => '꼣',
  '��' => '꼤',
  '��' => '꼥',
  '��' => '꼦',
  '��' => '꼧',
  '��' => '꼨',
  '��' => '꼩',
  '��' => '꼪',
  '��' => '꼫',
  '��' => '꼮',
  '��' => '꼯',
  '��' => '꼱',
  '��' => '꼳',
  '��' => '꼵',
  '��' => '꼶',
  '��' => '꼷',
  '��' => '꼸',
  '��' => '꼹',
  '��' => '꼺',
  '��' => '꼻',
  '��' => '꼾',
  '��' => '꽀',
  '��' => '꽄',
  '��' => '꽅',
  '��' => '꽆',
  '��' => '꽇',
  '��' => '꽊',
  '��' => '꽋',
  '��' => '꽌',
  '��' => '꽍',
  '��' => '꽎',
  '��' => '꽏',
  '��' => '꽑',
  '��' => '꽒',
  '��' => '꽓',
  '��' => '꽔',
  '��' => '꽕',
  '��' => '꽖',
  '��' => '꽗',
  '��' => '꽘',
  '��' => '꽙',
  '��' => '꽚',
  '��' => '꽛',
  '��' => '꽞',
  '��' => '꽟',
  '��' => '꽠',
  '��' => '꽡',
  '��' => '꽢',
  '��' => '꽣',
  '��' => '꽦',
  '��' => '꽧',
  '��' => '꽨',
  '��' => '꽩',
  '��' => '꽪',
  '��' => '꽫',
  '��' => '꽬',
  '��' => '꽭',
  '��' => '꽮',
  '��' => '꽯',
  '��' => '꽰',
  '��' => '꽱',
  '��' => '꽲',
  '��' => '꽳',
  '��' => '꽴',
  '��' => '꽵',
  '��' => '꽶',
  '��' => '꽷',
  '��' => '꽸',
  '��' => '꽺',
  '��' => '꽻',
  '��' => '꽼',
  '��' => '꽽',
  '��' => '꽾',
  '��' => '꽿',
  '��' => '꾁',
  '��' => '꾂',
  '��' => '꾃',
  '��' => '꾅',
  '��' => '꾆',
  '��' => '꾇',
  '��' => '꾉',
  '��' => '꾊',
  '��' => '꾋',
  '��' => '꾌',
  '��' => '꾍',
  '��' => '꾎',
  '��' => '꾏',
  '��' => '꾒',
  '��' => '꾓',
  '��' => '꾔',
  '��' => '꾖',
  '��' => '꾗',
  '��' => '꾘',
  '��' => '꾙',
  '��' => '꾚',
  '��' => '꾛',
  '��' => '꾝',
  '��' => '꾞',
  '��' => '꾟',
  '��' => '꾠',
  '��' => '꾡',
  '��' => '꾢',
  '��' => '꾣',
  '��' => '꾤',
  '��' => '꾥',
  '��' => '꾦',
  '��' => '꾧',
  '��' => '꾨',
  '��' => '꾩',
  '��' => '꾪',
  '��' => '꾫',
  '��' => '꾬',
  '��' => '꾭',
  '��' => '꾮',
  '��' => '꾯',
  '��' => '꾰',
  '��' => '꾱',
  '��' => '꾲',
  '��' => '꾳',
  '��' => '꾴',
  '��' => '꾵',
  '��' => '꾶',
  '��' => '꾷',
  '��' => '꾺',
  '��' => '꾻',
  '��' => '꾽',
  '��' => '꾾',
  '�A' => '꾿',
  '�B' => '꿁',
  '�C' => '꿂',
  '�D' => '꿃',
  '�E' => '꿄',
  '�F' => '꿅',
  '�G' => '꿆',
  '�H' => '꿊',
  '�I' => '꿌',
  '�J' => '꿏',
  '�K' => '꿐',
  '�L' => '꿑',
  '�M' => '꿒',
  '�N' => '꿓',
  '�O' => '꿕',
  '�P' => '꿖',
  '�Q' => '꿗',
  '�R' => '꿘',
  '�S' => '꿙',
  '�T' => '꿚',
  '�U' => '꿛',
  '�V' => '꿝',
  '�W' => '꿞',
  '�X' => '꿟',
  '�Y' => '꿠',
  '�Z' => '꿡',
  '�a' => '꿢',
  '�b' => '꿣',
  '�c' => '꿤',
  '�d' => '꿥',
  '�e' => '꿦',
  '�f' => '꿧',
  '�g' => '꿪',
  '�h' => '꿫',
  '�i' => '꿬',
  '�j' => '꿭',
  '�k' => '꿮',
  '�l' => '꿯',
  '�m' => '꿲',
  '�n' => '꿳',
  '�o' => '꿵',
  '�p' => '꿶',
  '�q' => '꿷',
  '�r' => '꿹',
  '�s' => '꿺',
  '�t' => '꿻',
  '�u' => '꿼',
  '�v' => '꿽',
  '�w' => '꿾',
  '�x' => '꿿',
  '�y' => '뀂',
  '�z' => '뀃',
  '��' => '뀅',
  '��' => '뀆',
  '��' => '뀇',
  '��' => '뀈',
  '��' => '뀉',
  '��' => '뀊',
  '��' => '뀋',
  '��' => '뀍',
  '��' => '뀎',
  '��' => '뀏',
  '��' => '뀑',
  '��' => '뀒',
  '��' => '뀓',
  '��' => '뀕',
  '��' => '뀖',
  '��' => '뀗',
  '��' => '뀘',
  '��' => '뀙',
  '��' => '뀚',
  '��' => '뀛',
  '��' => '뀞',
  '��' => '뀟',
  '��' => '뀠',
  '��' => '뀡',
  '��' => '뀢',
  '��' => '뀣',
  '��' => '뀤',
  '��' => '뀥',
  '��' => '뀦',
  '��' => '뀧',
  '��' => '뀩',
  '��' => '뀪',
  '��' => '뀫',
  '��' => '뀬',
  '��' => '뀭',
  '��' => '뀮',
  '��' => '뀯',
  '��' => '뀰',
  '��' => '뀱',
  '��' => '뀲',
  '��' => '뀳',
  '��' => '뀴',
  '��' => '뀵',
  '��' => '뀶',
  '��' => '뀷',
  '��' => '뀸',
  '��' => '뀹',
  '��' => '뀺',
  '��' => '뀻',
  '��' => '뀼',
  '��' => '뀽',
  '��' => '뀾',
  '��' => '뀿',
  '��' => '끀',
  '��' => '끁',
  '��' => '끂',
  '��' => '끃',
  '��' => '끆',
  '��' => '끇',
  '��' => '끉',
  '��' => '끋',
  '��' => '끍',
  '��' => '끏',
  '��' => '끐',
  '��' => '끑',
  '��' => '끒',
  '��' => '끖',
  '��' => '끘',
  '��' => '끚',
  '��' => '끛',
  '��' => '끜',
  '��' => '끞',
  '��' => '끟',
  '��' => '끠',
  '��' => '끡',
  '��' => '끢',
  '��' => '끣',
  '��' => '끤',
  '��' => '끥',
  '��' => '끦',
  '��' => '끧',
  '��' => '끨',
  '��' => '끩',
  '��' => '끪',
  '��' => '끫',
  '��' => '끬',
  '��' => '끭',
  '��' => '끮',
  '��' => '끯',
  '��' => '끰',
  '��' => '끱',
  '��' => '끲',
  '��' => '끳',
  '��' => '끴',
  '��' => '끵',
  '��' => '끶',
  '��' => '끷',
  '��' => '끸',
  '��' => '끹',
  '��' => '끺',
  '��' => '끻',
  '��' => '끾',
  '��' => '끿',
  '��' => '낁',
  '��' => '낂',
  '��' => '낃',
  '��' => '낅',
  '��' => '낆',
  '��' => '낇',
  '��' => '낈',
  '��' => '낉',
  '��' => '낊',
  '��' => '낋',
  '��' => '낎',
  '��' => '낐',
  '��' => '낒',
  '��' => '낓',
  '��' => '낔',
  '��' => '낕',
  '��' => '낖',
  '��' => '낗',
  '��' => '낛',
  '��' => '낝',
  '��' => '낞',
  '��' => '낣',
  '��' => '낤',
  '�A' => '낥',
  '�B' => '낦',
  '�C' => '낧',
  '�D' => '낪',
  '�E' => '낰',
  '�F' => '낲',
  '�G' => '낶',
  '�H' => '낷',
  '�I' => '낹',
  '�J' => '낺',
  '�K' => '낻',
  '�L' => '낽',
  '�M' => '낾',
  '�N' => '낿',
  '�O' => '냀',
  '�P' => '냁',
  '�Q' => '냂',
  '�R' => '냃',
  '�S' => '냆',
  '�T' => '냊',
  '�U' => '냋',
  '�V' => '냌',
  '�W' => '냍',
  '�X' => '냎',
  '�Y' => '냏',
  '�Z' => '냒',
  '�a' => '냓',
  '�b' => '냕',
  '�c' => '냖',
  '�d' => '냗',
  '�e' => '냙',
  '�f' => '냚',
  '�g' => '냛',
  '�h' => '냜',
  '�i' => '냝',
  '�j' => '냞',
  '�k' => '냟',
  '�l' => '냡',
  '�m' => '냢',
  '�n' => '냣',
  '�o' => '냤',
  '�p' => '냦',
  '�q' => '냧',
  '�r' => '냨',
  '�s' => '냩',
  '�t' => '냪',
  '�u' => '냫',
  '�v' => '냬',
  '�w' => '냭',
  '�x' => '냮',
  '�y' => '냯',
  '�z' => '냰',
  '��' => '냱',
  '��' => '냲',
  '��' => '냳',
  '��' => '냴',
  '��' => '냵',
  '��' => '냶',
  '��' => '냷',
  '��' => '냸',
  '��' => '냹',
  '��' => '냺',
  '��' => '냻',
  '��' => '냼',
  '��' => '냽',
  '��' => '냾',
  '��' => '냿',
  '��' => '넀',
  '��' => '넁',
  '��' => '넂',
  '��' => '넃',
  '��' => '넄',
  '��' => '넅',
  '��' => '넆',
  '��' => '넇',
  '��' => '넊',
  '��' => '넍',
  '��' => '넎',
  '��' => '넏',
  '��' => '넑',
  '��' => '넔',
  '��' => '넕',
  '��' => '넖',
  '��' => '넗',
  '��' => '넚',
  '��' => '넞',
  '��' => '넟',
  '��' => '넠',
  '��' => '넡',
  '��' => '넢',
  '��' => '넦',
  '��' => '넧',
  '��' => '넩',
  '��' => '넪',
  '��' => '넫',
  '��' => '넭',
  '��' => '넮',
  '��' => '넯',
  '��' => '넰',
  '��' => '넱',
  '��' => '넲',
  '��' => '넳',
  '��' => '넶',
  '��' => '넺',
  '��' => '넻',
  '��' => '넼',
  '��' => '넽',
  '��' => '넾',
  '��' => '넿',
  '��' => '녂',
  '��' => '녃',
  '��' => '녅',
  '��' => '녆',
  '��' => '녇',
  '��' => '녉',
  '��' => '녊',
  '��' => '녋',
  '��' => '녌',
  '��' => '녍',
  '��' => '녎',
  '��' => '녏',
  '��' => '녒',
  '��' => '녓',
  '��' => '녖',
  '��' => '녗',
  '��' => '녙',
  '��' => '녚',
  '��' => '녛',
  '��' => '녝',
  '��' => '녞',
  '��' => '녟',
  '��' => '녡',
  '��' => '녢',
  '��' => '녣',
  '��' => '녤',
  '��' => '녥',
  '��' => '녦',
  '��' => '녧',
  '��' => '녨',
  '��' => '녩',
  '��' => '녪',
  '��' => '녫',
  '��' => '녬',
  '��' => '녭',
  '��' => '녮',
  '��' => '녯',
  '��' => '녰',
  '��' => '녱',
  '��' => '녲',
  '��' => '녳',
  '��' => '녴',
  '��' => '녵',
  '��' => '녶',
  '��' => '녷',
  '��' => '녺',
  '��' => '녻',
  '��' => '녽',
  '��' => '녾',
  '��' => '녿',
  '��' => '놁',
  '��' => '놃',
  '��' => '놄',
  '��' => '놅',
  '��' => '놆',
  '��' => '놇',
  '��' => '놊',
  '��' => '놌',
  '��' => '놎',
  '��' => '놏',
  '��' => '놐',
  '��' => '놑',
  '��' => '놕',
  '��' => '놖',
  '��' => '놗',
  '��' => '놙',
  '��' => '놚',
  '��' => '놛',
  '��' => '놝',
  '�A' => '놞',
  '�B' => '놟',
  '�C' => '놠',
  '�D' => '놡',
  '�E' => '놢',
  '�F' => '놣',
  '�G' => '놤',
  '�H' => '놥',
  '�I' => '놦',
  '�J' => '놧',
  '�K' => '놩',
  '�L' => '놪',
  '�M' => '놫',
  '�N' => '놬',
  '�O' => '놭',
  '�P' => '놮',
  '�Q' => '놯',
  '�R' => '놰',
  '�S' => '놱',
  '�T' => '놲',
  '�U' => '놳',
  '�V' => '놴',
  '�W' => '놵',
  '�X' => '놶',
  '�Y' => '놷',
  '�Z' => '놸',
  '�a' => '놹',
  '�b' => '놺',
  '�c' => '놻',
  '�d' => '놼',
  '�e' => '놽',
  '�f' => '놾',
  '�g' => '놿',
  '�h' => '뇀',
  '�i' => '뇁',
  '�j' => '뇂',
  '�k' => '뇃',
  '�l' => '뇄',
  '�m' => '뇅',
  '�n' => '뇆',
  '�o' => '뇇',
  '�p' => '뇈',
  '�q' => '뇉',
  '�r' => '뇊',
  '�s' => '뇋',
  '�t' => '뇍',
  '�u' => '뇎',
  '�v' => '뇏',
  '�w' => '뇑',
  '�x' => '뇒',
  '�y' => '뇓',
  '�z' => '뇕',
  '��' => '뇖',
  '��' => '뇗',
  '��' => '뇘',
  '��' => '뇙',
  '��' => '뇚',
  '��' => '뇛',
  '��' => '뇞',
  '��' => '뇠',
  '��' => '뇡',
  '��' => '뇢',
  '��' => '뇣',
  '��' => '뇤',
  '��' => '뇥',
  '��' => '뇦',
  '��' => '뇧',
  '��' => '뇪',
  '��' => '뇫',
  '��' => '뇭',
  '��' => '뇮',
  '��' => '뇯',
  '��' => '뇱',
  '��' => '뇲',
  '��' => '뇳',
  '��' => '뇴',
  '��' => '뇵',
  '��' => '뇶',
  '��' => '뇷',
  '��' => '뇸',
  '��' => '뇺',
  '��' => '뇼',
  '��' => '뇾',
  '��' => '뇿',
  '��' => '눀',
  '��' => '눁',
  '��' => '눂',
  '��' => '눃',
  '��' => '눆',
  '��' => '눇',
  '��' => '눉',
  '��' => '눊',
  '��' => '눍',
  '��' => '눎',
  '��' => '눏',
  '��' => '눐',
  '��' => '눑',
  '��' => '눒',
  '��' => '눓',
  '��' => '눖',
  '��' => '눘',
  '��' => '눚',
  '��' => '눛',
  '��' => '눜',
  '��' => '눝',
  '��' => '눞',
  '��' => '눟',
  '��' => '눡',
  '��' => '눢',
  '��' => '눣',
  '��' => '눤',
  '��' => '눥',
  '��' => '눦',
  '��' => '눧',
  '��' => '눨',
  '��' => '눩',
  '��' => '눪',
  '��' => '눫',
  '��' => '눬',
  '��' => '눭',
  '��' => '눮',
  '��' => '눯',
  '��' => '눰',
  '��' => '눱',
  '��' => '눲',
  '��' => '눳',
  '��' => '눵',
  '��' => '눶',
  '��' => '눷',
  '��' => '눸',
  '��' => '눹',
  '��' => '눺',
  '��' => '눻',
  '��' => '눽',
  '��' => '눾',
  '��' => '눿',
  '��' => '뉀',
  '��' => '뉁',
  '��' => '뉂',
  '��' => '뉃',
  '��' => '뉄',
  '��' => '뉅',
  '��' => '뉆',
  '��' => '뉇',
  '��' => '뉈',
  '��' => '뉉',
  '��' => '뉊',
  '��' => '뉋',
  '��' => '뉌',
  '��' => '뉍',
  '��' => '뉎',
  '��' => '뉏',
  '��' => '뉐',
  '��' => '뉑',
  '��' => '뉒',
  '��' => '뉓',
  '��' => '뉔',
  '��' => '뉕',
  '��' => '뉖',
  '��' => '뉗',
  '��' => '뉙',
  '��' => '뉚',
  '��' => '뉛',
  '��' => '뉝',
  '��' => '뉞',
  '��' => '뉟',
  '��' => '뉡',
  '��' => '뉢',
  '��' => '뉣',
  '��' => '뉤',
  '��' => '뉥',
  '��' => '뉦',
  '��' => '뉧',
  '��' => '뉪',
  '��' => '뉫',
  '��' => '뉬',
  '��' => '뉭',
  '��' => '뉮',
  '�A' => '뉯',
  '�B' => '뉰',
  '�C' => '뉱',
  '�D' => '뉲',
  '�E' => '뉳',
  '�F' => '뉶',
  '�G' => '뉷',
  '�H' => '뉸',
  '�I' => '뉹',
  '�J' => '뉺',
  '�K' => '뉻',
  '�L' => '뉽',
  '�M' => '뉾',
  '�N' => '뉿',
  '�O' => '늀',
  '�P' => '늁',
  '�Q' => '늂',
  '�R' => '늃',
  '�S' => '늆',
  '�T' => '늇',
  '�U' => '늈',
  '�V' => '늊',
  '�W' => '늋',
  '�X' => '늌',
  '�Y' => '늍',
  '�Z' => '늎',
  '�a' => '늏',
  '�b' => '늒',
  '�c' => '늓',
  '�d' => '늕',
  '�e' => '늖',
  '�f' => '늗',
  '�g' => '늛',
  '�h' => '늜',
  '�i' => '늝',
  '�j' => '늞',
  '�k' => '늟',
  '�l' => '늢',
  '�m' => '늤',
  '�n' => '늧',
  '�o' => '늨',
  '�p' => '늩',
  '�q' => '늫',
  '�r' => '늭',
  '�s' => '늮',
  '�t' => '늯',
  '�u' => '늱',
  '�v' => '늲',
  '�w' => '늳',
  '�x' => '늵',
  '�y' => '늶',
  '�z' => '늷',
  '��' => '늸',
  '��' => '늹',
  '��' => '늺',
  '��' => '늻',
  '��' => '늼',
  '��' => '늽',
  '��' => '늾',
  '��' => '늿',
  '��' => '닀',
  '��' => '닁',
  '��' => '닂',
  '��' => '닃',
  '��' => '닄',
  '��' => '닅',
  '��' => '닆',
  '��' => '닇',
  '��' => '닊',
  '��' => '닋',
  '��' => '닍',
  '��' => '닎',
  '��' => '닏',
  '��' => '닑',
  '��' => '닓',
  '��' => '닔',
  '��' => '닕',
  '��' => '닖',
  '��' => '닗',
  '��' => '닚',
  '��' => '닜',
  '��' => '닞',
  '��' => '닟',
  '��' => '닠',
  '��' => '닡',
  '��' => '닣',
  '��' => '닧',
  '��' => '닩',
  '��' => '닪',
  '��' => '닰',
  '��' => '닱',
  '��' => '닲',
  '��' => '닶',
  '��' => '닼',
  '��' => '닽',
  '��' => '닾',
  '��' => '댂',
  '��' => '댃',
  '��' => '댅',
  '��' => '댆',
  '��' => '댇',
  '��' => '댉',
  '��' => '댊',
  '��' => '댋',
  '��' => '댌',
  '��' => '댍',
  '��' => '댎',
  '��' => '댏',
  '��' => '댒',
  '��' => '댖',
  '��' => '댗',
  '��' => '댘',
  '��' => '댙',
  '��' => '댚',
  '��' => '댛',
  '��' => '댝',
  '��' => '댞',
  '��' => '댟',
  '��' => '댠',
  '��' => '댡',
  '��' => '댢',
  '��' => '댣',
  '��' => '댤',
  '��' => '댥',
  '��' => '댦',
  '��' => '댧',
  '��' => '댨',
  '��' => '댩',
  '��' => '댪',
  '��' => '댫',
  '��' => '댬',
  '��' => '댭',
  '��' => '댮',
  '��' => '댯',
  '��' => '댰',
  '��' => '댱',
  '��' => '댲',
  '��' => '댳',
  '��' => '댴',
  '��' => '댵',
  '��' => '댶',
  '��' => '댷',
  '��' => '댸',
  '��' => '댹',
  '��' => '댺',
  '��' => '댻',
  '��' => '댼',
  '��' => '댽',
  '��' => '댾',
  '��' => '댿',
  '��' => '덀',
  '��' => '덁',
  '��' => '덂',
  '��' => '덃',
  '��' => '덄',
  '��' => '덅',
  '��' => '덆',
  '��' => '덇',
  '��' => '덈',
  '��' => '덉',
  '��' => '덊',
  '��' => '덋',
  '��' => '덌',
  '��' => '덍',
  '��' => '덎',
  '��' => '덏',
  '��' => '덐',
  '��' => '덑',
  '��' => '덒',
  '��' => '덓',
  '��' => '덗',
  '��' => '덙',
  '��' => '덚',
  '��' => '덝',
  '��' => '덠',
  '��' => '덡',
  '��' => '덢',
  '��' => '덣',
  '�A' => '덦',
  '�B' => '덨',
  '�C' => '덪',
  '�D' => '덬',
  '�E' => '덭',
  '�F' => '덯',
  '�G' => '덲',
  '�H' => '덳',
  '�I' => '덵',
  '�J' => '덶',
  '�K' => '덷',
  '�L' => '덹',
  '�M' => '덺',
  '�N' => '덻',
  '�O' => '덼',
  '�P' => '덽',
  '�Q' => '덾',
  '�R' => '덿',
  '�S' => '뎂',
  '�T' => '뎆',
  '�U' => '뎇',
  '�V' => '뎈',
  '�W' => '뎉',
  '�X' => '뎊',
  '�Y' => '뎋',
  '�Z' => '뎍',
  '�a' => '뎎',
  '�b' => '뎏',
  '�c' => '뎑',
  '�d' => '뎒',
  '�e' => '뎓',
  '�f' => '뎕',
  '�g' => '뎖',
  '�h' => '뎗',
  '�i' => '뎘',
  '�j' => '뎙',
  '�k' => '뎚',
  '�l' => '뎛',
  '�m' => '뎜',
  '�n' => '뎝',
  '�o' => '뎞',
  '�p' => '뎟',
  '�q' => '뎢',
  '�r' => '뎣',
  '�s' => '뎤',
  '�t' => '뎥',
  '�u' => '뎦',
  '�v' => '뎧',
  '�w' => '뎩',
  '�x' => '뎪',
  '�y' => '뎫',
  '�z' => '뎭',
  '��' => '뎮',
  '��' => '뎯',
  '��' => '뎰',
  '��' => '뎱',
  '��' => '뎲',
  '��' => '뎳',
  '��' => '뎴',
  '��' => '뎵',
  '��' => '뎶',
  '��' => '뎷',
  '��' => '뎸',
  '��' => '뎹',
  '��' => '뎺',
  '��' => '뎻',
  '��' => '뎼',
  '��' => '뎽',
  '��' => '뎾',
  '��' => '뎿',
  '��' => '돀',
  '��' => '돁',
  '��' => '돂',
  '��' => '돃',
  '��' => '돆',
  '��' => '돇',
  '��' => '돉',
  '��' => '돊',
  '��' => '돍',
  '��' => '돏',
  '��' => '돑',
  '��' => '돒',
  '��' => '돓',
  '��' => '돖',
  '��' => '돘',
  '��' => '돚',
  '��' => '돜',
  '��' => '돞',
  '��' => '돟',
  '��' => '돡',
  '��' => '돢',
  '��' => '돣',
  '��' => '돥',
  '��' => '돦',
  '��' => '돧',
  '��' => '돩',
  '��' => '돪',
  '��' => '돫',
  '��' => '돬',
  '��' => '돭',
  '��' => '돮',
  '��' => '돯',
  '��' => '돰',
  '��' => '돱',
  '��' => '돲',
  '��' => '돳',
  '��' => '돴',
  '��' => '돵',
  '��' => '돶',
  '��' => '돷',
  '��' => '돸',
  '��' => '돹',
  '��' => '돺',
  '��' => '돻',
  '��' => '돽',
  '��' => '돾',
  '��' => '돿',
  '��' => '됀',
  '��' => '됁',
  '��' => '됂',
  '��' => '됃',
  '��' => '됄',
  '��' => '됅',
  '��' => '됆',
  '��' => '됇',
  '��' => '됈',
  '��' => '됉',
  '��' => '됊',
  '��' => '됋',
  '��' => '됌',
  '��' => '됍',
  '��' => '됎',
  '��' => '됏',
  '��' => '됑',
  '��' => '됒',
  '��' => '됓',
  '��' => '됔',
  '��' => '됕',
  '��' => '됖',
  '��' => '됗',
  '��' => '됙',
  '��' => '됚',
  '��' => '됛',
  '��' => '됝',
  '��' => '됞',
  '��' => '됟',
  '��' => '됡',
  '��' => '됢',
  '��' => '됣',
  '��' => '됤',
  '��' => '됥',
  '��' => '됦',
  '��' => '됧',
  '��' => '됪',
  '��' => '됬',
  '��' => '됭',
  '��' => '됮',
  '��' => '됯',
  '��' => '됰',
  '��' => '됱',
  '��' => '됲',
  '��' => '됳',
  '��' => '됵',
  '��' => '됶',
  '��' => '됷',
  '��' => '됸',
  '��' => '됹',
  '��' => '됺',
  '��' => '됻',
  '��' => '됼',
  '��' => '됽',
  '��' => '됾',
  '��' => '됿',
  '��' => '둀',
  '��' => '둁',
  '��' => '둂',
  '��' => '둃',
  '��' => '둄',
  '�A' => '둅',
  '�B' => '둆',
  '�C' => '둇',
  '�D' => '둈',
  '�E' => '둉',
  '�F' => '둊',
  '�G' => '둋',
  '�H' => '둌',
  '�I' => '둍',
  '�J' => '둎',
  '�K' => '둏',
  '�L' => '둒',
  '�M' => '둓',
  '�N' => '둕',
  '�O' => '둖',
  '�P' => '둗',
  '�Q' => '둙',
  '�R' => '둚',
  '�S' => '둛',
  '�T' => '둜',
  '�U' => '둝',
  '�V' => '둞',
  '�W' => '둟',
  '�X' => '둢',
  '�Y' => '둤',
  '�Z' => '둦',
  '�a' => '둧',
  '�b' => '둨',
  '�c' => '둩',
  '�d' => '둪',
  '�e' => '둫',
  '�f' => '둭',
  '�g' => '둮',
  '�h' => '둯',
  '�i' => '둰',
  '�j' => '둱',
  '�k' => '둲',
  '�l' => '둳',
  '�m' => '둴',
  '�n' => '둵',
  '�o' => '둶',
  '�p' => '둷',
  '�q' => '둸',
  '�r' => '둹',
  '�s' => '둺',
  '�t' => '둻',
  '�u' => '둼',
  '�v' => '둽',
  '�w' => '둾',
  '�x' => '둿',
  '�y' => '뒁',
  '�z' => '뒂',
  '��' => '뒃',
  '��' => '뒄',
  '��' => '뒅',
  '��' => '뒆',
  '��' => '뒇',
  '��' => '뒉',
  '��' => '뒊',
  '��' => '뒋',
  '��' => '뒌',
  '��' => '뒍',
  '��' => '뒎',
  '��' => '뒏',
  '��' => '뒐',
  '��' => '뒑',
  '��' => '뒒',
  '��' => '뒓',
  '��' => '뒔',
  '��' => '뒕',
  '��' => '뒖',
  '��' => '뒗',
  '��' => '뒘',
  '��' => '뒙',
  '��' => '뒚',
  '��' => '뒛',
  '��' => '뒜',
  '��' => '뒞',
  '��' => '뒟',
  '��' => '뒠',
  '��' => '뒡',
  '��' => '뒢',
  '��' => '뒣',
  '��' => '뒥',
  '��' => '뒦',
  '��' => '뒧',
  '��' => '뒩',
  '��' => '뒪',
  '��' => '뒫',
  '��' => '뒭',
  '��' => '뒮',
  '��' => '뒯',
  '��' => '뒰',
  '��' => '뒱',
  '��' => '뒲',
  '��' => '뒳',
  '��' => '뒴',
  '��' => '뒶',
  '��' => '뒸',
  '��' => '뒺',
  '��' => '뒻',
  '��' => '뒼',
  '��' => '뒽',
  '��' => '뒾',
  '��' => '뒿',
  '��' => '듁',
  '��' => '듂',
  '��' => '듃',
  '��' => '듅',
  '��' => '듆',
  '��' => '듇',
  '��' => '듉',
  '��' => '듊',
  '��' => '듋',
  '��' => '듌',
  '��' => '듍',
  '��' => '듎',
  '��' => '듏',
  '��' => '듑',
  '��' => '듒',
  '��' => '듓',
  '��' => '듔',
  '��' => '듖',
  '��' => '듗',
  '��' => '듘',
  '��' => '듙',
  '��' => '듚',
  '��' => '듛',
  '��' => '듞',
  '��' => '듟',
  '��' => '듡',
  '��' => '듢',
  '��' => '듥',
  '��' => '듧',
  '��' => '듨',
  '��' => '듩',
  '��' => '듪',
  '��' => '듫',
  '��' => '듮',
  '��' => '듰',
  '��' => '듲',
  '��' => '듳',
  '��' => '듴',
  '��' => '듵',
  '��' => '듶',
  '��' => '듷',
  '��' => '듹',
  '��' => '듺',
  '��' => '듻',
  '��' => '듼',
  '��' => '듽',
  '��' => '듾',
  '��' => '듿',
  '��' => '딀',
  '��' => '딁',
  '��' => '딂',
  '��' => '딃',
  '��' => '딄',
  '��' => '딅',
  '��' => '딆',
  '��' => '딇',
  '��' => '딈',
  '��' => '딉',
  '��' => '딊',
  '��' => '딋',
  '��' => '딌',
  '��' => '딍',
  '��' => '딎',
  '��' => '딏',
  '��' => '딐',
  '��' => '딑',
  '��' => '딒',
  '��' => '딓',
  '��' => '딖',
  '��' => '딗',
  '��' => '딙',
  '��' => '딚',
  '��' => '딝',
  '�A' => '딞',
  '�B' => '딟',
  '�C' => '딠',
  '�D' => '딡',
  '�E' => '딢',
  '�F' => '딣',
  '�G' => '딦',
  '�H' => '딫',
  '�I' => '딬',
  '�J' => '딭',
  '�K' => '딮',
  '�L' => '딯',
  '�M' => '딲',
  '�N' => '딳',
  '�O' => '딵',
  '�P' => '딶',
  '�Q' => '딷',
  '�R' => '딹',
  '�S' => '딺',
  '�T' => '딻',
  '�U' => '딼',
  '�V' => '딽',
  '�W' => '딾',
  '�X' => '딿',
  '�Y' => '땂',
  '�Z' => '땆',
  '�a' => '땇',
  '�b' => '땈',
  '�c' => '땉',
  '�d' => '땊',
  '�e' => '땎',
  '�f' => '땏',
  '�g' => '땑',
  '�h' => '땒',
  '�i' => '땓',
  '�j' => '땕',
  '�k' => '땖',
  '�l' => '땗',
  '�m' => '땘',
  '�n' => '땙',
  '�o' => '땚',
  '�p' => '땛',
  '�q' => '땞',
  '�r' => '땢',
  '�s' => '땣',
  '�t' => '땤',
  '�u' => '땥',
  '�v' => '땦',
  '�w' => '땧',
  '�x' => '땨',
  '�y' => '땩',
  '�z' => '땪',
  '��' => '땫',
  '��' => '땬',
  '��' => '땭',
  '��' => '땮',
  '��' => '땯',
  '��' => '땰',
  '��' => '땱',
  '��' => '땲',
  '��' => '땳',
  '��' => '땴',
  '��' => '땵',
  '��' => '땶',
  '��' => '땷',
  '��' => '땸',
  '��' => '땹',
  '��' => '땺',
  '��' => '땻',
  '��' => '땼',
  '��' => '땽',
  '��' => '땾',
  '��' => '땿',
  '��' => '떀',
  '��' => '떁',
  '��' => '떂',
  '��' => '떃',
  '��' => '떄',
  '��' => '떅',
  '��' => '떆',
  '��' => '떇',
  '��' => '떈',
  '��' => '떉',
  '��' => '떊',
  '��' => '떋',
  '��' => '떌',
  '��' => '떍',
  '��' => '떎',
  '��' => '떏',
  '��' => '떐',
  '��' => '떑',
  '��' => '떒',
  '��' => '떓',
  '��' => '떔',
  '��' => '떕',
  '��' => '떖',
  '��' => '떗',
  '��' => '떘',
  '��' => '떙',
  '��' => '떚',
  '��' => '떛',
  '��' => '떜',
  '��' => '떝',
  '��' => '떞',
  '��' => '떟',
  '��' => '떢',
  '��' => '떣',
  '��' => '떥',
  '��' => '떦',
  '��' => '떧',
  '��' => '떩',
  '��' => '떬',
  '��' => '떭',
  '��' => '떮',
  '��' => '떯',
  '��' => '떲',
  '��' => '떶',
  '��' => '떷',
  '��' => '떸',
  '��' => '떹',
  '��' => '떺',
  '��' => '떾',
  '��' => '떿',
  '��' => '뗁',
  '��' => '뗂',
  '��' => '뗃',
  '��' => '뗅',
  '��' => '뗆',
  '��' => '뗇',
  '��' => '뗈',
  '��' => '뗉',
  '��' => '뗊',
  '��' => '뗋',
  '��' => '뗎',
  '��' => '뗒',
  '��' => '뗓',
  '��' => '뗔',
  '��' => '뗕',
  '��' => '뗖',
  '��' => '뗗',
  '��' => '뗙',
  '��' => '뗚',
  '��' => '뗛',
  '��' => '뗜',
  '��' => '뗝',
  '��' => '뗞',
  '��' => '뗟',
  '��' => '뗠',
  '��' => '뗡',
  '��' => '뗢',
  '��' => '뗣',
  '��' => '뗤',
  '��' => '뗥',
  '��' => '뗦',
  '��' => '뗧',
  '��' => '뗨',
  '��' => '뗩',
  '��' => '뗪',
  '��' => '뗫',
  '��' => '뗭',
  '��' => '뗮',
  '��' => '뗯',
  '��' => '뗰',
  '��' => '뗱',
  '��' => '뗲',
  '��' => '뗳',
  '��' => '뗴',
  '��' => '뗵',
  '��' => '뗶',
  '��' => '뗷',
  '��' => '뗸',
  '��' => '뗹',
  '��' => '뗺',
  '��' => '뗻',
  '��' => '뗼',
  '��' => '뗽',
  '��' => '뗾',
  '��' => '뗿',
  '�A' => '똀',
  '�B' => '똁',
  '�C' => '똂',
  '�D' => '똃',
  '�E' => '똄',
  '�F' => '똅',
  '�G' => '똆',
  '�H' => '똇',
  '�I' => '똈',
  '�J' => '똉',
  '�K' => '똊',
  '�L' => '똋',
  '�M' => '똌',
  '�N' => '똍',
  '�O' => '똎',
  '�P' => '똏',
  '�Q' => '똒',
  '�R' => '똓',
  '�S' => '똕',
  '�T' => '똖',
  '�U' => '똗',
  '�V' => '똙',
  '�W' => '똚',
  '�X' => '똛',
  '�Y' => '똜',
  '�Z' => '똝',
  '�a' => '똞',
  '�b' => '똟',
  '�c' => '똠',
  '�d' => '똡',
  '�e' => '똢',
  '�f' => '똣',
  '�g' => '똤',
  '�h' => '똦',
  '�i' => '똧',
  '�j' => '똨',
  '�k' => '똩',
  '�l' => '똪',
  '�m' => '똫',
  '�n' => '똭',
  '�o' => '똮',
  '�p' => '똯',
  '�q' => '똰',
  '�r' => '똱',
  '�s' => '똲',
  '�t' => '똳',
  '�u' => '똵',
  '�v' => '똶',
  '�w' => '똷',
  '�x' => '똸',
  '�y' => '똹',
  '�z' => '똺',
  '��' => '똻',
  '��' => '똼',
  '��' => '똽',
  '��' => '똾',
  '��' => '똿',
  '��' => '뙀',
  '��' => '뙁',
  '��' => '뙂',
  '��' => '뙃',
  '��' => '뙄',
  '��' => '뙅',
  '��' => '뙆',
  '��' => '뙇',
  '��' => '뙉',
  '��' => '뙊',
  '��' => '뙋',
  '��' => '뙌',
  '��' => '뙍',
  '��' => '뙎',
  '��' => '뙏',
  '��' => '뙐',
  '��' => '뙑',
  '��' => '뙒',
  '��' => '뙓',
  '��' => '뙔',
  '��' => '뙕',
  '��' => '뙖',
  '��' => '뙗',
  '��' => '뙘',
  '��' => '뙙',
  '��' => '뙚',
  '��' => '뙛',
  '��' => '뙜',
  '��' => '뙝',
  '��' => '뙞',
  '��' => '뙟',
  '��' => '뙠',
  '��' => '뙡',
  '��' => '뙢',
  '��' => '뙣',
  '��' => '뙥',
  '��' => '뙦',
  '��' => '뙧',
  '��' => '뙩',
  '��' => '뙪',
  '��' => '뙫',
  '��' => '뙬',
  '��' => '뙭',
  '��' => '뙮',
  '��' => '뙯',
  '��' => '뙰',
  '��' => '뙱',
  '��' => '뙲',
  '��' => '뙳',
  '��' => '뙴',
  '��' => '뙵',
  '��' => '뙶',
  '��' => '뙷',
  '��' => '뙸',
  '��' => '뙹',
  '��' => '뙺',
  '��' => '뙻',
  '��' => '뙼',
  '��' => '뙽',
  '��' => '뙾',
  '��' => '뙿',
  '��' => '뚀',
  '��' => '뚁',
  '��' => '뚂',
  '��' => '뚃',
  '��' => '뚄',
  '��' => '뚅',
  '��' => '뚆',
  '��' => '뚇',
  '��' => '뚈',
  '��' => '뚉',
  '��' => '뚊',
  '��' => '뚋',
  '��' => '뚌',
  '��' => '뚍',
  '��' => '뚎',
  '��' => '뚏',
  '��' => '뚐',
  '��' => '뚑',
  '��' => '뚒',
  '��' => '뚓',
  '��' => '뚔',
  '��' => '뚕',
  '��' => '뚖',
  '��' => '뚗',
  '��' => '뚘',
  '��' => '뚙',
  '��' => '뚚',
  '��' => '뚛',
  '��' => '뚞',
  '��' => '뚟',
  '��' => '뚡',
  '��' => '뚢',
  '��' => '뚣',
  '��' => '뚥',
  '��' => '뚦',
  '��' => '뚧',
  '��' => '뚨',
  '��' => '뚩',
  '��' => '뚪',
  '��' => '뚭',
  '��' => '뚮',
  '��' => '뚯',
  '��' => '뚰',
  '��' => '뚲',
  '��' => '뚳',
  '��' => '뚴',
  '��' => '뚵',
  '��' => '뚶',
  '��' => '뚷',
  '��' => '뚸',
  '��' => '뚹',
  '��' => '뚺',
  '��' => '뚻',
  '��' => '뚼',
  '��' => '뚽',
  '��' => '뚾',
  '��' => '뚿',
  '��' => '뛀',
  '��' => '뛁',
  '��' => '뛂',
  '�A' => '뛃',
  '�B' => '뛄',
  '�C' => '뛅',
  '�D' => '뛆',
  '�E' => '뛇',
  '�F' => '뛈',
  '�G' => '뛉',
  '�H' => '뛊',
  '�I' => '뛋',
  '�J' => '뛌',
  '�K' => '뛍',
  '�L' => '뛎',
  '�M' => '뛏',
  '�N' => '뛐',
  '�O' => '뛑',
  '�P' => '뛒',
  '�Q' => '뛓',
  '�R' => '뛕',
  '�S' => '뛖',
  '�T' => '뛗',
  '�U' => '뛘',
  '�V' => '뛙',
  '�W' => '뛚',
  '�X' => '뛛',
  '�Y' => '뛜',
  '�Z' => '뛝',
  '�a' => '뛞',
  '�b' => '뛟',
  '�c' => '뛠',
  '�d' => '뛡',
  '�e' => '뛢',
  '�f' => '뛣',
  '�g' => '뛤',
  '�h' => '뛥',
  '�i' => '뛦',
  '�j' => '뛧',
  '�k' => '뛨',
  '�l' => '뛩',
  '�m' => '뛪',
  '�n' => '뛫',
  '�o' => '뛬',
  '�p' => '뛭',
  '�q' => '뛮',
  '�r' => '뛯',
  '�s' => '뛱',
  '�t' => '뛲',
  '�u' => '뛳',
  '�v' => '뛵',
  '�w' => '뛶',
  '�x' => '뛷',
  '�y' => '뛹',
  '�z' => '뛺',
  '��' => '뛻',
  '��' => '뛼',
  '��' => '뛽',
  '��' => '뛾',
  '��' => '뛿',
  '��' => '뜂',
  '��' => '뜃',
  '��' => '뜄',
  '��' => '뜆',
  '��' => '뜇',
  '��' => '뜈',
  '��' => '뜉',
  '��' => '뜊',
  '��' => '뜋',
  '��' => '뜌',
  '��' => '뜍',
  '��' => '뜎',
  '��' => '뜏',
  '��' => '뜐',
  '��' => '뜑',
  '��' => '뜒',
  '��' => '뜓',
  '��' => '뜔',
  '��' => '뜕',
  '��' => '뜖',
  '��' => '뜗',
  '��' => '뜘',
  '��' => '뜙',
  '��' => '뜚',
  '��' => '뜛',
  '��' => '뜜',
  '��' => '뜝',
  '��' => '뜞',
  '��' => '뜟',
  '��' => '뜠',
  '��' => '뜡',
  '��' => '뜢',
  '��' => '뜣',
  '��' => '뜤',
  '��' => '뜥',
  '��' => '뜦',
  '��' => '뜧',
  '��' => '뜪',
  '��' => '뜫',
  '��' => '뜭',
  '��' => '뜮',
  '��' => '뜱',
  '��' => '뜲',
  '��' => '뜳',
  '��' => '뜴',
  '��' => '뜵',
  '��' => '뜶',
  '��' => '뜷',
  '��' => '뜺',
  '��' => '뜼',
  '��' => '뜽',
  '��' => '뜾',
  '��' => '뜿',
  '��' => '띀',
  '��' => '띁',
  '��' => '띂',
  '��' => '띃',
  '��' => '띅',
  '��' => '띆',
  '��' => '띇',
  '��' => '띉',
  '��' => '띊',
  '��' => '띋',
  '��' => '띍',
  '��' => '띎',
  '��' => '띏',
  '��' => '띐',
  '��' => '띑',
  '��' => '띒',
  '��' => '띓',
  '��' => '띖',
  '��' => '띗',
  '��' => '띘',
  '��' => '띙',
  '��' => '띚',
  '��' => '띛',
  '��' => '띜',
  '��' => '띝',
  '��' => '띞',
  '��' => '띟',
  '��' => '띡',
  '��' => '띢',
  '��' => '띣',
  '��' => '띥',
  '��' => '띦',
  '��' => '띧',
  '��' => '띩',
  '��' => '띪',
  '��' => '띫',
  '��' => '띬',
  '��' => '띭',
  '��' => '띮',
  '��' => '띯',
  '��' => '띲',
  '��' => '띴',
  '��' => '띶',
  '��' => '띷',
  '��' => '띸',
  '��' => '띹',
  '��' => '띺',
  '��' => '띻',
  '��' => '띾',
  '��' => '띿',
  '��' => '랁',
  '��' => '랂',
  '��' => '랃',
  '��' => '랅',
  '��' => '랆',
  '��' => '랇',
  '��' => '랈',
  '��' => '랉',
  '��' => '랊',
  '��' => '랋',
  '��' => '랎',
  '��' => '랓',
  '��' => '랔',
  '��' => '랕',
  '��' => '랚',
  '��' => '랛',
  '��' => '랝',
  '��' => '랞',
  '�A' => '랟',
  '�B' => '랡',
  '�C' => '랢',
  '�D' => '랣',
  '�E' => '랤',
  '�F' => '랥',
  '�G' => '랦',
  '�H' => '랧',
  '�I' => '랪',
  '�J' => '랮',
  '�K' => '랯',
  '�L' => '랰',
  '�M' => '랱',
  '�N' => '랲',
  '�O' => '랳',
  '�P' => '랶',
  '�Q' => '랷',
  '�R' => '랹',
  '�S' => '랺',
  '�T' => '랻',
  '�U' => '랼',
  '�V' => '랽',
  '�W' => '랾',
  '�X' => '랿',
  '�Y' => '럀',
  '�Z' => '럁',
  '�a' => '럂',
  '�b' => '럃',
  '�c' => '럄',
  '�d' => '럅',
  '�e' => '럆',
  '�f' => '럈',
  '�g' => '럊',
  '�h' => '럋',
  '�i' => '럌',
  '�j' => '럍',
  '�k' => '럎',
  '�l' => '럏',
  '�m' => '럐',
  '�n' => '럑',
  '�o' => '럒',
  '�p' => '럓',
  '�q' => '럔',
  '�r' => '럕',
  '�s' => '럖',
  '�t' => '럗',
  '�u' => '럘',
  '�v' => '럙',
  '�w' => '럚',
  '�x' => '럛',
  '�y' => '럜',
  '�z' => '럝',
  '��' => '럞',
  '��' => '럟',
  '��' => '럠',
  '��' => '럡',
  '��' => '럢',
  '��' => '럣',
  '��' => '럤',
  '��' => '럥',
  '��' => '럦',
  '��' => '럧',
  '��' => '럨',
  '��' => '럩',
  '��' => '럪',
  '��' => '럫',
  '��' => '럮',
  '��' => '럯',
  '��' => '럱',
  '��' => '럲',
  '��' => '럳',
  '��' => '럵',
  '��' => '럶',
  '��' => '럷',
  '��' => '럸',
  '��' => '럹',
  '��' => '럺',
  '��' => '럻',
  '��' => '럾',
  '��' => '렂',
  '��' => '렃',
  '��' => '렄',
  '��' => '렅',
  '��' => '렆',
  '��' => '렊',
  '��' => '렋',
  '��' => '렍',
  '��' => '렎',
  '��' => '렏',
  '��' => '렑',
  '��' => '렒',
  '��' => '렓',
  '��' => '렔',
  '��' => '렕',
  '��' => '렖',
  '��' => '렗',
  '��' => '렚',
  '��' => '렜',
  '��' => '렞',
  '��' => '렟',
  '��' => '렠',
  '��' => '렡',
  '��' => '렢',
  '��' => '렣',
  '��' => '렦',
  '��' => '렧',
  '��' => '렩',
  '��' => '렪',
  '��' => '렫',
  '��' => '렭',
  '��' => '렮',
  '��' => '렯',
  '��' => '렰',
  '��' => '렱',
  '��' => '렲',
  '��' => '렳',
  '��' => '렶',
  '��' => '렺',
  '��' => '렻',
  '��' => '렼',
  '��' => '렽',
  '��' => '렾',
  '��' => '렿',
  '��' => '롁',
  '��' => '롂',
  '��' => '롃',
  '��' => '롅',
  '��' => '롆',
  '��' => '롇',
  '��' => '롈',
  '��' => '롉',
  '��' => '롊',
  '��' => '롋',
  '��' => '롌',
  '��' => '롍',
  '��' => '롎',
  '��' => '롏',
  '��' => '롐',
  '��' => '롒',
  '��' => '롔',
  '��' => '롕',
  '��' => '롖',
  '��' => '롗',
  '��' => '롘',
  '��' => '롙',
  '��' => '롚',
  '��' => '롛',
  '��' => '롞',
  '��' => '롟',
  '��' => '롡',
  '��' => '롢',
  '��' => '롣',
  '��' => '롥',
  '��' => '롦',
  '��' => '롧',
  '��' => '롨',
  '��' => '롩',
  '��' => '롪',
  '��' => '롫',
  '��' => '롮',
  '��' => '롰',
  '��' => '롲',
  '��' => '롳',
  '��' => '롴',
  '��' => '롵',
  '��' => '롶',
  '��' => '롷',
  '��' => '롹',
  '��' => '롺',
  '��' => '롻',
  '��' => '롽',
  '��' => '롾',
  '��' => '롿',
  '��' => '뢀',
  '��' => '뢁',
  '��' => '뢂',
  '��' => '뢃',
  '��' => '뢄',
  '�A' => '뢅',
  '�B' => '뢆',
  '�C' => '뢇',
  '�D' => '뢈',
  '�E' => '뢉',
  '�F' => '뢊',
  '�G' => '뢋',
  '�H' => '뢌',
  '�I' => '뢎',
  '�J' => '뢏',
  '�K' => '뢐',
  '�L' => '뢑',
  '�M' => '뢒',
  '�N' => '뢓',
  '�O' => '뢔',
  '�P' => '뢕',
  '�Q' => '뢖',
  '�R' => '뢗',
  '�S' => '뢘',
  '�T' => '뢙',
  '�U' => '뢚',
  '�V' => '뢛',
  '�W' => '뢜',
  '�X' => '뢝',
  '�Y' => '뢞',
  '�Z' => '뢟',
  '�a' => '뢠',
  '�b' => '뢡',
  '�c' => '뢢',
  '�d' => '뢣',
  '�e' => '뢤',
  '�f' => '뢥',
  '�g' => '뢦',
  '�h' => '뢧',
  '�i' => '뢩',
  '�j' => '뢪',
  '�k' => '뢫',
  '�l' => '뢬',
  '�m' => '뢭',
  '�n' => '뢮',
  '�o' => '뢯',
  '�p' => '뢱',
  '�q' => '뢲',
  '�r' => '뢳',
  '�s' => '뢵',
  '�t' => '뢶',
  '�u' => '뢷',
  '�v' => '뢹',
  '�w' => '뢺',
  '�x' => '뢻',
  '�y' => '뢼',
  '�z' => '뢽',
  '��' => '뢾',
  '��' => '뢿',
  '��' => '룂',
  '��' => '룄',
  '��' => '룆',
  '��' => '룇',
  '��' => '룈',
  '��' => '룉',
  '��' => '룊',
  '��' => '룋',
  '��' => '룍',
  '��' => '룎',
  '��' => '룏',
  '��' => '룑',
  '��' => '룒',
  '��' => '룓',
  '��' => '룕',
  '��' => '룖',
  '��' => '룗',
  '��' => '룘',
  '��' => '룙',
  '��' => '룚',
  '��' => '룛',
  '��' => '룜',
  '��' => '룞',
  '��' => '룠',
  '��' => '룢',
  '��' => '룣',
  '��' => '룤',
  '��' => '룥',
  '��' => '룦',
  '��' => '룧',
  '��' => '룪',
  '��' => '룫',
  '��' => '룭',
  '��' => '룮',
  '��' => '룯',
  '��' => '룱',
  '��' => '룲',
  '��' => '룳',
  '��' => '룴',
  '��' => '룵',
  '��' => '룶',
  '��' => '룷',
  '��' => '룺',
  '��' => '룼',
  '��' => '룾',
  '��' => '룿',
  '��' => '뤀',
  '��' => '뤁',
  '��' => '뤂',
  '��' => '뤃',
  '��' => '뤅',
  '��' => '뤆',
  '��' => '뤇',
  '��' => '뤈',
  '��' => '뤉',
  '��' => '뤊',
  '��' => '뤋',
  '��' => '뤌',
  '��' => '뤍',
  '��' => '뤎',
  '��' => '뤏',
  '��' => '뤐',
  '��' => '뤑',
  '��' => '뤒',
  '��' => '뤓',
  '��' => '뤔',
  '��' => '뤕',
  '��' => '뤖',
  '��' => '뤗',
  '��' => '뤙',
  '��' => '뤚',
  '��' => '뤛',
  '��' => '뤜',
  '��' => '뤝',
  '��' => '뤞',
  '��' => '뤟',
  '��' => '뤡',
  '��' => '뤢',
  '��' => '뤣',
  '��' => '뤤',
  '��' => '뤥',
  '��' => '뤦',
  '��' => '뤧',
  '��' => '뤨',
  '��' => '뤩',
  '��' => '뤪',
  '��' => '뤫',
  '��' => '뤬',
  '��' => '뤭',
  '��' => '뤮',
  '��' => '뤯',
  '��' => '뤰',
  '��' => '뤱',
  '��' => '뤲',
  '��' => '뤳',
  '��' => '뤴',
  '��' => '뤵',
  '��' => '뤶',
  '��' => '뤷',
  '��' => '뤸',
  '��' => '뤹',
  '��' => '뤺',
  '��' => '뤻',
  '��' => '뤾',
  '��' => '뤿',
  '��' => '륁',
  '��' => '륂',
  '��' => '륃',
  '��' => '륅',
  '��' => '륆',
  '��' => '륇',
  '��' => '륈',
  '��' => '륉',
  '��' => '륊',
  '��' => '륋',
  '��' => '륍',
  '��' => '륎',
  '��' => '륐',
  '��' => '륒',
  '��' => '륓',
  '��' => '륔',
  '��' => '륕',
  '��' => '륖',
  '��' => '륗',
  '�A' => '륚',
  '�B' => '륛',
  '�C' => '륝',
  '�D' => '륞',
  '�E' => '륟',
  '�F' => '륡',
  '�G' => '륢',
  '�H' => '륣',
  '�I' => '륤',
  '�J' => '륥',
  '�K' => '륦',
  '�L' => '륧',
  '�M' => '륪',
  '�N' => '륬',
  '�O' => '륮',
  '�P' => '륯',
  '�Q' => '륰',
  '�R' => '륱',
  '�S' => '륲',
  '�T' => '륳',
  '�U' => '륶',
  '�V' => '륷',
  '�W' => '륹',
  '�X' => '륺',
  '�Y' => '륻',
  '�Z' => '륽',
  '�a' => '륾',
  '�b' => '륿',
  '�c' => '릀',
  '�d' => '릁',
  '�e' => '릂',
  '�f' => '릃',
  '�g' => '릆',
  '�h' => '릈',
  '�i' => '릋',
  '�j' => '릌',
  '�k' => '릏',
  '�l' => '릐',
  '�m' => '릑',
  '�n' => '릒',
  '�o' => '릓',
  '�p' => '릔',
  '�q' => '릕',
  '�r' => '릖',
  '�s' => '릗',
  '�t' => '릘',
  '�u' => '릙',
  '�v' => '릚',
  '�w' => '릛',
  '�x' => '릜',
  '�y' => '릝',
  '�z' => '릞',
  '��' => '릟',
  '��' => '릠',
  '��' => '릡',
  '��' => '릢',
  '��' => '릣',
  '��' => '릤',
  '��' => '릥',
  '��' => '릦',
  '��' => '릧',
  '��' => '릨',
  '��' => '릩',
  '��' => '릪',
  '��' => '릫',
  '��' => '릮',
  '��' => '릯',
  '��' => '릱',
  '��' => '릲',
  '��' => '릳',
  '��' => '릵',
  '��' => '릶',
  '��' => '릷',
  '��' => '릸',
  '��' => '릹',
  '��' => '릺',
  '��' => '릻',
  '��' => '릾',
  '��' => '맀',
  '��' => '맂',
  '��' => '맃',
  '��' => '맄',
  '��' => '맅',
  '��' => '맆',
  '��' => '맇',
  '��' => '맊',
  '��' => '맋',
  '��' => '맍',
  '��' => '맓',
  '��' => '맔',
  '��' => '맕',
  '��' => '맖',
  '��' => '맗',
  '��' => '맚',
  '��' => '맜',
  '��' => '맟',
  '��' => '맠',
  '��' => '맢',
  '��' => '맦',
  '��' => '맧',
  '��' => '맩',
  '��' => '맪',
  '��' => '맫',
  '��' => '맭',
  '��' => '맮',
  '��' => '맯',
  '��' => '맰',
  '��' => '맱',
  '��' => '맲',
  '��' => '맳',
  '��' => '맶',
  '��' => '맻',
  '��' => '맼',
  '��' => '맽',
  '��' => '맾',
  '��' => '맿',
  '��' => '먂',
  '��' => '먃',
  '��' => '먄',
  '��' => '먅',
  '��' => '먆',
  '��' => '먇',
  '��' => '먉',
  '��' => '먊',
  '��' => '먋',
  '��' => '먌',
  '��' => '먍',
  '��' => '먎',
  '��' => '먏',
  '��' => '먐',
  '��' => '먑',
  '��' => '먒',
  '��' => '먓',
  '��' => '먔',
  '��' => '먖',
  '��' => '먗',
  '��' => '먘',
  '��' => '먙',
  '��' => '먚',
  '��' => '먛',
  '��' => '먜',
  '��' => '먝',
  '��' => '먞',
  '��' => '먟',
  '��' => '먠',
  '��' => '먡',
  '��' => '먢',
  '��' => '먣',
  '��' => '먤',
  '��' => '먥',
  '��' => '먦',
  '��' => '먧',
  '��' => '먨',
  '��' => '먩',
  '��' => '먪',
  '��' => '먫',
  '��' => '먬',
  '��' => '먭',
  '��' => '먮',
  '��' => '먯',
  '��' => '먰',
  '��' => '먱',
  '��' => '먲',
  '��' => '먳',
  '��' => '먴',
  '��' => '먵',
  '��' => '먶',
  '��' => '먷',
  '��' => '먺',
  '��' => '먻',
  '��' => '먽',
  '��' => '먾',
  '��' => '먿',
  '��' => '멁',
  '��' => '멃',
  '��' => '멄',
  '��' => '멅',
  '��' => '멆',
  '�A' => '멇',
  '�B' => '멊',
  '�C' => '멌',
  '�D' => '멏',
  '�E' => '멐',
  '�F' => '멑',
  '�G' => '멒',
  '�H' => '멖',
  '�I' => '멗',
  '�J' => '멙',
  '�K' => '멚',
  '�L' => '멛',
  '�M' => '멝',
  '�N' => '멞',
  '�O' => '멟',
  '�P' => '멠',
  '�Q' => '멡',
  '�R' => '멢',
  '�S' => '멣',
  '�T' => '멦',
  '�U' => '멪',
  '�V' => '멫',
  '�W' => '멬',
  '�X' => '멭',
  '�Y' => '멮',
  '�Z' => '멯',
  '�a' => '멲',
  '�b' => '멳',
  '�c' => '멵',
  '�d' => '멶',
  '�e' => '멷',
  '�f' => '멹',
  '�g' => '멺',
  '�h' => '멻',
  '�i' => '멼',
  '�j' => '멽',
  '�k' => '멾',
  '�l' => '멿',
  '�m' => '몀',
  '�n' => '몁',
  '�o' => '몂',
  '�p' => '몆',
  '�q' => '몈',
  '�r' => '몉',
  '�s' => '몊',
  '�t' => '몋',
  '�u' => '몍',
  '�v' => '몎',
  '�w' => '몏',
  '�x' => '몐',
  '�y' => '몑',
  '�z' => '몒',
  '��' => '몓',
  '��' => '몔',
  '��' => '몕',
  '��' => '몖',
  '��' => '몗',
  '��' => '몘',
  '��' => '몙',
  '��' => '몚',
  '��' => '몛',
  '��' => '몜',
  '��' => '몝',
  '��' => '몞',
  '��' => '몟',
  '��' => '몠',
  '��' => '몡',
  '��' => '몢',
  '��' => '몣',
  '��' => '몤',
  '��' => '몥',
  '��' => '몦',
  '��' => '몧',
  '��' => '몪',
  '��' => '몭',
  '��' => '몮',
  '��' => '몯',
  '��' => '몱',
  '��' => '몳',
  '��' => '몴',
  '��' => '몵',
  '��' => '몶',
  '��' => '몷',
  '��' => '몺',
  '��' => '몼',
  '��' => '몾',
  '��' => '몿',
  '��' => '뫀',
  '��' => '뫁',
  '��' => '뫂',
  '��' => '뫃',
  '��' => '뫅',
  '��' => '뫆',
  '��' => '뫇',
  '��' => '뫉',
  '��' => '뫊',
  '��' => '뫋',
  '��' => '뫌',
  '��' => '뫍',
  '��' => '뫎',
  '��' => '뫏',
  '��' => '뫐',
  '��' => '뫑',
  '��' => '뫒',
  '��' => '뫓',
  '��' => '뫔',
  '��' => '뫕',
  '��' => '뫖',
  '��' => '뫗',
  '��' => '뫚',
  '��' => '뫛',
  '��' => '뫜',
  '��' => '뫝',
  '��' => '뫞',
  '��' => '뫟',
  '��' => '뫠',
  '��' => '뫡',
  '��' => '뫢',
  '��' => '뫣',
  '��' => '뫤',
  '��' => '뫥',
  '��' => '뫦',
  '��' => '뫧',
  '��' => '뫨',
  '��' => '뫩',
  '��' => '뫪',
  '��' => '뫫',
  '��' => '뫬',
  '��' => '뫭',
  '��' => '뫮',
  '��' => '뫯',
  '��' => '뫰',
  '��' => '뫱',
  '��' => '뫲',
  '��' => '뫳',
  '��' => '뫴',
  '��' => '뫵',
  '��' => '뫶',
  '��' => '뫷',
  '��' => '뫸',
  '��' => '뫹',
  '��' => '뫺',
  '��' => '뫻',
  '��' => '뫽',
  '��' => '뫾',
  '��' => '뫿',
  '��' => '묁',
  '��' => '묂',
  '��' => '묃',
  '��' => '묅',
  '��' => '묆',
  '��' => '묇',
  '��' => '묈',
  '��' => '묉',
  '��' => '묊',
  '��' => '묋',
  '��' => '묌',
  '��' => '묎',
  '��' => '묐',
  '��' => '묒',
  '��' => '묓',
  '��' => '묔',
  '��' => '묕',
  '��' => '묖',
  '��' => '묗',
  '��' => '묙',
  '��' => '묚',
  '��' => '묛',
  '��' => '묝',
  '��' => '묞',
  '��' => '묟',
  '��' => '묡',
  '��' => '묢',
  '��' => '묣',
  '��' => '묤',
  '��' => '묥',
  '��' => '묦',
  '��' => '묧',
  '�A' => '묨',
  '�B' => '묪',
  '�C' => '묬',
  '�D' => '묭',
  '�E' => '묮',
  '�F' => '묯',
  '�G' => '묰',
  '�H' => '묱',
  '�I' => '묲',
  '�J' => '묳',
  '�K' => '묷',
  '�L' => '묹',
  '�M' => '묺',
  '�N' => '묿',
  '�O' => '뭀',
  '�P' => '뭁',
  '�Q' => '뭂',
  '�R' => '뭃',
  '�S' => '뭆',
  '�T' => '뭈',
  '�U' => '뭊',
  '�V' => '뭋',
  '�W' => '뭌',
  '�X' => '뭎',
  '�Y' => '뭑',
  '�Z' => '뭒',
  '�a' => '뭓',
  '�b' => '뭕',
  '�c' => '뭖',
  '�d' => '뭗',
  '�e' => '뭙',
  '�f' => '뭚',
  '�g' => '뭛',
  '�h' => '뭜',
  '�i' => '뭝',
  '�j' => '뭞',
  '�k' => '뭟',
  '�l' => '뭠',
  '�m' => '뭢',
  '�n' => '뭤',
  '�o' => '뭥',
  '�p' => '뭦',
  '�q' => '뭧',
  '�r' => '뭨',
  '�s' => '뭩',
  '�t' => '뭪',
  '�u' => '뭫',
  '�v' => '뭭',
  '�w' => '뭮',
  '�x' => '뭯',
  '�y' => '뭰',
  '�z' => '뭱',
  '��' => '뭲',
  '��' => '뭳',
  '��' => '뭴',
  '��' => '뭵',
  '��' => '뭶',
  '��' => '뭷',
  '��' => '뭸',
  '��' => '뭹',
  '��' => '뭺',
  '��' => '뭻',
  '��' => '뭼',
  '��' => '뭽',
  '��' => '뭾',
  '��' => '뭿',
  '��' => '뮀',
  '��' => '뮁',
  '��' => '뮂',
  '��' => '뮃',
  '��' => '뮄',
  '��' => '뮅',
  '��' => '뮆',
  '��' => '뮇',
  '��' => '뮉',
  '��' => '뮊',
  '��' => '뮋',
  '��' => '뮍',
  '��' => '뮎',
  '��' => '뮏',
  '��' => '뮑',
  '��' => '뮒',
  '��' => '뮓',
  '��' => '뮔',
  '��' => '뮕',
  '��' => '뮖',
  '��' => '뮗',
  '��' => '뮘',
  '��' => '뮙',
  '��' => '뮚',
  '��' => '뮛',
  '��' => '뮜',
  '��' => '뮝',
  '��' => '뮞',
  '��' => '뮟',
  '��' => '뮠',
  '��' => '뮡',
  '��' => '뮢',
  '��' => '뮣',
  '��' => '뮥',
  '��' => '뮦',
  '��' => '뮧',
  '��' => '뮩',
  '��' => '뮪',
  '��' => '뮫',
  '��' => '뮭',
  '��' => '뮮',
  '��' => '뮯',
  '��' => '뮰',
  '��' => '뮱',
  '��' => '뮲',
  '��' => '뮳',
  '��' => '뮵',
  '��' => '뮶',
  '��' => '뮸',
  '��' => '뮹',
  '��' => '뮺',
  '��' => '뮻',
  '��' => '뮼',
  '��' => '뮽',
  '��' => '뮾',
  '��' => '뮿',
  '��' => '믁',
  '��' => '믂',
  '��' => '믃',
  '��' => '믅',
  '��' => '믆',
  '��' => '믇',
  '��' => '믉',
  '��' => '믊',
  '��' => '믋',
  '��' => '믌',
  '��' => '믍',
  '��' => '믎',
  '��' => '믏',
  '��' => '믑',
  '��' => '믒',
  '��' => '믔',
  '��' => '믕',
  '��' => '믖',
  '��' => '믗',
  '��' => '믘',
  '��' => '믙',
  '��' => '믚',
  '��' => '믛',
  '��' => '믜',
  '��' => '믝',
  '��' => '믞',
  '��' => '믟',
  '��' => '믠',
  '��' => '믡',
  '��' => '믢',
  '��' => '믣',
  '��' => '믤',
  '��' => '믥',
  '��' => '믦',
  '��' => '믧',
  '��' => '믨',
  '��' => '믩',
  '��' => '믪',
  '��' => '믫',
  '��' => '믬',
  '��' => '믭',
  '��' => '믮',
  '��' => '믯',
  '��' => '믰',
  '��' => '믱',
  '��' => '믲',
  '��' => '믳',
  '��' => '믴',
  '��' => '믵',
  '��' => '믶',
  '��' => '믷',
  '��' => '믺',
  '��' => '믻',
  '��' => '믽',
  '��' => '믾',
  '��' => '밁',
  '�A' => '밃',
  '�B' => '밄',
  '�C' => '밅',
  '�D' => '밆',
  '�E' => '밇',
  '�F' => '밊',
  '�G' => '밎',
  '�H' => '밐',
  '�I' => '밒',
  '�J' => '밓',
  '�K' => '밙',
  '�L' => '밚',
  '�M' => '밠',
  '�N' => '밡',
  '�O' => '밢',
  '�P' => '밣',
  '�Q' => '밦',
  '�R' => '밨',
  '�S' => '밪',
  '�T' => '밫',
  '�U' => '밬',
  '�V' => '밮',
  '�W' => '밯',
  '�X' => '밲',
  '�Y' => '밳',
  '�Z' => '밵',
  '�a' => '밶',
  '�b' => '밷',
  '�c' => '밹',
  '�d' => '밺',
  '�e' => '밻',
  '�f' => '밼',
  '�g' => '밽',
  '�h' => '밾',
  '�i' => '밿',
  '�j' => '뱂',
  '�k' => '뱆',
  '�l' => '뱇',
  '�m' => '뱈',
  '�n' => '뱊',
  '�o' => '뱋',
  '�p' => '뱎',
  '�q' => '뱏',
  '�r' => '뱑',
  '�s' => '뱒',
  '�t' => '뱓',
  '�u' => '뱔',
  '�v' => '뱕',
  '�w' => '뱖',
  '�x' => '뱗',
  '�y' => '뱘',
  '�z' => '뱙',
  '��' => '뱚',
  '��' => '뱛',
  '��' => '뱜',
  '��' => '뱞',
  '��' => '뱟',
  '��' => '뱠',
  '��' => '뱡',
  '��' => '뱢',
  '��' => '뱣',
  '��' => '뱤',
  '��' => '뱥',
  '��' => '뱦',
  '��' => '뱧',
  '��' => '뱨',
  '��' => '뱩',
  '��' => '뱪',
  '��' => '뱫',
  '��' => '뱬',
  '��' => '뱭',
  '��' => '뱮',
  '��' => '뱯',
  '��' => '뱰',
  '��' => '뱱',
  '��' => '뱲',
  '��' => '뱳',
  '��' => '뱴',
  '��' => '뱵',
  '��' => '뱶',
  '��' => '뱷',
  '��' => '뱸',
  '��' => '뱹',
  '��' => '뱺',
  '��' => '뱻',
  '��' => '뱼',
  '��' => '뱽',
  '��' => '뱾',
  '��' => '뱿',
  '��' => '벀',
  '��' => '벁',
  '��' => '벂',
  '��' => '벃',
  '��' => '벆',
  '��' => '벇',
  '��' => '벉',
  '��' => '벊',
  '��' => '벍',
  '��' => '벏',
  '��' => '벐',
  '��' => '벑',
  '��' => '벒',
  '��' => '벓',
  '��' => '벖',
  '��' => '벘',
  '��' => '벛',
  '��' => '벜',
  '��' => '벝',
  '��' => '벞',
  '��' => '벟',
  '��' => '벢',
  '��' => '벣',
  '��' => '벥',
  '��' => '벦',
  '��' => '벩',
  '��' => '벪',
  '��' => '벫',
  '��' => '벬',
  '��' => '벭',
  '��' => '벮',
  '��' => '벯',
  '��' => '벲',
  '��' => '벶',
  '��' => '벷',
  '��' => '벸',
  '��' => '벹',
  '��' => '벺',
  '��' => '벻',
  '��' => '벾',
  '��' => '벿',
  '��' => '볁',
  '��' => '볂',
  '��' => '볃',
  '��' => '볅',
  '��' => '볆',
  '��' => '볇',
  '��' => '볈',
  '��' => '볉',
  '��' => '볊',
  '��' => '볋',
  '��' => '볌',
  '��' => '볎',
  '��' => '볒',
  '��' => '볓',
  '��' => '볔',
  '��' => '볖',
  '��' => '볗',
  '��' => '볙',
  '��' => '볚',
  '��' => '볛',
  '��' => '볝',
  '��' => '볞',
  '��' => '볟',
  '��' => '볠',
  '��' => '볡',
  '��' => '볢',
  '��' => '볣',
  '��' => '볤',
  '��' => '볥',
  '��' => '볦',
  '��' => '볧',
  '��' => '볨',
  '��' => '볩',
  '��' => '볪',
  '��' => '볫',
  '��' => '볬',
  '��' => '볭',
  '��' => '볮',
  '��' => '볯',
  '��' => '볰',
  '��' => '볱',
  '��' => '볲',
  '��' => '볳',
  '��' => '볷',
  '��' => '볹',
  '��' => '볺',
  '��' => '볻',
  '��' => '볽',
  '�A' => '볾',
  '�B' => '볿',
  '�C' => '봀',
  '�D' => '봁',
  '�E' => '봂',
  '�F' => '봃',
  '�G' => '봆',
  '�H' => '봈',
  '�I' => '봊',
  '�J' => '봋',
  '�K' => '봌',
  '�L' => '봍',
  '�M' => '봎',
  '�N' => '봏',
  '�O' => '봑',
  '�P' => '봒',
  '�Q' => '봓',
  '�R' => '봕',
  '�S' => '봖',
  '�T' => '봗',
  '�U' => '봘',
  '�V' => '봙',
  '�W' => '봚',
  '�X' => '봛',
  '�Y' => '봜',
  '�Z' => '봝',
  '�a' => '봞',
  '�b' => '봟',
  '�c' => '봠',
  '�d' => '봡',
  '�e' => '봢',
  '�f' => '봣',
  '�g' => '봥',
  '�h' => '봦',
  '�i' => '봧',
  '�j' => '봨',
  '�k' => '봩',
  '�l' => '봪',
  '�m' => '봫',
  '�n' => '봭',
  '�o' => '봮',
  '�p' => '봯',
  '�q' => '봰',
  '�r' => '봱',
  '�s' => '봲',
  '�t' => '봳',
  '�u' => '봴',
  '�v' => '봵',
  '�w' => '봶',
  '�x' => '봷',
  '�y' => '봸',
  '�z' => '봹',
  '��' => '봺',
  '��' => '봻',
  '��' => '봼',
  '��' => '봽',
  '��' => '봾',
  '��' => '봿',
  '��' => '뵁',
  '��' => '뵂',
  '��' => '뵃',
  '��' => '뵄',
  '��' => '뵅',
  '��' => '뵆',
  '��' => '뵇',
  '��' => '뵊',
  '��' => '뵋',
  '��' => '뵍',
  '��' => '뵎',
  '��' => '뵏',
  '��' => '뵑',
  '��' => '뵒',
  '��' => '뵓',
  '��' => '뵔',
  '��' => '뵕',
  '��' => '뵖',
  '��' => '뵗',
  '��' => '뵚',
  '��' => '뵛',
  '��' => '뵜',
  '��' => '뵝',
  '��' => '뵞',
  '��' => '뵟',
  '��' => '뵠',
  '��' => '뵡',
  '��' => '뵢',
  '��' => '뵣',
  '��' => '뵥',
  '��' => '뵦',
  '��' => '뵧',
  '��' => '뵩',
  '��' => '뵪',
  '��' => '뵫',
  '��' => '뵬',
  '��' => '뵭',
  '��' => '뵮',
  '��' => '뵯',
  '��' => '뵰',
  '��' => '뵱',
  '��' => '뵲',
  '��' => '뵳',
  '��' => '뵴',
  '��' => '뵵',
  '��' => '뵶',
  '��' => '뵷',
  '��' => '뵸',
  '��' => '뵹',
  '��' => '뵺',
  '��' => '뵻',
  '��' => '뵼',
  '��' => '뵽',
  '��' => '뵾',
  '��' => '뵿',
  '��' => '붂',
  '��' => '붃',
  '��' => '붅',
  '��' => '붆',
  '��' => '붋',
  '��' => '붌',
  '��' => '붍',
  '��' => '붎',
  '��' => '붏',
  '��' => '붒',
  '��' => '붔',
  '��' => '붖',
  '��' => '붗',
  '��' => '붘',
  '��' => '붛',
  '��' => '붝',
  '��' => '붞',
  '��' => '붟',
  '��' => '붠',
  '��' => '붡',
  '��' => '붢',
  '��' => '붣',
  '��' => '붥',
  '��' => '붦',
  '��' => '붧',
  '��' => '붨',
  '��' => '붩',
  '��' => '붪',
  '��' => '붫',
  '��' => '붬',
  '��' => '붭',
  '��' => '붮',
  '��' => '붯',
  '��' => '붱',
  '��' => '붲',
  '��' => '붳',
  '��' => '붴',
  '��' => '붵',
  '��' => '붶',
  '��' => '붷',
  '��' => '붹',
  '��' => '붺',
  '��' => '붻',
  '��' => '붼',
  '��' => '붽',
  '��' => '붾',
  '��' => '붿',
  '��' => '뷀',
  '��' => '뷁',
  '��' => '뷂',
  '��' => '뷃',
  '��' => '뷄',
  '��' => '뷅',
  '��' => '뷆',
  '��' => '뷇',
  '��' => '뷈',
  '��' => '뷉',
  '��' => '뷊',
  '��' => '뷋',
  '��' => '뷌',
  '��' => '뷍',
  '��' => '뷎',
  '��' => '뷏',
  '��' => '뷐',
  '��' => '뷑',
  '�A' => '뷒',
  '�B' => '뷓',
  '�C' => '뷖',
  '�D' => '뷗',
  '�E' => '뷙',
  '�F' => '뷚',
  '�G' => '뷛',
  '�H' => '뷝',
  '�I' => '뷞',
  '�J' => '뷟',
  '�K' => '뷠',
  '�L' => '뷡',
  '�M' => '뷢',
  '�N' => '뷣',
  '�O' => '뷤',
  '�P' => '뷥',
  '�Q' => '뷦',
  '�R' => '뷧',
  '�S' => '뷨',
  '�T' => '뷪',
  '�U' => '뷫',
  '�V' => '뷬',
  '�W' => '뷭',
  '�X' => '뷮',
  '�Y' => '뷯',
  '�Z' => '뷱',
  '�a' => '뷲',
  '�b' => '뷳',
  '�c' => '뷵',
  '�d' => '뷶',
  '�e' => '뷷',
  '�f' => '뷹',
  '�g' => '뷺',
  '�h' => '뷻',
  '�i' => '뷼',
  '�j' => '뷽',
  '�k' => '뷾',
  '�l' => '뷿',
  '�m' => '븁',
  '�n' => '븂',
  '�o' => '븄',
  '�p' => '븆',
  '�q' => '븇',
  '�r' => '븈',
  '�s' => '븉',
  '�t' => '븊',
  '�u' => '븋',
  '�v' => '븎',
  '�w' => '븏',
  '�x' => '븑',
  '�y' => '븒',
  '�z' => '븓',
  '��' => '븕',
  '��' => '븖',
  '��' => '븗',
  '��' => '븘',
  '��' => '븙',
  '��' => '븚',
  '��' => '븛',
  '��' => '븞',
  '��' => '븠',
  '��' => '븡',
  '��' => '븢',
  '��' => '븣',
  '��' => '븤',
  '��' => '븥',
  '��' => '븦',
  '��' => '븧',
  '��' => '븨',
  '��' => '븩',
  '��' => '븪',
  '��' => '븫',
  '��' => '븬',
  '��' => '븭',
  '��' => '븮',
  '��' => '븯',
  '��' => '븰',
  '��' => '븱',
  '��' => '븲',
  '��' => '븳',
  '��' => '븴',
  '��' => '븵',
  '��' => '븶',
  '��' => '븷',
  '��' => '븸',
  '��' => '븹',
  '��' => '븺',
  '��' => '븻',
  '��' => '븼',
  '��' => '븽',
  '��' => '븾',
  '��' => '븿',
  '��' => '빀',
  '��' => '빁',
  '��' => '빂',
  '��' => '빃',
  '��' => '빆',
  '��' => '빇',
  '��' => '빉',
  '��' => '빊',
  '��' => '빋',
  '��' => '빍',
  '��' => '빏',
  '��' => '빐',
  '��' => '빑',
  '��' => '빒',
  '��' => '빓',
  '��' => '빖',
  '��' => '빘',
  '��' => '빜',
  '��' => '빝',
  '��' => '빞',
  '��' => '빟',
  '��' => '빢',
  '��' => '빣',
  '��' => '빥',
  '��' => '빦',
  '��' => '빧',
  '��' => '빩',
  '��' => '빫',
  '��' => '빬',
  '��' => '빭',
  '��' => '빮',
  '��' => '빯',
  '��' => '빲',
  '��' => '빶',
  '��' => '빷',
  '��' => '빸',
  '��' => '빹',
  '��' => '빺',
  '��' => '빾',
  '��' => '빿',
  '��' => '뺁',
  '��' => '뺂',
  '��' => '뺃',
  '��' => '뺅',
  '��' => '뺆',
  '��' => '뺇',
  '��' => '뺈',
  '��' => '뺉',
  '��' => '뺊',
  '��' => '뺋',
  '��' => '뺎',
  '��' => '뺒',
  '��' => '뺓',
  '��' => '뺔',
  '��' => '뺕',
  '��' => '뺖',
  '��' => '뺗',
  '��' => '뺚',
  '��' => '뺛',
  '��' => '뺜',
  '��' => '뺝',
  '��' => '뺞',
  '��' => '뺟',
  '��' => '뺠',
  '��' => '뺡',
  '��' => '뺢',
  '��' => '뺣',
  '��' => '뺤',
  '��' => '뺥',
  '��' => '뺦',
  '��' => '뺧',
  '��' => '뺩',
  '��' => '뺪',
  '��' => '뺫',
  '��' => '뺬',
  '��' => '뺭',
  '��' => '뺮',
  '��' => '뺯',
  '��' => '뺰',
  '��' => '뺱',
  '��' => '뺲',
  '��' => '뺳',
  '��' => '뺴',
  '��' => '뺵',
  '��' => '뺶',
  '��' => '뺷',
  '�A' => '뺸',
  '�B' => '뺹',
  '�C' => '뺺',
  '�D' => '뺻',
  '�E' => '뺼',
  '�F' => '뺽',
  '�G' => '뺾',
  '�H' => '뺿',
  '�I' => '뻀',
  '�J' => '뻁',
  '�K' => '뻂',
  '�L' => '뻃',
  '�M' => '뻄',
  '�N' => '뻅',
  '�O' => '뻆',
  '�P' => '뻇',
  '�Q' => '뻈',
  '�R' => '뻉',
  '�S' => '뻊',
  '�T' => '뻋',
  '�U' => '뻌',
  '�V' => '뻍',
  '�W' => '뻎',
  '�X' => '뻏',
  '�Y' => '뻒',
  '�Z' => '뻓',
  '�a' => '뻕',
  '�b' => '뻖',
  '�c' => '뻙',
  '�d' => '뻚',
  '�e' => '뻛',
  '�f' => '뻜',
  '�g' => '뻝',
  '�h' => '뻞',
  '�i' => '뻟',
  '�j' => '뻡',
  '�k' => '뻢',
  '�l' => '뻦',
  '�m' => '뻧',
  '�n' => '뻨',
  '�o' => '뻩',
  '�p' => '뻪',
  '�q' => '뻫',
  '�r' => '뻭',
  '�s' => '뻮',
  '�t' => '뻯',
  '�u' => '뻰',
  '�v' => '뻱',
  '�w' => '뻲',
  '�x' => '뻳',
  '�y' => '뻴',
  '�z' => '뻵',
  '��' => '뻶',
  '��' => '뻷',
  '��' => '뻸',
  '��' => '뻹',
  '��' => '뻺',
  '��' => '뻻',
  '��' => '뻼',
  '��' => '뻽',
  '��' => '뻾',
  '��' => '뻿',
  '��' => '뼀',
  '��' => '뼂',
  '��' => '뼃',
  '��' => '뼄',
  '��' => '뼅',
  '��' => '뼆',
  '��' => '뼇',
  '��' => '뼊',
  '��' => '뼋',
  '��' => '뼌',
  '��' => '뼍',
  '��' => '뼎',
  '��' => '뼏',
  '��' => '뼐',
  '��' => '뼑',
  '��' => '뼒',
  '��' => '뼓',
  '��' => '뼔',
  '��' => '뼕',
  '��' => '뼖',
  '��' => '뼗',
  '��' => '뼚',
  '��' => '뼞',
  '��' => '뼟',
  '��' => '뼠',
  '��' => '뼡',
  '��' => '뼢',
  '��' => '뼣',
  '��' => '뼤',
  '��' => '뼥',
  '��' => '뼦',
  '��' => '뼧',
  '��' => '뼨',
  '��' => '뼩',
  '��' => '뼪',
  '��' => '뼫',
  '��' => '뼬',
  '��' => '뼭',
  '��' => '뼮',
  '��' => '뼯',
  '��' => '뼰',
  '��' => '뼱',
  '��' => '뼲',
  '��' => '뼳',
  '��' => '뼴',
  '��' => '뼵',
  '��' => '뼶',
  '��' => '뼷',
  '��' => '뼸',
  '��' => '뼹',
  '��' => '뼺',
  '��' => '뼻',
  '��' => '뼼',
  '��' => '뼽',
  '��' => '뼾',
  '��' => '뼿',
  '��' => '뽂',
  '��' => '뽃',
  '��' => '뽅',
  '��' => '뽆',
  '��' => '뽇',
  '��' => '뽉',
  '��' => '뽊',
  '��' => '뽋',
  '��' => '뽌',
  '��' => '뽍',
  '��' => '뽎',
  '��' => '뽏',
  '��' => '뽒',
  '��' => '뽓',
  '��' => '뽔',
  '��' => '뽖',
  '��' => '뽗',
  '��' => '뽘',
  '��' => '뽙',
  '��' => '뽚',
  '��' => '뽛',
  '��' => '뽜',
  '��' => '뽝',
  '��' => '뽞',
  '��' => '뽟',
  '��' => '뽠',
  '��' => '뽡',
  '��' => '뽢',
  '��' => '뽣',
  '��' => '뽤',
  '��' => '뽥',
  '��' => '뽦',
  '��' => '뽧',
  '��' => '뽨',
  '��' => '뽩',
  '��' => '뽪',
  '��' => '뽫',
  '��' => '뽬',
  '��' => '뽭',
  '��' => '뽮',
  '��' => '뽯',
  '��' => '뽰',
  '��' => '뽱',
  '��' => '뽲',
  '��' => '뽳',
  '��' => '뽴',
  '��' => '뽵',
  '��' => '뽶',
  '��' => '뽷',
  '��' => '뽸',
  '��' => '뽹',
  '��' => '뽺',
  '��' => '뽻',
  '��' => '뽼',
  '��' => '뽽',
  '��' => '뽾',
  '��' => '뽿',
  '��' => '뾀',
  '��' => '뾁',
  '��' => '뾂',
  '�A' => '뾃',
  '�B' => '뾄',
  '�C' => '뾅',
  '�D' => '뾆',
  '�E' => '뾇',
  '�F' => '뾈',
  '�G' => '뾉',
  '�H' => '뾊',
  '�I' => '뾋',
  '�J' => '뾌',
  '�K' => '뾍',
  '�L' => '뾎',
  '�M' => '뾏',
  '�N' => '뾐',
  '�O' => '뾑',
  '�P' => '뾒',
  '�Q' => '뾓',
  '�R' => '뾕',
  '�S' => '뾖',
  '�T' => '뾗',
  '�U' => '뾘',
  '�V' => '뾙',
  '�W' => '뾚',
  '�X' => '뾛',
  '�Y' => '뾜',
  '�Z' => '뾝',
  '�a' => '뾞',
  '�b' => '뾟',
  '�c' => '뾠',
  '�d' => '뾡',
  '�e' => '뾢',
  '�f' => '뾣',
  '�g' => '뾤',
  '�h' => '뾥',
  '�i' => '뾦',
  '�j' => '뾧',
  '�k' => '뾨',
  '�l' => '뾩',
  '�m' => '뾪',
  '�n' => '뾫',
  '�o' => '뾬',
  '�p' => '뾭',
  '�q' => '뾮',
  '�r' => '뾯',
  '�s' => '뾱',
  '�t' => '뾲',
  '�u' => '뾳',
  '�v' => '뾴',
  '�w' => '뾵',
  '�x' => '뾶',
  '�y' => '뾷',
  '�z' => '뾸',
  '��' => '뾹',
  '��' => '뾺',
  '��' => '뾻',
  '��' => '뾼',
  '��' => '뾽',
  '��' => '뾾',
  '��' => '뾿',
  '��' => '뿀',
  '��' => '뿁',
  '��' => '뿂',
  '��' => '뿃',
  '��' => '뿄',
  '��' => '뿆',
  '��' => '뿇',
  '��' => '뿈',
  '��' => '뿉',
  '��' => '뿊',
  '��' => '뿋',
  '��' => '뿎',
  '��' => '뿏',
  '��' => '뿑',
  '��' => '뿒',
  '��' => '뿓',
  '��' => '뿕',
  '��' => '뿖',
  '��' => '뿗',
  '��' => '뿘',
  '��' => '뿙',
  '��' => '뿚',
  '��' => '뿛',
  '��' => '뿝',
  '��' => '뿞',
  '��' => '뿠',
  '��' => '뿢',
  '��' => '뿣',
  '��' => '뿤',
  '��' => '뿥',
  '��' => '뿦',
  '��' => '뿧',
  '��' => '뿨',
  '��' => '뿩',
  '��' => '뿪',
  '��' => '뿫',
  '��' => '뿬',
  '��' => '뿭',
  '��' => '뿮',
  '��' => '뿯',
  '��' => '뿰',
  '��' => '뿱',
  '��' => '뿲',
  '��' => '뿳',
  '��' => '뿴',
  '��' => '뿵',
  '��' => '뿶',
  '��' => '뿷',
  '��' => '뿸',
  '��' => '뿹',
  '��' => '뿺',
  '��' => '뿻',
  '��' => '뿼',
  '��' => '뿽',
  '��' => '뿾',
  '��' => '뿿',
  '��' => '쀀',
  '��' => '쀁',
  '��' => '쀂',
  '��' => '쀃',
  '��' => '쀄',
  '��' => '쀅',
  '��' => '쀆',
  '��' => '쀇',
  '��' => '쀈',
  '��' => '쀉',
  '��' => '쀊',
  '��' => '쀋',
  '��' => '쀌',
  '��' => '쀍',
  '��' => '쀎',
  '��' => '쀏',
  '��' => '쀐',
  '��' => '쀑',
  '��' => '쀒',
  '��' => '쀓',
  '��' => '쀔',
  '��' => '쀕',
  '��' => '쀖',
  '��' => '쀗',
  '��' => '쀘',
  '��' => '쀙',
  '��' => '쀚',
  '��' => '쀛',
  '��' => '쀜',
  '��' => '쀝',
  '��' => '쀞',
  '��' => '쀟',
  '��' => '쀠',
  '��' => '쀡',
  '��' => '쀢',
  '��' => '쀣',
  '��' => '쀤',
  '��' => '쀥',
  '��' => '쀦',
  '��' => '쀧',
  '��' => '쀨',
  '��' => '쀩',
  '��' => '쀪',
  '��' => '쀫',
  '��' => '쀬',
  '��' => '쀭',
  '��' => '쀮',
  '��' => '쀯',
  '��' => '쀰',
  '��' => '쀱',
  '��' => '쀲',
  '��' => '쀳',
  '��' => '쀴',
  '��' => '쀵',
  '��' => '쀶',
  '��' => '쀷',
  '��' => '쀸',
  '��' => '쀹',
  '��' => '쀺',
  '��' => '쀻',
  '��' => '쀽',
  '��' => '쀾',
  '��' => '쀿',
  '�A' => '쁀',
  '�B' => '쁁',
  '�C' => '쁂',
  '�D' => '쁃',
  '�E' => '쁄',
  '�F' => '쁅',
  '�G' => '쁆',
  '�H' => '쁇',
  '�I' => '쁈',
  '�J' => '쁉',
  '�K' => '쁊',
  '�L' => '쁋',
  '�M' => '쁌',
  '�N' => '쁍',
  '�O' => '쁎',
  '�P' => '쁏',
  '�Q' => '쁐',
  '�R' => '쁒',
  '�S' => '쁓',
  '�T' => '쁔',
  '�U' => '쁕',
  '�V' => '쁖',
  '�W' => '쁗',
  '�X' => '쁙',
  '�Y' => '쁚',
  '�Z' => '쁛',
  '�a' => '쁝',
  '�b' => '쁞',
  '�c' => '쁟',
  '�d' => '쁡',
  '�e' => '쁢',
  '�f' => '쁣',
  '�g' => '쁤',
  '�h' => '쁥',
  '�i' => '쁦',
  '�j' => '쁧',
  '�k' => '쁪',
  '�l' => '쁫',
  '�m' => '쁬',
  '�n' => '쁭',
  '�o' => '쁮',
  '�p' => '쁯',
  '�q' => '쁰',
  '�r' => '쁱',
  '�s' => '쁲',
  '�t' => '쁳',
  '�u' => '쁴',
  '�v' => '쁵',
  '�w' => '쁶',
  '�x' => '쁷',
  '�y' => '쁸',
  '�z' => '쁹',
  '��' => '쁺',
  '��' => '쁻',
  '��' => '쁼',
  '��' => '쁽',
  '��' => '쁾',
  '��' => '쁿',
  '��' => '삀',
  '��' => '삁',
  '��' => '삂',
  '��' => '삃',
  '��' => '삄',
  '��' => '삅',
  '��' => '삆',
  '��' => '삇',
  '��' => '삈',
  '��' => '삉',
  '��' => '삊',
  '��' => '삋',
  '��' => '삌',
  '��' => '삍',
  '��' => '삎',
  '��' => '삏',
  '��' => '삒',
  '��' => '삓',
  '��' => '삕',
  '��' => '삖',
  '��' => '삗',
  '��' => '삙',
  '��' => '삚',
  '��' => '삛',
  '��' => '삜',
  '��' => '삝',
  '��' => '삞',
  '��' => '삟',
  '��' => '삢',
  '��' => '삤',
  '��' => '삦',
  '��' => '삧',
  '��' => '삨',
  '��' => '삩',
  '��' => '삪',
  '��' => '삫',
  '��' => '삮',
  '��' => '삱',
  '��' => '삲',
  '��' => '삷',
  '��' => '삸',
  '��' => '삹',
  '��' => '삺',
  '��' => '삻',
  '��' => '삾',
  '��' => '샂',
  '��' => '샃',
  '��' => '샄',
  '��' => '샆',
  '��' => '샇',
  '��' => '샊',
  '��' => '샋',
  '��' => '샍',
  '��' => '샎',
  '��' => '샏',
  '��' => '샑',
  '��' => '샒',
  '��' => '샓',
  '��' => '샔',
  '��' => '샕',
  '��' => '샖',
  '��' => '샗',
  '��' => '샚',
  '��' => '샞',
  '��' => '샟',
  '��' => '샠',
  '��' => '샡',
  '��' => '샢',
  '��' => '샣',
  '��' => '샦',
  '��' => '샧',
  '��' => '샩',
  '��' => '샪',
  '��' => '샫',
  '��' => '샭',
  '��' => '샮',
  '��' => '샯',
  '��' => '샰',
  '��' => '샱',
  '��' => '샲',
  '��' => '샳',
  '��' => '샶',
  '��' => '샸',
  '��' => '샺',
  '��' => '샻',
  '��' => '샼',
  '��' => '샽',
  '��' => '샾',
  '��' => '샿',
  '��' => '섁',
  '��' => '섂',
  '��' => '섃',
  '��' => '섅',
  '��' => '섆',
  '��' => '섇',
  '��' => '섉',
  '��' => '섊',
  '��' => '섋',
  '��' => '섌',
  '��' => '섍',
  '��' => '섎',
  '��' => '섏',
  '��' => '섑',
  '��' => '섒',
  '��' => '섓',
  '��' => '섔',
  '��' => '섖',
  '��' => '섗',
  '��' => '섘',
  '��' => '섙',
  '��' => '섚',
  '��' => '섛',
  '��' => '섡',
  '��' => '섢',
  '��' => '섥',
  '��' => '섨',
  '��' => '섩',
  '��' => '섪',
  '��' => '섫',
  '��' => '섮',
  '�A' => '섲',
  '�B' => '섳',
  '�C' => '섴',
  '�D' => '섵',
  '�E' => '섷',
  '�F' => '섺',
  '�G' => '섻',
  '�H' => '섽',
  '�I' => '섾',
  '�J' => '섿',
  '�K' => '셁',
  '�L' => '셂',
  '�M' => '셃',
  '�N' => '셄',
  '�O' => '셅',
  '�P' => '셆',
  '�Q' => '셇',
  '�R' => '셊',
  '�S' => '셎',
  '�T' => '셏',
  '�U' => '셐',
  '�V' => '셑',
  '�W' => '셒',
  '�X' => '셓',
  '�Y' => '셖',
  '�Z' => '셗',
  '�a' => '셙',
  '�b' => '셚',
  '�c' => '셛',
  '�d' => '셝',
  '�e' => '셞',
  '�f' => '셟',
  '�g' => '셠',
  '�h' => '셡',
  '�i' => '셢',
  '�j' => '셣',
  '�k' => '셦',
  '�l' => '셪',
  '�m' => '셫',
  '�n' => '셬',
  '�o' => '셭',
  '�p' => '셮',
  '�q' => '셯',
  '�r' => '셱',
  '�s' => '셲',
  '�t' => '셳',
  '�u' => '셵',
  '�v' => '셶',
  '�w' => '셷',
  '�x' => '셹',
  '�y' => '셺',
  '�z' => '셻',
  '��' => '셼',
  '��' => '셽',
  '��' => '셾',
  '��' => '셿',
  '��' => '솀',
  '��' => '솁',
  '��' => '솂',
  '��' => '솃',
  '��' => '솄',
  '��' => '솆',
  '��' => '솇',
  '��' => '솈',
  '��' => '솉',
  '��' => '솊',
  '��' => '솋',
  '��' => '솏',
  '��' => '솑',
  '��' => '솒',
  '��' => '솓',
  '��' => '솕',
  '��' => '솗',
  '��' => '솘',
  '��' => '솙',
  '��' => '솚',
  '��' => '솛',
  '��' => '솞',
  '��' => '솠',
  '��' => '솢',
  '��' => '솣',
  '��' => '솤',
  '��' => '솦',
  '��' => '솧',
  '��' => '솪',
  '��' => '솫',
  '��' => '솭',
  '��' => '솮',
  '��' => '솯',
  '��' => '솱',
  '��' => '솲',
  '��' => '솳',
  '��' => '솴',
  '��' => '솵',
  '��' => '솶',
  '��' => '솷',
  '��' => '솸',
  '��' => '솹',
  '��' => '솺',
  '��' => '솻',
  '��' => '솼',
  '��' => '솾',
  '��' => '솿',
  '��' => '쇀',
  '��' => '쇁',
  '��' => '쇂',
  '��' => '쇃',
  '��' => '쇅',
  '��' => '쇆',
  '��' => '쇇',
  '��' => '쇉',
  '��' => '쇊',
  '��' => '쇋',
  '��' => '쇍',
  '��' => '쇎',
  '��' => '쇏',
  '��' => '쇐',
  '��' => '쇑',
  '��' => '쇒',
  '��' => '쇓',
  '��' => '쇕',
  '��' => '쇖',
  '��' => '쇙',
  '��' => '쇚',
  '��' => '쇛',
  '��' => '쇜',
  '��' => '쇝',
  '��' => '쇞',
  '��' => '쇟',
  '��' => '쇡',
  '��' => '쇢',
  '��' => '쇣',
  '��' => '쇥',
  '��' => '쇦',
  '��' => '쇧',
  '��' => '쇩',
  '��' => '쇪',
  '��' => '쇫',
  '��' => '쇬',
  '��' => '쇭',
  '��' => '쇮',
  '��' => '쇯',
  '��' => '쇲',
  '��' => '쇴',
  '��' => '쇵',
  '��' => '쇶',
  '��' => '쇷',
  '��' => '쇸',
  '��' => '쇹',
  '��' => '쇺',
  '��' => '쇻',
  '��' => '쇾',
  '��' => '쇿',
  '��' => '숁',
  '��' => '숂',
  '��' => '숃',
  '��' => '숅',
  '��' => '숆',
  '��' => '숇',
  '��' => '숈',
  '��' => '숉',
  '��' => '숊',
  '��' => '숋',
  '��' => '숎',
  '��' => '숐',
  '��' => '숒',
  '��' => '숓',
  '��' => '숔',
  '��' => '숕',
  '��' => '숖',
  '��' => '숗',
  '��' => '숚',
  '��' => '숛',
  '��' => '숝',
  '��' => '숞',
  '��' => '숡',
  '��' => '숢',
  '��' => '숣',
  '�A' => '숤',
  '�B' => '숥',
  '�C' => '숦',
  '�D' => '숧',
  '�E' => '숪',
  '�F' => '숬',
  '�G' => '숮',
  '�H' => '숰',
  '�I' => '숳',
  '�J' => '숵',
  '�K' => '숶',
  '�L' => '숷',
  '�M' => '숸',
  '�N' => '숹',
  '�O' => '숺',
  '�P' => '숻',
  '�Q' => '숼',
  '�R' => '숽',
  '�S' => '숾',
  '�T' => '숿',
  '�U' => '쉀',
  '�V' => '쉁',
  '�W' => '쉂',
  '�X' => '쉃',
  '�Y' => '쉄',
  '�Z' => '쉅',
  '�a' => '쉆',
  '�b' => '쉇',
  '�c' => '쉉',
  '�d' => '쉊',
  '�e' => '쉋',
  '�f' => '쉌',
  '�g' => '쉍',
  '�h' => '쉎',
  '�i' => '쉏',
  '�j' => '쉒',
  '�k' => '쉓',
  '�l' => '쉕',
  '�m' => '쉖',
  '�n' => '쉗',
  '�o' => '쉙',
  '�p' => '쉚',
  '�q' => '쉛',
  '�r' => '쉜',
  '�s' => '쉝',
  '�t' => '쉞',
  '�u' => '쉟',
  '�v' => '쉡',
  '�w' => '쉢',
  '�x' => '쉣',
  '�y' => '쉤',
  '�z' => '쉦',
  '��' => '쉧',
  '��' => '쉨',
  '��' => '쉩',
  '��' => '쉪',
  '��' => '쉫',
  '��' => '쉮',
  '��' => '쉯',
  '��' => '쉱',
  '��' => '쉲',
  '��' => '쉳',
  '��' => '쉵',
  '��' => '쉶',
  '��' => '쉷',
  '��' => '쉸',
  '��' => '쉹',
  '��' => '쉺',
  '��' => '쉻',
  '��' => '쉾',
  '��' => '슀',
  '��' => '슂',
  '��' => '슃',
  '��' => '슄',
  '��' => '슅',
  '��' => '슆',
  '��' => '슇',
  '��' => '슊',
  '��' => '슋',
  '��' => '슌',
  '��' => '슍',
  '��' => '슎',
  '��' => '슏',
  '��' => '슑',
  '��' => '슒',
  '��' => '슓',
  '��' => '슔',
  '��' => '슕',
  '��' => '슖',
  '��' => '슗',
  '��' => '슙',
  '��' => '슚',
  '��' => '슜',
  '��' => '슞',
  '��' => '슟',
  '��' => '슠',
  '��' => '슡',
  '��' => '슢',
  '��' => '슣',
  '��' => '슦',
  '��' => '슧',
  '��' => '슩',
  '��' => '슪',
  '��' => '슫',
  '��' => '슮',
  '��' => '슯',
  '��' => '슰',
  '��' => '슱',
  '��' => '슲',
  '��' => '슳',
  '��' => '슶',
  '��' => '슸',
  '��' => '슺',
  '��' => '슻',
  '��' => '슼',
  '��' => '슽',
  '��' => '슾',
  '��' => '슿',
  '��' => '싀',
  '��' => '싁',
  '��' => '싂',
  '��' => '싃',
  '��' => '싄',
  '��' => '싅',
  '��' => '싆',
  '��' => '싇',
  '��' => '싈',
  '��' => '싉',
  '��' => '싊',
  '��' => '싋',
  '��' => '싌',
  '��' => '싍',
  '��' => '싎',
  '��' => '싏',
  '��' => '싐',
  '��' => '싑',
  '��' => '싒',
  '��' => '싓',
  '��' => '싔',
  '��' => '싕',
  '��' => '싖',
  '��' => '싗',
  '��' => '싘',
  '��' => '싙',
  '��' => '싚',
  '��' => '싛',
  '��' => '싞',
  '��' => '싟',
  '��' => '싡',
  '��' => '싢',
  '��' => '싥',
  '��' => '싦',
  '��' => '싧',
  '��' => '싨',
  '��' => '싩',
  '��' => '싪',
  '��' => '싮',
  '��' => '싰',
  '��' => '싲',
  '��' => '싳',
  '��' => '싴',
  '��' => '싵',
  '��' => '싷',
  '��' => '싺',
  '��' => '싽',
  '��' => '싾',
  '��' => '싿',
  '��' => '쌁',
  '��' => '쌂',
  '��' => '쌃',
  '��' => '쌄',
  '��' => '쌅',
  '��' => '쌆',
  '��' => '쌇',
  '��' => '쌊',
  '��' => '쌋',
  '��' => '쌎',
  '��' => '쌏',
  '�A' => '쌐',
  '�B' => '쌑',
  '�C' => '쌒',
  '�D' => '쌖',
  '�E' => '쌗',
  '�F' => '쌙',
  '�G' => '쌚',
  '�H' => '쌛',
  '�I' => '쌝',
  '�J' => '쌞',
  '�K' => '쌟',
  '�L' => '쌠',
  '�M' => '쌡',
  '�N' => '쌢',
  '�O' => '쌣',
  '�P' => '쌦',
  '�Q' => '쌧',
  '�R' => '쌪',
  '�S' => '쌫',
  '�T' => '쌬',
  '�U' => '쌭',
  '�V' => '쌮',
  '�W' => '쌯',
  '�X' => '쌰',
  '�Y' => '쌱',
  '�Z' => '쌲',
  '�a' => '쌳',
  '�b' => '쌴',
  '�c' => '쌵',
  '�d' => '쌶',
  '�e' => '쌷',
  '�f' => '쌸',
  '�g' => '쌹',
  '�h' => '쌺',
  '�i' => '쌻',
  '�j' => '쌼',
  '�k' => '쌽',
  '�l' => '쌾',
  '�m' => '쌿',
  '�n' => '썀',
  '�o' => '썁',
  '�p' => '썂',
  '�q' => '썃',
  '�r' => '썄',
  '�s' => '썆',
  '�t' => '썇',
  '�u' => '썈',
  '�v' => '썉',
  '�w' => '썊',
  '�x' => '썋',
  '�y' => '썌',
  '�z' => '썍',
  '��' => '썎',
  '��' => '썏',
  '��' => '썐',
  '��' => '썑',
  '��' => '썒',
  '��' => '썓',
  '��' => '썔',
  '��' => '썕',
  '��' => '썖',
  '��' => '썗',
  '��' => '썘',
  '��' => '썙',
  '��' => '썚',
  '��' => '썛',
  '��' => '썜',
  '��' => '썝',
  '��' => '썞',
  '��' => '썟',
  '��' => '썠',
  '��' => '썡',
  '��' => '썢',
  '��' => '썣',
  '��' => '썤',
  '��' => '썥',
  '��' => '썦',
  '��' => '썧',
  '��' => '썪',
  '��' => '썫',
  '��' => '썭',
  '��' => '썮',
  '��' => '썯',
  '��' => '썱',
  '��' => '썳',
  '��' => '썴',
  '��' => '썵',
  '��' => '썶',
  '��' => '썷',
  '��' => '썺',
  '��' => '썻',
  '��' => '썾',
  '��' => '썿',
  '��' => '쎀',
  '��' => '쎁',
  '��' => '쎂',
  '��' => '쎃',
  '��' => '쎅',
  '��' => '쎆',
  '��' => '쎇',
  '��' => '쎉',
  '��' => '쎊',
  '��' => '쎋',
  '��' => '쎍',
  '��' => '쎎',
  '��' => '쎏',
  '��' => '쎐',
  '��' => '쎑',
  '��' => '쎒',
  '��' => '쎓',
  '��' => '쎔',
  '��' => '쎕',
  '��' => '쎖',
  '��' => '쎗',
  '��' => '쎘',
  '��' => '쎙',
  '��' => '쎚',
  '��' => '쎛',
  '��' => '쎜',
  '��' => '쎝',
  '��' => '쎞',
  '��' => '쎟',
  '��' => '쎠',
  '��' => '쎡',
  '��' => '쎢',
  '��' => '쎣',
  '��' => '쎤',
  '��' => '쎥',
  '��' => '쎦',
  '��' => '쎧',
  '��' => '쎨',
  '��' => '쎩',
  '��' => '쎪',
  '��' => '쎫',
  '��' => '쎬',
  '��' => '쎭',
  '��' => '쎮',
  '��' => '쎯',
  '��' => '쎰',
  '��' => '쎱',
  '��' => '쎲',
  '��' => '쎳',
  '��' => '쎴',
  '��' => '쎵',
  '��' => '쎶',
  '��' => '쎷',
  '��' => '쎸',
  '��' => '쎹',
  '��' => '쎺',
  '��' => '쎻',
  '��' => '쎼',
  '��' => '쎽',
  '��' => '쎾',
  '��' => '쎿',
  '��' => '쏁',
  '��' => '쏂',
  '��' => '쏃',
  '��' => '쏄',
  '��' => '쏅',
  '��' => '쏆',
  '��' => '쏇',
  '��' => '쏈',
  '��' => '쏉',
  '��' => '쏊',
  '��' => '쏋',
  '��' => '쏌',
  '��' => '쏍',
  '��' => '쏎',
  '��' => '쏏',
  '��' => '쏐',
  '��' => '쏑',
  '��' => '쏒',
  '��' => '쏓',
  '��' => '쏔',
  '��' => '쏕',
  '��' => '쏖',
  '��' => '쏗',
  '��' => '쏚',
  '�A' => '쏛',
  '�B' => '쏝',
  '�C' => '쏞',
  '�D' => '쏡',
  '�E' => '쏣',
  '�F' => '쏤',
  '�G' => '쏥',
  '�H' => '쏦',
  '�I' => '쏧',
  '�J' => '쏪',
  '�K' => '쏫',
  '�L' => '쏬',
  '�M' => '쏮',
  '�N' => '쏯',
  '�O' => '쏰',
  '�P' => '쏱',
  '�Q' => '쏲',
  '�R' => '쏳',
  '�S' => '쏶',
  '�T' => '쏷',
  '�U' => '쏹',
  '�V' => '쏺',
  '�W' => '쏻',
  '�X' => '쏼',
  '�Y' => '쏽',
  '�Z' => '쏾',
  '�a' => '쏿',
  '�b' => '쐀',
  '�c' => '쐁',
  '�d' => '쐂',
  '�e' => '쐃',
  '�f' => '쐄',
  '�g' => '쐅',
  '�h' => '쐆',
  '�i' => '쐇',
  '�j' => '쐉',
  '�k' => '쐊',
  '�l' => '쐋',
  '�m' => '쐌',
  '�n' => '쐍',
  '�o' => '쐎',
  '�p' => '쐏',
  '�q' => '쐑',
  '�r' => '쐒',
  '�s' => '쐓',
  '�t' => '쐔',
  '�u' => '쐕',
  '�v' => '쐖',
  '�w' => '쐗',
  '�x' => '쐘',
  '�y' => '쐙',
  '�z' => '쐚',
  '��' => '쐛',
  '��' => '쐜',
  '��' => '쐝',
  '��' => '쐞',
  '��' => '쐟',
  '��' => '쐠',
  '��' => '쐡',
  '��' => '쐢',
  '��' => '쐣',
  '��' => '쐥',
  '��' => '쐦',
  '��' => '쐧',
  '��' => '쐨',
  '��' => '쐩',
  '��' => '쐪',
  '��' => '쐫',
  '��' => '쐭',
  '��' => '쐮',
  '��' => '쐯',
  '��' => '쐱',
  '��' => '쐲',
  '��' => '쐳',
  '��' => '쐵',
  '��' => '쐶',
  '��' => '쐷',
  '��' => '쐸',
  '��' => '쐹',
  '��' => '쐺',
  '��' => '쐻',
  '��' => '쐾',
  '��' => '쐿',
  '��' => '쑀',
  '��' => '쑁',
  '��' => '쑂',
  '��' => '쑃',
  '��' => '쑄',
  '��' => '쑅',
  '��' => '쑆',
  '��' => '쑇',
  '��' => '쑉',
  '��' => '쑊',
  '��' => '쑋',
  '��' => '쑌',
  '��' => '쑍',
  '��' => '쑎',
  '��' => '쑏',
  '��' => '쑐',
  '��' => '쑑',
  '��' => '쑒',
  '��' => '쑓',
  '��' => '쑔',
  '��' => '쑕',
  '��' => '쑖',
  '��' => '쑗',
  '��' => '쑘',
  '��' => '쑙',
  '��' => '쑚',
  '��' => '쑛',
  '��' => '쑜',
  '��' => '쑝',
  '��' => '쑞',
  '��' => '쑟',
  '��' => '쑠',
  '��' => '쑡',
  '��' => '쑢',
  '��' => '쑣',
  '��' => '쑦',
  '��' => '쑧',
  '��' => '쑩',
  '��' => '쑪',
  '��' => '쑫',
  '��' => '쑭',
  '��' => '쑮',
  '��' => '쑯',
  '��' => '쑰',
  '��' => '쑱',
  '��' => '쑲',
  '��' => '쑳',
  '��' => '쑶',
  '��' => '쑷',
  '��' => '쑸',
  '��' => '쑺',
  '��' => '쑻',
  '��' => '쑼',
  '��' => '쑽',
  '��' => '쑾',
  '��' => '쑿',
  '��' => '쒁',
  '��' => '쒂',
  '��' => '쒃',
  '��' => '쒄',
  '��' => '쒅',
  '��' => '쒆',
  '��' => '쒇',
  '��' => '쒈',
  '��' => '쒉',
  '��' => '쒊',
  '��' => '쒋',
  '��' => '쒌',
  '��' => '쒍',
  '��' => '쒎',
  '��' => '쒏',
  '��' => '쒐',
  '��' => '쒑',
  '��' => '쒒',
  '��' => '쒓',
  '��' => '쒕',
  '��' => '쒖',
  '��' => '쒗',
  '��' => '쒘',
  '��' => '쒙',
  '��' => '쒚',
  '��' => '쒛',
  '��' => '쒝',
  '��' => '쒞',
  '��' => '쒟',
  '��' => '쒠',
  '��' => '쒡',
  '��' => '쒢',
  '��' => '쒣',
  '��' => '쒤',
  '��' => '쒥',
  '��' => '쒦',
  '��' => '쒧',
  '��' => '쒨',
  '��' => '쒩',
  '�A' => '쒪',
  '�B' => '쒫',
  '�C' => '쒬',
  '�D' => '쒭',
  '�E' => '쒮',
  '�F' => '쒯',
  '�G' => '쒰',
  '�H' => '쒱',
  '�I' => '쒲',
  '�J' => '쒳',
  '�K' => '쒴',
  '�L' => '쒵',
  '�M' => '쒶',
  '�N' => '쒷',
  '�O' => '쒹',
  '�P' => '쒺',
  '�Q' => '쒻',
  '�R' => '쒽',
  '�S' => '쒾',
  '�T' => '쒿',
  '�U' => '쓀',
  '�V' => '쓁',
  '�W' => '쓂',
  '�X' => '쓃',
  '�Y' => '쓄',
  '�Z' => '쓅',
  '�a' => '쓆',
  '�b' => '쓇',
  '�c' => '쓈',
  '�d' => '쓉',
  '�e' => '쓊',
  '�f' => '쓋',
  '�g' => '쓌',
  '�h' => '쓍',
  '�i' => '쓎',
  '�j' => '쓏',
  '�k' => '쓐',
  '�l' => '쓑',
  '�m' => '쓒',
  '�n' => '쓓',
  '�o' => '쓔',
  '�p' => '쓕',
  '�q' => '쓖',
  '�r' => '쓗',
  '�s' => '쓘',
  '�t' => '쓙',
  '�u' => '쓚',
  '�v' => '쓛',
  '�w' => '쓜',
  '�x' => '쓝',
  '�y' => '쓞',
  '�z' => '쓟',
  '��' => '쓠',
  '��' => '쓡',
  '��' => '쓢',
  '��' => '쓣',
  '��' => '쓤',
  '��' => '쓥',
  '��' => '쓦',
  '��' => '쓧',
  '��' => '쓨',
  '��' => '쓪',
  '��' => '쓫',
  '��' => '쓬',
  '��' => '쓭',
  '��' => '쓮',
  '��' => '쓯',
  '��' => '쓲',
  '��' => '쓳',
  '��' => '쓵',
  '��' => '쓶',
  '��' => '쓷',
  '��' => '쓹',
  '��' => '쓻',
  '��' => '쓼',
  '��' => '쓽',
  '��' => '쓾',
  '��' => '씂',
  '��' => '씃',
  '��' => '씄',
  '��' => '씅',
  '��' => '씆',
  '��' => '씇',
  '��' => '씈',
  '��' => '씉',
  '��' => '씊',
  '��' => '씋',
  '��' => '씍',
  '��' => '씎',
  '��' => '씏',
  '��' => '씑',
  '��' => '씒',
  '��' => '씓',
  '��' => '씕',
  '��' => '씖',
  '��' => '씗',
  '��' => '씘',
  '��' => '씙',
  '��' => '씚',
  '��' => '씛',
  '��' => '씝',
  '��' => '씞',
  '��' => '씟',
  '��' => '씠',
  '��' => '씡',
  '��' => '씢',
  '��' => '씣',
  '��' => '씤',
  '��' => '씥',
  '��' => '씦',
  '��' => '씧',
  '��' => '씪',
  '��' => '씫',
  '��' => '씭',
  '��' => '씮',
  '��' => '씯',
  '��' => '씱',
  '��' => '씲',
  '��' => '씳',
  '��' => '씴',
  '��' => '씵',
  '��' => '씶',
  '��' => '씷',
  '��' => '씺',
  '��' => '씼',
  '��' => '씾',
  '��' => '씿',
  '��' => '앀',
  '��' => '앁',
  '��' => '앂',
  '��' => '앃',
  '��' => '앆',
  '��' => '앇',
  '��' => '앋',
  '��' => '앏',
  '��' => '앐',
  '��' => '앑',
  '��' => '앒',
  '��' => '앖',
  '��' => '앚',
  '��' => '앛',
  '��' => '앜',
  '��' => '앟',
  '��' => '앢',
  '��' => '앣',
  '��' => '앥',
  '��' => '앦',
  '��' => '앧',
  '��' => '앩',
  '��' => '앪',
  '��' => '앫',
  '��' => '앬',
  '��' => '앭',
  '��' => '앮',
  '��' => '앯',
  '��' => '앲',
  '��' => '앶',
  '��' => '앷',
  '��' => '앸',
  '��' => '앹',
  '��' => '앺',
  '��' => '앻',
  '��' => '앾',
  '��' => '앿',
  '��' => '얁',
  '��' => '얂',
  '��' => '얃',
  '��' => '얅',
  '��' => '얆',
  '��' => '얈',
  '��' => '얉',
  '��' => '얊',
  '��' => '얋',
  '��' => '얎',
  '��' => '얐',
  '��' => '얒',
  '��' => '얓',
  '��' => '얔',
  '�A' => '얖',
  '�B' => '얙',
  '�C' => '얚',
  '�D' => '얛',
  '�E' => '얝',
  '�F' => '얞',
  '�G' => '얟',
  '�H' => '얡',
  '�I' => '얢',
  '�J' => '얣',
  '�K' => '얤',
  '�L' => '얥',
  '�M' => '얦',
  '�N' => '얧',
  '�O' => '얨',
  '�P' => '얪',
  '�Q' => '얫',
  '�R' => '얬',
  '�S' => '얭',
  '�T' => '얮',
  '�U' => '얯',
  '�V' => '얰',
  '�W' => '얱',
  '�X' => '얲',
  '�Y' => '얳',
  '�Z' => '얶',
  '�a' => '얷',
  '�b' => '얺',
  '�c' => '얿',
  '�d' => '엀',
  '�e' => '엁',
  '�f' => '엂',
  '�g' => '엃',
  '�h' => '엋',
  '�i' => '엍',
  '�j' => '엏',
  '�k' => '엒',
  '�l' => '엓',
  '�m' => '엕',
  '�n' => '엖',
  '�o' => '엗',
  '�p' => '엙',
  '�q' => '엚',
  '�r' => '엛',
  '�s' => '엜',
  '�t' => '엝',
  '�u' => '엞',
  '�v' => '엟',
  '�w' => '엢',
  '�x' => '엤',
  '�y' => '엦',
  '�z' => '엧',
  '��' => '엨',
  '��' => '엩',
  '��' => '엪',
  '��' => '엫',
  '��' => '엯',
  '��' => '엱',
  '��' => '엲',
  '��' => '엳',
  '��' => '엵',
  '��' => '엸',
  '��' => '엹',
  '��' => '엺',
  '��' => '엻',
  '��' => '옂',
  '��' => '옃',
  '��' => '옄',
  '��' => '옉',
  '��' => '옊',
  '��' => '옋',
  '��' => '옍',
  '��' => '옎',
  '��' => '옏',
  '��' => '옑',
  '��' => '옒',
  '��' => '옓',
  '��' => '옔',
  '��' => '옕',
  '��' => '옖',
  '��' => '옗',
  '��' => '옚',
  '��' => '옝',
  '��' => '옞',
  '��' => '옟',
  '��' => '옠',
  '��' => '옡',
  '��' => '옢',
  '��' => '옣',
  '��' => '옦',
  '��' => '옧',
  '��' => '옩',
  '��' => '옪',
  '��' => '옫',
  '��' => '옯',
  '��' => '옱',
  '��' => '옲',
  '��' => '옶',
  '��' => '옸',
  '��' => '옺',
  '��' => '옼',
  '��' => '옽',
  '��' => '옾',
  '��' => '옿',
  '��' => '왂',
  '��' => '왃',
  '��' => '왅',
  '��' => '왆',
  '��' => '왇',
  '��' => '왉',
  '��' => '왊',
  '��' => '왋',
  '��' => '왌',
  '��' => '왍',
  '��' => '왎',
  '��' => '왏',
  '��' => '왒',
  '��' => '왖',
  '��' => '왗',
  '��' => '왘',
  '��' => '왙',
  '��' => '왚',
  '��' => '왛',
  '��' => '왞',
  '��' => '왟',
  '��' => '왡',
  '��' => '왢',
  '��' => '왣',
  '��' => '왤',
  '��' => '왥',
  '��' => '왦',
  '��' => '왧',
  '��' => '왨',
  '��' => '왩',
  '��' => '왪',
  '��' => '왫',
  '��' => '왭',
  '��' => '왮',
  '��' => '왰',
  '��' => '왲',
  '��' => '왳',
  '��' => '왴',
  '��' => '왵',
  '��' => '왶',
  '��' => '왷',
  '��' => '왺',
  '��' => '왻',
  '��' => '왽',
  '��' => '왾',
  '��' => '왿',
  '��' => '욁',
  '��' => '욂',
  '��' => '욃',
  '��' => '욄',
  '��' => '욅',
  '��' => '욆',
  '��' => '욇',
  '��' => '욊',
  '��' => '욌',
  '��' => '욎',
  '��' => '욏',
  '��' => '욐',
  '��' => '욑',
  '��' => '욒',
  '��' => '욓',
  '��' => '욖',
  '��' => '욗',
  '��' => '욙',
  '��' => '욚',
  '��' => '욛',
  '��' => '욝',
  '��' => '욞',
  '��' => '욟',
  '��' => '욠',
  '��' => '욡',
  '��' => '욢',
  '��' => '욣',
  '��' => '욦',
  '�A' => '욨',
  '�B' => '욪',
  '�C' => '욫',
  '�D' => '욬',
  '�E' => '욭',
  '�F' => '욮',
  '�G' => '욯',
  '�H' => '욲',
  '�I' => '욳',
  '�J' => '욵',
  '�K' => '욶',
  '�L' => '욷',
  '�M' => '욻',
  '�N' => '욼',
  '�O' => '욽',
  '�P' => '욾',
  '�Q' => '욿',
  '�R' => '웂',
  '�S' => '웄',
  '�T' => '웆',
  '�U' => '웇',
  '�V' => '웈',
  '�W' => '웉',
  '�X' => '웊',
  '�Y' => '웋',
  '�Z' => '웎',
  '�a' => '웏',
  '�b' => '웑',
  '�c' => '웒',
  '�d' => '웓',
  '�e' => '웕',
  '�f' => '웖',
  '�g' => '웗',
  '�h' => '웘',
  '�i' => '웙',
  '�j' => '웚',
  '�k' => '웛',
  '�l' => '웞',
  '�m' => '웟',
  '�n' => '웢',
  '�o' => '웣',
  '�p' => '웤',
  '�q' => '웥',
  '�r' => '웦',
  '�s' => '웧',
  '�t' => '웪',
  '�u' => '웫',
  '�v' => '웭',
  '�w' => '웮',
  '�x' => '웯',
  '�y' => '웱',
  '�z' => '웲',
  '��' => '웳',
  '��' => '웴',
  '��' => '웵',
  '��' => '웶',
  '��' => '웷',
  '��' => '웺',
  '��' => '웻',
  '��' => '웼',
  '��' => '웾',
  '��' => '웿',
  '��' => '윀',
  '��' => '윁',
  '��' => '윂',
  '��' => '윃',
  '��' => '윆',
  '��' => '윇',
  '��' => '윉',
  '��' => '윊',
  '��' => '윋',
  '��' => '윍',
  '��' => '윎',
  '��' => '윏',
  '��' => '윐',
  '��' => '윑',
  '��' => '윒',
  '��' => '윓',
  '��' => '윖',
  '��' => '윘',
  '��' => '윚',
  '��' => '윛',
  '��' => '윜',
  '��' => '윝',
  '��' => '윞',
  '��' => '윟',
  '��' => '윢',
  '��' => '윣',
  '��' => '윥',
  '��' => '윦',
  '��' => '윧',
  '��' => '윩',
  '��' => '윪',
  '��' => '윫',
  '��' => '윬',
  '��' => '윭',
  '��' => '윮',
  '��' => '윯',
  '��' => '윲',
  '��' => '윴',
  '��' => '윶',
  '��' => '윸',
  '��' => '윹',
  '��' => '윺',
  '��' => '윻',
  '��' => '윾',
  '��' => '윿',
  '��' => '읁',
  '��' => '읂',
  '��' => '읃',
  '��' => '읅',
  '��' => '읆',
  '��' => '읇',
  '��' => '읈',
  '��' => '읉',
  '��' => '읋',
  '��' => '읎',
  '��' => '읐',
  '��' => '읙',
  '��' => '읚',
  '��' => '읛',
  '��' => '읝',
  '��' => '읞',
  '��' => '읟',
  '��' => '읡',
  '��' => '읢',
  '��' => '읣',
  '��' => '읤',
  '��' => '읥',
  '��' => '읦',
  '��' => '읧',
  '��' => '읩',
  '��' => '읪',
  '��' => '읬',
  '��' => '읭',
  '��' => '읮',
  '��' => '읯',
  '��' => '읰',
  '��' => '읱',
  '��' => '읲',
  '��' => '읳',
  '��' => '읶',
  '��' => '읷',
  '��' => '읹',
  '��' => '읺',
  '��' => '읻',
  '��' => '읿',
  '��' => '잀',
  '��' => '잁',
  '��' => '잂',
  '��' => '잆',
  '��' => '잋',
  '��' => '잌',
  '��' => '잍',
  '��' => '잏',
  '��' => '잒',
  '��' => '잓',
  '��' => '잕',
  '��' => '잙',
  '��' => '잛',
  '��' => '잜',
  '��' => '잝',
  '��' => '잞',
  '��' => '잟',
  '��' => '잢',
  '��' => '잧',
  '��' => '잨',
  '��' => '잩',
  '��' => '잪',
  '��' => '잫',
  '��' => '잮',
  '��' => '잯',
  '��' => '잱',
  '��' => '잲',
  '��' => '잳',
  '��' => '잵',
  '��' => '잶',
  '��' => '잷',
  '�A' => '잸',
  '�B' => '잹',
  '�C' => '잺',
  '�D' => '잻',
  '�E' => '잾',
  '�F' => '쟂',
  '�G' => '쟃',
  '�H' => '쟄',
  '�I' => '쟅',
  '�J' => '쟆',
  '�K' => '쟇',
  '�L' => '쟊',
  '�M' => '쟋',
  '�N' => '쟍',
  '�O' => '쟏',
  '�P' => '쟑',
  '�Q' => '쟒',
  '�R' => '쟓',
  '�S' => '쟔',
  '�T' => '쟕',
  '�U' => '쟖',
  '�V' => '쟗',
  '�W' => '쟙',
  '�X' => '쟚',
  '�Y' => '쟛',
  '�Z' => '쟜',
  '�a' => '쟞',
  '�b' => '쟟',
  '�c' => '쟠',
  '�d' => '쟡',
  '�e' => '쟢',
  '�f' => '쟣',
  '�g' => '쟥',
  '�h' => '쟦',
  '�i' => '쟧',
  '�j' => '쟩',
  '�k' => '쟪',
  '�l' => '쟫',
  '�m' => '쟭',
  '�n' => '쟮',
  '�o' => '쟯',
  '�p' => '쟰',
  '�q' => '쟱',
  '�r' => '쟲',
  '�s' => '쟳',
  '�t' => '쟴',
  '�u' => '쟵',
  '�v' => '쟶',
  '�w' => '쟷',
  '�x' => '쟸',
  '�y' => '쟹',
  '�z' => '쟺',
  '��' => '쟻',
  '��' => '쟼',
  '��' => '쟽',
  '��' => '쟾',
  '��' => '쟿',
  '��' => '젂',
  '��' => '젃',
  '��' => '젅',
  '��' => '젆',
  '��' => '젇',
  '��' => '젉',
  '��' => '젋',
  '��' => '젌',
  '��' => '젍',
  '��' => '젎',
  '��' => '젏',
  '��' => '젒',
  '��' => '젔',
  '��' => '젗',
  '��' => '젘',
  '��' => '젙',
  '��' => '젚',
  '��' => '젛',
  '��' => '젞',
  '��' => '젟',
  '��' => '젡',
  '��' => '젢',
  '��' => '젣',
  '��' => '젥',
  '��' => '젦',
  '��' => '젧',
  '��' => '젨',
  '��' => '젩',
  '��' => '젪',
  '��' => '젫',
  '��' => '젮',
  '��' => '젰',
  '��' => '젲',
  '��' => '젳',
  '��' => '젴',
  '��' => '젵',
  '��' => '젶',
  '��' => '젷',
  '��' => '젹',
  '��' => '젺',
  '��' => '젻',
  '��' => '젽',
  '��' => '젾',
  '��' => '젿',
  '��' => '졁',
  '��' => '졂',
  '��' => '졃',
  '��' => '졄',
  '��' => '졅',
  '��' => '졆',
  '��' => '졇',
  '��' => '졊',
  '��' => '졋',
  '��' => '졎',
  '��' => '졏',
  '��' => '졐',
  '��' => '졑',
  '��' => '졒',
  '��' => '졓',
  '��' => '졕',
  '��' => '졖',
  '��' => '졗',
  '��' => '졘',
  '��' => '졙',
  '��' => '졚',
  '��' => '졛',
  '��' => '졜',
  '��' => '졝',
  '��' => '졞',
  '��' => '졟',
  '��' => '졠',
  '��' => '졡',
  '��' => '졢',
  '��' => '졣',
  '��' => '졤',
  '��' => '졥',
  '��' => '졦',
  '��' => '졧',
  '��' => '졨',
  '��' => '졩',
  '��' => '졪',
  '��' => '졫',
  '��' => '졬',
  '��' => '졭',
  '��' => '졮',
  '��' => '졯',
  '��' => '졲',
  '��' => '졳',
  '��' => '졵',
  '��' => '졶',
  '��' => '졷',
  '��' => '졹',
  '��' => '졻',
  '��' => '졼',
  '��' => '졽',
  '��' => '졾',
  '��' => '졿',
  '��' => '좂',
  '��' => '좄',
  '��' => '좈',
  '��' => '좉',
  '��' => '좊',
  '��' => '좎',
  '��' => '좏',
  '��' => '좐',
  '��' => '좑',
  '��' => '좒',
  '��' => '좓',
  '��' => '좕',
  '��' => '좖',
  '��' => '좗',
  '��' => '좘',
  '��' => '좙',
  '��' => '좚',
  '��' => '좛',
  '��' => '좜',
  '��' => '좞',
  '��' => '좠',
  '��' => '좢',
  '��' => '좣',
  '��' => '좤',
  '�A' => '좥',
  '�B' => '좦',
  '�C' => '좧',
  '�D' => '좩',
  '�E' => '좪',
  '�F' => '좫',
  '�G' => '좬',
  '�H' => '좭',
  '�I' => '좮',
  '�J' => '좯',
  '�K' => '좰',
  '�L' => '좱',
  '�M' => '좲',
  '�N' => '좳',
  '�O' => '좴',
  '�P' => '좵',
  '�Q' => '좶',
  '�R' => '좷',
  '�S' => '좸',
  '�T' => '좹',
  '�U' => '좺',
  '�V' => '좻',
  '�W' => '좾',
  '�X' => '좿',
  '�Y' => '죀',
  '�Z' => '죁',
  '�a' => '죂',
  '�b' => '죃',
  '�c' => '죅',
  '�d' => '죆',
  '�e' => '죇',
  '�f' => '죉',
  '�g' => '죊',
  '�h' => '죋',
  '�i' => '죍',
  '�j' => '죎',
  '�k' => '죏',
  '�l' => '죐',
  '�m' => '죑',
  '�n' => '죒',
  '�o' => '죓',
  '�p' => '죖',
  '�q' => '죘',
  '�r' => '죚',
  '�s' => '죛',
  '�t' => '죜',
  '�u' => '죝',
  '�v' => '죞',
  '�w' => '죟',
  '�x' => '죢',
  '�y' => '죣',
  '�z' => '죥',
  '��' => '죦',
  '��' => '죧',
  '��' => '죨',
  '��' => '죩',
  '��' => '죪',
  '��' => '죫',
  '��' => '죬',
  '��' => '죭',
  '��' => '죮',
  '��' => '죯',
  '��' => '죰',
  '��' => '죱',
  '��' => '죲',
  '��' => '죳',
  '��' => '죴',
  '��' => '죶',
  '��' => '죷',
  '��' => '죸',
  '��' => '죹',
  '��' => '죺',
  '��' => '죻',
  '��' => '죾',
  '��' => '죿',
  '��' => '줁',
  '��' => '줂',
  '��' => '줃',
  '��' => '줇',
  '��' => '줈',
  '��' => '줉',
  '��' => '줊',
  '��' => '줋',
  '��' => '줎',
  '��' => '　',
  '��' => '、',
  '��' => '。',
  '��' => '·',
  '��' => '‥',
  '��' => '…',
  '��' => '¨',
  '��' => '〃',
  '��' => '­',
  '��' => '―',
  '��' => '∥',
  '��' => '＼',
  '��' => '∼',
  '��' => '‘',
  '��' => '’',
  '��' => '“',
  '��' => '”',
  '��' => '〔',
  '��' => '〕',
  '��' => '〈',
  '��' => '〉',
  '��' => '《',
  '��' => '》',
  '��' => '「',
  '��' => '」',
  '��' => '『',
  '��' => '』',
  '��' => '【',
  '��' => '】',
  '��' => '±',
  '��' => '×',
  '��' => '÷',
  '��' => '≠',
  '��' => '≤',
  '��' => '≥',
  '��' => '∞',
  '��' => '∴',
  '��' => '°',
  '��' => '′',
  '��' => '″',
  '��' => '℃',
  '��' => 'Å',
  '��' => '￠',
  '��' => '￡',
  '��' => '￥',
  '��' => '♂',
  '��' => '♀',
  '��' => '∠',
  '��' => '⊥',
  '��' => '⌒',
  '��' => '∂',
  '��' => '∇',
  '��' => '≡',
  '��' => '≒',
  '��' => '§',
  '��' => '※',
  '��' => '☆',
  '��' => '★',
  '��' => '○',
  '��' => '●',
  '��' => '◎',
  '��' => '◇',
  '��' => '◆',
  '��' => '□',
  '��' => '■',
  '��' => '△',
  '��' => '▲',
  '��' => '▽',
  '��' => '▼',
  '��' => '→',
  '��' => '←',
  '��' => '↑',
  '��' => '↓',
  '��' => '↔',
  '��' => '〓',
  '��' => '≪',
  '��' => '≫',
  '��' => '√',
  '��' => '∽',
  '��' => '∝',
  '��' => '∵',
  '��' => '∫',
  '��' => '∬',
  '��' => '∈',
  '��' => '∋',
  '��' => '⊆',
  '��' => '⊇',
  '��' => '⊂',
  '��' => '⊃',
  '��' => '∪',
  '��' => '∩',
  '��' => '∧',
  '��' => '∨',
  '��' => '￢',
  '�A' => '줐',
  '�B' => '줒',
  '�C' => '줓',
  '�D' => '줔',
  '�E' => '줕',
  '�F' => '줖',
  '�G' => '줗',
  '�H' => '줙',
  '�I' => '줚',
  '�J' => '줛',
  '�K' => '줜',
  '�L' => '줝',
  '�M' => '줞',
  '�N' => '줟',
  '�O' => '줠',
  '�P' => '줡',
  '�Q' => '줢',
  '�R' => '줣',
  '�S' => '줤',
  '�T' => '줥',
  '�U' => '줦',
  '�V' => '줧',
  '�W' => '줨',
  '�X' => '줩',
  '�Y' => '줪',
  '�Z' => '줫',
  '�a' => '줭',
  '�b' => '줮',
  '�c' => '줯',
  '�d' => '줰',
  '�e' => '줱',
  '�f' => '줲',
  '�g' => '줳',
  '�h' => '줵',
  '�i' => '줶',
  '�j' => '줷',
  '�k' => '줸',
  '�l' => '줹',
  '�m' => '줺',
  '�n' => '줻',
  '�o' => '줼',
  '�p' => '줽',
  '�q' => '줾',
  '�r' => '줿',
  '�s' => '쥀',
  '�t' => '쥁',
  '�u' => '쥂',
  '�v' => '쥃',
  '�w' => '쥄',
  '�x' => '쥅',
  '�y' => '쥆',
  '�z' => '쥇',
  '��' => '쥈',
  '��' => '쥉',
  '��' => '쥊',
  '��' => '쥋',
  '��' => '쥌',
  '��' => '쥍',
  '��' => '쥎',
  '��' => '쥏',
  '��' => '쥒',
  '��' => '쥓',
  '��' => '쥕',
  '��' => '쥖',
  '��' => '쥗',
  '��' => '쥙',
  '��' => '쥚',
  '��' => '쥛',
  '��' => '쥜',
  '��' => '쥝',
  '��' => '쥞',
  '��' => '쥟',
  '��' => '쥢',
  '��' => '쥤',
  '��' => '쥥',
  '��' => '쥦',
  '��' => '쥧',
  '��' => '쥨',
  '��' => '쥩',
  '��' => '쥪',
  '��' => '쥫',
  '��' => '쥭',
  '��' => '쥮',
  '��' => '쥯',
  '��' => '⇒',
  '��' => '⇔',
  '��' => '∀',
  '��' => '∃',
  '��' => '´',
  '��' => '～',
  '��' => 'ˇ',
  '��' => '˘',
  '��' => '˝',
  '��' => '˚',
  '��' => '˙',
  '��' => '¸',
  '��' => '˛',
  '��' => '¡',
  '��' => '¿',
  '��' => 'ː',
  '��' => '∮',
  '��' => '∑',
  '��' => '∏',
  '��' => '¤',
  '��' => '℉',
  '��' => '‰',
  '��' => '◁',
  '��' => '◀',
  '��' => '▷',
  '��' => '▶',
  '��' => '♤',
  '��' => '♠',
  '��' => '♡',
  '��' => '♥',
  '��' => '♧',
  '��' => '♣',
  '��' => '⊙',
  '��' => '◈',
  '��' => '▣',
  '��' => '◐',
  '��' => '◑',
  '��' => '▒',
  '��' => '▤',
  '��' => '▥',
  '��' => '▨',
  '��' => '▧',
  '��' => '▦',
  '��' => '▩',
  '��' => '♨',
  '��' => '☏',
  '��' => '☎',
  '��' => '☜',
  '��' => '☞',
  '��' => '¶',
  '��' => '†',
  '��' => '‡',
  '��' => '↕',
  '��' => '↗',
  '��' => '↙',
  '��' => '↖',
  '��' => '↘',
  '��' => '♭',
  '��' => '♩',
  '��' => '♪',
  '��' => '♬',
  '��' => '㉿',
  '��' => '㈜',
  '��' => '№',
  '��' => '㏇',
  '��' => '™',
  '��' => '㏂',
  '��' => '㏘',
  '��' => '℡',
  '��' => '€',
  '��' => '®',
  '�A' => '쥱',
  '�B' => '쥲',
  '�C' => '쥳',
  '�D' => '쥵',
  '�E' => '쥶',
  '�F' => '쥷',
  '�G' => '쥸',
  '�H' => '쥹',
  '�I' => '쥺',
  '�J' => '쥻',
  '�K' => '쥽',
  '�L' => '쥾',
  '�M' => '쥿',
  '�N' => '즀',
  '�O' => '즁',
  '�P' => '즂',
  '�Q' => '즃',
  '�R' => '즄',
  '�S' => '즅',
  '�T' => '즆',
  '�U' => '즇',
  '�V' => '즊',
  '�W' => '즋',
  '�X' => '즍',
  '�Y' => '즎',
  '�Z' => '즏',
  '�a' => '즑',
  '�b' => '즒',
  '�c' => '즓',
  '�d' => '즔',
  '�e' => '즕',
  '�f' => '즖',
  '�g' => '즗',
  '�h' => '즚',
  '�i' => '즜',
  '�j' => '즞',
  '�k' => '즟',
  '�l' => '즠',
  '�m' => '즡',
  '�n' => '즢',
  '�o' => '즣',
  '�p' => '즤',
  '�q' => '즥',
  '�r' => '즦',
  '�s' => '즧',
  '�t' => '즨',
  '�u' => '즩',
  '�v' => '즪',
  '�w' => '즫',
  '�x' => '즬',
  '�y' => '즭',
  '�z' => '즮',
  '��' => '즯',
  '��' => '즰',
  '��' => '즱',
  '��' => '즲',
  '��' => '즳',
  '��' => '즴',
  '��' => '즵',
  '��' => '즶',
  '��' => '즷',
  '��' => '즸',
  '��' => '즹',
  '��' => '즺',
  '��' => '즻',
  '��' => '즼',
  '��' => '즽',
  '��' => '즾',
  '��' => '즿',
  '��' => '짂',
  '��' => '짃',
  '��' => '짅',
  '��' => '짆',
  '��' => '짉',
  '��' => '짋',
  '��' => '짌',
  '��' => '짍',
  '��' => '짎',
  '��' => '짏',
  '��' => '짒',
  '��' => '짔',
  '��' => '짗',
  '��' => '짘',
  '��' => '짛',
  '��' => '！',
  '��' => '＂',
  '��' => '＃',
  '��' => '＄',
  '��' => '％',
  '��' => '＆',
  '��' => '＇',
  '��' => '（',
  '��' => '）',
  '��' => '＊',
  '��' => '＋',
  '��' => '，',
  '��' => '－',
  '��' => '．',
  '��' => '／',
  '��' => '０',
  '��' => '１',
  '��' => '２',
  '��' => '３',
  '��' => '４',
  '��' => '５',
  '��' => '６',
  '��' => '７',
  '��' => '８',
  '��' => '９',
  '��' => '：',
  '��' => '；',
  '��' => '＜',
  '��' => '＝',
  '��' => '＞',
  '��' => '？',
  '��' => '＠',
  '��' => 'Ａ',
  '��' => 'Ｂ',
  '��' => 'Ｃ',
  '��' => 'Ｄ',
  '��' => 'Ｅ',
  '��' => 'Ｆ',
  '��' => 'Ｇ',
  '��' => 'Ｈ',
  '��' => 'Ｉ',
  '��' => 'Ｊ',
  '��' => 'Ｋ',
  '��' => 'Ｌ',
  '��' => 'Ｍ',
  '��' => 'Ｎ',
  '��' => 'Ｏ',
  '��' => 'Ｐ',
  '��' => 'Ｑ',
  '��' => 'Ｒ',
  '��' => 'Ｓ',
  '��' => 'Ｔ',
  '��' => 'Ｕ',
  '��' => 'Ｖ',
  '��' => 'Ｗ',
  '��' => 'Ｘ',
  '��' => 'Ｙ',
  '��' => 'Ｚ',
  '��' => '［',
  '��' => '￦',
  '��' => '］',
  '��' => '＾',
  '��' => '＿',
  '��' => '｀',
  '��' => 'ａ',
  '��' => 'ｂ',
  '��' => 'ｃ',
  '��' => 'ｄ',
  '��' => 'ｅ',
  '��' => 'ｆ',
  '��' => 'ｇ',
  '��' => 'ｈ',
  '��' => 'ｉ',
  '��' => 'ｊ',
  '��' => 'ｋ',
  '��' => 'ｌ',
  '��' => 'ｍ',
  '��' => 'ｎ',
  '��' => 'ｏ',
  '��' => 'ｐ',
  '��' => 'ｑ',
  '��' => 'ｒ',
  '��' => 'ｓ',
  '��' => 'ｔ',
  '��' => 'ｕ',
  '��' => 'ｖ',
  '��' => 'ｗ',
  '��' => 'ｘ',
  '��' => 'ｙ',
  '��' => 'ｚ',
  '��' => '｛',
  '��' => '｜',
  '��' => '｝',
  '��' => '￣',
  '�A' => '짞',
  '�B' => '짟',
  '�C' => '짡',
  '�D' => '짣',
  '�E' => '짥',
  '�F' => '짦',
  '�G' => '짨',
  '�H' => '짩',
  '�I' => '짪',
  '�J' => '짫',
  '�K' => '짮',
  '�L' => '짲',
  '�M' => '짳',
  '�N' => '짴',
  '�O' => '짵',
  '�P' => '짶',
  '�Q' => '짷',
  '�R' => '짺',
  '�S' => '짻',
  '�T' => '짽',
  '�U' => '짾',
  '�V' => '짿',
  '�W' => '쨁',
  '�X' => '쨂',
  '�Y' => '쨃',
  '�Z' => '쨄',
  '�a' => '쨅',
  '�b' => '쨆',
  '�c' => '쨇',
  '�d' => '쨊',
  '�e' => '쨎',
  '�f' => '쨏',
  '�g' => '쨐',
  '�h' => '쨑',
  '�i' => '쨒',
  '�j' => '쨓',
  '�k' => '쨕',
  '�l' => '쨖',
  '�m' => '쨗',
  '�n' => '쨙',
  '�o' => '쨚',
  '�p' => '쨛',
  '�q' => '쨜',
  '�r' => '쨝',
  '�s' => '쨞',
  '�t' => '쨟',
  '�u' => '쨠',
  '�v' => '쨡',
  '�w' => '쨢',
  '�x' => '쨣',
  '�y' => '쨤',
  '�z' => '쨥',
  '��' => '쨦',
  '��' => '쨧',
  '��' => '쨨',
  '��' => '쨪',
  '��' => '쨫',
  '��' => '쨬',
  '��' => '쨭',
  '��' => '쨮',
  '��' => '쨯',
  '��' => '쨰',
  '��' => '쨱',
  '��' => '쨲',
  '��' => '쨳',
  '��' => '쨴',
  '��' => '쨵',
  '��' => '쨶',
  '��' => '쨷',
  '��' => '쨸',
  '��' => '쨹',
  '��' => '쨺',
  '��' => '쨻',
  '��' => '쨼',
  '��' => '쨽',
  '��' => '쨾',
  '��' => '쨿',
  '��' => '쩀',
  '��' => '쩁',
  '��' => '쩂',
  '��' => '쩃',
  '��' => '쩄',
  '��' => '쩅',
  '��' => '쩆',
  '��' => 'ㄱ',
  '��' => 'ㄲ',
  '��' => 'ㄳ',
  '��' => 'ㄴ',
  '��' => 'ㄵ',
  '��' => 'ㄶ',
  '��' => 'ㄷ',
  '��' => 'ㄸ',
  '��' => 'ㄹ',
  '��' => 'ㄺ',
  '��' => 'ㄻ',
  '��' => 'ㄼ',
  '��' => 'ㄽ',
  '��' => 'ㄾ',
  '��' => 'ㄿ',
  '��' => 'ㅀ',
  '��' => 'ㅁ',
  '��' => 'ㅂ',
  '��' => 'ㅃ',
  '��' => 'ㅄ',
  '��' => 'ㅅ',
  '��' => 'ㅆ',
  '��' => 'ㅇ',
  '��' => 'ㅈ',
  '��' => 'ㅉ',
  '��' => 'ㅊ',
  '��' => 'ㅋ',
  '��' => 'ㅌ',
  '��' => 'ㅍ',
  '��' => 'ㅎ',
  '��' => 'ㅏ',
  '��' => 'ㅐ',
  '��' => 'ㅑ',
  '��' => 'ㅒ',
  '��' => 'ㅓ',
  '��' => 'ㅔ',
  '��' => 'ㅕ',
  '��' => 'ㅖ',
  '��' => 'ㅗ',
  '��' => 'ㅘ',
  '��' => 'ㅙ',
  '��' => 'ㅚ',
  '��' => 'ㅛ',
  '��' => 'ㅜ',
  '��' => 'ㅝ',
  '��' => 'ㅞ',
  '��' => 'ㅟ',
  '��' => 'ㅠ',
  '��' => 'ㅡ',
  '��' => 'ㅢ',
  '��' => 'ㅣ',
  '��' => 'ㅤ',
  '��' => 'ㅥ',
  '��' => 'ㅦ',
  '��' => 'ㅧ',
  '��' => 'ㅨ',
  '��' => 'ㅩ',
  '��' => 'ㅪ',
  '��' => 'ㅫ',
  '��' => 'ㅬ',
  '��' => 'ㅭ',
  '��' => 'ㅮ',
  '��' => 'ㅯ',
  '��' => 'ㅰ',
  '��' => 'ㅱ',
  '��' => 'ㅲ',
  '��' => 'ㅳ',
  '��' => 'ㅴ',
  '��' => 'ㅵ',
  '��' => 'ㅶ',
  '��' => 'ㅷ',
  '��' => 'ㅸ',
  '��' => 'ㅹ',
  '��' => 'ㅺ',
  '��' => 'ㅻ',
  '��' => 'ㅼ',
  '��' => 'ㅽ',
  '��' => 'ㅾ',
  '��' => 'ㅿ',
  '��' => 'ㆀ',
  '��' => 'ㆁ',
  '��' => 'ㆂ',
  '��' => 'ㆃ',
  '��' => 'ㆄ',
  '��' => 'ㆅ',
  '��' => 'ㆆ',
  '��' => 'ㆇ',
  '��' => 'ㆈ',
  '��' => 'ㆉ',
  '��' => 'ㆊ',
  '��' => 'ㆋ',
  '��' => 'ㆌ',
  '��' => 'ㆍ',
  '��' => 'ㆎ',
  '�A' => '쩇',
  '�B' => '쩈',
  '�C' => '쩉',
  '�D' => '쩊',
  '�E' => '쩋',
  '�F' => '쩎',
  '�G' => '쩏',
  '�H' => '쩑',
  '�I' => '쩒',
  '�J' => '쩓',
  '�K' => '쩕',
  '�L' => '쩖',
  '�M' => '쩗',
  '�N' => '쩘',
  '�O' => '쩙',
  '�P' => '쩚',
  '�Q' => '쩛',
  '�R' => '쩞',
  '�S' => '쩢',
  '�T' => '쩣',
  '�U' => '쩤',
  '�V' => '쩥',
  '�W' => '쩦',
  '�X' => '쩧',
  '�Y' => '쩩',
  '�Z' => '쩪',
  '�a' => '쩫',
  '�b' => '쩬',
  '�c' => '쩭',
  '�d' => '쩮',
  '�e' => '쩯',
  '�f' => '쩰',
  '�g' => '쩱',
  '�h' => '쩲',
  '�i' => '쩳',
  '�j' => '쩴',
  '�k' => '쩵',
  '�l' => '쩶',
  '�m' => '쩷',
  '�n' => '쩸',
  '�o' => '쩹',
  '�p' => '쩺',
  '�q' => '쩻',
  '�r' => '쩼',
  '�s' => '쩾',
  '�t' => '쩿',
  '�u' => '쪀',
  '�v' => '쪁',
  '�w' => '쪂',
  '�x' => '쪃',
  '�y' => '쪅',
  '�z' => '쪆',
  '��' => '쪇',
  '��' => '쪈',
  '��' => '쪉',
  '��' => '쪊',
  '��' => '쪋',
  '��' => '쪌',
  '��' => '쪍',
  '��' => '쪎',
  '��' => '쪏',
  '��' => '쪐',
  '��' => '쪑',
  '��' => '쪒',
  '��' => '쪓',
  '��' => '쪔',
  '��' => '쪕',
  '��' => '쪖',
  '��' => '쪗',
  '��' => '쪙',
  '��' => '쪚',
  '��' => '쪛',
  '��' => '쪜',
  '��' => '쪝',
  '��' => '쪞',
  '��' => '쪟',
  '��' => '쪠',
  '��' => '쪡',
  '��' => '쪢',
  '��' => '쪣',
  '��' => '쪤',
  '��' => '쪥',
  '��' => '쪦',
  '��' => '쪧',
  '��' => 'ⅰ',
  '��' => 'ⅱ',
  '��' => 'ⅲ',
  '��' => 'ⅳ',
  '��' => 'ⅴ',
  '��' => 'ⅵ',
  '��' => 'ⅶ',
  '��' => 'ⅷ',
  '��' => 'ⅸ',
  '��' => 'ⅹ',
  '��' => 'Ⅰ',
  '��' => 'Ⅱ',
  '��' => 'Ⅲ',
  '��' => 'Ⅳ',
  '��' => 'Ⅴ',
  '��' => 'Ⅵ',
  '��' => 'Ⅶ',
  '��' => 'Ⅷ',
  '��' => 'Ⅸ',
  '��' => 'Ⅹ',
  '��' => 'Α',
  '��' => 'Β',
  '��' => 'Γ',
  '��' => 'Δ',
  '��' => 'Ε',
  '��' => 'Ζ',
  '��' => 'Η',
  '��' => 'Θ',
  '��' => 'Ι',
  '��' => 'Κ',
  '��' => 'Λ',
  '��' => 'Μ',
  '��' => 'Ν',
  '��' => 'Ξ',
  '��' => 'Ο',
  '��' => 'Π',
  '��' => 'Ρ',
  '��' => 'Σ',
  '��' => 'Τ',
  '��' => 'Υ',
  '��' => 'Φ',
  '��' => 'Χ',
  '��' => 'Ψ',
  '��' => 'Ω',
  '��' => 'α',
  '��' => 'β',
  '��' => 'γ',
  '��' => 'δ',
  '��' => 'ε',
  '��' => 'ζ',
  '��' => 'η',
  '��' => 'θ',
  '��' => 'ι',
  '��' => 'κ',
  '��' => 'λ',
  '��' => 'μ',
  '��' => 'ν',
  '��' => 'ξ',
  '��' => 'ο',
  '��' => 'π',
  '��' => 'ρ',
  '��' => 'σ',
  '��' => 'τ',
  '��' => 'υ',
  '��' => 'φ',
  '��' => 'χ',
  '��' => 'ψ',
  '��' => 'ω',
  '�A' => '쪨',
  '�B' => '쪩',
  '�C' => '쪪',
  '�D' => '쪫',
  '�E' => '쪬',
  '�F' => '쪭',
  '�G' => '쪮',
  '�H' => '쪯',
  '�I' => '쪰',
  '�J' => '쪱',
  '�K' => '쪲',
  '�L' => '쪳',
  '�M' => '쪴',
  '�N' => '쪵',
  '�O' => '쪶',
  '�P' => '쪷',
  '�Q' => '쪸',
  '�R' => '쪹',
  '�S' => '쪺',
  '�T' => '쪻',
  '�U' => '쪾',
  '�V' => '쪿',
  '�W' => '쫁',
  '�X' => '쫂',
  '�Y' => '쫃',
  '�Z' => '쫅',
  '�a' => '쫆',
  '�b' => '쫇',
  '�c' => '쫈',
  '�d' => '쫉',
  '�e' => '쫊',
  '�f' => '쫋',
  '�g' => '쫎',
  '�h' => '쫐',
  '�i' => '쫒',
  '�j' => '쫔',
  '�k' => '쫕',
  '�l' => '쫖',
  '�m' => '쫗',
  '�n' => '쫚',
  '�o' => '쫛',
  '�p' => '쫜',
  '�q' => '쫝',
  '�r' => '쫞',
  '�s' => '쫟',
  '�t' => '쫡',
  '�u' => '쫢',
  '�v' => '쫣',
  '�w' => '쫤',
  '�x' => '쫥',
  '�y' => '쫦',
  '�z' => '쫧',
  '��' => '쫨',
  '��' => '쫩',
  '��' => '쫪',
  '��' => '쫫',
  '��' => '쫭',
  '��' => '쫮',
  '��' => '쫯',
  '��' => '쫰',
  '��' => '쫱',
  '��' => '쫲',
  '��' => '쫳',
  '��' => '쫵',
  '��' => '쫶',
  '��' => '쫷',
  '��' => '쫸',
  '��' => '쫹',
  '��' => '쫺',
  '��' => '쫻',
  '��' => '쫼',
  '��' => '쫽',
  '��' => '쫾',
  '��' => '쫿',
  '��' => '쬀',
  '��' => '쬁',
  '��' => '쬂',
  '��' => '쬃',
  '��' => '쬄',
  '��' => '쬅',
  '��' => '쬆',
  '��' => '쬇',
  '��' => '쬉',
  '��' => '쬊',
  '��' => '─',
  '��' => '│',
  '��' => '┌',
  '��' => '┐',
  '��' => '┘',
  '��' => '└',
  '��' => '├',
  '��' => '┬',
  '��' => '┤',
  '��' => '┴',
  '��' => '┼',
  '��' => '━',
  '��' => '┃',
  '��' => '┏',
  '��' => '┓',
  '��' => '┛',
  '��' => '┗',
  '��' => '┣',
  '��' => '┳',
  '��' => '┫',
  '��' => '┻',
  '��' => '╋',
  '��' => '┠',
  '��' => '┯',
  '��' => '┨',
  '��' => '┷',
  '��' => '┿',
  '��' => '┝',
  '��' => '┰',
  '��' => '┥',
  '��' => '┸',
  '��' => '╂',
  '��' => '┒',
  '��' => '┑',
  '��' => '┚',
  '��' => '┙',
  '��' => '┖',
  '��' => '┕',
  '��' => '┎',
  '��' => '┍',
  '��' => '┞',
  '��' => '┟',
  '��' => '┡',
  '��' => '┢',
  '��' => '┦',
  '��' => '┧',
  '��' => '┩',
  '��' => '┪',
  '��' => '┭',
  '��' => '┮',
  '��' => '┱',
  '��' => '┲',
  '��' => '┵',
  '��' => '┶',
  '��' => '┹',
  '��' => '┺',
  '��' => '┽',
  '��' => '┾',
  '��' => '╀',
  '��' => '╁',
  '��' => '╃',
  '��' => '╄',
  '��' => '╅',
  '��' => '╆',
  '��' => '╇',
  '��' => '╈',
  '��' => '╉',
  '��' => '╊',
  '�A' => '쬋',
  '�B' => '쬌',
  '�C' => '쬍',
  '�D' => '쬎',
  '�E' => '쬏',
  '�F' => '쬑',
  '�G' => '쬒',
  '�H' => '쬓',
  '�I' => '쬕',
  '�J' => '쬖',
  '�K' => '쬗',
  '�L' => '쬙',
  '�M' => '쬚',
  '�N' => '쬛',
  '�O' => '쬜',
  '�P' => '쬝',
  '�Q' => '쬞',
  '�R' => '쬟',
  '�S' => '쬢',
  '�T' => '쬣',
  '�U' => '쬤',
  '�V' => '쬥',
  '�W' => '쬦',
  '�X' => '쬧',
  '�Y' => '쬨',
  '�Z' => '쬩',
  '�a' => '쬪',
  '�b' => '쬫',
  '�c' => '쬬',
  '�d' => '쬭',
  '�e' => '쬮',
  '�f' => '쬯',
  '�g' => '쬰',
  '�h' => '쬱',
  '�i' => '쬲',
  '�j' => '쬳',
  '�k' => '쬴',
  '�l' => '쬵',
  '�m' => '쬶',
  '�n' => '쬷',
  '�o' => '쬸',
  '�p' => '쬹',
  '�q' => '쬺',
  '�r' => '쬻',
  '�s' => '쬼',
  '�t' => '쬽',
  '�u' => '쬾',
  '�v' => '쬿',
  '�w' => '쭀',
  '�x' => '쭂',
  '�y' => '쭃',
  '�z' => '쭄',
  '��' => '쭅',
  '��' => '쭆',
  '��' => '쭇',
  '��' => '쭊',
  '��' => '쭋',
  '��' => '쭍',
  '��' => '쭎',
  '��' => '쭏',
  '��' => '쭑',
  '��' => '쭒',
  '��' => '쭓',
  '��' => '쭔',
  '��' => '쭕',
  '��' => '쭖',
  '��' => '쭗',
  '��' => '쭚',
  '��' => '쭛',
  '��' => '쭜',
  '��' => '쭞',
  '��' => '쭟',
  '��' => '쭠',
  '��' => '쭡',
  '��' => '쭢',
  '��' => '쭣',
  '��' => '쭥',
  '��' => '쭦',
  '��' => '쭧',
  '��' => '쭨',
  '��' => '쭩',
  '��' => '쭪',
  '��' => '쭫',
  '��' => '쭬',
  '��' => '㎕',
  '��' => '㎖',
  '��' => '㎗',
  '��' => 'ℓ',
  '��' => '㎘',
  '��' => '㏄',
  '��' => '㎣',
  '��' => '㎤',
  '��' => '㎥',
  '��' => '㎦',
  '��' => '㎙',
  '��' => '㎚',
  '��' => '㎛',
  '��' => '㎜',
  '��' => '㎝',
  '��' => '㎞',
  '��' => '㎟',
  '��' => '㎠',
  '��' => '㎡',
  '��' => '㎢',
  '��' => '㏊',
  '��' => '㎍',
  '��' => '㎎',
  '��' => '㎏',
  '��' => '㏏',
  '��' => '㎈',
  '��' => '㎉',
  '��' => '㏈',
  '��' => '㎧',
  '��' => '㎨',
  '��' => '㎰',
  '��' => '㎱',
  '��' => '㎲',
  '��' => '㎳',
  '��' => '㎴',
  '��' => '㎵',
  '��' => '㎶',
  '��' => '㎷',
  '��' => '㎸',
  '��' => '㎹',
  '��' => '㎀',
  '��' => '㎁',
  '��' => '㎂',
  '��' => '㎃',
  '��' => '㎄',
  '��' => '㎺',
  '��' => '㎻',
  '��' => '㎼',
  '��' => '㎽',
  '��' => '㎾',
  '��' => '㎿',
  '��' => '㎐',
  '��' => '㎑',
  '��' => '㎒',
  '��' => '㎓',
  '��' => '㎔',
  '��' => 'Ω',
  '��' => '㏀',
  '��' => '㏁',
  '��' => '㎊',
  '��' => '㎋',
  '��' => '㎌',
  '��' => '㏖',
  '��' => '㏅',
  '��' => '㎭',
  '��' => '㎮',
  '��' => '㎯',
  '��' => '㏛',
  '��' => '㎩',
  '��' => '㎪',
  '��' => '㎫',
  '��' => '㎬',
  '��' => '㏝',
  '��' => '㏐',
  '��' => '㏓',
  '��' => '㏃',
  '��' => '㏉',
  '��' => '㏜',
  '��' => '㏆',
  '�A' => '쭭',
  '�B' => '쭮',
  '�C' => '쭯',
  '�D' => '쭰',
  '�E' => '쭱',
  '�F' => '쭲',
  '�G' => '쭳',
  '�H' => '쭴',
  '�I' => '쭵',
  '�J' => '쭶',
  '�K' => '쭷',
  '�L' => '쭺',
  '�M' => '쭻',
  '�N' => '쭼',
  '�O' => '쭽',
  '�P' => '쭾',
  '�Q' => '쭿',
  '�R' => '쮀',
  '�S' => '쮁',
  '�T' => '쮂',
  '�U' => '쮃',
  '�V' => '쮄',
  '�W' => '쮅',
  '�X' => '쮆',
  '�Y' => '쮇',
  '�Z' => '쮈',
  '�a' => '쮉',
  '�b' => '쮊',
  '�c' => '쮋',
  '�d' => '쮌',
  '�e' => '쮍',
  '�f' => '쮎',
  '�g' => '쮏',
  '�h' => '쮐',
  '�i' => '쮑',
  '�j' => '쮒',
  '�k' => '쮓',
  '�l' => '쮔',
  '�m' => '쮕',
  '�n' => '쮖',
  '�o' => '쮗',
  '�p' => '쮘',
  '�q' => '쮙',
  '�r' => '쮚',
  '�s' => '쮛',
  '�t' => '쮝',
  '�u' => '쮞',
  '�v' => '쮟',
  '�w' => '쮠',
  '�x' => '쮡',
  '�y' => '쮢',
  '�z' => '쮣',
  '��' => '쮤',
  '��' => '쮥',
  '��' => '쮦',
  '��' => '쮧',
  '��' => '쮨',
  '��' => '쮩',
  '��' => '쮪',
  '��' => '쮫',
  '��' => '쮬',
  '��' => '쮭',
  '��' => '쮮',
  '��' => '쮯',
  '��' => '쮰',
  '��' => '쮱',
  '��' => '쮲',
  '��' => '쮳',
  '��' => '쮴',
  '��' => '쮵',
  '��' => '쮶',
  '��' => '쮷',
  '��' => '쮹',
  '��' => '쮺',
  '��' => '쮻',
  '��' => '쮼',
  '��' => '쮽',
  '��' => '쮾',
  '��' => '쮿',
  '��' => '쯀',
  '��' => '쯁',
  '��' => '쯂',
  '��' => '쯃',
  '��' => '쯄',
  '��' => 'Æ',
  '��' => 'Ð',
  '��' => 'ª',
  '��' => 'Ħ',
  '��' => 'Ĳ',
  '��' => 'Ŀ',
  '��' => 'Ł',
  '��' => 'Ø',
  '��' => 'Œ',
  '��' => 'º',
  '��' => 'Þ',
  '��' => 'Ŧ',
  '��' => 'Ŋ',
  '��' => '㉠',
  '��' => '㉡',
  '��' => '㉢',
  '��' => '㉣',
  '��' => '㉤',
  '��' => '㉥',
  '��' => '㉦',
  '��' => '㉧',
  '��' => '㉨',
  '��' => '㉩',
  '��' => '㉪',
  '��' => '㉫',
  '��' => '㉬',
  '��' => '㉭',
  '��' => '㉮',
  '��' => '㉯',
  '��' => '㉰',
  '��' => '㉱',
  '��' => '㉲',
  '��' => '㉳',
  '��' => '㉴',
  '��' => '㉵',
  '��' => '㉶',
  '��' => '㉷',
  '��' => '㉸',
  '��' => '㉹',
  '��' => '㉺',
  '��' => '㉻',
  '��' => 'ⓐ',
  '��' => 'ⓑ',
  '��' => 'ⓒ',
  '��' => 'ⓓ',
  '��' => 'ⓔ',
  '��' => 'ⓕ',
  '��' => 'ⓖ',
  '��' => 'ⓗ',
  '��' => 'ⓘ',
  '��' => 'ⓙ',
  '��' => 'ⓚ',
  '��' => 'ⓛ',
  '��' => 'ⓜ',
  '��' => 'ⓝ',
  '��' => 'ⓞ',
  '��' => 'ⓟ',
  '��' => 'ⓠ',
  '��' => 'ⓡ',
  '��' => 'ⓢ',
  '��' => 'ⓣ',
  '��' => 'ⓤ',
  '��' => 'ⓥ',
  '��' => 'ⓦ',
  '��' => 'ⓧ',
  '��' => 'ⓨ',
  '��' => 'ⓩ',
  '��' => '①',
  '��' => '②',
  '��' => '③',
  '��' => '④',
  '��' => '⑤',
  '��' => '⑥',
  '��' => '⑦',
  '��' => '⑧',
  '��' => '⑨',
  '��' => '⑩',
  '��' => '⑪',
  '��' => '⑫',
  '��' => '⑬',
  '��' => '⑭',
  '��' => '⑮',
  '��' => '½',
  '��' => '⅓',
  '��' => '⅔',
  '��' => '¼',
  '��' => '¾',
  '��' => '⅛',
  '��' => '⅜',
  '��' => '⅝',
  '��' => '⅞',
  '�A' => '쯅',
  '�B' => '쯆',
  '�C' => '쯇',
  '�D' => '쯈',
  '�E' => '쯉',
  '�F' => '쯊',
  '�G' => '쯋',
  '�H' => '쯌',
  '�I' => '쯍',
  '�J' => '쯎',
  '�K' => '쯏',
  '�L' => '쯐',
  '�M' => '쯑',
  '�N' => '쯒',
  '�O' => '쯓',
  '�P' => '쯕',
  '�Q' => '쯖',
  '�R' => '쯗',
  '�S' => '쯘',
  '�T' => '쯙',
  '�U' => '쯚',
  '�V' => '쯛',
  '�W' => '쯜',
  '�X' => '쯝',
  '�Y' => '쯞',
  '�Z' => '쯟',
  '�a' => '쯠',
  '�b' => '쯡',
  '�c' => '쯢',
  '�d' => '쯣',
  '�e' => '쯥',
  '�f' => '쯦',
  '�g' => '쯨',
  '�h' => '쯪',
  '�i' => '쯫',
  '�j' => '쯬',
  '�k' => '쯭',
  '�l' => '쯮',
  '�m' => '쯯',
  '�n' => '쯰',
  '�o' => '쯱',
  '�p' => '쯲',
  '�q' => '쯳',
  '�r' => '쯴',
  '�s' => '쯵',
  '�t' => '쯶',
  '�u' => '쯷',
  '�v' => '쯸',
  '�w' => '쯹',
  '�x' => '쯺',
  '�y' => '쯻',
  '�z' => '쯼',
  '��' => '쯽',
  '��' => '쯾',
  '��' => '쯿',
  '��' => '찀',
  '��' => '찁',
  '��' => '찂',
  '��' => '찃',
  '��' => '찄',
  '��' => '찅',
  '��' => '찆',
  '��' => '찇',
  '��' => '찈',
  '��' => '찉',
  '��' => '찊',
  '��' => '찋',
  '��' => '찎',
  '��' => '찏',
  '��' => '찑',
  '��' => '찒',
  '��' => '찓',
  '��' => '찕',
  '��' => '찖',
  '��' => '찗',
  '��' => '찘',
  '��' => '찙',
  '��' => '찚',
  '��' => '찛',
  '��' => '찞',
  '��' => '찟',
  '��' => '찠',
  '��' => '찣',
  '��' => '찤',
  '��' => 'æ',
  '��' => 'đ',
  '��' => 'ð',
  '��' => 'ħ',
  '��' => 'ı',
  '��' => 'ĳ',
  '��' => 'ĸ',
  '��' => 'ŀ',
  '��' => 'ł',
  '��' => 'ø',
  '��' => 'œ',
  '��' => 'ß',
  '��' => 'þ',
  '��' => 'ŧ',
  '��' => 'ŋ',
  '��' => 'ŉ',
  '��' => '㈀',
  '��' => '㈁',
  '��' => '㈂',
  '��' => '㈃',
  '��' => '㈄',
  '��' => '㈅',
  '��' => '㈆',
  '��' => '㈇',
  '��' => '㈈',
  '��' => '㈉',
  '��' => '㈊',
  '��' => '㈋',
  '��' => '㈌',
  '��' => '㈍',
  '��' => '㈎',
  '��' => '㈏',
  '��' => '㈐',
  '��' => '㈑',
  '��' => '㈒',
  '��' => '㈓',
  '��' => '㈔',
  '��' => '㈕',
  '��' => '㈖',
  '��' => '㈗',
  '��' => '㈘',
  '��' => '㈙',
  '��' => '㈚',
  '��' => '㈛',
  '��' => '⒜',
  '��' => '⒝',
  '��' => '⒞',
  '��' => '⒟',
  '��' => '⒠',
  '��' => '⒡',
  '��' => '⒢',
  '��' => '⒣',
  '��' => '⒤',
  '��' => '⒥',
  '��' => '⒦',
  '��' => '⒧',
  '��' => '⒨',
  '��' => '⒩',
  '��' => '⒪',
  '��' => '⒫',
  '��' => '⒬',
  '��' => '⒭',
  '��' => '⒮',
  '��' => '⒯',
  '��' => '⒰',
  '��' => '⒱',
  '��' => '⒲',
  '��' => '⒳',
  '��' => '⒴',
  '��' => '⒵',
  '��' => '⑴',
  '��' => '⑵',
  '��' => '⑶',
  '��' => '⑷',
  '��' => '⑸',
  '��' => '⑹',
  '��' => '⑺',
  '��' => '⑻',
  '��' => '⑼',
  '��' => '⑽',
  '��' => '⑾',
  '��' => '⑿',
  '��' => '⒀',
  '��' => '⒁',
  '��' => '⒂',
  '��' => '¹',
  '��' => '²',
  '��' => '³',
  '��' => '⁴',
  '��' => 'ⁿ',
  '��' => '₁',
  '��' => '₂',
  '��' => '₃',
  '��' => '₄',
  '�A' => '찥',
  '�B' => '찦',
  '�C' => '찪',
  '�D' => '찫',
  '�E' => '찭',
  '�F' => '찯',
  '�G' => '찱',
  '�H' => '찲',
  '�I' => '찳',
  '�J' => '찴',
  '�K' => '찵',
  '�L' => '찶',
  '�M' => '찷',
  '�N' => '찺',
  '�O' => '찿',
  '�P' => '챀',
  '�Q' => '챁',
  '�R' => '챂',
  '�S' => '챃',
  '�T' => '챆',
  '�U' => '챇',
  '�V' => '챉',
  '�W' => '챊',
  '�X' => '챋',
  '�Y' => '챍',
  '�Z' => '챎',
  '�a' => '챏',
  '�b' => '챐',
  '�c' => '챑',
  '�d' => '챒',
  '�e' => '챓',
  '�f' => '챖',
  '�g' => '챚',
  '�h' => '챛',
  '�i' => '챜',
  '�j' => '챝',
  '�k' => '챞',
  '�l' => '챟',
  '�m' => '챡',
  '�n' => '챢',
  '�o' => '챣',
  '�p' => '챥',
  '�q' => '챧',
  '�r' => '챩',
  '�s' => '챪',
  '�t' => '챫',
  '�u' => '챬',
  '�v' => '챭',
  '�w' => '챮',
  '�x' => '챯',
  '�y' => '챱',
  '�z' => '챲',
  '��' => '챳',
  '��' => '챴',
  '��' => '챶',
  '��' => '챷',
  '��' => '챸',
  '��' => '챹',
  '��' => '챺',
  '��' => '챻',
  '��' => '챼',
  '��' => '챽',
  '��' => '챾',
  '��' => '챿',
  '��' => '첀',
  '��' => '첁',
  '��' => '첂',
  '��' => '첃',
  '��' => '첄',
  '��' => '첅',
  '��' => '첆',
  '��' => '첇',
  '��' => '첈',
  '��' => '첉',
  '��' => '첊',
  '��' => '첋',
  '��' => '첌',
  '��' => '첍',
  '��' => '첎',
  '��' => '첏',
  '��' => '첐',
  '��' => '첑',
  '��' => '첒',
  '��' => '첓',
  '��' => 'ぁ',
  '��' => 'あ',
  '��' => 'ぃ',
  '��' => 'い',
  '��' => 'ぅ',
  '��' => 'う',
  '��' => 'ぇ',
  '��' => 'え',
  '��' => 'ぉ',
  '��' => 'お',
  '��' => 'か',
  '��' => 'が',
  '��' => 'き',
  '��' => 'ぎ',
  '��' => 'く',
  '��' => 'ぐ',
  '��' => 'け',
  '��' => 'げ',
  '��' => 'こ',
  '��' => 'ご',
  '��' => 'さ',
  '��' => 'ざ',
  '��' => 'し',
  '��' => 'じ',
  '��' => 'す',
  '��' => 'ず',
  '��' => 'せ',
  '��' => 'ぜ',
  '��' => 'そ',
  '��' => 'ぞ',
  '��' => 'た',
  '��' => 'だ',
  '��' => 'ち',
  '��' => 'ぢ',
  '��' => 'っ',
  '��' => 'つ',
  '��' => 'づ',
  '��' => 'て',
  '��' => 'で',
  '��' => 'と',
  '��' => 'ど',
  '��' => 'な',
  '��' => 'に',
  '��' => 'ぬ',
  '��' => 'ね',
  '��' => 'の',
  '��' => 'は',
  '��' => 'ば',
  '��' => 'ぱ',
  '��' => 'ひ',
  '��' => 'び',
  '��' => 'ぴ',
  '��' => 'ふ',
  '��' => 'ぶ',
  '��' => 'ぷ',
  '��' => 'へ',
  '��' => 'べ',
  '��' => 'ぺ',
  '��' => 'ほ',
  '��' => 'ぼ',
  '��' => 'ぽ',
  '��' => 'ま',
  '��' => 'み',
  '��' => 'む',
  '��' => 'め',
  '��' => 'も',
  '��' => 'ゃ',
  '��' => 'や',
  '��' => 'ゅ',
  '��' => 'ゆ',
  '��' => 'ょ',
  '��' => 'よ',
  '��' => 'ら',
  '��' => 'り',
  '��' => 'る',
  '��' => 'れ',
  '��' => 'ろ',
  '��' => 'ゎ',
  '��' => 'わ',
  '��' => 'ゐ',
  '��' => 'ゑ',
  '��' => 'を',
  '��' => 'ん',
  '�A' => '첔',
  '�B' => '첕',
  '�C' => '첖',
  '�D' => '첗',
  '�E' => '첚',
  '�F' => '첛',
  '�G' => '첝',
  '�H' => '첞',
  '�I' => '첟',
  '�J' => '첡',
  '�K' => '첢',
  '�L' => '첣',
  '�M' => '첤',
  '�N' => '첥',
  '�O' => '첦',
  '�P' => '첧',
  '�Q' => '첪',
  '�R' => '첮',
  '�S' => '첯',
  '�T' => '첰',
  '�U' => '첱',
  '�V' => '첲',
  '�W' => '첳',
  '�X' => '첶',
  '�Y' => '첷',
  '�Z' => '첹',
  '�a' => '첺',
  '�b' => '첻',
  '�c' => '첽',
  '�d' => '첾',
  '�e' => '첿',
  '�f' => '쳀',
  '�g' => '쳁',
  '�h' => '쳂',
  '�i' => '쳃',
  '�j' => '쳆',
  '�k' => '쳈',
  '�l' => '쳊',
  '�m' => '쳋',
  '�n' => '쳌',
  '�o' => '쳍',
  '�p' => '쳎',
  '�q' => '쳏',
  '�r' => '쳑',
  '�s' => '쳒',
  '�t' => '쳓',
  '�u' => '쳕',
  '�v' => '쳖',
  '�w' => '쳗',
  '�x' => '쳘',
  '�y' => '쳙',
  '�z' => '쳚',
  '��' => '쳛',
  '��' => '쳜',
  '��' => '쳝',
  '��' => '쳞',
  '��' => '쳟',
  '��' => '쳠',
  '��' => '쳡',
  '��' => '쳢',
  '��' => '쳣',
  '��' => '쳥',
  '��' => '쳦',
  '��' => '쳧',
  '��' => '쳨',
  '��' => '쳩',
  '��' => '쳪',
  '��' => '쳫',
  '��' => '쳭',
  '��' => '쳮',
  '��' => '쳯',
  '��' => '쳱',
  '��' => '쳲',
  '��' => '쳳',
  '��' => '쳴',
  '��' => '쳵',
  '��' => '쳶',
  '��' => '쳷',
  '��' => '쳸',
  '��' => '쳹',
  '��' => '쳺',
  '��' => '쳻',
  '��' => '쳼',
  '��' => '쳽',
  '��' => 'ァ',
  '��' => 'ア',
  '��' => 'ィ',
  '��' => 'イ',
  '��' => 'ゥ',
  '��' => 'ウ',
  '��' => 'ェ',
  '��' => 'エ',
  '��' => 'ォ',
  '��' => 'オ',
  '��' => 'カ',
  '��' => 'ガ',
  '��' => 'キ',
  '��' => 'ギ',
  '��' => 'ク',
  '��' => 'グ',
  '��' => 'ケ',
  '��' => 'ゲ',
  '��' => 'コ',
  '��' => 'ゴ',
  '��' => 'サ',
  '��' => 'ザ',
  '��' => 'シ',
  '��' => 'ジ',
  '��' => 'ス',
  '��' => 'ズ',
  '��' => 'セ',
  '��' => 'ゼ',
  '��' => 'ソ',
  '��' => 'ゾ',
  '��' => 'タ',
  '��' => 'ダ',
  '��' => 'チ',
  '��' => 'ヂ',
  '��' => 'ッ',
  '��' => 'ツ',
  '��' => 'ヅ',
  '��' => 'テ',
  '��' => 'デ',
  '��' => 'ト',
  '��' => 'ド',
  '��' => 'ナ',
  '��' => 'ニ',
  '��' => 'ヌ',
  '��' => 'ネ',
  '��' => 'ノ',
  '��' => 'ハ',
  '��' => 'バ',
  '��' => 'パ',
  '��' => 'ヒ',
  '��' => 'ビ',
  '��' => 'ピ',
  '��' => 'フ',
  '��' => 'ブ',
  '��' => 'プ',
  '��' => 'ヘ',
  '��' => 'ベ',
  '��' => 'ペ',
  '��' => 'ホ',
  '��' => 'ボ',
  '��' => 'ポ',
  '��' => 'マ',
  '��' => 'ミ',
  '��' => 'ム',
  '��' => 'メ',
  '��' => 'モ',
  '��' => 'ャ',
  '��' => 'ヤ',
  '��' => 'ュ',
  '��' => 'ユ',
  '��' => 'ョ',
  '��' => 'ヨ',
  '��' => 'ラ',
  '��' => 'リ',
  '��' => 'ル',
  '��' => 'レ',
  '��' => 'ロ',
  '��' => 'ヮ',
  '��' => 'ワ',
  '��' => 'ヰ',
  '��' => 'ヱ',
  '��' => 'ヲ',
  '��' => 'ン',
  '��' => 'ヴ',
  '��' => 'ヵ',
  '��' => 'ヶ',
  '�A' => '쳾',
  '�B' => '쳿',
  '�C' => '촀',
  '�D' => '촂',
  '�E' => '촃',
  '�F' => '촄',
  '�G' => '촅',
  '�H' => '촆',
  '�I' => '촇',
  '�J' => '촊',
  '�K' => '촋',
  '�L' => '촍',
  '�M' => '촎',
  '�N' => '촏',
  '�O' => '촑',
  '�P' => '촒',
  '�Q' => '촓',
  '�R' => '촔',
  '�S' => '촕',
  '�T' => '촖',
  '�U' => '촗',
  '�V' => '촚',
  '�W' => '촜',
  '�X' => '촞',
  '�Y' => '촟',
  '�Z' => '촠',
  '�a' => '촡',
  '�b' => '촢',
  '�c' => '촣',
  '�d' => '촥',
  '�e' => '촦',
  '�f' => '촧',
  '�g' => '촩',
  '�h' => '촪',
  '�i' => '촫',
  '�j' => '촭',
  '�k' => '촮',
  '�l' => '촯',
  '�m' => '촰',
  '�n' => '촱',
  '�o' => '촲',
  '�p' => '촳',
  '�q' => '촴',
  '�r' => '촵',
  '�s' => '촶',
  '�t' => '촷',
  '�u' => '촸',
  '�v' => '촺',
  '�w' => '촻',
  '�x' => '촼',
  '�y' => '촽',
  '�z' => '촾',
  '��' => '촿',
  '��' => '쵀',
  '��' => '쵁',
  '��' => '쵂',
  '��' => '쵃',
  '��' => '쵄',
  '��' => '쵅',
  '��' => '쵆',
  '��' => '쵇',
  '��' => '쵈',
  '��' => '쵉',
  '��' => '쵊',
  '��' => '쵋',
  '��' => '쵌',
  '��' => '쵍',
  '��' => '쵎',
  '��' => '쵏',
  '��' => '쵐',
  '��' => '쵑',
  '��' => '쵒',
  '��' => '쵓',
  '��' => '쵔',
  '��' => '쵕',
  '��' => '쵖',
  '��' => '쵗',
  '��' => '쵘',
  '��' => '쵙',
  '��' => '쵚',
  '��' => '쵛',
  '��' => '쵝',
  '��' => '쵞',
  '��' => '쵟',
  '��' => 'А',
  '��' => 'Б',
  '��' => 'В',
  '��' => 'Г',
  '��' => 'Д',
  '��' => 'Е',
  '��' => 'Ё',
  '��' => 'Ж',
  '��' => 'З',
  '��' => 'И',
  '��' => 'Й',
  '��' => 'К',
  '��' => 'Л',
  '��' => 'М',
  '��' => 'Н',
  '��' => 'О',
  '��' => 'П',
  '��' => 'Р',
  '��' => 'С',
  '��' => 'Т',
  '��' => 'У',
  '��' => 'Ф',
  '��' => 'Х',
  '��' => 'Ц',
  '��' => 'Ч',
  '��' => 'Ш',
  '��' => 'Щ',
  '��' => 'Ъ',
  '��' => 'Ы',
  '��' => 'Ь',
  '��' => 'Э',
  '��' => 'Ю',
  '��' => 'Я',
  '��' => 'а',
  '��' => 'б',
  '��' => 'в',
  '��' => 'г',
  '��' => 'д',
  '��' => 'е',
  '��' => 'ё',
  '��' => 'ж',
  '��' => 'з',
  '��' => 'и',
  '��' => 'й',
  '��' => 'к',
  '��' => 'л',
  '��' => 'м',
  '��' => 'н',
  '��' => 'о',
  '��' => 'п',
  '��' => 'р',
  '��' => 'с',
  '��' => 'т',
  '��' => 'у',
  '��' => 'ф',
  '��' => 'х',
  '��' => 'ц',
  '��' => 'ч',
  '��' => 'ш',
  '��' => 'щ',
  '��' => 'ъ',
  '��' => 'ы',
  '��' => 'ь',
  '��' => 'э',
  '��' => 'ю',
  '��' => 'я',
  '�A' => '쵡',
  '�B' => '쵢',
  '�C' => '쵣',
  '�D' => '쵥',
  '�E' => '쵦',
  '�F' => '쵧',
  '�G' => '쵨',
  '�H' => '쵩',
  '�I' => '쵪',
  '�J' => '쵫',
  '�K' => '쵮',
  '�L' => '쵰',
  '�M' => '쵲',
  '�N' => '쵳',
  '�O' => '쵴',
  '�P' => '쵵',
  '�Q' => '쵶',
  '�R' => '쵷',
  '�S' => '쵹',
  '�T' => '쵺',
  '�U' => '쵻',
  '�V' => '쵼',
  '�W' => '쵽',
  '�X' => '쵾',
  '�Y' => '쵿',
  '�Z' => '춀',
  '�a' => '춁',
  '�b' => '춂',
  '�c' => '춃',
  '�d' => '춄',
  '�e' => '춅',
  '�f' => '춆',
  '�g' => '춇',
  '�h' => '춉',
  '�i' => '춊',
  '�j' => '춋',
  '�k' => '춌',
  '�l' => '춍',
  '�m' => '춎',
  '�n' => '춏',
  '�o' => '춐',
  '�p' => '춑',
  '�q' => '춒',
  '�r' => '춓',
  '�s' => '춖',
  '�t' => '춗',
  '�u' => '춙',
  '�v' => '춚',
  '�w' => '춛',
  '�x' => '춝',
  '�y' => '춞',
  '�z' => '춟',
  '��' => '춠',
  '��' => '춡',
  '��' => '춢',
  '��' => '춣',
  '��' => '춦',
  '��' => '춨',
  '��' => '춪',
  '��' => '춫',
  '��' => '춬',
  '��' => '춭',
  '��' => '춮',
  '��' => '춯',
  '��' => '춱',
  '��' => '춲',
  '��' => '춳',
  '��' => '춴',
  '��' => '춵',
  '��' => '춶',
  '��' => '춷',
  '��' => '춸',
  '��' => '춹',
  '��' => '춺',
  '��' => '춻',
  '��' => '춼',
  '��' => '춽',
  '��' => '춾',
  '��' => '춿',
  '��' => '췀',
  '��' => '췁',
  '��' => '췂',
  '��' => '췃',
  '��' => '췅',
  '�A' => '췆',
  '�B' => '췇',
  '�C' => '췈',
  '�D' => '췉',
  '�E' => '췊',
  '�F' => '췋',
  '�G' => '췍',
  '�H' => '췎',
  '�I' => '췏',
  '�J' => '췑',
  '�K' => '췒',
  '�L' => '췓',
  '�M' => '췔',
  '�N' => '췕',
  '�O' => '췖',
  '�P' => '췗',
  '�Q' => '췘',
  '�R' => '췙',
  '�S' => '췚',
  '�T' => '췛',
  '�U' => '췜',
  '�V' => '췝',
  '�W' => '췞',
  '�X' => '췟',
  '�Y' => '췠',
  '�Z' => '췡',
  '�a' => '췢',
  '�b' => '췣',
  '�c' => '췤',
  '�d' => '췥',
  '�e' => '췦',
  '�f' => '췧',
  '�g' => '췩',
  '�h' => '췪',
  '�i' => '췫',
  '�j' => '췭',
  '�k' => '췮',
  '�l' => '췯',
  '�m' => '췱',
  '�n' => '췲',
  '�o' => '췳',
  '�p' => '췴',
  '�q' => '췵',
  '�r' => '췶',
  '�s' => '췷',
  '�t' => '췺',
  '�u' => '췼',
  '�v' => '췾',
  '�w' => '췿',
  '�x' => '츀',
  '�y' => '츁',
  '�z' => '츂',
  '��' => '츃',
  '��' => '츅',
  '��' => '츆',
  '��' => '츇',
  '��' => '츉',
  '��' => '츊',
  '��' => '츋',
  '��' => '츍',
  '��' => '츎',
  '��' => '츏',
  '��' => '츐',
  '��' => '츑',
  '��' => '츒',
  '��' => '츓',
  '��' => '츕',
  '��' => '츖',
  '��' => '츗',
  '��' => '츘',
  '��' => '츚',
  '��' => '츛',
  '��' => '츜',
  '��' => '츝',
  '��' => '츞',
  '��' => '츟',
  '��' => '츢',
  '��' => '츣',
  '��' => '츥',
  '��' => '츦',
  '��' => '츧',
  '��' => '츩',
  '��' => '츪',
  '��' => '츫',
  '�A' => '츬',
  '�B' => '츭',
  '�C' => '츮',
  '�D' => '츯',
  '�E' => '츲',
  '�F' => '츴',
  '�G' => '츶',
  '�H' => '츷',
  '�I' => '츸',
  '�J' => '츹',
  '�K' => '츺',
  '�L' => '츻',
  '�M' => '츼',
  '�N' => '츽',
  '�O' => '츾',
  '�P' => '츿',
  '�Q' => '칀',
  '�R' => '칁',
  '�S' => '칂',
  '�T' => '칃',
  '�U' => '칄',
  '�V' => '칅',
  '�W' => '칆',
  '�X' => '칇',
  '�Y' => '칈',
  '�Z' => '칉',
  '�a' => '칊',
  '�b' => '칋',
  '�c' => '칌',
  '�d' => '칍',
  '�e' => '칎',
  '�f' => '칏',
  '�g' => '칐',
  '�h' => '칑',
  '�i' => '칒',
  '�j' => '칓',
  '�k' => '칔',
  '�l' => '칕',
  '�m' => '칖',
  '�n' => '칗',
  '�o' => '칚',
  '�p' => '칛',
  '�q' => '칝',
  '�r' => '칞',
  '�s' => '칢',
  '�t' => '칣',
  '�u' => '칤',
  '�v' => '칥',
  '�w' => '칦',
  '�x' => '칧',
  '�y' => '칪',
  '�z' => '칬',
  '��' => '칮',
  '��' => '칯',
  '��' => '칰',
  '��' => '칱',
  '��' => '칲',
  '��' => '칳',
  '��' => '칶',
  '��' => '칷',
  '��' => '칹',
  '��' => '칺',
  '��' => '칻',
  '��' => '칽',
  '��' => '칾',
  '��' => '칿',
  '��' => '캀',
  '��' => '캁',
  '��' => '캂',
  '��' => '캃',
  '��' => '캆',
  '��' => '캈',
  '��' => '캊',
  '��' => '캋',
  '��' => '캌',
  '��' => '캍',
  '��' => '캎',
  '��' => '캏',
  '��' => '캒',
  '��' => '캓',
  '��' => '캕',
  '��' => '캖',
  '��' => '캗',
  '��' => '캙',
  '�A' => '캚',
  '�B' => '캛',
  '�C' => '캜',
  '�D' => '캝',
  '�E' => '캞',
  '�F' => '캟',
  '�G' => '캢',
  '�H' => '캦',
  '�I' => '캧',
  '�J' => '캨',
  '�K' => '캩',
  '�L' => '캪',
  '�M' => '캫',
  '�N' => '캮',
  '�O' => '캯',
  '�P' => '캰',
  '�Q' => '캱',
  '�R' => '캲',
  '�S' => '캳',
  '�T' => '캴',
  '�U' => '캵',
  '�V' => '캶',
  '�W' => '캷',
  '�X' => '캸',
  '�Y' => '캹',
  '�Z' => '캺',
  '�a' => '캻',
  '�b' => '캼',
  '�c' => '캽',
  '�d' => '캾',
  '�e' => '캿',
  '�f' => '컀',
  '�g' => '컂',
  '�h' => '컃',
  '�i' => '컄',
  '�j' => '컅',
  '�k' => '컆',
  '�l' => '컇',
  '�m' => '컈',
  '�n' => '컉',
  '�o' => '컊',
  '�p' => '컋',
  '�q' => '컌',
  '�r' => '컍',
  '�s' => '컎',
  '�t' => '컏',
  '�u' => '컐',
  '�v' => '컑',
  '�w' => '컒',
  '�x' => '컓',
  '�y' => '컔',
  '�z' => '컕',
  '��' => '컖',
  '��' => '컗',
  '��' => '컘',
  '��' => '컙',
  '��' => '컚',
  '��' => '컛',
  '��' => '컜',
  '��' => '컝',
  '��' => '컞',
  '��' => '컟',
  '��' => '컠',
  '��' => '컡',
  '��' => '컢',
  '��' => '컣',
  '��' => '컦',
  '��' => '컧',
  '��' => '컩',
  '��' => '컪',
  '��' => '컭',
  '��' => '컮',
  '��' => '컯',
  '��' => '컰',
  '��' => '컱',
  '��' => '컲',
  '��' => '컳',
  '��' => '컶',
  '��' => '컺',
  '��' => '컻',
  '��' => '컼',
  '��' => '컽',
  '��' => '컾',
  '��' => '컿',
  '��' => '가',
  '��' => '각',
  '��' => '간',
  '��' => '갇',
  '��' => '갈',
  '��' => '갉',
  '��' => '갊',
  '��' => '감',
  '��' => '갑',
  '��' => '값',
  '��' => '갓',
  '��' => '갔',
  '��' => '강',
  '��' => '갖',
  '��' => '갗',
  '��' => '같',
  '��' => '갚',
  '��' => '갛',
  '��' => '개',
  '��' => '객',
  '��' => '갠',
  '��' => '갤',
  '��' => '갬',
  '��' => '갭',
  '��' => '갯',
  '��' => '갰',
  '��' => '갱',
  '��' => '갸',
  '��' => '갹',
  '��' => '갼',
  '��' => '걀',
  '��' => '걋',
  '��' => '걍',
  '��' => '걔',
  '��' => '걘',
  '��' => '걜',
  '��' => '거',
  '��' => '걱',
  '��' => '건',
  '��' => '걷',
  '��' => '걸',
  '��' => '걺',
  '��' => '검',
  '��' => '겁',
  '��' => '것',
  '��' => '겄',
  '��' => '겅',
  '��' => '겆',
  '��' => '겉',
  '��' => '겊',
  '��' => '겋',
  '��' => '게',
  '��' => '겐',
  '��' => '겔',
  '��' => '겜',
  '��' => '겝',
  '��' => '겟',
  '��' => '겠',
  '��' => '겡',
  '��' => '겨',
  '��' => '격',
  '��' => '겪',
  '��' => '견',
  '��' => '겯',
  '��' => '결',
  '��' => '겸',
  '��' => '겹',
  '��' => '겻',
  '��' => '겼',
  '��' => '경',
  '��' => '곁',
  '��' => '계',
  '��' => '곈',
  '��' => '곌',
  '��' => '곕',
  '��' => '곗',
  '��' => '고',
  '��' => '곡',
  '��' => '곤',
  '��' => '곧',
  '��' => '골',
  '��' => '곪',
  '��' => '곬',
  '��' => '곯',
  '��' => '곰',
  '��' => '곱',
  '��' => '곳',
  '��' => '공',
  '��' => '곶',
  '��' => '과',
  '��' => '곽',
  '��' => '관',
  '��' => '괄',
  '��' => '괆',
  '�A' => '켂',
  '�B' => '켃',
  '�C' => '켅',
  '�D' => '켆',
  '�E' => '켇',
  '�F' => '켉',
  '�G' => '켊',
  '�H' => '켋',
  '�I' => '켌',
  '�J' => '켍',
  '�K' => '켎',
  '�L' => '켏',
  '�M' => '켒',
  '�N' => '켔',
  '�O' => '켖',
  '�P' => '켗',
  '�Q' => '켘',
  '�R' => '켙',
  '�S' => '켚',
  '�T' => '켛',
  '�U' => '켝',
  '�V' => '켞',
  '�W' => '켟',
  '�X' => '켡',
  '�Y' => '켢',
  '�Z' => '켣',
  '�a' => '켥',
  '�b' => '켦',
  '�c' => '켧',
  '�d' => '켨',
  '�e' => '켩',
  '�f' => '켪',
  '�g' => '켫',
  '�h' => '켮',
  '�i' => '켲',
  '�j' => '켳',
  '�k' => '켴',
  '�l' => '켵',
  '�m' => '켶',
  '�n' => '켷',
  '�o' => '켹',
  '�p' => '켺',
  '�q' => '켻',
  '�r' => '켼',
  '�s' => '켽',
  '�t' => '켾',
  '�u' => '켿',
  '�v' => '콀',
  '�w' => '콁',
  '�x' => '콂',
  '�y' => '콃',
  '�z' => '콄',
  '��' => '콅',
  '��' => '콆',
  '��' => '콇',
  '��' => '콈',
  '��' => '콉',
  '��' => '콊',
  '��' => '콋',
  '��' => '콌',
  '��' => '콍',
  '��' => '콎',
  '��' => '콏',
  '��' => '콐',
  '��' => '콑',
  '��' => '콒',
  '��' => '콓',
  '��' => '콖',
  '��' => '콗',
  '��' => '콙',
  '��' => '콚',
  '��' => '콛',
  '��' => '콝',
  '��' => '콞',
  '��' => '콟',
  '��' => '콠',
  '��' => '콡',
  '��' => '콢',
  '��' => '콣',
  '��' => '콦',
  '��' => '콨',
  '��' => '콪',
  '��' => '콫',
  '��' => '콬',
  '��' => '괌',
  '��' => '괍',
  '��' => '괏',
  '��' => '광',
  '��' => '괘',
  '��' => '괜',
  '��' => '괠',
  '��' => '괩',
  '��' => '괬',
  '��' => '괭',
  '��' => '괴',
  '��' => '괵',
  '��' => '괸',
  '��' => '괼',
  '��' => '굄',
  '��' => '굅',
  '��' => '굇',
  '��' => '굉',
  '��' => '교',
  '��' => '굔',
  '��' => '굘',
  '��' => '굡',
  '��' => '굣',
  '��' => '구',
  '��' => '국',
  '��' => '군',
  '��' => '굳',
  '��' => '굴',
  '��' => '굵',
  '��' => '굶',
  '��' => '굻',
  '��' => '굼',
  '��' => '굽',
  '��' => '굿',
  '��' => '궁',
  '��' => '궂',
  '��' => '궈',
  '��' => '궉',
  '��' => '권',
  '��' => '궐',
  '��' => '궜',
  '��' => '궝',
  '��' => '궤',
  '��' => '궷',
  '��' => '귀',
  '��' => '귁',
  '��' => '귄',
  '��' => '귈',
  '��' => '귐',
  '��' => '귑',
  '��' => '귓',
  '��' => '규',
  '��' => '균',
  '��' => '귤',
  '��' => '그',
  '��' => '극',
  '��' => '근',
  '��' => '귿',
  '��' => '글',
  '��' => '긁',
  '��' => '금',
  '��' => '급',
  '��' => '긋',
  '��' => '긍',
  '��' => '긔',
  '��' => '기',
  '��' => '긱',
  '��' => '긴',
  '��' => '긷',
  '��' => '길',
  '��' => '긺',
  '��' => '김',
  '��' => '깁',
  '��' => '깃',
  '��' => '깅',
  '��' => '깆',
  '��' => '깊',
  '��' => '까',
  '��' => '깍',
  '��' => '깎',
  '��' => '깐',
  '��' => '깔',
  '��' => '깖',
  '��' => '깜',
  '��' => '깝',
  '��' => '깟',
  '��' => '깠',
  '��' => '깡',
  '��' => '깥',
  '��' => '깨',
  '��' => '깩',
  '��' => '깬',
  '��' => '깰',
  '��' => '깸',
  '�A' => '콭',
  '�B' => '콮',
  '�C' => '콯',
  '�D' => '콲',
  '�E' => '콳',
  '�F' => '콵',
  '�G' => '콶',
  '�H' => '콷',
  '�I' => '콹',
  '�J' => '콺',
  '�K' => '콻',
  '�L' => '콼',
  '�M' => '콽',
  '�N' => '콾',
  '�O' => '콿',
  '�P' => '쾁',
  '�Q' => '쾂',
  '�R' => '쾃',
  '�S' => '쾄',
  '�T' => '쾆',
  '�U' => '쾇',
  '�V' => '쾈',
  '�W' => '쾉',
  '�X' => '쾊',
  '�Y' => '쾋',
  '�Z' => '쾍',
  '�a' => '쾎',
  '�b' => '쾏',
  '�c' => '쾐',
  '�d' => '쾑',
  '�e' => '쾒',
  '�f' => '쾓',
  '�g' => '쾔',
  '�h' => '쾕',
  '�i' => '쾖',
  '�j' => '쾗',
  '�k' => '쾘',
  '�l' => '쾙',
  '�m' => '쾚',
  '�n' => '쾛',
  '�o' => '쾜',
  '�p' => '쾝',
  '�q' => '쾞',
  '�r' => '쾟',
  '�s' => '쾠',
  '�t' => '쾢',
  '�u' => '쾣',
  '�v' => '쾤',
  '�w' => '쾥',
  '�x' => '쾦',
  '�y' => '쾧',
  '�z' => '쾩',
  '��' => '쾪',
  '��' => '쾫',
  '��' => '쾬',
  '��' => '쾭',
  '��' => '쾮',
  '��' => '쾯',
  '��' => '쾱',
  '��' => '쾲',
  '��' => '쾳',
  '��' => '쾴',
  '��' => '쾵',
  '��' => '쾶',
  '��' => '쾷',
  '��' => '쾸',
  '��' => '쾹',
  '��' => '쾺',
  '��' => '쾻',
  '��' => '쾼',
  '��' => '쾽',
  '��' => '쾾',
  '��' => '쾿',
  '��' => '쿀',
  '��' => '쿁',
  '��' => '쿂',
  '��' => '쿃',
  '��' => '쿅',
  '��' => '쿆',
  '��' => '쿇',
  '��' => '쿈',
  '��' => '쿉',
  '��' => '쿊',
  '��' => '쿋',
  '��' => '깹',
  '��' => '깻',
  '��' => '깼',
  '��' => '깽',
  '��' => '꺄',
  '��' => '꺅',
  '��' => '꺌',
  '��' => '꺼',
  '��' => '꺽',
  '��' => '꺾',
  '��' => '껀',
  '��' => '껄',
  '��' => '껌',
  '��' => '껍',
  '��' => '껏',
  '��' => '껐',
  '��' => '껑',
  '��' => '께',
  '��' => '껙',
  '��' => '껜',
  '��' => '껨',
  '��' => '껫',
  '��' => '껭',
  '��' => '껴',
  '��' => '껸',
  '��' => '껼',
  '��' => '꼇',
  '��' => '꼈',
  '��' => '꼍',
  '��' => '꼐',
  '��' => '꼬',
  '��' => '꼭',
  '��' => '꼰',
  '��' => '꼲',
  '��' => '꼴',
  '��' => '꼼',
  '��' => '꼽',
  '��' => '꼿',
  '��' => '꽁',
  '��' => '꽂',
  '��' => '꽃',
  '��' => '꽈',
  '��' => '꽉',
  '��' => '꽐',
  '��' => '꽜',
  '��' => '꽝',
  '��' => '꽤',
  '��' => '꽥',
  '��' => '꽹',
  '��' => '꾀',
  '��' => '꾄',
  '��' => '꾈',
  '��' => '꾐',
  '��' => '꾑',
  '��' => '꾕',
  '��' => '꾜',
  '��' => '꾸',
  '��' => '꾹',
  '��' => '꾼',
  '��' => '꿀',
  '��' => '꿇',
  '��' => '꿈',
  '��' => '꿉',
  '��' => '꿋',
  '��' => '꿍',
  '��' => '꿎',
  '��' => '꿔',
  '��' => '꿜',
  '��' => '꿨',
  '��' => '꿩',
  '��' => '꿰',
  '��' => '꿱',
  '��' => '꿴',
  '��' => '꿸',
  '��' => '뀀',
  '��' => '뀁',
  '��' => '뀄',
  '��' => '뀌',
  '��' => '뀐',
  '��' => '뀔',
  '��' => '뀜',
  '��' => '뀝',
  '��' => '뀨',
  '��' => '끄',
  '��' => '끅',
  '��' => '끈',
  '��' => '끊',
  '��' => '끌',
  '��' => '끎',
  '��' => '끓',
  '��' => '끔',
  '��' => '끕',
  '��' => '끗',
  '��' => '끙',
  '�A' => '쿌',
  '�B' => '쿍',
  '�C' => '쿎',
  '�D' => '쿏',
  '�E' => '쿐',
  '�F' => '쿑',
  '�G' => '쿒',
  '�H' => '쿓',
  '�I' => '쿔',
  '�J' => '쿕',
  '�K' => '쿖',
  '�L' => '쿗',
  '�M' => '쿘',
  '�N' => '쿙',
  '�O' => '쿚',
  '�P' => '쿛',
  '�Q' => '쿜',
  '�R' => '쿝',
  '�S' => '쿞',
  '�T' => '쿟',
  '�U' => '쿢',
  '�V' => '쿣',
  '�W' => '쿥',
  '�X' => '쿦',
  '�Y' => '쿧',
  '�Z' => '쿩',
  '�a' => '쿪',
  '�b' => '쿫',
  '�c' => '쿬',
  '�d' => '쿭',
  '�e' => '쿮',
  '�f' => '쿯',
  '�g' => '쿲',
  '�h' => '쿴',
  '�i' => '쿶',
  '�j' => '쿷',
  '�k' => '쿸',
  '�l' => '쿹',
  '�m' => '쿺',
  '�n' => '쿻',
  '�o' => '쿽',
  '�p' => '쿾',
  '�q' => '쿿',
  '�r' => '퀁',
  '�s' => '퀂',
  '�t' => '퀃',
  '�u' => '퀅',
  '�v' => '퀆',
  '�w' => '퀇',
  '�x' => '퀈',
  '�y' => '퀉',
  '�z' => '퀊',
  '��' => '퀋',
  '��' => '퀌',
  '��' => '퀍',
  '��' => '퀎',
  '��' => '퀏',
  '��' => '퀐',
  '��' => '퀒',
  '��' => '퀓',
  '��' => '퀔',
  '��' => '퀕',
  '��' => '퀖',
  '��' => '퀗',
  '��' => '퀙',
  '��' => '퀚',
  '��' => '퀛',
  '��' => '퀜',
  '��' => '퀝',
  '��' => '퀞',
  '��' => '퀟',
  '��' => '퀠',
  '��' => '퀡',
  '��' => '퀢',
  '��' => '퀣',
  '��' => '퀤',
  '��' => '퀥',
  '��' => '퀦',
  '��' => '퀧',
  '��' => '퀨',
  '��' => '퀩',
  '��' => '퀪',
  '��' => '퀫',
  '��' => '퀬',
  '��' => '끝',
  '��' => '끼',
  '��' => '끽',
  '��' => '낀',
  '��' => '낄',
  '��' => '낌',
  '��' => '낍',
  '��' => '낏',
  '��' => '낑',
  '��' => '나',
  '��' => '낙',
  '��' => '낚',
  '��' => '난',
  '��' => '낟',
  '��' => '날',
  '��' => '낡',
  '��' => '낢',
  '��' => '남',
  '��' => '납',
  '��' => '낫',
  '��' => '났',
  '��' => '낭',
  '��' => '낮',
  '��' => '낯',
  '��' => '낱',
  '��' => '낳',
  '��' => '내',
  '��' => '낵',
  '��' => '낸',
  '��' => '낼',
  '��' => '냄',
  '��' => '냅',
  '��' => '냇',
  '��' => '냈',
  '��' => '냉',
  '��' => '냐',
  '��' => '냑',
  '��' => '냔',
  '��' => '냘',
  '��' => '냠',
  '��' => '냥',
  '��' => '너',
  '��' => '넉',
  '��' => '넋',
  '��' => '넌',
  '��' => '널',
  '��' => '넒',
  '��' => '넓',
  '��' => '넘',
  '��' => '넙',
  '��' => '넛',
  '��' => '넜',
  '��' => '넝',
  '��' => '넣',
  '��' => '네',
  '��' => '넥',
  '��' => '넨',
  '��' => '넬',
  '��' => '넴',
  '��' => '넵',
  '��' => '넷',
  '��' => '넸',
  '��' => '넹',
  '��' => '녀',
  '��' => '녁',
  '��' => '년',
  '��' => '녈',
  '��' => '념',
  '��' => '녑',
  '��' => '녔',
  '��' => '녕',
  '��' => '녘',
  '��' => '녜',
  '��' => '녠',
  '��' => '노',
  '��' => '녹',
  '��' => '논',
  '��' => '놀',
  '��' => '놂',
  '��' => '놈',
  '��' => '놉',
  '��' => '놋',
  '��' => '농',
  '��' => '높',
  '��' => '놓',
  '��' => '놔',
  '��' => '놘',
  '��' => '놜',
  '��' => '놨',
  '��' => '뇌',
  '��' => '뇐',
  '��' => '뇔',
  '��' => '뇜',
  '��' => '뇝',
  '�A' => '퀮',
  '�B' => '퀯',
  '�C' => '퀰',
  '�D' => '퀱',
  '�E' => '퀲',
  '�F' => '퀳',
  '�G' => '퀶',
  '�H' => '퀷',
  '�I' => '퀹',
  '�J' => '퀺',
  '�K' => '퀻',
  '�L' => '퀽',
  '�M' => '퀾',
  '�N' => '퀿',
  '�O' => '큀',
  '�P' => '큁',
  '�Q' => '큂',
  '�R' => '큃',
  '�S' => '큆',
  '�T' => '큈',
  '�U' => '큊',
  '�V' => '큋',
  '�W' => '큌',
  '�X' => '큍',
  '�Y' => '큎',
  '�Z' => '큏',
  '�a' => '큑',
  '�b' => '큒',
  '�c' => '큓',
  '�d' => '큕',
  '�e' => '큖',
  '�f' => '큗',
  '�g' => '큙',
  '�h' => '큚',
  '�i' => '큛',
  '�j' => '큜',
  '�k' => '큝',
  '�l' => '큞',
  '�m' => '큟',
  '�n' => '큡',
  '�o' => '큢',
  '�p' => '큣',
  '�q' => '큤',
  '�r' => '큥',
  '�s' => '큦',
  '�t' => '큧',
  '�u' => '큨',
  '�v' => '큩',
  '�w' => '큪',
  '�x' => '큫',
  '�y' => '큮',
  '�z' => '큯',
  '��' => '큱',
  '��' => '큲',
  '��' => '큳',
  '��' => '큵',
  '��' => '큶',
  '��' => '큷',
  '��' => '큸',
  '��' => '큹',
  '��' => '큺',
  '��' => '큻',
  '��' => '큾',
  '��' => '큿',
  '��' => '킀',
  '��' => '킂',
  '��' => '킃',
  '��' => '킄',
  '��' => '킅',
  '��' => '킆',
  '��' => '킇',
  '��' => '킈',
  '��' => '킉',
  '��' => '킊',
  '��' => '킋',
  '��' => '킌',
  '��' => '킍',
  '��' => '킎',
  '��' => '킏',
  '��' => '킐',
  '��' => '킑',
  '��' => '킒',
  '��' => '킓',
  '��' => '킔',
  '��' => '뇟',
  '��' => '뇨',
  '��' => '뇩',
  '��' => '뇬',
  '��' => '뇰',
  '��' => '뇹',
  '��' => '뇻',
  '��' => '뇽',
  '��' => '누',
  '��' => '눅',
  '��' => '눈',
  '��' => '눋',
  '��' => '눌',
  '��' => '눔',
  '��' => '눕',
  '��' => '눗',
  '��' => '눙',
  '��' => '눠',
  '��' => '눴',
  '��' => '눼',
  '��' => '뉘',
  '��' => '뉜',
  '��' => '뉠',
  '��' => '뉨',
  '��' => '뉩',
  '��' => '뉴',
  '��' => '뉵',
  '��' => '뉼',
  '��' => '늄',
  '��' => '늅',
  '��' => '늉',
  '��' => '느',
  '��' => '늑',
  '��' => '는',
  '��' => '늘',
  '��' => '늙',
  '��' => '늚',
  '��' => '늠',
  '��' => '늡',
  '��' => '늣',
  '��' => '능',
  '��' => '늦',
  '��' => '늪',
  '��' => '늬',
  '��' => '늰',
  '��' => '늴',
  '��' => '니',
  '��' => '닉',
  '��' => '닌',
  '��' => '닐',
  '��' => '닒',
  '��' => '님',
  '��' => '닙',
  '��' => '닛',
  '��' => '닝',
  '��' => '닢',
  '��' => '다',
  '��' => '닥',
  '��' => '닦',
  '��' => '단',
  '��' => '닫',
  '��' => '달',
  '��' => '닭',
  '��' => '닮',
  '��' => '닯',
  '��' => '닳',
  '��' => '담',
  '��' => '답',
  '��' => '닷',
  '��' => '닸',
  '��' => '당',
  '��' => '닺',
  '��' => '닻',
  '��' => '닿',
  '��' => '대',
  '��' => '댁',
  '��' => '댄',
  '��' => '댈',
  '��' => '댐',
  '��' => '댑',
  '��' => '댓',
  '��' => '댔',
  '��' => '댕',
  '��' => '댜',
  '��' => '더',
  '��' => '덕',
  '��' => '덖',
  '��' => '던',
  '��' => '덛',
  '��' => '덜',
  '��' => '덞',
  '��' => '덟',
  '��' => '덤',
  '��' => '덥',
  '�A' => '킕',
  '�B' => '킖',
  '�C' => '킗',
  '�D' => '킘',
  '�E' => '킙',
  '�F' => '킚',
  '�G' => '킛',
  '�H' => '킜',
  '�I' => '킝',
  '�J' => '킞',
  '�K' => '킟',
  '�L' => '킠',
  '�M' => '킡',
  '�N' => '킢',
  '�O' => '킣',
  '�P' => '킦',
  '�Q' => '킧',
  '�R' => '킩',
  '�S' => '킪',
  '�T' => '킫',
  '�U' => '킭',
  '�V' => '킮',
  '�W' => '킯',
  '�X' => '킰',
  '�Y' => '킱',
  '�Z' => '킲',
  '�a' => '킳',
  '�b' => '킶',
  '�c' => '킸',
  '�d' => '킺',
  '�e' => '킻',
  '�f' => '킼',
  '�g' => '킽',
  '�h' => '킾',
  '�i' => '킿',
  '�j' => '탂',
  '�k' => '탃',
  '�l' => '탅',
  '�m' => '탆',
  '�n' => '탇',
  '�o' => '탊',
  '�p' => '탋',
  '�q' => '탌',
  '�r' => '탍',
  '�s' => '탎',
  '�t' => '탏',
  '�u' => '탒',
  '�v' => '탖',
  '�w' => '탗',
  '�x' => '탘',
  '�y' => '탙',
  '�z' => '탚',
  '��' => '탛',
  '��' => '탞',
  '��' => '탟',
  '��' => '탡',
  '��' => '탢',
  '��' => '탣',
  '��' => '탥',
  '��' => '탦',
  '��' => '탧',
  '��' => '탨',
  '��' => '탩',
  '��' => '탪',
  '��' => '탫',
  '��' => '탮',
  '��' => '탲',
  '��' => '탳',
  '��' => '탴',
  '��' => '탵',
  '��' => '탶',
  '��' => '탷',
  '��' => '탹',
  '��' => '탺',
  '��' => '탻',
  '��' => '탼',
  '��' => '탽',
  '��' => '탾',
  '��' => '탿',
  '��' => '턀',
  '��' => '턁',
  '��' => '턂',
  '��' => '턃',
  '��' => '턄',
  '��' => '덧',
  '��' => '덩',
  '��' => '덫',
  '��' => '덮',
  '��' => '데',
  '��' => '덱',
  '��' => '덴',
  '��' => '델',
  '��' => '뎀',
  '��' => '뎁',
  '��' => '뎃',
  '��' => '뎄',
  '��' => '뎅',
  '��' => '뎌',
  '��' => '뎐',
  '��' => '뎔',
  '��' => '뎠',
  '��' => '뎡',
  '��' => '뎨',
  '��' => '뎬',
  '��' => '도',
  '��' => '독',
  '��' => '돈',
  '��' => '돋',
  '��' => '돌',
  '��' => '돎',
  '��' => '돐',
  '��' => '돔',
  '��' => '돕',
  '��' => '돗',
  '��' => '동',
  '��' => '돛',
  '��' => '돝',
  '��' => '돠',
  '��' => '돤',
  '��' => '돨',
  '��' => '돼',
  '��' => '됐',
  '��' => '되',
  '��' => '된',
  '��' => '될',
  '��' => '됨',
  '��' => '됩',
  '��' => '됫',
  '��' => '됴',
  '��' => '두',
  '��' => '둑',
  '��' => '둔',
  '��' => '둘',
  '��' => '둠',
  '��' => '둡',
  '��' => '둣',
  '��' => '둥',
  '��' => '둬',
  '��' => '뒀',
  '��' => '뒈',
  '��' => '뒝',
  '��' => '뒤',
  '��' => '뒨',
  '��' => '뒬',
  '��' => '뒵',
  '��' => '뒷',
  '��' => '뒹',
  '��' => '듀',
  '��' => '듄',
  '��' => '듈',
  '��' => '듐',
  '��' => '듕',
  '��' => '드',
  '��' => '득',
  '��' => '든',
  '��' => '듣',
  '��' => '들',
  '��' => '듦',
  '��' => '듬',
  '��' => '듭',
  '��' => '듯',
  '��' => '등',
  '��' => '듸',
  '��' => '디',
  '��' => '딕',
  '��' => '딘',
  '��' => '딛',
  '��' => '딜',
  '��' => '딤',
  '��' => '딥',
  '��' => '딧',
  '��' => '딨',
  '��' => '딩',
  '��' => '딪',
  '��' => '따',
  '��' => '딱',
  '��' => '딴',
  '��' => '딸',
  '�A' => '턅',
  '�B' => '턆',
  '�C' => '턇',
  '�D' => '턈',
  '�E' => '턉',
  '�F' => '턊',
  '�G' => '턋',
  '�H' => '턌',
  '�I' => '턎',
  '�J' => '턏',
  '�K' => '턐',
  '�L' => '턑',
  '�M' => '턒',
  '�N' => '턓',
  '�O' => '턔',
  '�P' => '턕',
  '�Q' => '턖',
  '�R' => '턗',
  '�S' => '턘',
  '�T' => '턙',
  '�U' => '턚',
  '�V' => '턛',
  '�W' => '턜',
  '�X' => '턝',
  '�Y' => '턞',
  '�Z' => '턟',
  '�a' => '턠',
  '�b' => '턡',
  '�c' => '턢',
  '�d' => '턣',
  '�e' => '턤',
  '�f' => '턥',
  '�g' => '턦',
  '�h' => '턧',
  '�i' => '턨',
  '�j' => '턩',
  '�k' => '턪',
  '�l' => '턫',
  '�m' => '턬',
  '�n' => '턭',
  '�o' => '턮',
  '�p' => '턯',
  '�q' => '턲',
  '�r' => '턳',
  '�s' => '턵',
  '�t' => '턶',
  '�u' => '턷',
  '�v' => '턹',
  '�w' => '턻',
  '�x' => '턼',
  '�y' => '턽',
  '�z' => '턾',
  '��' => '턿',
  '��' => '텂',
  '��' => '텆',
  '��' => '텇',
  '��' => '텈',
  '��' => '텉',
  '��' => '텊',
  '��' => '텋',
  '��' => '텎',
  '��' => '텏',
  '��' => '텑',
  '��' => '텒',
  '��' => '텓',
  '��' => '텕',
  '��' => '텖',
  '��' => '텗',
  '��' => '텘',
  '��' => '텙',
  '��' => '텚',
  '��' => '텛',
  '��' => '텞',
  '��' => '텠',
  '��' => '텢',
  '��' => '텣',
  '��' => '텤',
  '��' => '텥',
  '��' => '텦',
  '��' => '텧',
  '��' => '텩',
  '��' => '텪',
  '��' => '텫',
  '��' => '텭',
  '��' => '땀',
  '��' => '땁',
  '��' => '땃',
  '��' => '땄',
  '��' => '땅',
  '��' => '땋',
  '��' => '때',
  '��' => '땍',
  '��' => '땐',
  '��' => '땔',
  '��' => '땜',
  '��' => '땝',
  '��' => '땟',
  '��' => '땠',
  '��' => '땡',
  '��' => '떠',
  '��' => '떡',
  '��' => '떤',
  '��' => '떨',
  '��' => '떪',
  '��' => '떫',
  '��' => '떰',
  '��' => '떱',
  '��' => '떳',
  '��' => '떴',
  '��' => '떵',
  '��' => '떻',
  '��' => '떼',
  '��' => '떽',
  '��' => '뗀',
  '��' => '뗄',
  '��' => '뗌',
  '��' => '뗍',
  '��' => '뗏',
  '��' => '뗐',
  '��' => '뗑',
  '��' => '뗘',
  '��' => '뗬',
  '��' => '또',
  '��' => '똑',
  '��' => '똔',
  '��' => '똘',
  '��' => '똥',
  '��' => '똬',
  '��' => '똴',
  '��' => '뙈',
  '��' => '뙤',
  '��' => '뙨',
  '��' => '뚜',
  '��' => '뚝',
  '��' => '뚠',
  '��' => '뚤',
  '��' => '뚫',
  '��' => '뚬',
  '��' => '뚱',
  '��' => '뛔',
  '��' => '뛰',
  '��' => '뛴',
  '��' => '뛸',
  '��' => '뜀',
  '��' => '뜁',
  '��' => '뜅',
  '��' => '뜨',
  '��' => '뜩',
  '��' => '뜬',
  '��' => '뜯',
  '��' => '뜰',
  '��' => '뜸',
  '��' => '뜹',
  '��' => '뜻',
  '��' => '띄',
  '��' => '띈',
  '��' => '띌',
  '��' => '띔',
  '��' => '띕',
  '��' => '띠',
  '��' => '띤',
  '��' => '띨',
  '��' => '띰',
  '��' => '띱',
  '��' => '띳',
  '��' => '띵',
  '��' => '라',
  '��' => '락',
  '��' => '란',
  '��' => '랄',
  '��' => '람',
  '��' => '랍',
  '��' => '랏',
  '��' => '랐',
  '��' => '랑',
  '��' => '랒',
  '��' => '랖',
  '��' => '랗',
  '�A' => '텮',
  '�B' => '텯',
  '�C' => '텰',
  '�D' => '텱',
  '�E' => '텲',
  '�F' => '텳',
  '�G' => '텴',
  '�H' => '텵',
  '�I' => '텶',
  '�J' => '텷',
  '�K' => '텸',
  '�L' => '텹',
  '�M' => '텺',
  '�N' => '텻',
  '�O' => '텽',
  '�P' => '텾',
  '�Q' => '텿',
  '�R' => '톀',
  '�S' => '톁',
  '�T' => '톂',
  '�U' => '톃',
  '�V' => '톅',
  '�W' => '톆',
  '�X' => '톇',
  '�Y' => '톉',
  '�Z' => '톊',
  '�a' => '톋',
  '�b' => '톌',
  '�c' => '톍',
  '�d' => '톎',
  '�e' => '톏',
  '�f' => '톐',
  '�g' => '톑',
  '�h' => '톒',
  '�i' => '톓',
  '�j' => '톔',
  '�k' => '톕',
  '�l' => '톖',
  '�m' => '톗',
  '�n' => '톘',
  '�o' => '톙',
  '�p' => '톚',
  '�q' => '톛',
  '�r' => '톜',
  '�s' => '톝',
  '�t' => '톞',
  '�u' => '톟',
  '�v' => '톢',
  '�w' => '톣',
  '�x' => '톥',
  '�y' => '톦',
  '�z' => '톧',
  '��' => '톩',
  '��' => '톪',
  '��' => '톫',
  '��' => '톬',
  '��' => '톭',
  '��' => '톮',
  '��' => '톯',
  '��' => '톲',
  '��' => '톴',
  '��' => '톶',
  '��' => '톷',
  '��' => '톸',
  '��' => '톹',
  '��' => '톻',
  '��' => '톽',
  '��' => '톾',
  '��' => '톿',
  '��' => '퇁',
  '��' => '퇂',
  '��' => '퇃',
  '��' => '퇄',
  '��' => '퇅',
  '��' => '퇆',
  '��' => '퇇',
  '��' => '퇈',
  '��' => '퇉',
  '��' => '퇊',
  '��' => '퇋',
  '��' => '퇌',
  '��' => '퇍',
  '��' => '퇎',
  '��' => '퇏',
  '��' => '래',
  '��' => '랙',
  '��' => '랜',
  '��' => '랠',
  '��' => '램',
  '��' => '랩',
  '��' => '랫',
  '��' => '랬',
  '��' => '랭',
  '��' => '랴',
  '��' => '략',
  '��' => '랸',
  '��' => '럇',
  '��' => '량',
  '��' => '러',
  '��' => '럭',
  '��' => '런',
  '��' => '럴',
  '��' => '럼',
  '��' => '럽',
  '��' => '럿',
  '��' => '렀',
  '��' => '렁',
  '��' => '렇',
  '��' => '레',
  '��' => '렉',
  '��' => '렌',
  '��' => '렐',
  '��' => '렘',
  '��' => '렙',
  '��' => '렛',
  '��' => '렝',
  '��' => '려',
  '��' => '력',
  '��' => '련',
  '��' => '렬',
  '��' => '렴',
  '��' => '렵',
  '��' => '렷',
  '��' => '렸',
  '��' => '령',
  '��' => '례',
  '��' => '롄',
  '��' => '롑',
  '��' => '롓',
  '��' => '로',
  '��' => '록',
  '��' => '론',
  '��' => '롤',
  '��' => '롬',
  '��' => '롭',
  '��' => '롯',
  '��' => '롱',
  '��' => '롸',
  '��' => '롼',
  '��' => '뢍',
  '��' => '뢨',
  '��' => '뢰',
  '��' => '뢴',
  '��' => '뢸',
  '��' => '룀',
  '��' => '룁',
  '��' => '룃',
  '��' => '룅',
  '��' => '료',
  '��' => '룐',
  '��' => '룔',
  '��' => '룝',
  '��' => '룟',
  '��' => '룡',
  '��' => '루',
  '��' => '룩',
  '��' => '룬',
  '��' => '룰',
  '��' => '룸',
  '��' => '룹',
  '��' => '룻',
  '��' => '룽',
  '��' => '뤄',
  '��' => '뤘',
  '��' => '뤠',
  '��' => '뤼',
  '��' => '뤽',
  '��' => '륀',
  '��' => '륄',
  '��' => '륌',
  '��' => '륏',
  '��' => '륑',
  '��' => '류',
  '��' => '륙',
  '��' => '륜',
  '��' => '률',
  '��' => '륨',
  '��' => '륩',
  '�A' => '퇐',
  '�B' => '퇑',
  '�C' => '퇒',
  '�D' => '퇓',
  '�E' => '퇔',
  '�F' => '퇕',
  '�G' => '퇖',
  '�H' => '퇗',
  '�I' => '퇙',
  '�J' => '퇚',
  '�K' => '퇛',
  '�L' => '퇜',
  '�M' => '퇝',
  '�N' => '퇞',
  '�O' => '퇟',
  '�P' => '퇠',
  '�Q' => '퇡',
  '�R' => '퇢',
  '�S' => '퇣',
  '�T' => '퇤',
  '�U' => '퇥',
  '�V' => '퇦',
  '�W' => '퇧',
  '�X' => '퇨',
  '�Y' => '퇩',
  '�Z' => '퇪',
  '�a' => '퇫',
  '�b' => '퇬',
  '�c' => '퇭',
  '�d' => '퇮',
  '�e' => '퇯',
  '�f' => '퇰',
  '�g' => '퇱',
  '�h' => '퇲',
  '�i' => '퇳',
  '�j' => '퇵',
  '�k' => '퇶',
  '�l' => '퇷',
  '�m' => '퇹',
  '�n' => '퇺',
  '�o' => '퇻',
  '�p' => '퇼',
  '�q' => '퇽',
  '�r' => '퇾',
  '�s' => '퇿',
  '�t' => '툀',
  '�u' => '툁',
  '�v' => '툂',
  '�w' => '툃',
  '�x' => '툄',
  '�y' => '툅',
  '�z' => '툆',
  '��' => '툈',
  '��' => '툊',
  '��' => '툋',
  '��' => '툌',
  '��' => '툍',
  '��' => '툎',
  '��' => '툏',
  '��' => '툑',
  '��' => '툒',
  '��' => '툓',
  '��' => '툔',
  '��' => '툕',
  '��' => '툖',
  '��' => '툗',
  '��' => '툘',
  '��' => '툙',
  '��' => '툚',
  '��' => '툛',
  '��' => '툜',
  '��' => '툝',
  '��' => '툞',
  '��' => '툟',
  '��' => '툠',
  '��' => '툡',
  '��' => '툢',
  '��' => '툣',
  '��' => '툤',
  '��' => '툥',
  '��' => '툦',
  '��' => '툧',
  '��' => '툨',
  '��' => '툩',
  '��' => '륫',
  '��' => '륭',
  '��' => '르',
  '��' => '륵',
  '��' => '른',
  '��' => '를',
  '��' => '름',
  '��' => '릅',
  '��' => '릇',
  '��' => '릉',
  '��' => '릊',
  '��' => '릍',
  '��' => '릎',
  '��' => '리',
  '��' => '릭',
  '��' => '린',
  '��' => '릴',
  '��' => '림',
  '��' => '립',
  '��' => '릿',
  '��' => '링',
  '��' => '마',
  '��' => '막',
  '��' => '만',
  '��' => '많',
  '��' => '맏',
  '��' => '말',
  '��' => '맑',
  '��' => '맒',
  '��' => '맘',
  '��' => '맙',
  '��' => '맛',
  '��' => '망',
  '��' => '맞',
  '��' => '맡',
  '��' => '맣',
  '��' => '매',
  '��' => '맥',
  '��' => '맨',
  '��' => '맬',
  '��' => '맴',
  '��' => '맵',
  '��' => '맷',
  '��' => '맸',
  '��' => '맹',
  '��' => '맺',
  '��' => '먀',
  '��' => '먁',
  '��' => '먈',
  '��' => '먕',
  '��' => '머',
  '��' => '먹',
  '��' => '먼',
  '��' => '멀',
  '��' => '멂',
  '��' => '멈',
  '��' => '멉',
  '��' => '멋',
  '��' => '멍',
  '��' => '멎',
  '��' => '멓',
  '��' => '메',
  '��' => '멕',
  '��' => '멘',
  '��' => '멜',
  '��' => '멤',
  '��' => '멥',
  '��' => '멧',
  '��' => '멨',
  '��' => '멩',
  '��' => '며',
  '��' => '멱',
  '��' => '면',
  '��' => '멸',
  '��' => '몃',
  '��' => '몄',
  '��' => '명',
  '��' => '몇',
  '��' => '몌',
  '��' => '모',
  '��' => '목',
  '��' => '몫',
  '��' => '몬',
  '��' => '몰',
  '��' => '몲',
  '��' => '몸',
  '��' => '몹',
  '��' => '못',
  '��' => '몽',
  '��' => '뫄',
  '��' => '뫈',
  '��' => '뫘',
  '��' => '뫙',
  '��' => '뫼',
  '�A' => '툪',
  '�B' => '툫',
  '�C' => '툮',
  '�D' => '툯',
  '�E' => '툱',
  '�F' => '툲',
  '�G' => '툳',
  '�H' => '툵',
  '�I' => '툶',
  '�J' => '툷',
  '�K' => '툸',
  '�L' => '툹',
  '�M' => '툺',
  '�N' => '툻',
  '�O' => '툾',
  '�P' => '퉀',
  '�Q' => '퉂',
  '�R' => '퉃',
  '�S' => '퉄',
  '�T' => '퉅',
  '�U' => '퉆',
  '�V' => '퉇',
  '�W' => '퉉',
  '�X' => '퉊',
  '�Y' => '퉋',
  '�Z' => '퉌',
  '�a' => '퉍',
  '�b' => '퉎',
  '�c' => '퉏',
  '�d' => '퉐',
  '�e' => '퉑',
  '�f' => '퉒',
  '�g' => '퉓',
  '�h' => '퉔',
  '�i' => '퉕',
  '�j' => '퉖',
  '�k' => '퉗',
  '�l' => '퉘',
  '�m' => '퉙',
  '�n' => '퉚',
  '�o' => '퉛',
  '�p' => '퉝',
  '�q' => '퉞',
  '�r' => '퉟',
  '�s' => '퉠',
  '�t' => '퉡',
  '�u' => '퉢',
  '�v' => '퉣',
  '�w' => '퉥',
  '�x' => '퉦',
  '�y' => '퉧',
  '�z' => '퉨',
  '��' => '퉩',
  '��' => '퉪',
  '��' => '퉫',
  '��' => '퉬',
  '��' => '퉭',
  '��' => '퉮',
  '��' => '퉯',
  '��' => '퉰',
  '��' => '퉱',
  '��' => '퉲',
  '��' => '퉳',
  '��' => '퉴',
  '��' => '퉵',
  '��' => '퉶',
  '��' => '퉷',
  '��' => '퉸',
  '��' => '퉹',
  '��' => '퉺',
  '��' => '퉻',
  '��' => '퉼',
  '��' => '퉽',
  '��' => '퉾',
  '��' => '퉿',
  '��' => '튂',
  '��' => '튃',
  '��' => '튅',
  '��' => '튆',
  '��' => '튇',
  '��' => '튉',
  '��' => '튊',
  '��' => '튋',
  '��' => '튌',
  '��' => '묀',
  '��' => '묄',
  '��' => '묍',
  '��' => '묏',
  '��' => '묑',
  '��' => '묘',
  '��' => '묜',
  '��' => '묠',
  '��' => '묩',
  '��' => '묫',
  '��' => '무',
  '��' => '묵',
  '��' => '묶',
  '��' => '문',
  '��' => '묻',
  '��' => '물',
  '��' => '묽',
  '��' => '묾',
  '��' => '뭄',
  '��' => '뭅',
  '��' => '뭇',
  '��' => '뭉',
  '��' => '뭍',
  '��' => '뭏',
  '��' => '뭐',
  '��' => '뭔',
  '��' => '뭘',
  '��' => '뭡',
  '��' => '뭣',
  '��' => '뭬',
  '��' => '뮈',
  '��' => '뮌',
  '��' => '뮐',
  '��' => '뮤',
  '��' => '뮨',
  '��' => '뮬',
  '��' => '뮴',
  '��' => '뮷',
  '��' => '므',
  '��' => '믄',
  '��' => '믈',
  '��' => '믐',
  '��' => '믓',
  '��' => '미',
  '��' => '믹',
  '��' => '민',
  '��' => '믿',
  '��' => '밀',
  '��' => '밂',
  '��' => '밈',
  '��' => '밉',
  '��' => '밋',
  '��' => '밌',
  '��' => '밍',
  '��' => '및',
  '��' => '밑',
  '��' => '바',
  '��' => '박',
  '��' => '밖',
  '��' => '밗',
  '��' => '반',
  '��' => '받',
  '��' => '발',
  '��' => '밝',
  '��' => '밞',
  '��' => '밟',
  '��' => '밤',
  '��' => '밥',
  '��' => '밧',
  '��' => '방',
  '��' => '밭',
  '��' => '배',
  '��' => '백',
  '��' => '밴',
  '��' => '밸',
  '��' => '뱀',
  '��' => '뱁',
  '��' => '뱃',
  '��' => '뱄',
  '��' => '뱅',
  '��' => '뱉',
  '��' => '뱌',
  '��' => '뱍',
  '��' => '뱐',
  '��' => '뱝',
  '��' => '버',
  '��' => '벅',
  '��' => '번',
  '��' => '벋',
  '��' => '벌',
  '��' => '벎',
  '��' => '범',
  '��' => '법',
  '��' => '벗',
  '�A' => '튍',
  '�B' => '튎',
  '�C' => '튏',
  '�D' => '튒',
  '�E' => '튓',
  '�F' => '튔',
  '�G' => '튖',
  '�H' => '튗',
  '�I' => '튘',
  '�J' => '튙',
  '�K' => '튚',
  '�L' => '튛',
  '�M' => '튝',
  '�N' => '튞',
  '�O' => '튟',
  '�P' => '튡',
  '�Q' => '튢',
  '�R' => '튣',
  '�S' => '튥',
  '�T' => '튦',
  '�U' => '튧',
  '�V' => '튨',
  '�W' => '튩',
  '�X' => '튪',
  '�Y' => '튫',
  '�Z' => '튭',
  '�a' => '튮',
  '�b' => '튯',
  '�c' => '튰',
  '�d' => '튲',
  '�e' => '튳',
  '�f' => '튴',
  '�g' => '튵',
  '�h' => '튶',
  '�i' => '튷',
  '�j' => '튺',
  '�k' => '튻',
  '�l' => '튽',
  '�m' => '튾',
  '�n' => '틁',
  '�o' => '틃',
  '�p' => '틄',
  '�q' => '틅',
  '�r' => '틆',
  '�s' => '틇',
  '�t' => '틊',
  '�u' => '틌',
  '�v' => '틍',
  '�w' => '틎',
  '�x' => '틏',
  '�y' => '틐',
  '�z' => '틑',
  '��' => '틒',
  '��' => '틓',
  '��' => '틕',
  '��' => '틖',
  '��' => '틗',
  '��' => '틙',
  '��' => '틚',
  '��' => '틛',
  '��' => '틝',
  '��' => '틞',
  '��' => '틟',
  '��' => '틠',
  '��' => '틡',
  '��' => '틢',
  '��' => '틣',
  '��' => '틦',
  '��' => '틧',
  '��' => '틨',
  '��' => '틩',
  '��' => '틪',
  '��' => '틫',
  '��' => '틬',
  '��' => '틭',
  '��' => '틮',
  '��' => '틯',
  '��' => '틲',
  '��' => '틳',
  '��' => '틵',
  '��' => '틶',
  '��' => '틷',
  '��' => '틹',
  '��' => '틺',
  '��' => '벙',
  '��' => '벚',
  '��' => '베',
  '��' => '벡',
  '��' => '벤',
  '��' => '벧',
  '��' => '벨',
  '��' => '벰',
  '��' => '벱',
  '��' => '벳',
  '��' => '벴',
  '��' => '벵',
  '��' => '벼',
  '��' => '벽',
  '��' => '변',
  '��' => '별',
  '��' => '볍',
  '��' => '볏',
  '��' => '볐',
  '��' => '병',
  '��' => '볕',
  '��' => '볘',
  '��' => '볜',
  '��' => '보',
  '��' => '복',
  '��' => '볶',
  '��' => '본',
  '��' => '볼',
  '��' => '봄',
  '��' => '봅',
  '��' => '봇',
  '��' => '봉',
  '��' => '봐',
  '��' => '봔',
  '��' => '봤',
  '��' => '봬',
  '��' => '뵀',
  '��' => '뵈',
  '��' => '뵉',
  '��' => '뵌',
  '��' => '뵐',
  '��' => '뵘',
  '��' => '뵙',
  '��' => '뵤',
  '��' => '뵨',
  '��' => '부',
  '��' => '북',
  '��' => '분',
  '��' => '붇',
  '��' => '불',
  '��' => '붉',
  '��' => '붊',
  '��' => '붐',
  '��' => '붑',
  '��' => '붓',
  '��' => '붕',
  '��' => '붙',
  '��' => '붚',
  '��' => '붜',
  '��' => '붤',
  '��' => '붰',
  '��' => '붸',
  '��' => '뷔',
  '��' => '뷕',
  '��' => '뷘',
  '��' => '뷜',
  '��' => '뷩',
  '��' => '뷰',
  '��' => '뷴',
  '��' => '뷸',
  '��' => '븀',
  '��' => '븃',
  '��' => '븅',
  '��' => '브',
  '��' => '븍',
  '��' => '븐',
  '��' => '블',
  '��' => '븜',
  '��' => '븝',
  '��' => '븟',
  '��' => '비',
  '��' => '빅',
  '��' => '빈',
  '��' => '빌',
  '��' => '빎',
  '��' => '빔',
  '��' => '빕',
  '��' => '빗',
  '��' => '빙',
  '��' => '빚',
  '��' => '빛',
  '��' => '빠',
  '��' => '빡',
  '��' => '빤',
  '�A' => '틻',
  '�B' => '틼',
  '�C' => '틽',
  '�D' => '틾',
  '�E' => '틿',
  '�F' => '팂',
  '�G' => '팄',
  '�H' => '팆',
  '�I' => '팇',
  '�J' => '팈',
  '�K' => '팉',
  '�L' => '팊',
  '�M' => '팋',
  '�N' => '팏',
  '�O' => '팑',
  '�P' => '팒',
  '�Q' => '팓',
  '�R' => '팕',
  '�S' => '팗',
  '�T' => '팘',
  '�U' => '팙',
  '�V' => '팚',
  '�W' => '팛',
  '�X' => '팞',
  '�Y' => '팢',
  '�Z' => '팣',
  '�a' => '팤',
  '�b' => '팦',
  '�c' => '팧',
  '�d' => '팪',
  '�e' => '팫',
  '�f' => '팭',
  '�g' => '팮',
  '�h' => '팯',
  '�i' => '팱',
  '�j' => '팲',
  '�k' => '팳',
  '�l' => '팴',
  '�m' => '팵',
  '�n' => '팶',
  '�o' => '팷',
  '�p' => '팺',
  '�q' => '팾',
  '�r' => '팿',
  '�s' => '퍀',
  '�t' => '퍁',
  '�u' => '퍂',
  '�v' => '퍃',
  '�w' => '퍆',
  '�x' => '퍇',
  '�y' => '퍈',
  '�z' => '퍉',
  '��' => '퍊',
  '��' => '퍋',
  '��' => '퍌',
  '��' => '퍍',
  '��' => '퍎',
  '��' => '퍏',
  '��' => '퍐',
  '��' => '퍑',
  '��' => '퍒',
  '��' => '퍓',
  '��' => '퍔',
  '��' => '퍕',
  '��' => '퍖',
  '��' => '퍗',
  '��' => '퍘',
  '��' => '퍙',
  '��' => '퍚',
  '��' => '퍛',
  '��' => '퍜',
  '��' => '퍝',
  '��' => '퍞',
  '��' => '퍟',
  '��' => '퍠',
  '��' => '퍡',
  '��' => '퍢',
  '��' => '퍣',
  '��' => '퍤',
  '��' => '퍥',
  '��' => '퍦',
  '��' => '퍧',
  '��' => '퍨',
  '��' => '퍩',
  '��' => '빨',
  '��' => '빪',
  '��' => '빰',
  '��' => '빱',
  '��' => '빳',
  '��' => '빴',
  '��' => '빵',
  '��' => '빻',
  '��' => '빼',
  '��' => '빽',
  '��' => '뺀',
  '��' => '뺄',
  '��' => '뺌',
  '��' => '뺍',
  '��' => '뺏',
  '��' => '뺐',
  '��' => '뺑',
  '��' => '뺘',
  '��' => '뺙',
  '��' => '뺨',
  '��' => '뻐',
  '��' => '뻑',
  '��' => '뻔',
  '��' => '뻗',
  '��' => '뻘',
  '��' => '뻠',
  '��' => '뻣',
  '��' => '뻤',
  '��' => '뻥',
  '��' => '뻬',
  '��' => '뼁',
  '��' => '뼈',
  '��' => '뼉',
  '��' => '뼘',
  '��' => '뼙',
  '��' => '뼛',
  '��' => '뼜',
  '��' => '뼝',
  '��' => '뽀',
  '��' => '뽁',
  '��' => '뽄',
  '��' => '뽈',
  '��' => '뽐',
  '��' => '뽑',
  '��' => '뽕',
  '��' => '뾔',
  '��' => '뾰',
  '��' => '뿅',
  '��' => '뿌',
  '��' => '뿍',
  '��' => '뿐',
  '��' => '뿔',
  '��' => '뿜',
  '��' => '뿟',
  '��' => '뿡',
  '��' => '쀼',
  '��' => '쁑',
  '��' => '쁘',
  '��' => '쁜',
  '��' => '쁠',
  '��' => '쁨',
  '��' => '쁩',
  '��' => '삐',
  '��' => '삑',
  '��' => '삔',
  '��' => '삘',
  '��' => '삠',
  '��' => '삡',
  '��' => '삣',
  '��' => '삥',
  '��' => '사',
  '��' => '삭',
  '��' => '삯',
  '��' => '산',
  '��' => '삳',
  '��' => '살',
  '��' => '삵',
  '��' => '삶',
  '��' => '삼',
  '��' => '삽',
  '��' => '삿',
  '��' => '샀',
  '��' => '상',
  '��' => '샅',
  '��' => '새',
  '��' => '색',
  '��' => '샌',
  '��' => '샐',
  '��' => '샘',
  '��' => '샙',
  '��' => '샛',
  '��' => '샜',
  '��' => '생',
  '��' => '샤',
  '�A' => '퍪',
  '�B' => '퍫',
  '�C' => '퍬',
  '�D' => '퍭',
  '�E' => '퍮',
  '�F' => '퍯',
  '�G' => '퍰',
  '�H' => '퍱',
  '�I' => '퍲',
  '�J' => '퍳',
  '�K' => '퍴',
  '�L' => '퍵',
  '�M' => '퍶',
  '�N' => '퍷',
  '�O' => '퍸',
  '�P' => '퍹',
  '�Q' => '퍺',
  '�R' => '퍻',
  '�S' => '퍾',
  '�T' => '퍿',
  '�U' => '펁',
  '�V' => '펂',
  '�W' => '펃',
  '�X' => '펅',
  '�Y' => '펆',
  '�Z' => '펇',
  '�a' => '펈',
  '�b' => '펉',
  '�c' => '펊',
  '�d' => '펋',
  '�e' => '펎',
  '�f' => '펒',
  '�g' => '펓',
  '�h' => '펔',
  '�i' => '펕',
  '�j' => '펖',
  '�k' => '펗',
  '�l' => '펚',
  '�m' => '펛',
  '�n' => '펝',
  '�o' => '펞',
  '�p' => '펟',
  '�q' => '펡',
  '�r' => '펢',
  '�s' => '펣',
  '�t' => '펤',
  '�u' => '펥',
  '�v' => '펦',
  '�w' => '펧',
  '�x' => '펪',
  '�y' => '펬',
  '�z' => '펮',
  '��' => '펯',
  '��' => '펰',
  '��' => '펱',
  '��' => '펲',
  '��' => '펳',
  '��' => '펵',
  '��' => '펶',
  '��' => '펷',
  '��' => '펹',
  '��' => '펺',
  '��' => '펻',
  '��' => '펽',
  '��' => '펾',
  '��' => '펿',
  '��' => '폀',
  '��' => '폁',
  '��' => '폂',
  '��' => '폃',
  '��' => '폆',
  '��' => '폇',
  '��' => '폊',
  '��' => '폋',
  '��' => '폌',
  '��' => '폍',
  '��' => '폎',
  '��' => '폏',
  '��' => '폑',
  '��' => '폒',
  '��' => '폓',
  '��' => '폔',
  '��' => '폕',
  '��' => '폖',
  '��' => '샥',
  '��' => '샨',
  '��' => '샬',
  '��' => '샴',
  '��' => '샵',
  '��' => '샷',
  '��' => '샹',
  '��' => '섀',
  '��' => '섄',
  '��' => '섈',
  '��' => '섐',
  '��' => '섕',
  '��' => '서',
  '��' => '석',
  '��' => '섞',
  '��' => '섟',
  '��' => '선',
  '��' => '섣',
  '��' => '설',
  '��' => '섦',
  '��' => '섧',
  '��' => '섬',
  '��' => '섭',
  '��' => '섯',
  '��' => '섰',
  '��' => '성',
  '��' => '섶',
  '��' => '세',
  '��' => '섹',
  '��' => '센',
  '��' => '셀',
  '��' => '셈',
  '��' => '셉',
  '��' => '셋',
  '��' => '셌',
  '��' => '셍',
  '��' => '셔',
  '��' => '셕',
  '��' => '션',
  '��' => '셜',
  '��' => '셤',
  '��' => '셥',
  '��' => '셧',
  '��' => '셨',
  '��' => '셩',
  '��' => '셰',
  '��' => '셴',
  '��' => '셸',
  '��' => '솅',
  '��' => '소',
  '��' => '속',
  '��' => '솎',
  '��' => '손',
  '��' => '솔',
  '��' => '솖',
  '��' => '솜',
  '��' => '솝',
  '��' => '솟',
  '��' => '송',
  '��' => '솥',
  '��' => '솨',
  '��' => '솩',
  '��' => '솬',
  '��' => '솰',
  '��' => '솽',
  '��' => '쇄',
  '��' => '쇈',
  '��' => '쇌',
  '��' => '쇔',
  '��' => '쇗',
  '��' => '쇘',
  '��' => '쇠',
  '��' => '쇤',
  '��' => '쇨',
  '��' => '쇰',
  '��' => '쇱',
  '��' => '쇳',
  '��' => '쇼',
  '��' => '쇽',
  '��' => '숀',
  '��' => '숄',
  '��' => '숌',
  '��' => '숍',
  '��' => '숏',
  '��' => '숑',
  '��' => '수',
  '��' => '숙',
  '��' => '순',
  '��' => '숟',
  '��' => '술',
  '��' => '숨',
  '��' => '숩',
  '��' => '숫',
  '��' => '숭',
  '�A' => '폗',
  '�B' => '폙',
  '�C' => '폚',
  '�D' => '폛',
  '�E' => '폜',
  '�F' => '폝',
  '�G' => '폞',
  '�H' => '폟',
  '�I' => '폠',
  '�J' => '폢',
  '�K' => '폤',
  '�L' => '폥',
  '�M' => '폦',
  '�N' => '폧',
  '�O' => '폨',
  '�P' => '폩',
  '�Q' => '폪',
  '�R' => '폫',
  '�S' => '폮',
  '�T' => '폯',
  '�U' => '폱',
  '�V' => '폲',
  '�W' => '폳',
  '�X' => '폵',
  '�Y' => '폶',
  '�Z' => '폷',
  '�a' => '폸',
  '�b' => '폹',
  '�c' => '폺',
  '�d' => '폻',
  '�e' => '폾',
  '�f' => '퐀',
  '�g' => '퐂',
  '�h' => '퐃',
  '�i' => '퐄',
  '�j' => '퐅',
  '�k' => '퐆',
  '�l' => '퐇',
  '�m' => '퐉',
  '�n' => '퐊',
  '�o' => '퐋',
  '�p' => '퐌',
  '�q' => '퐍',
  '�r' => '퐎',
  '�s' => '퐏',
  '�t' => '퐐',
  '�u' => '퐑',
  '�v' => '퐒',
  '�w' => '퐓',
  '�x' => '퐔',
  '�y' => '퐕',
  '�z' => '퐖',
  '��' => '퐗',
  '��' => '퐘',
  '��' => '퐙',
  '��' => '퐚',
  '��' => '퐛',
  '��' => '퐜',
  '��' => '퐞',
  '��' => '퐟',
  '��' => '퐠',
  '��' => '퐡',
  '��' => '퐢',
  '��' => '퐣',
  '��' => '퐤',
  '��' => '퐥',
  '��' => '퐦',
  '��' => '퐧',
  '��' => '퐨',
  '��' => '퐩',
  '��' => '퐪',
  '��' => '퐫',
  '��' => '퐬',
  '��' => '퐭',
  '��' => '퐮',
  '��' => '퐯',
  '��' => '퐰',
  '��' => '퐱',
  '��' => '퐲',
  '��' => '퐳',
  '��' => '퐴',
  '��' => '퐵',
  '��' => '퐶',
  '��' => '퐷',
  '��' => '숯',
  '��' => '숱',
  '��' => '숲',
  '��' => '숴',
  '��' => '쉈',
  '��' => '쉐',
  '��' => '쉑',
  '��' => '쉔',
  '��' => '쉘',
  '��' => '쉠',
  '��' => '쉥',
  '��' => '쉬',
  '��' => '쉭',
  '��' => '쉰',
  '��' => '쉴',
  '��' => '쉼',
  '��' => '쉽',
  '��' => '쉿',
  '��' => '슁',
  '��' => '슈',
  '��' => '슉',
  '��' => '슐',
  '��' => '슘',
  '��' => '슛',
  '��' => '슝',
  '��' => '스',
  '��' => '슥',
  '��' => '슨',
  '��' => '슬',
  '��' => '슭',
  '��' => '슴',
  '��' => '습',
  '��' => '슷',
  '��' => '승',
  '��' => '시',
  '��' => '식',
  '��' => '신',
  '��' => '싣',
  '��' => '실',
  '��' => '싫',
  '��' => '심',
  '��' => '십',
  '��' => '싯',
  '��' => '싱',
  '��' => '싶',
  '��' => '싸',
  '��' => '싹',
  '��' => '싻',
  '��' => '싼',
  '��' => '쌀',
  '��' => '쌈',
  '��' => '쌉',
  '��' => '쌌',
  '��' => '쌍',
  '��' => '쌓',
  '��' => '쌔',
  '��' => '쌕',
  '��' => '쌘',
  '��' => '쌜',
  '��' => '쌤',
  '��' => '쌥',
  '��' => '쌨',
  '��' => '쌩',
  '��' => '썅',
  '��' => '써',
  '��' => '썩',
  '��' => '썬',
  '��' => '썰',
  '��' => '썲',
  '��' => '썸',
  '��' => '썹',
  '��' => '썼',
  '��' => '썽',
  '��' => '쎄',
  '��' => '쎈',
  '��' => '쎌',
  '��' => '쏀',
  '��' => '쏘',
  '��' => '쏙',
  '��' => '쏜',
  '��' => '쏟',
  '��' => '쏠',
  '��' => '쏢',
  '��' => '쏨',
  '��' => '쏩',
  '��' => '쏭',
  '��' => '쏴',
  '��' => '쏵',
  '��' => '쏸',
  '��' => '쐈',
  '��' => '쐐',
  '��' => '쐤',
  '��' => '쐬',
  '��' => '쐰',
  '�A' => '퐸',
  '�B' => '퐹',
  '�C' => '퐺',
  '�D' => '퐻',
  '�E' => '퐼',
  '�F' => '퐽',
  '�G' => '퐾',
  '�H' => '퐿',
  '�I' => '푁',
  '�J' => '푂',
  '�K' => '푃',
  '�L' => '푅',
  '�M' => '푆',
  '�N' => '푇',
  '�O' => '푈',
  '�P' => '푉',
  '�Q' => '푊',
  '�R' => '푋',
  '�S' => '푌',
  '�T' => '푍',
  '�U' => '푎',
  '�V' => '푏',
  '�W' => '푐',
  '�X' => '푑',
  '�Y' => '푒',
  '�Z' => '푓',
  '�a' => '푔',
  '�b' => '푕',
  '�c' => '푖',
  '�d' => '푗',
  '�e' => '푘',
  '�f' => '푙',
  '�g' => '푚',
  '�h' => '푛',
  '�i' => '푝',
  '�j' => '푞',
  '�k' => '푟',
  '�l' => '푡',
  '�m' => '푢',
  '�n' => '푣',
  '�o' => '푥',
  '�p' => '푦',
  '�q' => '푧',
  '�r' => '푨',
  '�s' => '푩',
  '�t' => '푪',
  '�u' => '푫',
  '�v' => '푬',
  '�w' => '푮',
  '�x' => '푰',
  '�y' => '푱',
  '�z' => '푲',
  '��' => '푳',
  '��' => '푴',
  '��' => '푵',
  '��' => '푶',
  '��' => '푷',
  '��' => '푺',
  '��' => '푻',
  '��' => '푽',
  '��' => '푾',
  '��' => '풁',
  '��' => '풃',
  '��' => '풄',
  '��' => '풅',
  '��' => '풆',
  '��' => '풇',
  '��' => '풊',
  '��' => '풌',
  '��' => '풎',
  '��' => '풏',
  '��' => '풐',
  '��' => '풑',
  '��' => '풒',
  '��' => '풓',
  '��' => '풕',
  '��' => '풖',
  '��' => '풗',
  '��' => '풘',
  '��' => '풙',
  '��' => '풚',
  '��' => '풛',
  '��' => '풜',
  '��' => '풝',
  '��' => '쐴',
  '��' => '쐼',
  '��' => '쐽',
  '��' => '쑈',
  '��' => '쑤',
  '��' => '쑥',
  '��' => '쑨',
  '��' => '쑬',
  '��' => '쑴',
  '��' => '쑵',
  '��' => '쑹',
  '��' => '쒀',
  '��' => '쒔',
  '��' => '쒜',
  '��' => '쒸',
  '��' => '쒼',
  '��' => '쓩',
  '��' => '쓰',
  '��' => '쓱',
  '��' => '쓴',
  '��' => '쓸',
  '��' => '쓺',
  '��' => '쓿',
  '��' => '씀',
  '��' => '씁',
  '��' => '씌',
  '��' => '씐',
  '��' => '씔',
  '��' => '씜',
  '��' => '씨',
  '��' => '씩',
  '��' => '씬',
  '��' => '씰',
  '��' => '씸',
  '��' => '씹',
  '��' => '씻',
  '��' => '씽',
  '��' => '아',
  '��' => '악',
  '��' => '안',
  '��' => '앉',
  '��' => '않',
  '��' => '알',
  '��' => '앍',
  '��' => '앎',
  '��' => '앓',
  '��' => '암',
  '��' => '압',
  '��' => '앗',
  '��' => '았',
  '��' => '앙',
  '��' => '앝',
  '��' => '앞',
  '��' => '애',
  '��' => '액',
  '��' => '앤',
  '��' => '앨',
  '��' => '앰',
  '��' => '앱',
  '��' => '앳',
  '��' => '앴',
  '��' => '앵',
  '��' => '야',
  '��' => '약',
  '��' => '얀',
  '��' => '얄',
  '��' => '얇',
  '��' => '얌',
  '��' => '얍',
  '��' => '얏',
  '��' => '양',
  '��' => '얕',
  '��' => '얗',
  '��' => '얘',
  '��' => '얜',
  '��' => '얠',
  '��' => '얩',
  '��' => '어',
  '��' => '억',
  '��' => '언',
  '��' => '얹',
  '��' => '얻',
  '��' => '얼',
  '��' => '얽',
  '��' => '얾',
  '��' => '엄',
  '��' => '업',
  '��' => '없',
  '��' => '엇',
  '��' => '었',
  '��' => '엉',
  '��' => '엊',
  '��' => '엌',
  '��' => '엎',
  '�A' => '풞',
  '�B' => '풟',
  '�C' => '풠',
  '�D' => '풡',
  '�E' => '풢',
  '�F' => '풣',
  '�G' => '풤',
  '�H' => '풥',
  '�I' => '풦',
  '�J' => '풧',
  '�K' => '풨',
  '�L' => '풪',
  '�M' => '풫',
  '�N' => '풬',
  '�O' => '풭',
  '�P' => '풮',
  '�Q' => '풯',
  '�R' => '풰',
  '�S' => '풱',
  '�T' => '풲',
  '�U' => '풳',
  '�V' => '풴',
  '�W' => '풵',
  '�X' => '풶',
  '�Y' => '풷',
  '�Z' => '풸',
  '�a' => '풹',
  '�b' => '풺',
  '�c' => '풻',
  '�d' => '풼',
  '�e' => '풽',
  '�f' => '풾',
  '�g' => '풿',
  '�h' => '퓀',
  '�i' => '퓁',
  '�j' => '퓂',
  '�k' => '퓃',
  '�l' => '퓄',
  '�m' => '퓅',
  '�n' => '퓆',
  '�o' => '퓇',
  '�p' => '퓈',
  '�q' => '퓉',
  '�r' => '퓊',
  '�s' => '퓋',
  '�t' => '퓍',
  '�u' => '퓎',
  '�v' => '퓏',
  '�w' => '퓑',
  '�x' => '퓒',
  '�y' => '퓓',
  '�z' => '퓕',
  '��' => '퓖',
  '��' => '퓗',
  '��' => '퓘',
  '��' => '퓙',
  '��' => '퓚',
  '��' => '퓛',
  '��' => '퓝',
  '��' => '퓞',
  '��' => '퓠',
  '��' => '퓡',
  '��' => '퓢',
  '��' => '퓣',
  '��' => '퓤',
  '��' => '퓥',
  '��' => '퓦',
  '��' => '퓧',
  '��' => '퓩',
  '��' => '퓪',
  '��' => '퓫',
  '��' => '퓭',
  '��' => '퓮',
  '��' => '퓯',
  '��' => '퓱',
  '��' => '퓲',
  '��' => '퓳',
  '��' => '퓴',
  '��' => '퓵',
  '��' => '퓶',
  '��' => '퓷',
  '��' => '퓹',
  '��' => '퓺',
  '��' => '퓼',
  '��' => '에',
  '��' => '엑',
  '��' => '엔',
  '��' => '엘',
  '��' => '엠',
  '��' => '엡',
  '��' => '엣',
  '��' => '엥',
  '��' => '여',
  '��' => '역',
  '��' => '엮',
  '��' => '연',
  '��' => '열',
  '��' => '엶',
  '��' => '엷',
  '��' => '염',
  '��' => '엽',
  '��' => '엾',
  '��' => '엿',
  '��' => '였',
  '��' => '영',
  '��' => '옅',
  '��' => '옆',
  '��' => '옇',
  '��' => '예',
  '��' => '옌',
  '��' => '옐',
  '��' => '옘',
  '��' => '옙',
  '��' => '옛',
  '��' => '옜',
  '��' => '오',
  '��' => '옥',
  '��' => '온',
  '��' => '올',
  '��' => '옭',
  '��' => '옮',
  '��' => '옰',
  '��' => '옳',
  '��' => '옴',
  '��' => '옵',
  '��' => '옷',
  '��' => '옹',
  '��' => '옻',
  '��' => '와',
  '��' => '왁',
  '��' => '완',
  '��' => '왈',
  '��' => '왐',
  '��' => '왑',
  '��' => '왓',
  '��' => '왔',
  '��' => '왕',
  '��' => '왜',
  '��' => '왝',
  '��' => '왠',
  '��' => '왬',
  '��' => '왯',
  '��' => '왱',
  '��' => '외',
  '��' => '왹',
  '��' => '왼',
  '��' => '욀',
  '��' => '욈',
  '��' => '욉',
  '��' => '욋',
  '��' => '욍',
  '��' => '요',
  '��' => '욕',
  '��' => '욘',
  '��' => '욜',
  '��' => '욤',
  '��' => '욥',
  '��' => '욧',
  '��' => '용',
  '��' => '우',
  '��' => '욱',
  '��' => '운',
  '��' => '울',
  '��' => '욹',
  '��' => '욺',
  '��' => '움',
  '��' => '웁',
  '��' => '웃',
  '��' => '웅',
  '��' => '워',
  '��' => '웍',
  '��' => '원',
  '��' => '월',
  '��' => '웜',
  '��' => '웝',
  '��' => '웠',
  '��' => '웡',
  '��' => '웨',
  '�A' => '퓾',
  '�B' => '퓿',
  '�C' => '픀',
  '�D' => '픁',
  '�E' => '픂',
  '�F' => '픃',
  '�G' => '픅',
  '�H' => '픆',
  '�I' => '픇',
  '�J' => '픉',
  '�K' => '픊',
  '�L' => '픋',
  '�M' => '픍',
  '�N' => '픎',
  '�O' => '픏',
  '�P' => '픐',
  '�Q' => '픑',
  '�R' => '픒',
  '�S' => '픓',
  '�T' => '픖',
  '�U' => '픘',
  '�V' => '픙',
  '�W' => '픚',
  '�X' => '픛',
  '�Y' => '픜',
  '�Z' => '픝',
  '�a' => '픞',
  '�b' => '픟',
  '�c' => '픠',
  '�d' => '픡',
  '�e' => '픢',
  '�f' => '픣',
  '�g' => '픤',
  '�h' => '픥',
  '�i' => '픦',
  '�j' => '픧',
  '�k' => '픨',
  '�l' => '픩',
  '�m' => '픪',
  '�n' => '픫',
  '�o' => '픬',
  '�p' => '픭',
  '�q' => '픮',
  '�r' => '픯',
  '�s' => '픰',
  '�t' => '픱',
  '�u' => '픲',
  '�v' => '픳',
  '�w' => '픴',
  '�x' => '픵',
  '�y' => '픶',
  '�z' => '픷',
  '��' => '픸',
  '��' => '픹',
  '��' => '픺',
  '��' => '픻',
  '��' => '픾',
  '��' => '픿',
  '��' => '핁',
  '��' => '핂',
  '��' => '핃',
  '��' => '핅',
  '��' => '핆',
  '��' => '핇',
  '��' => '핈',
  '��' => '핉',
  '��' => '핊',
  '��' => '핋',
  '��' => '핎',
  '��' => '핐',
  '��' => '핒',
  '��' => '핓',
  '��' => '핔',
  '��' => '핕',
  '��' => '핖',
  '��' => '핗',
  '��' => '핚',
  '��' => '핛',
  '��' => '핝',
  '��' => '핞',
  '��' => '핟',
  '��' => '핡',
  '��' => '핢',
  '��' => '핣',
  '��' => '웩',
  '��' => '웬',
  '��' => '웰',
  '��' => '웸',
  '��' => '웹',
  '��' => '웽',
  '��' => '위',
  '��' => '윅',
  '��' => '윈',
  '��' => '윌',
  '��' => '윔',
  '��' => '윕',
  '��' => '윗',
  '��' => '윙',
  '��' => '유',
  '��' => '육',
  '��' => '윤',
  '��' => '율',
  '��' => '윰',
  '��' => '윱',
  '��' => '윳',
  '��' => '융',
  '��' => '윷',
  '��' => '으',
  '��' => '윽',
  '��' => '은',
  '��' => '을',
  '��' => '읊',
  '��' => '음',
  '��' => '읍',
  '��' => '읏',
  '��' => '응',
  '��' => '읒',
  '��' => '읓',
  '��' => '읔',
  '��' => '읕',
  '��' => '읖',
  '��' => '읗',
  '��' => '의',
  '��' => '읜',
  '��' => '읠',
  '��' => '읨',
  '��' => '읫',
  '��' => '이',
  '��' => '익',
  '��' => '인',
  '��' => '일',
  '��' => '읽',
  '��' => '읾',
  '��' => '잃',
  '��' => '임',
  '��' => '입',
  '��' => '잇',
  '��' => '있',
  '��' => '잉',
  '��' => '잊',
  '��' => '잎',
  '��' => '자',
  '��' => '작',
  '��' => '잔',
  '��' => '잖',
  '��' => '잗',
  '��' => '잘',
  '��' => '잚',
  '��' => '잠',
  '��' => '잡',
  '��' => '잣',
  '��' => '잤',
  '��' => '장',
  '��' => '잦',
  '��' => '재',
  '��' => '잭',
  '��' => '잰',
  '��' => '잴',
  '��' => '잼',
  '��' => '잽',
  '��' => '잿',
  '��' => '쟀',
  '��' => '쟁',
  '��' => '쟈',
  '��' => '쟉',
  '��' => '쟌',
  '��' => '쟎',
  '��' => '쟐',
  '��' => '쟘',
  '��' => '쟝',
  '��' => '쟤',
  '��' => '쟨',
  '��' => '쟬',
  '��' => '저',
  '��' => '적',
  '��' => '전',
  '��' => '절',
  '��' => '젊',
  '�A' => '핤',
  '�B' => '핦',
  '�C' => '핧',
  '�D' => '핪',
  '�E' => '핬',
  '�F' => '핮',
  '�G' => '핯',
  '�H' => '핰',
  '�I' => '핱',
  '�J' => '핲',
  '�K' => '핳',
  '�L' => '핶',
  '�M' => '핷',
  '�N' => '핹',
  '�O' => '핺',
  '�P' => '핻',
  '�Q' => '핽',
  '�R' => '핾',
  '�S' => '핿',
  '�T' => '햀',
  '�U' => '햁',
  '�V' => '햂',
  '�W' => '햃',
  '�X' => '햆',
  '�Y' => '햊',
  '�Z' => '햋',
  '�a' => '햌',
  '�b' => '햍',
  '�c' => '햎',
  '�d' => '햏',
  '�e' => '햑',
  '�f' => '햒',
  '�g' => '햓',
  '�h' => '햔',
  '�i' => '햕',
  '�j' => '햖',
  '�k' => '햗',
  '�l' => '햘',
  '�m' => '햙',
  '�n' => '햚',
  '�o' => '햛',
  '�p' => '햜',
  '�q' => '햝',
  '�r' => '햞',
  '�s' => '햟',
  '�t' => '햠',
  '�u' => '햡',
  '�v' => '햢',
  '�w' => '햣',
  '�x' => '햤',
  '�y' => '햦',
  '�z' => '햧',
  '��' => '햨',
  '��' => '햩',
  '��' => '햪',
  '��' => '햫',
  '��' => '햬',
  '��' => '햭',
  '��' => '햮',
  '��' => '햯',
  '��' => '햰',
  '��' => '햱',
  '��' => '햲',
  '��' => '햳',
  '��' => '햴',
  '��' => '햵',
  '��' => '햶',
  '��' => '햷',
  '��' => '햸',
  '��' => '햹',
  '��' => '햺',
  '��' => '햻',
  '��' => '햼',
  '��' => '햽',
  '��' => '햾',
  '��' => '햿',
  '��' => '헀',
  '��' => '헁',
  '��' => '헂',
  '��' => '헃',
  '��' => '헄',
  '��' => '헅',
  '��' => '헆',
  '��' => '헇',
  '��' => '점',
  '��' => '접',
  '��' => '젓',
  '��' => '정',
  '��' => '젖',
  '��' => '제',
  '��' => '젝',
  '��' => '젠',
  '��' => '젤',
  '��' => '젬',
  '��' => '젭',
  '��' => '젯',
  '��' => '젱',
  '��' => '져',
  '��' => '젼',
  '��' => '졀',
  '��' => '졈',
  '��' => '졉',
  '��' => '졌',
  '��' => '졍',
  '��' => '졔',
  '��' => '조',
  '��' => '족',
  '��' => '존',
  '��' => '졸',
  '��' => '졺',
  '��' => '좀',
  '��' => '좁',
  '��' => '좃',
  '��' => '종',
  '��' => '좆',
  '��' => '좇',
  '��' => '좋',
  '��' => '좌',
  '��' => '좍',
  '��' => '좔',
  '��' => '좝',
  '��' => '좟',
  '��' => '좡',
  '��' => '좨',
  '��' => '좼',
  '��' => '좽',
  '��' => '죄',
  '��' => '죈',
  '��' => '죌',
  '��' => '죔',
  '��' => '죕',
  '��' => '죗',
  '��' => '죙',
  '��' => '죠',
  '��' => '죡',
  '��' => '죤',
  '��' => '죵',
  '��' => '주',
  '��' => '죽',
  '��' => '준',
  '��' => '줄',
  '��' => '줅',
  '��' => '줆',
  '��' => '줌',
  '��' => '줍',
  '��' => '줏',
  '��' => '중',
  '��' => '줘',
  '��' => '줬',
  '��' => '줴',
  '��' => '쥐',
  '��' => '쥑',
  '��' => '쥔',
  '��' => '쥘',
  '��' => '쥠',
  '��' => '쥡',
  '��' => '쥣',
  '��' => '쥬',
  '��' => '쥰',
  '��' => '쥴',
  '��' => '쥼',
  '��' => '즈',
  '��' => '즉',
  '��' => '즌',
  '��' => '즐',
  '��' => '즘',
  '��' => '즙',
  '��' => '즛',
  '��' => '증',
  '��' => '지',
  '��' => '직',
  '��' => '진',
  '��' => '짇',
  '��' => '질',
  '��' => '짊',
  '��' => '짐',
  '��' => '집',
  '��' => '짓',
  '�A' => '헊',
  '�B' => '헋',
  '�C' => '헍',
  '�D' => '헎',
  '�E' => '헏',
  '�F' => '헑',
  '�G' => '헓',
  '�H' => '헔',
  '�I' => '헕',
  '�J' => '헖',
  '�K' => '헗',
  '�L' => '헚',
  '�M' => '헜',
  '�N' => '헞',
  '�O' => '헟',
  '�P' => '헠',
  '�Q' => '헡',
  '�R' => '헢',
  '�S' => '헣',
  '�T' => '헦',
  '�U' => '헧',
  '�V' => '헩',
  '�W' => '헪',
  '�X' => '헫',
  '�Y' => '헭',
  '�Z' => '헮',
  '�a' => '헯',
  '�b' => '헰',
  '�c' => '헱',
  '�d' => '헲',
  '�e' => '헳',
  '�f' => '헶',
  '�g' => '헸',
  '�h' => '헺',
  '�i' => '헻',
  '�j' => '헼',
  '�k' => '헽',
  '�l' => '헾',
  '�m' => '헿',
  '�n' => '혂',
  '�o' => '혃',
  '�p' => '혅',
  '�q' => '혆',
  '�r' => '혇',
  '�s' => '혉',
  '�t' => '혊',
  '�u' => '혋',
  '�v' => '혌',
  '�w' => '혍',
  '�x' => '혎',
  '�y' => '혏',
  '�z' => '혒',
  '' => '혖',
  '' => '혗',
  '' => '혘',
  '' => '혙',
  '' => '혚',
  '' => '혛',
  '' => '혝',
  '' => '혞',
  '' => '혟',
  '' => '혡',
  '' => '혢',
  '' => '혣',
  '' => '혥',
  '' => '혦',
  '' => '혧',
  '' => '혨',
  '' => '혩',
  '' => '혪',
  '' => '혫',
  '' => '혬',
  '' => '혮',
  '' => '혯',
  '' => '혰',
  '' => '혱',
  '' => '혲',
  '' => '혳',
  '' => '혴',
  '' => '혵',
  '' => '혶',
  '' => '혷',
  '' => '혺',
  ' ' => '혻',
  '¡' => '징',
  '¢' => '짖',
  '£' => '짙',
  '¤' => '짚',
  '¥' => '짜',
  '¦' => '짝',
  '§' => '짠',
  '¨' => '짢',
  '©' => '짤',
  'ª' => '짧',
  '«' => '짬',
  '¬' => '짭',
  '­' => '짯',
  '®' => '짰',
  '¯' => '짱',
  '°' => '째',
  '±' => '짹',
  '²' => '짼',
  '³' => '쨀',
  '´' => '쨈',
  'µ' => '쨉',
  '¶' => '쨋',
  '·' => '쨌',
  '¸' => '쨍',
  '¹' => '쨔',
  'º' => '쨘',
  '»' => '쨩',
  '¼' => '쩌',
  '½' => '쩍',
  '¾' => '쩐',
  '¿' => '쩔',
  '��' => '쩜',
  '��' => '쩝',
  '��' => '쩟',
  '��' => '쩠',
  '��' => '쩡',
  '��' => '쩨',
  '��' => '쩽',
  '��' => '쪄',
  '��' => '쪘',
  '��' => '쪼',
  '��' => '쪽',
  '��' => '쫀',
  '��' => '쫄',
  '��' => '쫌',
  '��' => '쫍',
  '��' => '쫏',
  '��' => '쫑',
  '��' => '쫓',
  '��' => '쫘',
  '��' => '쫙',
  '��' => '쫠',
  '��' => '쫬',
  '��' => '쫴',
  '��' => '쬈',
  '��' => '쬐',
  '��' => '쬔',
  '��' => '쬘',
  '��' => '쬠',
  '��' => '쬡',
  '��' => '쭁',
  '��' => '쭈',
  '��' => '쭉',
  '��' => '쭌',
  '��' => '쭐',
  '��' => '쭘',
  '��' => '쭙',
  '��' => '쭝',
  '��' => '쭤',
  '��' => '쭸',
  '��' => '쭹',
  '��' => '쮜',
  '��' => '쮸',
  '��' => '쯔',
  '��' => '쯤',
  '��' => '쯧',
  '��' => '쯩',
  '��' => '찌',
  '��' => '찍',
  '��' => '찐',
  '��' => '찔',
  '��' => '찜',
  '��' => '찝',
  '��' => '찡',
  '��' => '찢',
  '��' => '찧',
  '��' => '차',
  '��' => '착',
  '��' => '찬',
  '��' => '찮',
  '��' => '찰',
  '��' => '참',
  '��' => '찹',
  '��' => '찻',
  '�A' => '혽',
  '�B' => '혾',
  '�C' => '혿',
  '�D' => '홁',
  '�E' => '홂',
  '�F' => '홃',
  '�G' => '홄',
  '�H' => '홆',
  '�I' => '홇',
  '�J' => '홊',
  '�K' => '홌',
  '�L' => '홎',
  '�M' => '홏',
  '�N' => '홐',
  '�O' => '홒',
  '�P' => '홓',
  '�Q' => '홖',
  '�R' => '홗',
  '�S' => '홙',
  '�T' => '홚',
  '�U' => '홛',
  '�V' => '홝',
  '�W' => '홞',
  '�X' => '홟',
  '�Y' => '홠',
  '�Z' => '홡',
  '�a' => '홢',
  '�b' => '홣',
  '�c' => '홤',
  '�d' => '홥',
  '�e' => '홦',
  '�f' => '홨',
  '�g' => '홪',
  '�h' => '홫',
  '�i' => '홬',
  '�j' => '홭',
  '�k' => '홮',
  '�l' => '홯',
  '�m' => '홲',
  '�n' => '홳',
  '�o' => '홵',
  '�p' => '홶',
  '�q' => '홷',
  '�r' => '홸',
  '�s' => '홹',
  '�t' => '홺',
  '�u' => '홻',
  '�v' => '홼',
  '�w' => '홽',
  '�x' => '홾',
  '�y' => '홿',
  '�z' => '횀',
  'Á' => '횁',
  'Â' => '횂',
  'Ã' => '횄',
  'Ä' => '횆',
  'Å' => '횇',
  'Æ' => '횈',
  'Ç' => '횉',
  'È' => '횊',
  'É' => '횋',
  'Ê' => '횎',
  'Ë' => '횏',
  'Ì' => '횑',
  'Í' => '횒',
  'Î' => '횓',
  'Ï' => '횕',
  'Ð' => '횖',
  'Ñ' => '횗',
  'Ò' => '횘',
  'Ó' => '횙',
  'Ô' => '횚',
  'Õ' => '횛',
  'Ö' => '횜',
  '×' => '횞',
  'Ø' => '횠',
  'Ù' => '횢',
  'Ú' => '횣',
  'Û' => '횤',
  'Ü' => '횥',
  'Ý' => '횦',
  'Þ' => '횧',
  'ß' => '횩',
  'à' => '횪',
  'á' => '찼',
  'â' => '창',
  'ã' => '찾',
  'ä' => '채',
  'å' => '책',
  'æ' => '챈',
  'ç' => '챌',
  'è' => '챔',
  'é' => '챕',
  'ê' => '챗',
  'ë' => '챘',
  'ì' => '챙',
  'í' => '챠',
  'î' => '챤',
  'ï' => '챦',
  'ð' => '챨',
  'ñ' => '챰',
  'ò' => '챵',
  'ó' => '처',
  'ô' => '척',
  'õ' => '천',
  'ö' => '철',
  '÷' => '첨',
  'ø' => '첩',
  'ù' => '첫',
  'ú' => '첬',
  'û' => '청',
  'ü' => '체',
  'ý' => '첵',
  'þ' => '첸',
  'ÿ' => '첼',
  '��' => '쳄',
  '��' => '쳅',
  '��' => '쳇',
  '��' => '쳉',
  '��' => '쳐',
  '��' => '쳔',
  '��' => '쳤',
  '��' => '쳬',
  '��' => '쳰',
  '��' => '촁',
  '��' => '초',
  '��' => '촉',
  '��' => '촌',
  '��' => '촐',
  '��' => '촘',
  '��' => '촙',
  '��' => '촛',
  '��' => '총',
  '��' => '촤',
  '��' => '촨',
  '��' => '촬',
  '��' => '촹',
  '��' => '최',
  '��' => '쵠',
  '��' => '쵤',
  '��' => '쵬',
  '��' => '쵭',
  '��' => '쵯',
  '��' => '쵱',
  '��' => '쵸',
  '��' => '춈',
  '��' => '추',
  '��' => '축',
  '��' => '춘',
  '��' => '출',
  '��' => '춤',
  '��' => '춥',
  '��' => '춧',
  '��' => '충',
  '��' => '춰',
  '��' => '췄',
  '��' => '췌',
  '��' => '췐',
  '��' => '취',
  '��' => '췬',
  '��' => '췰',
  '��' => '췸',
  '��' => '췹',
  '��' => '췻',
  '��' => '췽',
  '��' => '츄',
  '��' => '츈',
  '��' => '츌',
  '��' => '츔',
  '��' => '츙',
  '��' => '츠',
  '��' => '측',
  '��' => '츤',
  '��' => '츨',
  '��' => '츰',
  '��' => '츱',
  '��' => '츳',
  '��' => '층',
  '�A' => '횫',
  '�B' => '횭',
  '�C' => '횮',
  '�D' => '횯',
  '�E' => '횱',
  '�F' => '횲',
  '�G' => '횳',
  '�H' => '횴',
  '�I' => '횵',
  '�J' => '횶',
  '�K' => '횷',
  '�L' => '횸',
  '�M' => '횺',
  '�N' => '횼',
  '�O' => '횽',
  '�P' => '횾',
  '�Q' => '횿',
  '�R' => '훀',
  '�S' => '훁',
  '�T' => '훂',
  '�U' => '훃',
  '�V' => '훆',
  '�W' => '훇',
  '�X' => '훉',
  '�Y' => '훊',
  '�Z' => '훋',
  '�a' => '훍',
  '�b' => '훎',
  '�c' => '훏',
  '�d' => '훐',
  '�e' => '훒',
  '�f' => '훓',
  '�g' => '훕',
  '�h' => '훖',
  '�i' => '훘',
  '�j' => '훚',
  '�k' => '훛',
  '�l' => '훜',
  '�m' => '훝',
  '�n' => '훞',
  '�o' => '훟',
  '�p' => '훡',
  '�q' => '훢',
  '�r' => '훣',
  '�s' => '훥',
  '�t' => '훦',
  '�u' => '훧',
  '�v' => '훩',
  '�w' => '훪',
  '�x' => '훫',
  '�y' => '훬',
  '�z' => '훭',
  'ā' => '훮',
  'Ă' => '훯',
  'ă' => '훱',
  'Ą' => '훲',
  'ą' => '훳',
  'Ć' => '훴',
  'ć' => '훶',
  'Ĉ' => '훷',
  'ĉ' => '훸',
  'Ċ' => '훹',
  'ċ' => '훺',
  'Č' => '훻',
  'č' => '훾',
  'Ď' => '훿',
  'ď' => '휁',
  'Đ' => '휂',
  'đ' => '휃',
  'Ē' => '휅',
  'ē' => '휆',
  'Ĕ' => '휇',
  'ĕ' => '휈',
  'Ė' => '휉',
  'ė' => '휊',
  'Ę' => '휋',
  'ę' => '휌',
  'Ě' => '휍',
  'ě' => '휎',
  'Ĝ' => '휏',
  'ĝ' => '휐',
  'Ğ' => '휒',
  'ğ' => '휓',
  'Ġ' => '휔',
  'ġ' => '치',
  'Ģ' => '칙',
  'ģ' => '친',
  'Ĥ' => '칟',
  'ĥ' => '칠',
  'Ħ' => '칡',
  'ħ' => '침',
  'Ĩ' => '칩',
  'ĩ' => '칫',
  'Ī' => '칭',
  'ī' => '카',
  'Ĭ' => '칵',
  'ĭ' => '칸',
  'Į' => '칼',
  'į' => '캄',
  'İ' => '캅',
  'ı' => '캇',
  'Ĳ' => '캉',
  'ĳ' => '캐',
  'Ĵ' => '캑',
  'ĵ' => '캔',
  'Ķ' => '캘',
  'ķ' => '캠',
  'ĸ' => '캡',
  'Ĺ' => '캣',
  'ĺ' => '캤',
  'Ļ' => '캥',
  'ļ' => '캬',
  'Ľ' => '캭',
  'ľ' => '컁',
  'Ŀ' => '커',
  '��' => '컥',
  '��' => '컨',
  '��' => '컫',
  '��' => '컬',
  '��' => '컴',
  '��' => '컵',
  '��' => '컷',
  '��' => '컸',
  '��' => '컹',
  '��' => '케',
  '��' => '켁',
  '��' => '켄',
  '��' => '켈',
  '��' => '켐',
  '��' => '켑',
  '��' => '켓',
  '��' => '켕',
  '��' => '켜',
  '��' => '켠',
  '��' => '켤',
  '��' => '켬',
  '��' => '켭',
  '��' => '켯',
  '��' => '켰',
  '��' => '켱',
  '��' => '켸',
  '��' => '코',
  '��' => '콕',
  '��' => '콘',
  '��' => '콜',
  '��' => '콤',
  '��' => '콥',
  '��' => '콧',
  '��' => '콩',
  '��' => '콰',
  '��' => '콱',
  '��' => '콴',
  '��' => '콸',
  '��' => '쾀',
  '��' => '쾅',
  '��' => '쾌',
  '��' => '쾡',
  '��' => '쾨',
  '��' => '쾰',
  '��' => '쿄',
  '��' => '쿠',
  '��' => '쿡',
  '��' => '쿤',
  '��' => '쿨',
  '��' => '쿰',
  '��' => '쿱',
  '��' => '쿳',
  '��' => '쿵',
  '��' => '쿼',
  '��' => '퀀',
  '��' => '퀄',
  '��' => '퀑',
  '��' => '퀘',
  '��' => '퀭',
  '��' => '퀴',
  '��' => '퀵',
  '��' => '퀸',
  '��' => '퀼',
  '�A' => '휕',
  '�B' => '휖',
  '�C' => '휗',
  '�D' => '휚',
  '�E' => '휛',
  '�F' => '휝',
  '�G' => '휞',
  '�H' => '휟',
  '�I' => '휡',
  '�J' => '휢',
  '�K' => '휣',
  '�L' => '휤',
  '�M' => '휥',
  '�N' => '휦',
  '�O' => '휧',
  '�P' => '휪',
  '�Q' => '휬',
  '�R' => '휮',
  '�S' => '휯',
  '�T' => '휰',
  '�U' => '휱',
  '�V' => '휲',
  '�W' => '휳',
  '�X' => '휶',
  '�Y' => '휷',
  '�Z' => '휹',
  '�a' => '휺',
  '�b' => '휻',
  '�c' => '휽',
  '�d' => '휾',
  '�e' => '휿',
  '�f' => '흀',
  '�g' => '흁',
  '�h' => '흂',
  '�i' => '흃',
  '�j' => '흅',
  '�k' => '흆',
  '�l' => '흈',
  '�m' => '흊',
  '�n' => '흋',
  '�o' => '흌',
  '�p' => '흍',
  '�q' => '흎',
  '�r' => '흏',
  '�s' => '흒',
  '�t' => '흓',
  '�u' => '흕',
  '�v' => '흚',
  '�w' => '흛',
  '�x' => '흜',
  '�y' => '흝',
  '�z' => '흞',
  'Ł' => '흟',
  'ł' => '흢',
  'Ń' => '흤',
  'ń' => '흦',
  'Ņ' => '흧',
  'ņ' => '흨',
  'Ň' => '흪',
  'ň' => '흫',
  'ŉ' => '흭',
  'Ŋ' => '흮',
  'ŋ' => '흯',
  'Ō' => '흱',
  'ō' => '흲',
  'Ŏ' => '흳',
  'ŏ' => '흵',
  'Ő' => '흶',
  'ő' => '흷',
  'Œ' => '흸',
  'œ' => '흹',
  'Ŕ' => '흺',
  'ŕ' => '흻',
  'Ŗ' => '흾',
  'ŗ' => '흿',
  'Ř' => '힀',
  'ř' => '힂',
  'Ś' => '힃',
  'ś' => '힄',
  'Ŝ' => '힅',
  'ŝ' => '힆',
  'Ş' => '힇',
  'ş' => '힊',
  'Š' => '힋',
  'š' => '큄',
  'Ţ' => '큅',
  'ţ' => '큇',
  'Ť' => '큉',
  'ť' => '큐',
  'Ŧ' => '큔',
  'ŧ' => '큘',
  'Ũ' => '큠',
  'ũ' => '크',
  'Ū' => '큭',
  'ū' => '큰',
  'Ŭ' => '클',
  'ŭ' => '큼',
  'Ů' => '큽',
  'ů' => '킁',
  'Ű' => '키',
  'ű' => '킥',
  'Ų' => '킨',
  'ų' => '킬',
  'Ŵ' => '킴',
  'ŵ' => '킵',
  'Ŷ' => '킷',
  'ŷ' => '킹',
  'Ÿ' => '타',
  'Ź' => '탁',
  'ź' => '탄',
  'Ż' => '탈',
  'ż' => '탉',
  'Ž' => '탐',
  'ž' => '탑',
  'ſ' => '탓',
  '��' => '탔',
  '��' => '탕',
  '��' => '태',
  '��' => '택',
  '��' => '탠',
  '��' => '탤',
  '��' => '탬',
  '��' => '탭',
  '��' => '탯',
  '��' => '탰',
  '��' => '탱',
  '��' => '탸',
  '��' => '턍',
  '��' => '터',
  '��' => '턱',
  '��' => '턴',
  '��' => '털',
  '��' => '턺',
  '��' => '텀',
  '��' => '텁',
  '��' => '텃',
  '��' => '텄',
  '��' => '텅',
  '��' => '테',
  '��' => '텍',
  '��' => '텐',
  '��' => '텔',
  '��' => '템',
  '��' => '텝',
  '��' => '텟',
  '��' => '텡',
  '��' => '텨',
  '��' => '텬',
  '��' => '텼',
  '��' => '톄',
  '��' => '톈',
  '��' => '토',
  '��' => '톡',
  '��' => '톤',
  '��' => '톨',
  '��' => '톰',
  '��' => '톱',
  '��' => '톳',
  '��' => '통',
  '��' => '톺',
  '��' => '톼',
  '��' => '퇀',
  '��' => '퇘',
  '��' => '퇴',
  '��' => '퇸',
  '��' => '툇',
  '��' => '툉',
  '��' => '툐',
  '��' => '투',
  '��' => '툭',
  '��' => '툰',
  '��' => '툴',
  '��' => '툼',
  '��' => '툽',
  '��' => '툿',
  '��' => '퉁',
  '��' => '퉈',
  '��' => '퉜',
  '�A' => '힍',
  '�B' => '힎',
  '�C' => '힏',
  '�D' => '힑',
  '�E' => '힒',
  '�F' => '힓',
  '�G' => '힔',
  '�H' => '힕',
  '�I' => '힖',
  '�J' => '힗',
  '�K' => '힚',
  '�L' => '힜',
  '�M' => '힞',
  '�N' => '힟',
  '�O' => '힠',
  '�P' => '힡',
  '�Q' => '힢',
  '�R' => '힣',
  'ơ' => '퉤',
  'Ƣ' => '튀',
  'ƣ' => '튁',
  'Ƥ' => '튄',
  'ƥ' => '튈',
  'Ʀ' => '튐',
  'Ƨ' => '튑',
  'ƨ' => '튕',
  'Ʃ' => '튜',
  'ƪ' => '튠',
  'ƫ' => '튤',
  'Ƭ' => '튬',
  'ƭ' => '튱',
  'Ʈ' => '트',
  'Ư' => '특',
  'ư' => '튼',
  'Ʊ' => '튿',
  'Ʋ' => '틀',
  'Ƴ' => '틂',
  'ƴ' => '틈',
  'Ƶ' => '틉',
  'ƶ' => '틋',
  'Ʒ' => '틔',
  'Ƹ' => '틘',
  'ƹ' => '틜',
  'ƺ' => '틤',
  'ƻ' => '틥',
  'Ƽ' => '티',
  'ƽ' => '틱',
  'ƾ' => '틴',
  'ƿ' => '틸',
  '��' => '팀',
  '��' => '팁',
  '��' => '팃',
  '��' => '팅',
  '��' => '파',
  '��' => '팍',
  '��' => '팎',
  '��' => '판',
  '��' => '팔',
  '��' => '팖',
  '��' => '팜',
  '��' => '팝',
  '��' => '팟',
  '��' => '팠',
  '��' => '팡',
  '��' => '팥',
  '��' => '패',
  '��' => '팩',
  '��' => '팬',
  '��' => '팰',
  '��' => '팸',
  '��' => '팹',
  '��' => '팻',
  '��' => '팼',
  '��' => '팽',
  '��' => '퍄',
  '��' => '퍅',
  '��' => '퍼',
  '��' => '퍽',
  '��' => '펀',
  '��' => '펄',
  '��' => '펌',
  '��' => '펍',
  '��' => '펏',
  '��' => '펐',
  '��' => '펑',
  '��' => '페',
  '��' => '펙',
  '��' => '펜',
  '��' => '펠',
  '��' => '펨',
  '��' => '펩',
  '��' => '펫',
  '��' => '펭',
  '��' => '펴',
  '��' => '편',
  '��' => '펼',
  '��' => '폄',
  '��' => '폅',
  '��' => '폈',
  '��' => '평',
  '��' => '폐',
  '��' => '폘',
  '��' => '폡',
  '��' => '폣',
  '��' => '포',
  '��' => '폭',
  '��' => '폰',
  '��' => '폴',
  '��' => '폼',
  '��' => '폽',
  '��' => '폿',
  '��' => '퐁',
  'ǡ' => '퐈',
  'Ǣ' => '퐝',
  'ǣ' => '푀',
  'Ǥ' => '푄',
  'ǥ' => '표',
  'Ǧ' => '푠',
  'ǧ' => '푤',
  'Ǩ' => '푭',
  'ǩ' => '푯',
  'Ǫ' => '푸',
  'ǫ' => '푹',
  'Ǭ' => '푼',
  'ǭ' => '푿',
  'Ǯ' => '풀',
  'ǯ' => '풂',
  'ǰ' => '품',
  'Ǳ' => '풉',
  'ǲ' => '풋',
  'ǳ' => '풍',
  'Ǵ' => '풔',
  'ǵ' => '풩',
  'Ƕ' => '퓌',
  'Ƿ' => '퓐',
  'Ǹ' => '퓔',
  'ǹ' => '퓜',
  'Ǻ' => '퓟',
  'ǻ' => '퓨',
  'Ǽ' => '퓬',
  'ǽ' => '퓰',
  'Ǿ' => '퓸',
  'ǿ' => '퓻',
  '��' => '퓽',
  '��' => '프',
  '��' => '픈',
  '��' => '플',
  '��' => '픔',
  '��' => '픕',
  '��' => '픗',
  '��' => '피',
  '��' => '픽',
  '��' => '핀',
  '��' => '필',
  '��' => '핌',
  '��' => '핍',
  '��' => '핏',
  '��' => '핑',
  '��' => '하',
  '��' => '학',
  '��' => '한',
  '��' => '할',
  '��' => '핥',
  '��' => '함',
  '��' => '합',
  '��' => '핫',
  '��' => '항',
  '��' => '해',
  '��' => '핵',
  '��' => '핸',
  '��' => '핼',
  '��' => '햄',
  '��' => '햅',
  '��' => '햇',
  '��' => '했',
  '��' => '행',
  '��' => '햐',
  '��' => '향',
  '��' => '허',
  '��' => '헉',
  '��' => '헌',
  '��' => '헐',
  '��' => '헒',
  '��' => '험',
  '��' => '헙',
  '��' => '헛',
  '��' => '헝',
  '��' => '헤',
  '��' => '헥',
  '��' => '헨',
  '��' => '헬',
  '��' => '헴',
  '��' => '헵',
  '��' => '헷',
  '��' => '헹',
  '��' => '혀',
  '��' => '혁',
  '��' => '현',
  '��' => '혈',
  '��' => '혐',
  '��' => '협',
  '��' => '혓',
  '��' => '혔',
  '��' => '형',
  '��' => '혜',
  '��' => '혠',
  'ȡ' => '혤',
  'Ȣ' => '혭',
  'ȣ' => '호',
  'Ȥ' => '혹',
  'ȥ' => '혼',
  'Ȧ' => '홀',
  'ȧ' => '홅',
  'Ȩ' => '홈',
  'ȩ' => '홉',
  'Ȫ' => '홋',
  'ȫ' => '홍',
  'Ȭ' => '홑',
  'ȭ' => '화',
  'Ȯ' => '확',
  'ȯ' => '환',
  'Ȱ' => '활',
  'ȱ' => '홧',
  'Ȳ' => '황',
  'ȳ' => '홰',
  'ȴ' => '홱',
  'ȵ' => '홴',
  'ȶ' => '횃',
  'ȷ' => '횅',
  'ȸ' => '회',
  'ȹ' => '획',
  'Ⱥ' => '횐',
  'Ȼ' => '횔',
  'ȼ' => '횝',
  'Ƚ' => '횟',
  'Ⱦ' => '횡',
  'ȿ' => '효',
  '��' => '횬',
  '��' => '횰',
  '��' => '횹',
  '��' => '횻',
  '��' => '후',
  '��' => '훅',
  '��' => '훈',
  '��' => '훌',
  '��' => '훑',
  '��' => '훔',
  '��' => '훗',
  '��' => '훙',
  '��' => '훠',
  '��' => '훤',
  '��' => '훨',
  '��' => '훰',
  '��' => '훵',
  '��' => '훼',
  '��' => '훽',
  '��' => '휀',
  '��' => '휄',
  '��' => '휑',
  '��' => '휘',
  '��' => '휙',
  '��' => '휜',
  '��' => '휠',
  '��' => '휨',
  '��' => '휩',
  '��' => '휫',
  '��' => '휭',
  '��' => '휴',
  '��' => '휵',
  '��' => '휸',
  '��' => '휼',
  '��' => '흄',
  '��' => '흇',
  '��' => '흉',
  '��' => '흐',
  '��' => '흑',
  '��' => '흔',
  '��' => '흖',
  '��' => '흗',
  '��' => '흘',
  '��' => '흙',
  '��' => '흠',
  '��' => '흡',
  '��' => '흣',
  '��' => '흥',
  '��' => '흩',
  '��' => '희',
  '��' => '흰',
  '��' => '흴',
  '��' => '흼',
  '��' => '흽',
  '��' => '힁',
  '��' => '히',
  '��' => '힉',
  '��' => '힌',
  '��' => '힐',
  '��' => '힘',
  '��' => '힙',
  '��' => '힛',
  '��' => '힝',
  'ʡ' => '伽',
  'ʢ' => '佳',
  'ʣ' => '假',
  'ʤ' => '價',
  'ʥ' => '加',
  'ʦ' => '可',
  'ʧ' => '呵',
  'ʨ' => '哥',
  'ʩ' => '嘉',
  'ʪ' => '嫁',
  'ʫ' => '家',
  'ʬ' => '暇',
  'ʭ' => '架',
  'ʮ' => '枷',
  'ʯ' => '柯',
  'ʰ' => '歌',
  'ʱ' => '珂',
  'ʲ' => '痂',
  'ʳ' => '稼',
  'ʴ' => '苛',
  'ʵ' => '茄',
  'ʶ' => '街',
  'ʷ' => '袈',
  'ʸ' => '訶',
  'ʹ' => '賈',
  'ʺ' => '跏',
  'ʻ' => '軻',
  'ʼ' => '迦',
  'ʽ' => '駕',
  'ʾ' => '刻',
  'ʿ' => '却',
  '��' => '各',
  '��' => '恪',
  '��' => '慤',
  '��' => '殼',
  '��' => '珏',
  '��' => '脚',
  '��' => '覺',
  '��' => '角',
  '��' => '閣',
  '��' => '侃',
  '��' => '刊',
  '��' => '墾',
  '��' => '奸',
  '��' => '姦',
  '��' => '干',
  '��' => '幹',
  '��' => '懇',
  '��' => '揀',
  '��' => '杆',
  '��' => '柬',
  '��' => '桿',
  '��' => '澗',
  '��' => '癎',
  '��' => '看',
  '��' => '磵',
  '��' => '稈',
  '��' => '竿',
  '��' => '簡',
  '��' => '肝',
  '��' => '艮',
  '��' => '艱',
  '��' => '諫',
  '��' => '間',
  '��' => '乫',
  '��' => '喝',
  '��' => '曷',
  '��' => '渴',
  '��' => '碣',
  '��' => '竭',
  '��' => '葛',
  '��' => '褐',
  '��' => '蝎',
  '��' => '鞨',
  '��' => '勘',
  '��' => '坎',
  '��' => '堪',
  '��' => '嵌',
  '��' => '感',
  '��' => '憾',
  '��' => '戡',
  '��' => '敢',
  '��' => '柑',
  '��' => '橄',
  '��' => '減',
  '��' => '甘',
  '��' => '疳',
  '��' => '監',
  '��' => '瞰',
  '��' => '紺',
  '��' => '邯',
  '��' => '鑑',
  '��' => '鑒',
  '��' => '龕',
  'ˡ' => '匣',
  'ˢ' => '岬',
  'ˣ' => '甲',
  'ˤ' => '胛',
  '˥' => '鉀',
  '˦' => '閘',
  '˧' => '剛',
  '˨' => '堈',
  '˩' => '姜',
  '˪' => '岡',
  '˫' => '崗',
  'ˬ' => '康',
  '˭' => '强',
  'ˮ' => '彊',
  '˯' => '慷',
  '˰' => '江',
  '˱' => '畺',
  '˲' => '疆',
  '˳' => '糠',
  '˴' => '絳',
  '˵' => '綱',
  '˶' => '羌',
  '˷' => '腔',
  '˸' => '舡',
  '˹' => '薑',
  '˺' => '襁',
  '˻' => '講',
  '˼' => '鋼',
  '˽' => '降',
  '˾' => '鱇',
  '˿' => '介',
  '��' => '价',
  '��' => '個',
  '��' => '凱',
  '��' => '塏',
  '��' => '愷',
  '��' => '愾',
  '��' => '慨',
  '��' => '改',
  '��' => '槪',
  '��' => '漑',
  '��' => '疥',
  '��' => '皆',
  '��' => '盖',
  '��' => '箇',
  '��' => '芥',
  '��' => '蓋',
  '��' => '豈',
  '��' => '鎧',
  '��' => '開',
  '��' => '喀',
  '��' => '客',
  '��' => '坑',
  '��' => '更',
  '��' => '粳',
  '��' => '羹',
  '��' => '醵',
  '��' => '倨',
  '��' => '去',
  '��' => '居',
  '��' => '巨',
  '��' => '拒',
  '��' => '据',
  '��' => '據',
  '��' => '擧',
  '��' => '渠',
  '��' => '炬',
  '��' => '祛',
  '��' => '距',
  '��' => '踞',
  '��' => '車',
  '��' => '遽',
  '��' => '鉅',
  '��' => '鋸',
  '��' => '乾',
  '��' => '件',
  '��' => '健',
  '��' => '巾',
  '��' => '建',
  '��' => '愆',
  '��' => '楗',
  '��' => '腱',
  '��' => '虔',
  '��' => '蹇',
  '��' => '鍵',
  '��' => '騫',
  '��' => '乞',
  '��' => '傑',
  '��' => '杰',
  '��' => '桀',
  '��' => '儉',
  '��' => '劍',
  '��' => '劒',
  '��' => '檢',
  '̡' => '瞼',
  '̢' => '鈐',
  '̣' => '黔',
  '̤' => '劫',
  '̥' => '怯',
  '̦' => '迲',
  '̧' => '偈',
  '̨' => '憩',
  '̩' => '揭',
  '̪' => '擊',
  '̫' => '格',
  '̬' => '檄',
  '̭' => '激',
  '̮' => '膈',
  '̯' => '覡',
  '̰' => '隔',
  '̱' => '堅',
  '̲' => '牽',
  '̳' => '犬',
  '̴' => '甄',
  '̵' => '絹',
  '̶' => '繭',
  '̷' => '肩',
  '̸' => '見',
  '̹' => '譴',
  '̺' => '遣',
  '̻' => '鵑',
  '̼' => '抉',
  '̽' => '決',
  '̾' => '潔',
  '̿' => '結',
  '��' => '缺',
  '��' => '訣',
  '��' => '兼',
  '��' => '慊',
  '��' => '箝',
  '��' => '謙',
  '��' => '鉗',
  '��' => '鎌',
  '��' => '京',
  '��' => '俓',
  '��' => '倞',
  '��' => '傾',
  '��' => '儆',
  '��' => '勁',
  '��' => '勍',
  '��' => '卿',
  '��' => '坰',
  '��' => '境',
  '��' => '庚',
  '��' => '徑',
  '��' => '慶',
  '��' => '憬',
  '��' => '擎',
  '��' => '敬',
  '��' => '景',
  '��' => '暻',
  '��' => '更',
  '��' => '梗',
  '��' => '涇',
  '��' => '炅',
  '��' => '烱',
  '��' => '璟',
  '��' => '璥',
  '��' => '瓊',
  '��' => '痙',
  '��' => '硬',
  '��' => '磬',
  '��' => '竟',
  '��' => '競',
  '��' => '絅',
  '��' => '經',
  '��' => '耕',
  '��' => '耿',
  '��' => '脛',
  '��' => '莖',
  '��' => '警',
  '��' => '輕',
  '��' => '逕',
  '��' => '鏡',
  '��' => '頃',
  '��' => '頸',
  '��' => '驚',
  '��' => '鯨',
  '��' => '係',
  '��' => '啓',
  '��' => '堺',
  '��' => '契',
  '��' => '季',
  '��' => '屆',
  '��' => '悸',
  '��' => '戒',
  '��' => '桂',
  '��' => '械',
  '͡' => '棨',
  '͢' => '溪',
  'ͣ' => '界',
  'ͤ' => '癸',
  'ͥ' => '磎',
  'ͦ' => '稽',
  'ͧ' => '系',
  'ͨ' => '繫',
  'ͩ' => '繼',
  'ͪ' => '計',
  'ͫ' => '誡',
  'ͬ' => '谿',
  'ͭ' => '階',
  'ͮ' => '鷄',
  'ͯ' => '古',
  'Ͱ' => '叩',
  'ͱ' => '告',
  'Ͳ' => '呱',
  'ͳ' => '固',
  'ʹ' => '姑',
  '͵' => '孤',
  'Ͷ' => '尻',
  'ͷ' => '庫',
  '͸' => '拷',
  '͹' => '攷',
  'ͺ' => '故',
  'ͻ' => '敲',
  'ͼ' => '暠',
  'ͽ' => '枯',
  ';' => '槁',
  'Ϳ' => '沽',
  '��' => '痼',
  '��' => '皐',
  '��' => '睾',
  '��' => '稿',
  '��' => '羔',
  '��' => '考',
  '��' => '股',
  '��' => '膏',
  '��' => '苦',
  '��' => '苽',
  '��' => '菰',
  '��' => '藁',
  '��' => '蠱',
  '��' => '袴',
  '��' => '誥',
  '��' => '賈',
  '��' => '辜',
  '��' => '錮',
  '��' => '雇',
  '��' => '顧',
  '��' => '高',
  '��' => '鼓',
  '��' => '哭',
  '��' => '斛',
  '��' => '曲',
  '��' => '梏',
  '��' => '穀',
  '��' => '谷',
  '��' => '鵠',
  '��' => '困',
  '��' => '坤',
  '��' => '崑',
  '��' => '昆',
  '��' => '梱',
  '��' => '棍',
  '��' => '滾',
  '��' => '琨',
  '��' => '袞',
  '��' => '鯤',
  '��' => '汨',
  '��' => '滑',
  '��' => '骨',
  '��' => '供',
  '��' => '公',
  '��' => '共',
  '��' => '功',
  '��' => '孔',
  '��' => '工',
  '��' => '恐',
  '��' => '恭',
  '��' => '拱',
  '��' => '控',
  '��' => '攻',
  '��' => '珙',
  '��' => '空',
  '��' => '蚣',
  '��' => '貢',
  '��' => '鞏',
  '��' => '串',
  '��' => '寡',
  '��' => '戈',
  '��' => '果',
  '��' => '瓜',
  'Ρ' => '科',
  '΢' => '菓',
  'Σ' => '誇',
  'Τ' => '課',
  'Υ' => '跨',
  'Φ' => '過',
  'Χ' => '鍋',
  'Ψ' => '顆',
  'Ω' => '廓',
  'Ϊ' => '槨',
  'Ϋ' => '藿',
  'ά' => '郭',
  'έ' => '串',
  'ή' => '冠',
  'ί' => '官',
  'ΰ' => '寬',
  'α' => '慣',
  'β' => '棺',
  'γ' => '款',
  'δ' => '灌',
  'ε' => '琯',
  'ζ' => '瓘',
  'η' => '管',
  'θ' => '罐',
  'ι' => '菅',
  'κ' => '觀',
  'λ' => '貫',
  'μ' => '關',
  'ν' => '館',
  'ξ' => '刮',
  'ο' => '恝',
  '��' => '括',
  '��' => '适',
  '��' => '侊',
  '��' => '光',
  '��' => '匡',
  '��' => '壙',
  '��' => '廣',
  '��' => '曠',
  '��' => '洸',
  '��' => '炚',
  '��' => '狂',
  '��' => '珖',
  '��' => '筐',
  '��' => '胱',
  '��' => '鑛',
  '��' => '卦',
  '��' => '掛',
  '��' => '罫',
  '��' => '乖',
  '��' => '傀',
  '��' => '塊',
  '��' => '壞',
  '��' => '怪',
  '��' => '愧',
  '��' => '拐',
  '��' => '槐',
  '��' => '魁',
  '��' => '宏',
  '��' => '紘',
  '��' => '肱',
  '��' => '轟',
  '��' => '交',
  '��' => '僑',
  '��' => '咬',
  '��' => '喬',
  '��' => '嬌',
  '��' => '嶠',
  '��' => '巧',
  '��' => '攪',
  '��' => '敎',
  '��' => '校',
  '��' => '橋',
  '��' => '狡',
  '��' => '皎',
  '��' => '矯',
  '��' => '絞',
  '��' => '翹',
  '��' => '膠',
  '��' => '蕎',
  '��' => '蛟',
  '��' => '較',
  '��' => '轎',
  '��' => '郊',
  '��' => '餃',
  '��' => '驕',
  '��' => '鮫',
  '��' => '丘',
  '��' => '久',
  '��' => '九',
  '��' => '仇',
  '��' => '俱',
  '��' => '具',
  '��' => '勾',
  'ϡ' => '區',
  'Ϣ' => '口',
  'ϣ' => '句',
  'Ϥ' => '咎',
  'ϥ' => '嘔',
  'Ϧ' => '坵',
  'ϧ' => '垢',
  'Ϩ' => '寇',
  'ϩ' => '嶇',
  'Ϫ' => '廐',
  'ϫ' => '懼',
  'Ϭ' => '拘',
  'ϭ' => '救',
  'Ϯ' => '枸',
  'ϯ' => '柩',
  'ϰ' => '構',
  'ϱ' => '歐',
  'ϲ' => '毆',
  'ϳ' => '毬',
  'ϴ' => '求',
  'ϵ' => '溝',
  '϶' => '灸',
  'Ϸ' => '狗',
  'ϸ' => '玖',
  'Ϲ' => '球',
  'Ϻ' => '瞿',
  'ϻ' => '矩',
  'ϼ' => '究',
  'Ͻ' => '絿',
  'Ͼ' => '耉',
  'Ͽ' => '臼',
  '��' => '舅',
  '��' => '舊',
  '��' => '苟',
  '��' => '衢',
  '��' => '謳',
  '��' => '購',
  '��' => '軀',
  '��' => '逑',
  '��' => '邱',
  '��' => '鉤',
  '��' => '銶',
  '��' => '駒',
  '��' => '驅',
  '��' => '鳩',
  '��' => '鷗',
  '��' => '龜',
  '��' => '國',
  '��' => '局',
  '��' => '菊',
  '��' => '鞠',
  '��' => '鞫',
  '��' => '麴',
  '��' => '君',
  '��' => '窘',
  '��' => '群',
  '��' => '裙',
  '��' => '軍',
  '��' => '郡',
  '��' => '堀',
  '��' => '屈',
  '��' => '掘',
  '��' => '窟',
  '��' => '宮',
  '��' => '弓',
  '��' => '穹',
  '��' => '窮',
  '��' => '芎',
  '��' => '躬',
  '��' => '倦',
  '��' => '券',
  '��' => '勸',
  '��' => '卷',
  '��' => '圈',
  '��' => '拳',
  '��' => '捲',
  '��' => '權',
  '��' => '淃',
  '��' => '眷',
  '��' => '厥',
  '��' => '獗',
  '��' => '蕨',
  '��' => '蹶',
  '��' => '闕',
  '��' => '机',
  '��' => '櫃',
  '��' => '潰',
  '��' => '詭',
  '��' => '軌',
  '��' => '饋',
  '��' => '句',
  '��' => '晷',
  '��' => '歸',
  '��' => '貴',
  'С' => '鬼',
  'Т' => '龜',
  'У' => '叫',
  'Ф' => '圭',
  'Х' => '奎',
  'Ц' => '揆',
  'Ч' => '槻',
  'Ш' => '珪',
  'Щ' => '硅',
  'Ъ' => '窺',
  'Ы' => '竅',
  'Ь' => '糾',
  'Э' => '葵',
  'Ю' => '規',
  'Я' => '赳',
  'а' => '逵',
  'б' => '閨',
  'в' => '勻',
  'г' => '均',
  'д' => '畇',
  'е' => '筠',
  'ж' => '菌',
  'з' => '鈞',
  'и' => '龜',
  'й' => '橘',
  'к' => '克',
  'л' => '剋',
  'м' => '劇',
  'н' => '戟',
  'о' => '棘',
  'п' => '極',
  '��' => '隙',
  '��' => '僅',
  '��' => '劤',
  '��' => '勤',
  '��' => '懃',
  '��' => '斤',
  '��' => '根',
  '��' => '槿',
  '��' => '瑾',
  '��' => '筋',
  '��' => '芹',
  '��' => '菫',
  '��' => '覲',
  '��' => '謹',
  '��' => '近',
  '��' => '饉',
  '��' => '契',
  '��' => '今',
  '��' => '妗',
  '��' => '擒',
  '��' => '昑',
  '��' => '檎',
  '��' => '琴',
  '��' => '禁',
  '��' => '禽',
  '��' => '芩',
  '��' => '衾',
  '��' => '衿',
  '��' => '襟',
  '��' => '金',
  '��' => '錦',
  '��' => '伋',
  '��' => '及',
  '��' => '急',
  '��' => '扱',
  '��' => '汲',
  '��' => '級',
  '��' => '給',
  '��' => '亘',
  '��' => '兢',
  '��' => '矜',
  '��' => '肯',
  '��' => '企',
  '��' => '伎',
  '��' => '其',
  '��' => '冀',
  '��' => '嗜',
  '��' => '器',
  '��' => '圻',
  '��' => '基',
  '��' => '埼',
  '��' => '夔',
  '��' => '奇',
  '��' => '妓',
  '��' => '寄',
  '��' => '岐',
  '��' => '崎',
  '��' => '己',
  '��' => '幾',
  '��' => '忌',
  '��' => '技',
  '��' => '旗',
  '��' => '旣',
  'ѡ' => '朞',
  'Ѣ' => '期',
  'ѣ' => '杞',
  'Ѥ' => '棋',
  'ѥ' => '棄',
  'Ѧ' => '機',
  'ѧ' => '欺',
  'Ѩ' => '氣',
  'ѩ' => '汽',
  'Ѫ' => '沂',
  'ѫ' => '淇',
  'Ѭ' => '玘',
  'ѭ' => '琦',
  'Ѯ' => '琪',
  'ѯ' => '璂',
  'Ѱ' => '璣',
  'ѱ' => '畸',
  'Ѳ' => '畿',
  'ѳ' => '碁',
  'Ѵ' => '磯',
  'ѵ' => '祁',
  'Ѷ' => '祇',
  'ѷ' => '祈',
  'Ѹ' => '祺',
  'ѹ' => '箕',
  'Ѻ' => '紀',
  'ѻ' => '綺',
  'Ѽ' => '羈',
  'ѽ' => '耆',
  'Ѿ' => '耭',
  'ѿ' => '肌',
  '��' => '記',
  '��' => '譏',
  '��' => '豈',
  '��' => '起',
  '��' => '錡',
  '��' => '錤',
  '��' => '飢',
  '��' => '饑',
  '��' => '騎',
  '��' => '騏',
  '��' => '驥',
  '��' => '麒',
  '��' => '緊',
  '��' => '佶',
  '��' => '吉',
  '��' => '拮',
  '��' => '桔',
  '��' => '金',
  '��' => '喫',
  '��' => '儺',
  '��' => '喇',
  '��' => '奈',
  '��' => '娜',
  '��' => '懦',
  '��' => '懶',
  '��' => '拏',
  '��' => '拿',
  '��' => '癩',
  '��' => '羅',
  '��' => '蘿',
  '��' => '螺',
  '��' => '裸',
  '��' => '邏',
  '��' => '那',
  '��' => '樂',
  '��' => '洛',
  '��' => '烙',
  '��' => '珞',
  '��' => '落',
  '��' => '諾',
  '��' => '酪',
  '��' => '駱',
  '��' => '亂',
  '��' => '卵',
  '��' => '暖',
  '��' => '欄',
  '��' => '煖',
  '��' => '爛',
  '��' => '蘭',
  '��' => '難',
  '��' => '鸞',
  '��' => '捏',
  '��' => '捺',
  '��' => '南',
  '��' => '嵐',
  '��' => '枏',
  '��' => '楠',
  '��' => '湳',
  '��' => '濫',
  '��' => '男',
  '��' => '藍',
  '��' => '襤',
  '��' => '拉',
  'ҡ' => '納',
  'Ң' => '臘',
  'ң' => '蠟',
  'Ҥ' => '衲',
  'ҥ' => '囊',
  'Ҧ' => '娘',
  'ҧ' => '廊',
  'Ҩ' => '朗',
  'ҩ' => '浪',
  'Ҫ' => '狼',
  'ҫ' => '郎',
  'Ҭ' => '乃',
  'ҭ' => '來',
  'Ү' => '內',
  'ү' => '奈',
  'Ұ' => '柰',
  'ұ' => '耐',
  'Ҳ' => '冷',
  'ҳ' => '女',
  'Ҵ' => '年',
  'ҵ' => '撚',
  'Ҷ' => '秊',
  'ҷ' => '念',
  'Ҹ' => '恬',
  'ҹ' => '拈',
  'Һ' => '捻',
  'һ' => '寧',
  'Ҽ' => '寗',
  'ҽ' => '努',
  'Ҿ' => '勞',
  'ҿ' => '奴',
  '��' => '弩',
  '��' => '怒',
  '��' => '擄',
  '��' => '櫓',
  '��' => '爐',
  '��' => '瑙',
  '��' => '盧',
  '��' => '老',
  '��' => '蘆',
  '��' => '虜',
  '��' => '路',
  '��' => '露',
  '��' => '駑',
  '��' => '魯',
  '��' => '鷺',
  '��' => '碌',
  '��' => '祿',
  '��' => '綠',
  '��' => '菉',
  '��' => '錄',
  '��' => '鹿',
  '��' => '論',
  '��' => '壟',
  '��' => '弄',
  '��' => '濃',
  '��' => '籠',
  '��' => '聾',
  '��' => '膿',
  '��' => '農',
  '��' => '惱',
  '��' => '牢',
  '��' => '磊',
  '��' => '腦',
  '��' => '賂',
  '��' => '雷',
  '��' => '尿',
  '��' => '壘',
  '��' => '屢',
  '��' => '樓',
  '��' => '淚',
  '��' => '漏',
  '��' => '累',
  '��' => '縷',
  '��' => '陋',
  '��' => '嫩',
  '��' => '訥',
  '��' => '杻',
  '��' => '紐',
  '��' => '勒',
  '��' => '肋',
  '��' => '凜',
  '��' => '凌',
  '��' => '稜',
  '��' => '綾',
  '��' => '能',
  '��' => '菱',
  '��' => '陵',
  '��' => '尼',
  '��' => '泥',
  '��' => '匿',
  '��' => '溺',
  '��' => '多',
  '��' => '茶',
  'ӡ' => '丹',
  'Ӣ' => '亶',
  'ӣ' => '但',
  'Ӥ' => '單',
  'ӥ' => '團',
  'Ӧ' => '壇',
  'ӧ' => '彖',
  'Ө' => '斷',
  'ө' => '旦',
  'Ӫ' => '檀',
  'ӫ' => '段',
  'Ӭ' => '湍',
  'ӭ' => '短',
  'Ӯ' => '端',
  'ӯ' => '簞',
  'Ӱ' => '緞',
  'ӱ' => '蛋',
  'Ӳ' => '袒',
  'ӳ' => '鄲',
  'Ӵ' => '鍛',
  'ӵ' => '撻',
  'Ӷ' => '澾',
  'ӷ' => '獺',
  'Ӹ' => '疸',
  'ӹ' => '達',
  'Ӻ' => '啖',
  'ӻ' => '坍',
  'Ӽ' => '憺',
  'ӽ' => '擔',
  'Ӿ' => '曇',
  'ӿ' => '淡',
  '��' => '湛',
  '��' => '潭',
  '��' => '澹',
  '��' => '痰',
  '��' => '聃',
  '��' => '膽',
  '��' => '蕁',
  '��' => '覃',
  '��' => '談',
  '��' => '譚',
  '��' => '錟',
  '��' => '沓',
  '��' => '畓',
  '��' => '答',
  '��' => '踏',
  '��' => '遝',
  '��' => '唐',
  '��' => '堂',
  '��' => '塘',
  '��' => '幢',
  '��' => '戇',
  '��' => '撞',
  '��' => '棠',
  '��' => '當',
  '��' => '糖',
  '��' => '螳',
  '��' => '黨',
  '��' => '代',
  '��' => '垈',
  '��' => '坮',
  '��' => '大',
  '��' => '對',
  '��' => '岱',
  '��' => '帶',
  '��' => '待',
  '��' => '戴',
  '��' => '擡',
  '��' => '玳',
  '��' => '臺',
  '��' => '袋',
  '��' => '貸',
  '��' => '隊',
  '��' => '黛',
  '��' => '宅',
  '��' => '德',
  '��' => '悳',
  '��' => '倒',
  '��' => '刀',
  '��' => '到',
  '��' => '圖',
  '��' => '堵',
  '��' => '塗',
  '��' => '導',
  '��' => '屠',
  '��' => '島',
  '��' => '嶋',
  '��' => '度',
  '��' => '徒',
  '��' => '悼',
  '��' => '挑',
  '��' => '掉',
  '��' => '搗',
  '��' => '桃',
  'ԡ' => '棹',
  'Ԣ' => '櫂',
  'ԣ' => '淘',
  'Ԥ' => '渡',
  'ԥ' => '滔',
  'Ԧ' => '濤',
  'ԧ' => '燾',
  'Ԩ' => '盜',
  'ԩ' => '睹',
  'Ԫ' => '禱',
  'ԫ' => '稻',
  'Ԭ' => '萄',
  'ԭ' => '覩',
  'Ԯ' => '賭',
  'ԯ' => '跳',
  '԰' => '蹈',
  'Ա' => '逃',
  'Բ' => '途',
  'Գ' => '道',
  'Դ' => '都',
  'Ե' => '鍍',
  'Զ' => '陶',
  'Է' => '韜',
  'Ը' => '毒',
  'Թ' => '瀆',
  'Ժ' => '牘',
  'Ի' => '犢',
  'Լ' => '獨',
  'Խ' => '督',
  'Ծ' => '禿',
  'Կ' => '篤',
  '��' => '纛',
  '��' => '讀',
  '��' => '墩',
  '��' => '惇',
  '��' => '敦',
  '��' => '旽',
  '��' => '暾',
  '��' => '沌',
  '��' => '焞',
  '��' => '燉',
  '��' => '豚',
  '��' => '頓',
  '��' => '乭',
  '��' => '突',
  '��' => '仝',
  '��' => '冬',
  '��' => '凍',
  '��' => '動',
  '��' => '同',
  '��' => '憧',
  '��' => '東',
  '��' => '桐',
  '��' => '棟',
  '��' => '洞',
  '��' => '潼',
  '��' => '疼',
  '��' => '瞳',
  '��' => '童',
  '��' => '胴',
  '��' => '董',
  '��' => '銅',
  '��' => '兜',
  '��' => '斗',
  '��' => '杜',
  '��' => '枓',
  '��' => '痘',
  '��' => '竇',
  '��' => '荳',
  '��' => '讀',
  '��' => '豆',
  '��' => '逗',
  '��' => '頭',
  '��' => '屯',
  '��' => '臀',
  '��' => '芚',
  '��' => '遁',
  '��' => '遯',
  '��' => '鈍',
  '��' => '得',
  '��' => '嶝',
  '��' => '橙',
  '��' => '燈',
  '��' => '登',
  '��' => '等',
  '��' => '藤',
  '��' => '謄',
  '��' => '鄧',
  '��' => '騰',
  '��' => '喇',
  '��' => '懶',
  '��' => '拏',
  '��' => '癩',
  '��' => '羅',
  'ա' => '蘿',
  'բ' => '螺',
  'գ' => '裸',
  'դ' => '邏',
  'ե' => '樂',
  'զ' => '洛',
  'է' => '烙',
  'ը' => '珞',
  'թ' => '絡',
  'ժ' => '落',
  'ի' => '諾',
  'լ' => '酪',
  'խ' => '駱',
  'ծ' => '丹',
  'կ' => '亂',
  'հ' => '卵',
  'ձ' => '欄',
  'ղ' => '欒',
  'ճ' => '瀾',
  'մ' => '爛',
  'յ' => '蘭',
  'ն' => '鸞',
  'շ' => '剌',
  'ո' => '辣',
  'չ' => '嵐',
  'պ' => '擥',
  'ջ' => '攬',
  'ռ' => '欖',
  'ս' => '濫',
  'վ' => '籃',
  'տ' => '纜',
  '��' => '藍',
  '��' => '襤',
  '��' => '覽',
  '��' => '拉',
  '��' => '臘',
  '��' => '蠟',
  '��' => '廊',
  '��' => '朗',
  '��' => '浪',
  '��' => '狼',
  '��' => '琅',
  '��' => '瑯',
  '��' => '螂',
  '��' => '郞',
  '��' => '來',
  '��' => '崍',
  '��' => '徠',
  '��' => '萊',
  '��' => '冷',
  '��' => '掠',
  '��' => '略',
  '��' => '亮',
  '��' => '倆',
  '��' => '兩',
  '��' => '凉',
  '��' => '梁',
  '��' => '樑',
  '��' => '粮',
  '��' => '粱',
  '��' => '糧',
  '��' => '良',
  '��' => '諒',
  '��' => '輛',
  '��' => '量',
  '��' => '侶',
  '��' => '儷',
  '��' => '勵',
  '��' => '呂',
  '��' => '廬',
  '��' => '慮',
  '��' => '戾',
  '��' => '旅',
  '��' => '櫚',
  '��' => '濾',
  '��' => '礪',
  '��' => '藜',
  '��' => '蠣',
  '��' => '閭',
  '��' => '驢',
  '��' => '驪',
  '��' => '麗',
  '��' => '黎',
  '��' => '力',
  '��' => '曆',
  '��' => '歷',
  '��' => '瀝',
  '��' => '礫',
  '��' => '轢',
  '��' => '靂',
  '��' => '憐',
  '��' => '戀',
  '��' => '攣',
  '��' => '漣',
  '֡' => '煉',
  '֢' => '璉',
  '֣' => '練',
  '֤' => '聯',
  '֥' => '蓮',
  '֦' => '輦',
  '֧' => '連',
  '֨' => '鍊',
  '֩' => '冽',
  '֪' => '列',
  '֫' => '劣',
  '֬' => '洌',
  '֭' => '烈',
  '֮' => '裂',
  '֯' => '廉',
  'ְ' => '斂',
  'ֱ' => '殮',
  'ֲ' => '濂',
  'ֳ' => '簾',
  'ִ' => '獵',
  'ֵ' => '令',
  'ֶ' => '伶',
  'ַ' => '囹',
  'ָ' => '寧',
  'ֹ' => '岺',
  'ֺ' => '嶺',
  'ֻ' => '怜',
  'ּ' => '玲',
  'ֽ' => '笭',
  '־' => '羚',
  'ֿ' => '翎',
  '��' => '聆',
  '��' => '逞',
  '��' => '鈴',
  '��' => '零',
  '��' => '靈',
  '��' => '領',
  '��' => '齡',
  '��' => '例',
  '��' => '澧',
  '��' => '禮',
  '��' => '醴',
  '��' => '隷',
  '��' => '勞',
  '��' => '怒',
  '��' => '撈',
  '��' => '擄',
  '��' => '櫓',
  '��' => '潞',
  '��' => '瀘',
  '��' => '爐',
  '��' => '盧',
  '��' => '老',
  '��' => '蘆',
  '��' => '虜',
  '��' => '路',
  '��' => '輅',
  '��' => '露',
  '��' => '魯',
  '��' => '鷺',
  '��' => '鹵',
  '��' => '碌',
  '��' => '祿',
  '��' => '綠',
  '��' => '菉',
  '��' => '錄',
  '��' => '鹿',
  '��' => '麓',
  '��' => '論',
  '��' => '壟',
  '��' => '弄',
  '��' => '朧',
  '��' => '瀧',
  '��' => '瓏',
  '��' => '籠',
  '��' => '聾',
  '��' => '儡',
  '��' => '瀨',
  '��' => '牢',
  '��' => '磊',
  '��' => '賂',
  '��' => '賚',
  '��' => '賴',
  '��' => '雷',
  '��' => '了',
  '��' => '僚',
  '��' => '寮',
  '��' => '廖',
  '��' => '料',
  '��' => '燎',
  '��' => '療',
  '��' => '瞭',
  '��' => '聊',
  '��' => '蓼',
  'ס' => '遼',
  'ע' => '鬧',
  'ף' => '龍',
  'פ' => '壘',
  'ץ' => '婁',
  'צ' => '屢',
  'ק' => '樓',
  'ר' => '淚',
  'ש' => '漏',
  'ת' => '瘻',
  '׫' => '累',
  '׬' => '縷',
  '׭' => '蔞',
  '׮' => '褸',
  'ׯ' => '鏤',
  'װ' => '陋',
  'ױ' => '劉',
  'ײ' => '旒',
  '׳' => '柳',
  '״' => '榴',
  '׵' => '流',
  '׶' => '溜',
  '׷' => '瀏',
  '׸' => '琉',
  '׹' => '瑠',
  '׺' => '留',
  '׻' => '瘤',
  '׼' => '硫',
  '׽' => '謬',
  '׾' => '類',
  '׿' => '六',
  '��' => '戮',
  '��' => '陸',
  '��' => '侖',
  '��' => '倫',
  '��' => '崙',
  '��' => '淪',
  '��' => '綸',
  '��' => '輪',
  '��' => '律',
  '��' => '慄',
  '��' => '栗',
  '��' => '率',
  '��' => '隆',
  '��' => '勒',
  '��' => '肋',
  '��' => '凜',
  '��' => '凌',
  '��' => '楞',
  '��' => '稜',
  '��' => '綾',
  '��' => '菱',
  '��' => '陵',
  '��' => '俚',
  '��' => '利',
  '��' => '厘',
  '��' => '吏',
  '��' => '唎',
  '��' => '履',
  '��' => '悧',
  '��' => '李',
  '��' => '梨',
  '��' => '浬',
  '��' => '犁',
  '��' => '狸',
  '��' => '理',
  '��' => '璃',
  '��' => '異',
  '��' => '痢',
  '��' => '籬',
  '��' => '罹',
  '��' => '羸',
  '��' => '莉',
  '��' => '裏',
  '��' => '裡',
  '��' => '里',
  '��' => '釐',
  '��' => '離',
  '��' => '鯉',
  '��' => '吝',
  '��' => '潾',
  '��' => '燐',
  '��' => '璘',
  '��' => '藺',
  '��' => '躪',
  '��' => '隣',
  '��' => '鱗',
  '��' => '麟',
  '��' => '林',
  '��' => '淋',
  '��' => '琳',
  '��' => '臨',
  '��' => '霖',
  '��' => '砬',
  'ء' => '立',
  'آ' => '笠',
  'أ' => '粒',
  'ؤ' => '摩',
  'إ' => '瑪',
  'ئ' => '痲',
  'ا' => '碼',
  'ب' => '磨',
  'ة' => '馬',
  'ت' => '魔',
  'ث' => '麻',
  'ج' => '寞',
  'ح' => '幕',
  'خ' => '漠',
  'د' => '膜',
  'ذ' => '莫',
  'ر' => '邈',
  'ز' => '万',
  'س' => '卍',
  'ش' => '娩',
  'ص' => '巒',
  'ض' => '彎',
  'ط' => '慢',
  'ظ' => '挽',
  'ع' => '晩',
  'غ' => '曼',
  'ػ' => '滿',
  'ؼ' => '漫',
  'ؽ' => '灣',
  'ؾ' => '瞞',
  'ؿ' => '萬',
  '��' => '蔓',
  '��' => '蠻',
  '��' => '輓',
  '��' => '饅',
  '��' => '鰻',
  '��' => '唜',
  '��' => '抹',
  '��' => '末',
  '��' => '沫',
  '��' => '茉',
  '��' => '襪',
  '��' => '靺',
  '��' => '亡',
  '��' => '妄',
  '��' => '忘',
  '��' => '忙',
  '��' => '望',
  '��' => '網',
  '��' => '罔',
  '��' => '芒',
  '��' => '茫',
  '��' => '莽',
  '��' => '輞',
  '��' => '邙',
  '��' => '埋',
  '��' => '妹',
  '��' => '媒',
  '��' => '寐',
  '��' => '昧',
  '��' => '枚',
  '��' => '梅',
  '��' => '每',
  '��' => '煤',
  '��' => '罵',
  '��' => '買',
  '��' => '賣',
  '��' => '邁',
  '��' => '魅',
  '��' => '脈',
  '��' => '貊',
  '��' => '陌',
  '��' => '驀',
  '��' => '麥',
  '��' => '孟',
  '��' => '氓',
  '��' => '猛',
  '��' => '盲',
  '��' => '盟',
  '��' => '萌',
  '��' => '冪',
  '��' => '覓',
  '��' => '免',
  '��' => '冕',
  '��' => '勉',
  '��' => '棉',
  '��' => '沔',
  '��' => '眄',
  '��' => '眠',
  '��' => '綿',
  '��' => '緬',
  '��' => '面',
  '��' => '麵',
  '��' => '滅',
  '١' => '蔑',
  '٢' => '冥',
  '٣' => '名',
  '٤' => '命',
  '٥' => '明',
  '٦' => '暝',
  '٧' => '椧',
  '٨' => '溟',
  '٩' => '皿',
  '٪' => '瞑',
  '٫' => '茗',
  '٬' => '蓂',
  '٭' => '螟',
  'ٮ' => '酩',
  'ٯ' => '銘',
  'ٰ' => '鳴',
  'ٱ' => '袂',
  'ٲ' => '侮',
  'ٳ' => '冒',
  'ٴ' => '募',
  'ٵ' => '姆',
  'ٶ' => '帽',
  'ٷ' => '慕',
  'ٸ' => '摸',
  'ٹ' => '摹',
  'ٺ' => '暮',
  'ٻ' => '某',
  'ټ' => '模',
  'ٽ' => '母',
  'پ' => '毛',
  'ٿ' => '牟',
  '��' => '牡',
  '��' => '瑁',
  '��' => '眸',
  '��' => '矛',
  '��' => '耗',
  '��' => '芼',
  '��' => '茅',
  '��' => '謀',
  '��' => '謨',
  '��' => '貌',
  '��' => '木',
  '��' => '沐',
  '��' => '牧',
  '��' => '目',
  '��' => '睦',
  '��' => '穆',
  '��' => '鶩',
  '��' => '歿',
  '��' => '沒',
  '��' => '夢',
  '��' => '朦',
  '��' => '蒙',
  '��' => '卯',
  '��' => '墓',
  '��' => '妙',
  '��' => '廟',
  '��' => '描',
  '��' => '昴',
  '��' => '杳',
  '��' => '渺',
  '��' => '猫',
  '��' => '竗',
  '��' => '苗',
  '��' => '錨',
  '��' => '務',
  '��' => '巫',
  '��' => '憮',
  '��' => '懋',
  '��' => '戊',
  '��' => '拇',
  '��' => '撫',
  '��' => '无',
  '��' => '楙',
  '��' => '武',
  '��' => '毋',
  '��' => '無',
  '��' => '珷',
  '��' => '畝',
  '��' => '繆',
  '��' => '舞',
  '��' => '茂',
  '��' => '蕪',
  '��' => '誣',
  '��' => '貿',
  '��' => '霧',
  '��' => '鵡',
  '��' => '墨',
  '��' => '默',
  '��' => '們',
  '��' => '刎',
  '��' => '吻',
  '��' => '問',
  '��' => '文',
  'ڡ' => '汶',
  'ڢ' => '紊',
  'ڣ' => '紋',
  'ڤ' => '聞',
  'ڥ' => '蚊',
  'ڦ' => '門',
  'ڧ' => '雯',
  'ڨ' => '勿',
  'ک' => '沕',
  'ڪ' => '物',
  'ګ' => '味',
  'ڬ' => '媚',
  'ڭ' => '尾',
  'ڮ' => '嵋',
  'گ' => '彌',
  'ڰ' => '微',
  'ڱ' => '未',
  'ڲ' => '梶',
  'ڳ' => '楣',
  'ڴ' => '渼',
  'ڵ' => '湄',
  'ڶ' => '眉',
  'ڷ' => '米',
  'ڸ' => '美',
  'ڹ' => '薇',
  'ں' => '謎',
  'ڻ' => '迷',
  'ڼ' => '靡',
  'ڽ' => '黴',
  'ھ' => '岷',
  'ڿ' => '悶',
  '��' => '愍',
  '��' => '憫',
  '��' => '敏',
  '��' => '旻',
  '��' => '旼',
  '��' => '民',
  '��' => '泯',
  '��' => '玟',
  '��' => '珉',
  '��' => '緡',
  '��' => '閔',
  '��' => '密',
  '��' => '蜜',
  '��' => '謐',
  '��' => '剝',
  '��' => '博',
  '��' => '拍',
  '��' => '搏',
  '��' => '撲',
  '��' => '朴',
  '��' => '樸',
  '��' => '泊',
  '��' => '珀',
  '��' => '璞',
  '��' => '箔',
  '��' => '粕',
  '��' => '縛',
  '��' => '膊',
  '��' => '舶',
  '��' => '薄',
  '��' => '迫',
  '��' => '雹',
  '��' => '駁',
  '��' => '伴',
  '��' => '半',
  '��' => '反',
  '��' => '叛',
  '��' => '拌',
  '��' => '搬',
  '��' => '攀',
  '��' => '斑',
  '��' => '槃',
  '��' => '泮',
  '��' => '潘',
  '��' => '班',
  '��' => '畔',
  '��' => '瘢',
  '��' => '盤',
  '��' => '盼',
  '��' => '磐',
  '��' => '磻',
  '��' => '礬',
  '��' => '絆',
  '��' => '般',
  '��' => '蟠',
  '��' => '返',
  '��' => '頒',
  '��' => '飯',
  '��' => '勃',
  '��' => '拔',
  '��' => '撥',
  '��' => '渤',
  '��' => '潑',
  'ۡ' => '發',
  'ۢ' => '跋',
  'ۣ' => '醱',
  'ۤ' => '鉢',
  'ۥ' => '髮',
  'ۦ' => '魃',
  'ۧ' => '倣',
  'ۨ' => '傍',
  '۩' => '坊',
  '۪' => '妨',
  '۫' => '尨',
  '۬' => '幇',
  'ۭ' => '彷',
  'ۮ' => '房',
  'ۯ' => '放',
  '۰' => '方',
  '۱' => '旁',
  '۲' => '昉',
  '۳' => '枋',
  '۴' => '榜',
  '۵' => '滂',
  '۶' => '磅',
  '۷' => '紡',
  '۸' => '肪',
  '۹' => '膀',
  'ۺ' => '舫',
  'ۻ' => '芳',
  'ۼ' => '蒡',
  '۽' => '蚌',
  '۾' => '訪',
  'ۿ' => '謗',
  '��' => '邦',
  '��' => '防',
  '��' => '龐',
  '��' => '倍',
  '��' => '俳',
  '��' => '北',
  '��' => '培',
  '��' => '徘',
  '��' => '拜',
  '��' => '排',
  '��' => '杯',
  '��' => '湃',
  '��' => '焙',
  '��' => '盃',
  '��' => '背',
  '��' => '胚',
  '��' => '裴',
  '��' => '裵',
  '��' => '褙',
  '��' => '賠',
  '��' => '輩',
  '��' => '配',
  '��' => '陪',
  '��' => '伯',
  '��' => '佰',
  '��' => '帛',
  '��' => '柏',
  '��' => '栢',
  '��' => '白',
  '��' => '百',
  '��' => '魄',
  '��' => '幡',
  '��' => '樊',
  '��' => '煩',
  '��' => '燔',
  '��' => '番',
  '��' => '磻',
  '��' => '繁',
  '��' => '蕃',
  '��' => '藩',
  '��' => '飜',
  '��' => '伐',
  '��' => '筏',
  '��' => '罰',
  '��' => '閥',
  '��' => '凡',
  '��' => '帆',
  '��' => '梵',
  '��' => '氾',
  '��' => '汎',
  '��' => '泛',
  '��' => '犯',
  '��' => '範',
  '��' => '范',
  '��' => '法',
  '��' => '琺',
  '��' => '僻',
  '��' => '劈',
  '��' => '壁',
  '��' => '擘',
  '��' => '檗',
  '��' => '璧',
  '��' => '癖',
  'ܡ' => '碧',
  'ܢ' => '蘗',
  'ܣ' => '闢',
  'ܤ' => '霹',
  'ܥ' => '便',
  'ܦ' => '卞',
  'ܧ' => '弁',
  'ܨ' => '變',
  'ܩ' => '辨',
  'ܪ' => '辯',
  'ܫ' => '邊',
  'ܬ' => '別',
  'ܭ' => '瞥',
  'ܮ' => '鱉',
  'ܯ' => '鼈',
  'ܰ' => '丙',
  'ܱ' => '倂',
  'ܲ' => '兵',
  'ܳ' => '屛',
  'ܴ' => '幷',
  'ܵ' => '昞',
  'ܶ' => '昺',
  'ܷ' => '柄',
  'ܸ' => '棅',
  'ܹ' => '炳',
  'ܺ' => '甁',
  'ܻ' => '病',
  'ܼ' => '秉',
  'ܽ' => '竝',
  'ܾ' => '輧',
  'ܿ' => '餠',
  '��' => '騈',
  '��' => '保',
  '��' => '堡',
  '��' => '報',
  '��' => '寶',
  '��' => '普',
  '��' => '步',
  '��' => '洑',
  '��' => '湺',
  '��' => '潽',
  '��' => '珤',
  '��' => '甫',
  '��' => '菩',
  '��' => '補',
  '��' => '褓',
  '��' => '譜',
  '��' => '輔',
  '��' => '伏',
  '��' => '僕',
  '��' => '匐',
  '��' => '卜',
  '��' => '宓',
  '��' => '復',
  '��' => '服',
  '��' => '福',
  '��' => '腹',
  '��' => '茯',
  '��' => '蔔',
  '��' => '複',
  '��' => '覆',
  '��' => '輹',
  '��' => '輻',
  '��' => '馥',
  '��' => '鰒',
  '��' => '本',
  '��' => '乶',
  '��' => '俸',
  '��' => '奉',
  '��' => '封',
  '��' => '峯',
  '��' => '峰',
  '��' => '捧',
  '��' => '棒',
  '��' => '烽',
  '��' => '熢',
  '��' => '琫',
  '��' => '縫',
  '��' => '蓬',
  '��' => '蜂',
  '��' => '逢',
  '��' => '鋒',
  '��' => '鳳',
  '��' => '不',
  '��' => '付',
  '��' => '俯',
  '��' => '傅',
  '��' => '剖',
  '��' => '副',
  '��' => '否',
  '��' => '咐',
  '��' => '埠',
  '��' => '夫',
  '��' => '婦',
  'ݡ' => '孚',
  'ݢ' => '孵',
  'ݣ' => '富',
  'ݤ' => '府',
  'ݥ' => '復',
  'ݦ' => '扶',
  'ݧ' => '敷',
  'ݨ' => '斧',
  'ݩ' => '浮',
  'ݪ' => '溥',
  'ݫ' => '父',
  'ݬ' => '符',
  'ݭ' => '簿',
  'ݮ' => '缶',
  'ݯ' => '腐',
  'ݰ' => '腑',
  'ݱ' => '膚',
  'ݲ' => '艀',
  'ݳ' => '芙',
  'ݴ' => '莩',
  'ݵ' => '訃',
  'ݶ' => '負',
  'ݷ' => '賦',
  'ݸ' => '賻',
  'ݹ' => '赴',
  'ݺ' => '趺',
  'ݻ' => '部',
  'ݼ' => '釜',
  'ݽ' => '阜',
  'ݾ' => '附',
  'ݿ' => '駙',
  '��' => '鳧',
  '��' => '北',
  '��' => '分',
  '��' => '吩',
  '��' => '噴',
  '��' => '墳',
  '��' => '奔',
  '��' => '奮',
  '��' => '忿',
  '��' => '憤',
  '��' => '扮',
  '��' => '昐',
  '��' => '汾',
  '��' => '焚',
  '��' => '盆',
  '��' => '粉',
  '��' => '糞',
  '��' => '紛',
  '��' => '芬',
  '��' => '賁',
  '��' => '雰',
  '��' => '不',
  '��' => '佛',
  '��' => '弗',
  '��' => '彿',
  '��' => '拂',
  '��' => '崩',
  '��' => '朋',
  '��' => '棚',
  '��' => '硼',
  '��' => '繃',
  '��' => '鵬',
  '��' => '丕',
  '��' => '備',
  '��' => '匕',
  '��' => '匪',
  '��' => '卑',
  '��' => '妃',
  '��' => '婢',
  '��' => '庇',
  '��' => '悲',
  '��' => '憊',
  '��' => '扉',
  '��' => '批',
  '��' => '斐',
  '��' => '枇',
  '��' => '榧',
  '��' => '比',
  '��' => '毖',
  '��' => '毗',
  '��' => '毘',
  '��' => '沸',
  '��' => '泌',
  '��' => '琵',
  '��' => '痺',
  '��' => '砒',
  '��' => '碑',
  '��' => '秕',
  '��' => '秘',
  '��' => '粃',
  '��' => '緋',
  '��' => '翡',
  '��' => '肥',
  'ޡ' => '脾',
  'ޢ' => '臂',
  'ޣ' => '菲',
  'ޤ' => '蜚',
  'ޥ' => '裨',
  'ަ' => '誹',
  'ާ' => '譬',
  'ި' => '費',
  'ީ' => '鄙',
  'ު' => '非',
  'ޫ' => '飛',
  'ެ' => '鼻',
  'ޭ' => '嚬',
  'ޮ' => '嬪',
  'ޯ' => '彬',
  'ް' => '斌',
  'ޱ' => '檳',
  '޲' => '殯',
  '޳' => '浜',
  '޴' => '濱',
  '޵' => '瀕',
  '޶' => '牝',
  '޷' => '玭',
  '޸' => '貧',
  '޹' => '賓',
  '޺' => '頻',
  '޻' => '憑',
  '޼' => '氷',
  '޽' => '聘',
  '޾' => '騁',
  '޿' => '乍',
  '��' => '事',
  '��' => '些',
  '��' => '仕',
  '��' => '伺',
  '��' => '似',
  '��' => '使',
  '��' => '俟',
  '��' => '僿',
  '��' => '史',
  '��' => '司',
  '��' => '唆',
  '��' => '嗣',
  '��' => '四',
  '��' => '士',
  '��' => '奢',
  '��' => '娑',
  '��' => '寫',
  '��' => '寺',
  '��' => '射',
  '��' => '巳',
  '��' => '師',
  '��' => '徙',
  '��' => '思',
  '��' => '捨',
  '��' => '斜',
  '��' => '斯',
  '��' => '柶',
  '��' => '査',
  '��' => '梭',
  '��' => '死',
  '��' => '沙',
  '��' => '泗',
  '��' => '渣',
  '��' => '瀉',
  '��' => '獅',
  '��' => '砂',
  '��' => '社',
  '��' => '祀',
  '��' => '祠',
  '��' => '私',
  '��' => '篩',
  '��' => '紗',
  '��' => '絲',
  '��' => '肆',
  '��' => '舍',
  '��' => '莎',
  '��' => '蓑',
  '��' => '蛇',
  '��' => '裟',
  '��' => '詐',
  '��' => '詞',
  '��' => '謝',
  '��' => '賜',
  '��' => '赦',
  '��' => '辭',
  '��' => '邪',
  '��' => '飼',
  '��' => '駟',
  '��' => '麝',
  '��' => '削',
  '��' => '數',
  '��' => '朔',
  '��' => '索',
  'ߡ' => '傘',
  'ߢ' => '刪',
  'ߣ' => '山',
  'ߤ' => '散',
  'ߥ' => '汕',
  'ߦ' => '珊',
  'ߧ' => '産',
  'ߨ' => '疝',
  'ߩ' => '算',
  'ߪ' => '蒜',
  '߫' => '酸',
  '߬' => '霰',
  '߭' => '乷',
  '߮' => '撒',
  '߯' => '殺',
  '߰' => '煞',
  '߱' => '薩',
  '߲' => '三',
  '߳' => '參',
  'ߴ' => '杉',
  'ߵ' => '森',
  '߶' => '渗',
  '߷' => '芟',
  '߸' => '蔘',
  '߹' => '衫',
  'ߺ' => '揷',
  '߻' => '澁',
  '߼' => '鈒',
  '߽' => '颯',
  '߾' => '上',
  '߿' => '傷',
  '��' => '像',
  '��' => '償',
  '��' => '商',
  '��' => '喪',
  '��' => '嘗',
  '��' => '孀',
  '��' => '尙',
  '��' => '峠',
  '��' => '常',
  '��' => '床',
  '��' => '庠',
  '��' => '廂',
  '��' => '想',
  '��' => '桑',
  '��' => '橡',
  '��' => '湘',
  '��' => '爽',
  '��' => '牀',
  '��' => '狀',
  '��' => '相',
  '��' => '祥',
  '��' => '箱',
  '��' => '翔',
  '��' => '裳',
  '��' => '觴',
  '��' => '詳',
  '��' => '象',
  '��' => '賞',
  '��' => '霜',
  '��' => '塞',
  '��' => '璽',
  '��' => '賽',
  '��' => '嗇',
  '��' => '塞',
  '��' => '穡',
  '��' => '索',
  '��' => '色',
  '��' => '牲',
  '��' => '生',
  '��' => '甥',
  '��' => '省',
  '��' => '笙',
  '��' => '墅',
  '��' => '壻',
  '��' => '嶼',
  '��' => '序',
  '��' => '庶',
  '��' => '徐',
  '��' => '恕',
  '��' => '抒',
  '��' => '捿',
  '��' => '敍',
  '��' => '暑',
  '��' => '曙',
  '��' => '書',
  '��' => '栖',
  '��' => '棲',
  '��' => '犀',
  '��' => '瑞',
  '��' => '筮',
  '��' => '絮',
  '��' => '緖',
  '��' => '署',
  '�' => '胥',
  '�' => '舒',
  '�' => '薯',
  '�' => '西',
  '�' => '誓',
  '�' => '逝',
  '�' => '鋤',
  '�' => '黍',
  '�' => '鼠',
  '�' => '夕',
  '�' => '奭',
  '�' => '席',
  '�' => '惜',
  '�' => '昔',
  '�' => '晳',
  '�' => '析',
  '�' => '汐',
  '�' => '淅',
  '�' => '潟',
  '�' => '石',
  '�' => '碩',
  '�' => '蓆',
  '�' => '釋',
  '�' => '錫',
  '�' => '仙',
  '�' => '僊',
  '�' => '先',
  '�' => '善',
  '�' => '嬋',
  '�' => '宣',
  '�' => '扇',
  '��' => '敾',
  '��' => '旋',
  '��' => '渲',
  '��' => '煽',
  '��' => '琁',
  '��' => '瑄',
  '��' => '璇',
  '��' => '璿',
  '��' => '癬',
  '��' => '禪',
  '��' => '線',
  '��' => '繕',
  '��' => '羨',
  '��' => '腺',
  '��' => '膳',
  '��' => '船',
  '��' => '蘚',
  '��' => '蟬',
  '��' => '詵',
  '��' => '跣',
  '��' => '選',
  '��' => '銑',
  '��' => '鐥',
  '��' => '饍',
  '��' => '鮮',
  '��' => '卨',
  '��' => '屑',
  '��' => '楔',
  '��' => '泄',
  '��' => '洩',
  '��' => '渫',
  '��' => '舌',
  '��' => '薛',
  '��' => '褻',
  '��' => '設',
  '��' => '說',
  '��' => '雪',
  '��' => '齧',
  '��' => '剡',
  '��' => '暹',
  '��' => '殲',
  '��' => '纖',
  '��' => '蟾',
  '��' => '贍',
  '��' => '閃',
  '��' => '陝',
  '��' => '攝',
  '��' => '涉',
  '��' => '燮',
  '��' => '葉',
  '��' => '城',
  '��' => '姓',
  '��' => '宬',
  '��' => '性',
  '��' => '惺',
  '��' => '成',
  '��' => '星',
  '��' => '晟',
  '��' => '猩',
  '��' => '珹',
  '��' => '盛',
  '��' => '省',
  '��' => '筬',
  '�' => '聖',
  '�' => '聲',
  '�' => '腥',
  '�' => '誠',
  '�' => '醒',
  '�' => '世',
  '�' => '勢',
  '�' => '歲',
  '�' => '洗',
  '�' => '稅',
  '�' => '笹',
  '�' => '細',
  '�' => '說',
  '�' => '貰',
  '�' => '召',
  '�' => '嘯',
  '�' => '塑',
  '�' => '宵',
  '�' => '小',
  '�' => '少',
  '�' => '巢',
  '�' => '所',
  '�' => '掃',
  '�' => '搔',
  '�' => '昭',
  '�' => '梳',
  '�' => '沼',
  '�' => '消',
  '�' => '溯',
  '�' => '瀟',
  '�' => '炤',
  '��' => '燒',
  '��' => '甦',
  '��' => '疏',
  '��' => '疎',
  '��' => '瘙',
  '��' => '笑',
  '��' => '篠',
  '��' => '簫',
  '��' => '素',
  '��' => '紹',
  '��' => '蔬',
  '��' => '蕭',
  '��' => '蘇',
  '��' => '訴',
  '��' => '逍',
  '��' => '遡',
  '��' => '邵',
  '��' => '銷',
  '��' => '韶',
  '��' => '騷',
  '��' => '俗',
  '��' => '屬',
  '��' => '束',
  '��' => '涑',
  '��' => '粟',
  '��' => '續',
  '��' => '謖',
  '��' => '贖',
  '��' => '速',
  '��' => '孫',
  '��' => '巽',
  '��' => '損',
  '��' => '蓀',
  '��' => '遜',
  '��' => '飡',
  '��' => '率',
  '��' => '宋',
  '��' => '悚',
  '��' => '松',
  '��' => '淞',
  '��' => '訟',
  '��' => '誦',
  '��' => '送',
  '��' => '頌',
  '��' => '刷',
  '��' => '殺',
  '��' => '灑',
  '��' => '碎',
  '��' => '鎖',
  '��' => '衰',
  '��' => '釗',
  '��' => '修',
  '��' => '受',
  '��' => '嗽',
  '��' => '囚',
  '��' => '垂',
  '��' => '壽',
  '��' => '嫂',
  '��' => '守',
  '��' => '岫',
  '��' => '峀',
  '��' => '帥',
  '��' => '愁',
  '�' => '戍',
  '�' => '手',
  '�' => '授',
  '�' => '搜',
  '�' => '收',
  '�' => '數',
  '�' => '樹',
  '�' => '殊',
  '�' => '水',
  '�' => '洙',
  '�' => '漱',
  '�' => '燧',
  '�' => '狩',
  '�' => '獸',
  '�' => '琇',
  '�' => '璲',
  '�' => '瘦',
  '�' => '睡',
  '�' => '秀',
  '�' => '穗',
  '�' => '竪',
  '�' => '粹',
  '�' => '綏',
  '�' => '綬',
  '�' => '繡',
  '�' => '羞',
  '�' => '脩',
  '�' => '茱',
  '�' => '蒐',
  '�' => '蓚',
  '�' => '藪',
  '��' => '袖',
  '��' => '誰',
  '��' => '讐',
  '��' => '輸',
  '��' => '遂',
  '��' => '邃',
  '��' => '酬',
  '��' => '銖',
  '��' => '銹',
  '��' => '隋',
  '��' => '隧',
  '��' => '隨',
  '��' => '雖',
  '��' => '需',
  '��' => '須',
  '��' => '首',
  '��' => '髓',
  '��' => '鬚',
  '��' => '叔',
  '��' => '塾',
  '��' => '夙',
  '��' => '孰',
  '��' => '宿',
  '��' => '淑',
  '��' => '潚',
  '��' => '熟',
  '��' => '琡',
  '��' => '璹',
  '��' => '肅',
  '��' => '菽',
  '��' => '巡',
  '��' => '徇',
  '��' => '循',
  '��' => '恂',
  '��' => '旬',
  '��' => '栒',
  '��' => '楯',
  '��' => '橓',
  '��' => '殉',
  '��' => '洵',
  '��' => '淳',
  '��' => '珣',
  '��' => '盾',
  '��' => '瞬',
  '��' => '筍',
  '��' => '純',
  '��' => '脣',
  '��' => '舜',
  '��' => '荀',
  '��' => '蓴',
  '��' => '蕣',
  '��' => '詢',
  '��' => '諄',
  '��' => '醇',
  '��' => '錞',
  '��' => '順',
  '��' => '馴',
  '��' => '戌',
  '��' => '術',
  '��' => '述',
  '��' => '鉥',
  '��' => '崇',
  '��' => '崧',
  '�' => '嵩',
  '�' => '瑟',
  '�' => '膝',
  '�' => '蝨',
  '�' => '濕',
  '�' => '拾',
  '�' => '習',
  '�' => '褶',
  '�' => '襲',
  '�' => '丞',
  '�' => '乘',
  '�' => '僧',
  '�' => '勝',
  '�' => '升',
  '�' => '承',
  '�' => '昇',
  '�' => '繩',
  '�' => '蠅',
  '�' => '陞',
  '�' => '侍',
  '�' => '匙',
  '�' => '嘶',
  '�' => '始',
  '�' => '媤',
  '�' => '尸',
  '�' => '屎',
  '�' => '屍',
  '�' => '市',
  '�' => '弑',
  '�' => '恃',
  '�' => '施',
  '��' => '是',
  '��' => '時',
  '��' => '枾',
  '��' => '柴',
  '��' => '猜',
  '��' => '矢',
  '��' => '示',
  '��' => '翅',
  '��' => '蒔',
  '��' => '蓍',
  '��' => '視',
  '��' => '試',
  '��' => '詩',
  '��' => '諡',
  '��' => '豕',
  '��' => '豺',
  '��' => '埴',
  '��' => '寔',
  '��' => '式',
  '��' => '息',
  '��' => '拭',
  '��' => '植',
  '��' => '殖',
  '��' => '湜',
  '��' => '熄',
  '��' => '篒',
  '��' => '蝕',
  '��' => '識',
  '��' => '軾',
  '��' => '食',
  '��' => '飾',
  '��' => '伸',
  '��' => '侁',
  '��' => '信',
  '��' => '呻',
  '��' => '娠',
  '��' => '宸',
  '��' => '愼',
  '��' => '新',
  '��' => '晨',
  '��' => '燼',
  '��' => '申',
  '��' => '神',
  '��' => '紳',
  '��' => '腎',
  '��' => '臣',
  '��' => '莘',
  '��' => '薪',
  '��' => '藎',
  '��' => '蜃',
  '��' => '訊',
  '��' => '身',
  '��' => '辛',
  '��' => '辰',
  '��' => '迅',
  '��' => '失',
  '��' => '室',
  '��' => '實',
  '��' => '悉',
  '��' => '審',
  '��' => '尋',
  '��' => '心',
  '��' => '沁',
  '�' => '沈',
  '�' => '深',
  '�' => '瀋',
  '�' => '甚',
  '�' => '芯',
  '�' => '諶',
  '�' => '什',
  '�' => '十',
  '�' => '拾',
  '�' => '雙',
  '�' => '氏',
  '�' => '亞',
  '�' => '俄',
  '�' => '兒',
  '�' => '啞',
  '�' => '娥',
  '�' => '峨',
  '�' => '我',
  '�' => '牙',
  '�' => '芽',
  '�' => '莪',
  '�' => '蛾',
  '�' => '衙',
  '�' => '訝',
  '�' => '阿',
  '�' => '雅',
  '�' => '餓',
  '�' => '鴉',
  '�' => '鵝',
  '�' => '堊',
  '�' => '岳',
  '��' => '嶽',
  '��' => '幄',
  '��' => '惡',
  '��' => '愕',
  '��' => '握',
  '��' => '樂',
  '��' => '渥',
  '��' => '鄂',
  '��' => '鍔',
  '��' => '顎',
  '��' => '鰐',
  '��' => '齷',
  '��' => '安',
  '��' => '岸',
  '��' => '按',
  '��' => '晏',
  '��' => '案',
  '��' => '眼',
  '��' => '雁',
  '��' => '鞍',
  '��' => '顔',
  '��' => '鮟',
  '��' => '斡',
  '��' => '謁',
  '��' => '軋',
  '��' => '閼',
  '��' => '唵',
  '��' => '岩',
  '��' => '巖',
  '��' => '庵',
  '��' => '暗',
  '��' => '癌',
  '��' => '菴',
  '��' => '闇',
  '��' => '壓',
  '��' => '押',
  '��' => '狎',
  '��' => '鴨',
  '��' => '仰',
  '��' => '央',
  '��' => '怏',
  '��' => '昻',
  '��' => '殃',
  '��' => '秧',
  '��' => '鴦',
  '��' => '厓',
  '��' => '哀',
  '��' => '埃',
  '��' => '崖',
  '��' => '愛',
  '��' => '曖',
  '��' => '涯',
  '��' => '碍',
  '��' => '艾',
  '��' => '隘',
  '��' => '靄',
  '��' => '厄',
  '��' => '扼',
  '��' => '掖',
  '��' => '液',
  '��' => '縊',
  '��' => '腋',
  '��' => '額',
  '�' => '櫻',
  '�' => '罌',
  '�' => '鶯',
  '�' => '鸚',
  '�' => '也',
  '�' => '倻',
  '�' => '冶',
  '�' => '夜',
  '�' => '惹',
  '�' => '揶',
  '�' => '椰',
  '�' => '爺',
  '�' => '耶',
  '�' => '若',
  '�' => '野',
  '�' => '弱',
  '�' => '掠',
  '�' => '略',
  '�' => '約',
  '�' => '若',
  '�' => '葯',
  '�' => '蒻',
  '�' => '藥',
  '�' => '躍',
  '�' => '亮',
  '�' => '佯',
  '�' => '兩',
  '�' => '凉',
  '�' => '壤',
  '�' => '孃',
  '�' => '恙',
  '��' => '揚',
  '��' => '攘',
  '��' => '敭',
  '��' => '暘',
  '��' => '梁',
  '��' => '楊',
  '��' => '樣',
  '��' => '洋',
  '��' => '瀁',
  '��' => '煬',
  '��' => '痒',
  '��' => '瘍',
  '��' => '禳',
  '��' => '穰',
  '��' => '糧',
  '��' => '羊',
  '��' => '良',
  '��' => '襄',
  '��' => '諒',
  '��' => '讓',
  '��' => '釀',
  '��' => '陽',
  '��' => '量',
  '��' => '養',
  '��' => '圄',
  '��' => '御',
  '��' => '於',
  '��' => '漁',
  '��' => '瘀',
  '��' => '禦',
  '��' => '語',
  '��' => '馭',
  '��' => '魚',
  '��' => '齬',
  '��' => '億',
  '��' => '憶',
  '��' => '抑',
  '��' => '檍',
  '��' => '臆',
  '��' => '偃',
  '��' => '堰',
  '��' => '彦',
  '��' => '焉',
  '��' => '言',
  '��' => '諺',
  '��' => '孼',
  '��' => '蘖',
  '��' => '俺',
  '��' => '儼',
  '��' => '嚴',
  '��' => '奄',
  '��' => '掩',
  '��' => '淹',
  '��' => '嶪',
  '��' => '業',
  '��' => '円',
  '��' => '予',
  '��' => '余',
  '��' => '勵',
  '��' => '呂',
  '��' => '女',
  '��' => '如',
  '��' => '廬',
  '�' => '旅',
  '�' => '歟',
  '�' => '汝',
  '�' => '濾',
  '�' => '璵',
  '�' => '礖',
  '�' => '礪',
  '�' => '與',
  '�' => '艅',
  '�' => '茹',
  '�' => '輿',
  '�' => '轝',
  '�' => '閭',
  '�' => '餘',
  '�' => '驪',
  '�' => '麗',
  '�' => '黎',
  '�' => '亦',
  '�' => '力',
  '�' => '域',
  '�' => '役',
  '�' => '易',
  '�' => '曆',
  '�' => '歷',
  '�' => '疫',
  '�' => '繹',
  '�' => '譯',
  '�' => '轢',
  '�' => '逆',
  '�' => '驛',
  '�' => '嚥',
  '��' => '堧',
  '��' => '姸',
  '��' => '娟',
  '��' => '宴',
  '��' => '年',
  '��' => '延',
  '��' => '憐',
  '��' => '戀',
  '��' => '捐',
  '��' => '挻',
  '��' => '撚',
  '��' => '椽',
  '��' => '沇',
  '��' => '沿',
  '��' => '涎',
  '��' => '涓',
  '��' => '淵',
  '��' => '演',
  '��' => '漣',
  '��' => '烟',
  '��' => '然',
  '��' => '煙',
  '��' => '煉',
  '��' => '燃',
  '��' => '燕',
  '��' => '璉',
  '��' => '硏',
  '��' => '硯',
  '��' => '秊',
  '��' => '筵',
  '��' => '緣',
  '��' => '練',
  '��' => '縯',
  '��' => '聯',
  '��' => '衍',
  '��' => '軟',
  '��' => '輦',
  '��' => '蓮',
  '��' => '連',
  '��' => '鉛',
  '��' => '鍊',
  '��' => '鳶',
  '��' => '列',
  '��' => '劣',
  '��' => '咽',
  '��' => '悅',
  '��' => '涅',
  '��' => '烈',
  '��' => '熱',
  '��' => '裂',
  '��' => '說',
  '��' => '閱',
  '��' => '厭',
  '��' => '廉',
  '��' => '念',
  '��' => '捻',
  '��' => '染',
  '��' => '殮',
  '��' => '炎',
  '��' => '焰',
  '��' => '琰',
  '��' => '艶',
  '��' => '苒',
  '�' => '簾',
  '�' => '閻',
  '�' => '髥',
  '�' => '鹽',
  '�' => '曄',
  '�' => '獵',
  '�' => '燁',
  '�' => '葉',
  '�' => '令',
  '�' => '囹',
  '�' => '塋',
  '�' => '寧',
  '�' => '嶺',
  '�' => '嶸',
  '�' => '影',
  '�' => '怜',
  '�' => '映',
  '�' => '暎',
  '�' => '楹',
  '�' => '榮',
  '�' => '永',
  '�' => '泳',
  '�' => '渶',
  '�' => '潁',
  '�' => '濚',
  '�' => '瀛',
  '�' => '瀯',
  '�' => '煐',
  '�' => '營',
  '�' => '獰',
  '�' => '玲',
  '��' => '瑛',
  '��' => '瑩',
  '��' => '瓔',
  '��' => '盈',
  '��' => '穎',
  '��' => '纓',
  '��' => '羚',
  '��' => '聆',
  '��' => '英',
  '��' => '詠',
  '��' => '迎',
  '��' => '鈴',
  '��' => '鍈',
  '��' => '零',
  '��' => '霙',
  '��' => '靈',
  '��' => '領',
  '��' => '乂',
  '��' => '倪',
  '��' => '例',
  '��' => '刈',
  '��' => '叡',
  '��' => '曳',
  '��' => '汭',
  '��' => '濊',
  '��' => '猊',
  '��' => '睿',
  '��' => '穢',
  '��' => '芮',
  '��' => '藝',
  '��' => '蘂',
  '��' => '禮',
  '��' => '裔',
  '��' => '詣',
  '��' => '譽',
  '��' => '豫',
  '��' => '醴',
  '��' => '銳',
  '��' => '隸',
  '��' => '霓',
  '��' => '預',
  '��' => '五',
  '��' => '伍',
  '��' => '俉',
  '��' => '傲',
  '��' => '午',
  '��' => '吾',
  '��' => '吳',
  '��' => '嗚',
  '��' => '塢',
  '��' => '墺',
  '��' => '奧',
  '��' => '娛',
  '��' => '寤',
  '��' => '悟',
  '��' => '惡',
  '��' => '懊',
  '��' => '敖',
  '��' => '旿',
  '��' => '晤',
  '��' => '梧',
  '��' => '汚',
  '��' => '澳',
  '�' => '烏',
  '�' => '熬',
  '�' => '獒',
  '�' => '筽',
  '�' => '蜈',
  '�' => '誤',
  '�' => '鰲',
  '�' => '鼇',
  '�' => '屋',
  '�' => '沃',
  '�' => '獄',
  '�' => '玉',
  '�' => '鈺',
  '�' => '溫',
  '�' => '瑥',
  '�' => '瘟',
  '�' => '穩',
  '�' => '縕',
  '�' => '蘊',
  '�' => '兀',
  '�' => '壅',
  '�' => '擁',
  '�' => '瓮',
  '�' => '甕',
  '�' => '癰',
  '�' => '翁',
  '�' => '邕',
  '�' => '雍',
  '�' => '饔',
  '�' => '渦',
  '�' => '瓦',
  '��' => '窩',
  '��' => '窪',
  '��' => '臥',
  '��' => '蛙',
  '��' => '蝸',
  '��' => '訛',
  '��' => '婉',
  '��' => '完',
  '��' => '宛',
  '��' => '梡',
  '��' => '椀',
  '��' => '浣',
  '��' => '玩',
  '��' => '琓',
  '��' => '琬',
  '��' => '碗',
  '��' => '緩',
  '��' => '翫',
  '��' => '脘',
  '��' => '腕',
  '��' => '莞',
  '��' => '豌',
  '��' => '阮',
  '��' => '頑',
  '��' => '曰',
  '��' => '往',
  '��' => '旺',
  '��' => '枉',
  '��' => '汪',
  '��' => '王',
  '��' => '倭',
  '��' => '娃',
  '��' => '歪',
  '��' => '矮',
  '��' => '外',
  '��' => '嵬',
  '��' => '巍',
  '��' => '猥',
  '��' => '畏',
  '��' => '了',
  '��' => '僚',
  '��' => '僥',
  '��' => '凹',
  '��' => '堯',
  '��' => '夭',
  '��' => '妖',
  '��' => '姚',
  '��' => '寥',
  '��' => '寮',
  '��' => '尿',
  '��' => '嶢',
  '��' => '拗',
  '��' => '搖',
  '��' => '撓',
  '��' => '擾',
  '��' => '料',
  '��' => '曜',
  '��' => '樂',
  '��' => '橈',
  '��' => '燎',
  '��' => '燿',
  '��' => '瑤',
  '��' => '療',
  '�' => '窈',
  '�' => '窯',
  '�' => '繇',
  '�' => '繞',
  '�' => '耀',
  '�' => '腰',
  '�' => '蓼',
  '�' => '蟯',
  '�' => '要',
  '�' => '謠',
  '�' => '遙',
  '�' => '遼',
  '�' => '邀',
  '�' => '饒',
  '�' => '慾',
  '�' => '欲',
  '�' => '浴',
  '�' => '縟',
  '�' => '褥',
  '�' => '辱',
  '�' => '俑',
  '�' => '傭',
  '�' => '冗',
  '�' => '勇',
  '�' => '埇',
  '�' => '墉',
  '�' => '容',
  '�' => '庸',
  '�' => '慂',
  '�' => '榕',
  '�' => '涌',
  '��' => '湧',
  '��' => '溶',
  '��' => '熔',
  '��' => '瑢',
  '��' => '用',
  '��' => '甬',
  '��' => '聳',
  '��' => '茸',
  '��' => '蓉',
  '��' => '踊',
  '��' => '鎔',
  '��' => '鏞',
  '��' => '龍',
  '��' => '于',
  '��' => '佑',
  '��' => '偶',
  '��' => '優',
  '��' => '又',
  '��' => '友',
  '��' => '右',
  '��' => '宇',
  '��' => '寓',
  '��' => '尤',
  '��' => '愚',
  '��' => '憂',
  '��' => '旴',
  '��' => '牛',
  '��' => '玗',
  '��' => '瑀',
  '��' => '盂',
  '��' => '祐',
  '��' => '禑',
  '��' => '禹',
  '��' => '紆',
  '��' => '羽',
  '��' => '芋',
  '��' => '藕',
  '��' => '虞',
  '��' => '迂',
  '��' => '遇',
  '��' => '郵',
  '��' => '釪',
  '��' => '隅',
  '��' => '雨',
  '��' => '雩',
  '��' => '勖',
  '��' => '彧',
  '��' => '旭',
  '��' => '昱',
  '��' => '栯',
  '��' => '煜',
  '��' => '稶',
  '��' => '郁',
  '��' => '頊',
  '��' => '云',
  '��' => '暈',
  '��' => '橒',
  '��' => '殞',
  '��' => '澐',
  '��' => '熉',
  '��' => '耘',
  '��' => '芸',
  '��' => '蕓',
  '�' => '運',
  '�' => '隕',
  '�' => '雲',
  '�' => '韻',
  '�' => '蔚',
  '�' => '鬱',
  '�' => '亐',
  '�' => '熊',
  '�' => '雄',
  '�' => '元',
  '�' => '原',
  '�' => '員',
  '�' => '圓',
  '�' => '園',
  '�' => '垣',
  '�' => '媛',
  '�' => '嫄',
  '�' => '寃',
  '�' => '怨',
  '�' => '愿',
  '�' => '援',
  '�' => '沅',
  '�' => '洹',
  '�' => '湲',
  '�' => '源',
  '�' => '爰',
  '�' => '猿',
  '�' => '瑗',
  '�' => '苑',
  '�' => '袁',
  '�' => '轅',
  '��' => '遠',
  '��' => '阮',
  '��' => '院',
  '��' => '願',
  '��' => '鴛',
  '��' => '月',
  '��' => '越',
  '��' => '鉞',
  '��' => '位',
  '��' => '偉',
  '��' => '僞',
  '��' => '危',
  '��' => '圍',
  '��' => '委',
  '��' => '威',
  '��' => '尉',
  '��' => '慰',
  '��' => '暐',
  '��' => '渭',
  '��' => '爲',
  '��' => '瑋',
  '��' => '緯',
  '��' => '胃',
  '��' => '萎',
  '��' => '葦',
  '��' => '蔿',
  '��' => '蝟',
  '��' => '衛',
  '��' => '褘',
  '��' => '謂',
  '��' => '違',
  '��' => '韋',
  '��' => '魏',
  '��' => '乳',
  '��' => '侑',
  '��' => '儒',
  '��' => '兪',
  '��' => '劉',
  '��' => '唯',
  '��' => '喩',
  '��' => '孺',
  '��' => '宥',
  '��' => '幼',
  '��' => '幽',
  '��' => '庾',
  '��' => '悠',
  '��' => '惟',
  '��' => '愈',
  '��' => '愉',
  '��' => '揄',
  '��' => '攸',
  '��' => '有',
  '��' => '杻',
  '��' => '柔',
  '��' => '柚',
  '��' => '柳',
  '��' => '楡',
  '��' => '楢',
  '��' => '油',
  '��' => '洧',
  '��' => '流',
  '��' => '游',
  '��' => '溜',
  '�' => '濡',
  '�' => '猶',
  '�' => '猷',
  '�' => '琉',
  '�' => '瑜',
  '�' => '由',
  '�' => '留',
  '�' => '癒',
  '�' => '硫',
  '�' => '紐',
  '�' => '維',
  '�' => '臾',
  '�' => '萸',
  '�' => '裕',
  '�' => '誘',
  '�' => '諛',
  '�' => '諭',
  '�' => '踰',
  '�' => '蹂',
  '�' => '遊',
  '�' => '逾',
  '�' => '遺',
  '�' => '酉',
  '�' => '釉',
  '�' => '鍮',
  '�' => '類',
  '�' => '六',
  '�' => '堉',
  '�' => '戮',
  '�' => '毓',
  '�' => '肉',
  '��' => '育',
  '��' => '陸',
  '��' => '倫',
  '��' => '允',
  '��' => '奫',
  '��' => '尹',
  '��' => '崙',
  '��' => '淪',
  '��' => '潤',
  '��' => '玧',
  '��' => '胤',
  '��' => '贇',
  '��' => '輪',
  '��' => '鈗',
  '��' => '閏',
  '��' => '律',
  '��' => '慄',
  '��' => '栗',
  '��' => '率',
  '��' => '聿',
  '��' => '戎',
  '��' => '瀜',
  '��' => '絨',
  '��' => '融',
  '��' => '隆',
  '��' => '垠',
  '��' => '恩',
  '��' => '慇',
  '��' => '殷',
  '��' => '誾',
  '��' => '銀',
  '��' => '隱',
  '��' => '乙',
  '��' => '吟',
  '��' => '淫',
  '��' => '蔭',
  '��' => '陰',
  '��' => '音',
  '��' => '飮',
  '��' => '揖',
  '��' => '泣',
  '��' => '邑',
  '��' => '凝',
  '��' => '應',
  '��' => '膺',
  '��' => '鷹',
  '��' => '依',
  '��' => '倚',
  '��' => '儀',
  '��' => '宜',
  '��' => '意',
  '��' => '懿',
  '��' => '擬',
  '��' => '椅',
  '��' => '毅',
  '��' => '疑',
  '��' => '矣',
  '��' => '義',
  '��' => '艤',
  '��' => '薏',
  '��' => '蟻',
  '��' => '衣',
  '��' => '誼',
  '�' => '議',
  '�' => '醫',
  '�' => '二',
  '�' => '以',
  '�' => '伊',
  '�' => '利',
  '�' => '吏',
  '�' => '夷',
  '�' => '姨',
  '�' => '履',
  '�' => '已',
  '�' => '弛',
  '�' => '彛',
  '�' => '怡',
  '�' => '易',
  '�' => '李',
  '�' => '梨',
  '�' => '泥',
  '�' => '爾',
  '�' => '珥',
  '�' => '理',
  '�' => '異',
  '�' => '痍',
  '�' => '痢',
  '�' => '移',
  '�' => '罹',
  '�' => '而',
  '�' => '耳',
  '�' => '肄',
  '�' => '苡',
  '�' => '荑',
  '��' => '裏',
  '��' => '裡',
  '��' => '貽',
  '��' => '貳',
  '��' => '邇',
  '��' => '里',
  '��' => '離',
  '��' => '飴',
  '��' => '餌',
  '��' => '匿',
  '��' => '溺',
  '��' => '瀷',
  '��' => '益',
  '��' => '翊',
  '��' => '翌',
  '��' => '翼',
  '��' => '謚',
  '��' => '人',
  '��' => '仁',
  '��' => '刃',
  '��' => '印',
  '��' => '吝',
  '��' => '咽',
  '��' => '因',
  '��' => '姻',
  '��' => '寅',
  '��' => '引',
  '��' => '忍',
  '��' => '湮',
  '��' => '燐',
  '��' => '璘',
  '��' => '絪',
  '��' => '茵',
  '��' => '藺',
  '��' => '蚓',
  '��' => '認',
  '��' => '隣',
  '��' => '靭',
  '��' => '靷',
  '��' => '鱗',
  '��' => '麟',
  '��' => '一',
  '��' => '佚',
  '��' => '佾',
  '��' => '壹',
  '��' => '日',
  '��' => '溢',
  '��' => '逸',
  '��' => '鎰',
  '��' => '馹',
  '��' => '任',
  '��' => '壬',
  '��' => '妊',
  '��' => '姙',
  '��' => '恁',
  '��' => '林',
  '��' => '淋',
  '��' => '稔',
  '��' => '臨',
  '��' => '荏',
  '��' => '賃',
  '��' => '入',
  '��' => '卄',
  '�' => '立',
  '�' => '笠',
  '�' => '粒',
  '�' => '仍',
  '�' => '剩',
  '�' => '孕',
  '�' => '芿',
  '�' => '仔',
  '�' => '刺',
  '�' => '咨',
  '�' => '姉',
  '�' => '姿',
  '�' => '子',
  '�' => '字',
  '�' => '孜',
  '�' => '恣',
  '�' => '慈',
  '�' => '滋',
  '�' => '炙',
  '�' => '煮',
  '�' => '玆',
  '�' => '瓷',
  '�' => '疵',
  '�' => '磁',
  '�' => '紫',
  '�' => '者',
  '�' => '自',
  '�' => '茨',
  '�' => '蔗',
  '�' => '藉',
  '�' => '諮',
  '��' => '資',
  '��' => '雌',
  '��' => '作',
  '��' => '勺',
  '��' => '嚼',
  '��' => '斫',
  '��' => '昨',
  '��' => '灼',
  '��' => '炸',
  '��' => '爵',
  '��' => '綽',
  '��' => '芍',
  '��' => '酌',
  '��' => '雀',
  '��' => '鵲',
  '��' => '孱',
  '��' => '棧',
  '��' => '殘',
  '��' => '潺',
  '��' => '盞',
  '��' => '岑',
  '��' => '暫',
  '��' => '潛',
  '��' => '箴',
  '��' => '簪',
  '��' => '蠶',
  '��' => '雜',
  '��' => '丈',
  '��' => '仗',
  '��' => '匠',
  '��' => '場',
  '��' => '墻',
  '��' => '壯',
  '��' => '奬',
  '��' => '將',
  '��' => '帳',
  '��' => '庄',
  '��' => '張',
  '��' => '掌',
  '��' => '暲',
  '��' => '杖',
  '��' => '樟',
  '��' => '檣',
  '��' => '欌',
  '��' => '漿',
  '��' => '牆',
  '��' => '狀',
  '��' => '獐',
  '��' => '璋',
  '��' => '章',
  '��' => '粧',
  '��' => '腸',
  '��' => '臟',
  '��' => '臧',
  '��' => '莊',
  '��' => '葬',
  '��' => '蔣',
  '��' => '薔',
  '��' => '藏',
  '��' => '裝',
  '��' => '贓',
  '��' => '醬',
  '��' => '長',
  '�' => '障',
  '�' => '再',
  '�' => '哉',
  '�' => '在',
  '�' => '宰',
  '�' => '才',
  '�' => '材',
  '�' => '栽',
  '�' => '梓',
  '�' => '渽',
  '�' => '滓',
  '�' => '災',
  '�' => '縡',
  '�' => '裁',
  '�' => '財',
  '�' => '載',
  '�' => '齋',
  '�' => '齎',
  '�' => '爭',
  '�' => '箏',
  '�' => '諍',
  '�' => '錚',
  '�' => '佇',
  '�' => '低',
  '�' => '儲',
  '�' => '咀',
  '�' => '姐',
  '�' => '底',
  '�' => '抵',
  '�' => '杵',
  '�' => '楮',
  '��' => '樗',
  '��' => '沮',
  '��' => '渚',
  '��' => '狙',
  '��' => '猪',
  '��' => '疽',
  '��' => '箸',
  '��' => '紵',
  '��' => '苧',
  '��' => '菹',
  '��' => '著',
  '��' => '藷',
  '��' => '詛',
  '��' => '貯',
  '��' => '躇',
  '��' => '這',
  '��' => '邸',
  '��' => '雎',
  '��' => '齟',
  '��' => '勣',
  '��' => '吊',
  '��' => '嫡',
  '��' => '寂',
  '��' => '摘',
  '��' => '敵',
  '��' => '滴',
  '��' => '狄',
  '��' => '炙',
  '��' => '的',
  '��' => '積',
  '��' => '笛',
  '��' => '籍',
  '��' => '績',
  '��' => '翟',
  '��' => '荻',
  '��' => '謫',
  '��' => '賊',
  '��' => '赤',
  '��' => '跡',
  '��' => '蹟',
  '��' => '迪',
  '��' => '迹',
  '��' => '適',
  '��' => '鏑',
  '��' => '佃',
  '��' => '佺',
  '��' => '傳',
  '��' => '全',
  '��' => '典',
  '��' => '前',
  '��' => '剪',
  '��' => '塡',
  '��' => '塼',
  '��' => '奠',
  '��' => '專',
  '��' => '展',
  '��' => '廛',
  '��' => '悛',
  '��' => '戰',
  '��' => '栓',
  '��' => '殿',
  '��' => '氈',
  '��' => '澱',
  '�' => '煎',
  '�' => '琠',
  '�' => '田',
  '�' => '甸',
  '�' => '畑',
  '�' => '癲',
  '�' => '筌',
  '�' => '箋',
  '�' => '箭',
  '�' => '篆',
  '�' => '纏',
  '�' => '詮',
  '�' => '輾',
  '�' => '轉',
  '�' => '鈿',
  '�' => '銓',
  '�' => '錢',
  '�' => '鐫',
  '�' => '電',
  '�' => '顚',
  '�' => '顫',
  '�' => '餞',
  '�' => '切',
  '�' => '截',
  '�' => '折',
  '�' => '浙',
  '�' => '癤',
  '�' => '竊',
  '�' => '節',
  '�' => '絶',
  '�' => '占',
  '��' => '岾',
  '��' => '店',
  '��' => '漸',
  '��' => '点',
  '��' => '粘',
  '��' => '霑',
  '��' => '鮎',
  '��' => '點',
  '��' => '接',
  '��' => '摺',
  '��' => '蝶',
  '��' => '丁',
  '��' => '井',
  '��' => '亭',
  '��' => '停',
  '��' => '偵',
  '��' => '呈',
  '��' => '姃',
  '��' => '定',
  '��' => '幀',
  '��' => '庭',
  '��' => '廷',
  '��' => '征',
  '��' => '情',
  '��' => '挺',
  '��' => '政',
  '��' => '整',
  '��' => '旌',
  '��' => '晶',
  '��' => '晸',
  '��' => '柾',
  '��' => '楨',
  '��' => '檉',
  '��' => '正',
  '��' => '汀',
  '��' => '淀',
  '��' => '淨',
  '��' => '渟',
  '��' => '湞',
  '��' => '瀞',
  '��' => '炡',
  '��' => '玎',
  '��' => '珽',
  '��' => '町',
  '��' => '睛',
  '��' => '碇',
  '��' => '禎',
  '��' => '程',
  '��' => '穽',
  '��' => '精',
  '��' => '綎',
  '��' => '艇',
  '��' => '訂',
  '��' => '諪',
  '��' => '貞',
  '��' => '鄭',
  '��' => '酊',
  '��' => '釘',
  '��' => '鉦',
  '��' => '鋌',
  '��' => '錠',
  '��' => '霆',
  '��' => '靖',
  '�' => '靜',
  '�' => '頂',
  '�' => '鼎',
  '�' => '制',
  '�' => '劑',
  '�' => '啼',
  '�' => '堤',
  '�' => '帝',
  '�' => '弟',
  '�' => '悌',
  '�' => '提',
  '�' => '梯',
  '�' => '濟',
  '�' => '祭',
  '�' => '第',
  '�' => '臍',
  '�' => '薺',
  '�' => '製',
  '�' => '諸',
  '�' => '蹄',
  '�' => '醍',
  '�' => '除',
  '�' => '際',
  '�' => '霽',
  '�' => '題',
  '�' => '齊',
  '�' => '俎',
  '�' => '兆',
  '�' => '凋',
  '�' => '助',
  '�' => '嘲',
  '��' => '弔',
  '��' => '彫',
  '��' => '措',
  '��' => '操',
  '��' => '早',
  '��' => '晁',
  '��' => '曺',
  '��' => '曹',
  '��' => '朝',
  '��' => '條',
  '��' => '棗',
  '��' => '槽',
  '��' => '漕',
  '��' => '潮',
  '��' => '照',
  '��' => '燥',
  '��' => '爪',
  '��' => '璪',
  '��' => '眺',
  '��' => '祖',
  '��' => '祚',
  '��' => '租',
  '��' => '稠',
  '��' => '窕',
  '��' => '粗',
  '��' => '糟',
  '��' => '組',
  '��' => '繰',
  '��' => '肇',
  '��' => '藻',
  '��' => '蚤',
  '��' => '詔',
  '��' => '調',
  '��' => '趙',
  '��' => '躁',
  '��' => '造',
  '��' => '遭',
  '��' => '釣',
  '��' => '阻',
  '��' => '雕',
  '��' => '鳥',
  '��' => '族',
  '��' => '簇',
  '��' => '足',
  '��' => '鏃',
  '��' => '存',
  '��' => '尊',
  '��' => '卒',
  '��' => '拙',
  '��' => '猝',
  '��' => '倧',
  '��' => '宗',
  '��' => '從',
  '��' => '悰',
  '��' => '慫',
  '��' => '棕',
  '��' => '淙',
  '��' => '琮',
  '��' => '種',
  '��' => '終',
  '��' => '綜',
  '��' => '縱',
  '��' => '腫',
  '�' => '踪',
  '�' => '踵',
  '�' => '鍾',
  '�' => '鐘',
  '�' => '佐',
  '�' => '坐',
  '�' => '左',
  '�' => '座',
  '�' => '挫',
  '�' => '罪',
  '�' => '主',
  '�' => '住',
  '�' => '侏',
  '�' => '做',
  '�' => '姝',
  '�' => '胄',
  '�' => '呪',
  '�' => '周',
  '�' => '嗾',
  '�' => '奏',
  '�' => '宙',
  '�' => '州',
  '�' => '廚',
  '�' => '晝',
  '�' => '朱',
  '�' => '柱',
  '�' => '株',
  '�' => '注',
  '�' => '洲',
  '�' => '湊',
  '�' => '澍',
  '��' => '炷',
  '��' => '珠',
  '��' => '疇',
  '��' => '籌',
  '��' => '紂',
  '��' => '紬',
  '��' => '綢',
  '��' => '舟',
  '��' => '蛛',
  '��' => '註',
  '��' => '誅',
  '��' => '走',
  '��' => '躊',
  '��' => '輳',
  '��' => '週',
  '��' => '酎',
  '��' => '酒',
  '��' => '鑄',
  '��' => '駐',
  '��' => '竹',
  '��' => '粥',
  '��' => '俊',
  '��' => '儁',
  '��' => '准',
  '��' => '埈',
  '��' => '寯',
  '��' => '峻',
  '��' => '晙',
  '��' => '樽',
  '��' => '浚',
  '��' => '準',
  '��' => '濬',
  '��' => '焌',
  '��' => '畯',
  '��' => '竣',
  '��' => '蠢',
  '��' => '逡',
  '��' => '遵',
  '��' => '雋',
  '��' => '駿',
  '��' => '茁',
  '��' => '中',
  '��' => '仲',
  '��' => '衆',
  '��' => '重',
  '��' => '卽',
  '��' => '櫛',
  '��' => '楫',
  '��' => '汁',
  '��' => '葺',
  '��' => '增',
  '��' => '憎',
  '��' => '曾',
  '��' => '拯',
  '��' => '烝',
  '��' => '甑',
  '��' => '症',
  '��' => '繒',
  '��' => '蒸',
  '��' => '證',
  '��' => '贈',
  '��' => '之',
  '��' => '只',
  '�' => '咫',
  '�' => '地',
  '�' => '址',
  '�' => '志',
  '�' => '持',
  '�' => '指',
  '�' => '摯',
  '�' => '支',
  '�' => '旨',
  '�' => '智',
  '�' => '枝',
  '�' => '枳',
  '�' => '止',
  '�' => '池',
  '�' => '沚',
  '�' => '漬',
  '�' => '知',
  '�' => '砥',
  '�' => '祉',
  '�' => '祗',
  '�' => '紙',
  '�' => '肢',
  '�' => '脂',
  '�' => '至',
  '�' => '芝',
  '�' => '芷',
  '�' => '蜘',
  '�' => '誌',
  '�' => '識',
  '�' => '贄',
  '�' => '趾',
  '��' => '遲',
  '��' => '直',
  '��' => '稙',
  '��' => '稷',
  '��' => '織',
  '��' => '職',
  '��' => '唇',
  '��' => '嗔',
  '��' => '塵',
  '��' => '振',
  '��' => '搢',
  '��' => '晉',
  '��' => '晋',
  '��' => '桭',
  '��' => '榛',
  '��' => '殄',
  '��' => '津',
  '��' => '溱',
  '��' => '珍',
  '��' => '瑨',
  '��' => '璡',
  '��' => '畛',
  '��' => '疹',
  '��' => '盡',
  '��' => '眞',
  '��' => '瞋',
  '��' => '秦',
  '��' => '縉',
  '��' => '縝',
  '��' => '臻',
  '��' => '蔯',
  '��' => '袗',
  '��' => '診',
  '��' => '賑',
  '��' => '軫',
  '��' => '辰',
  '��' => '進',
  '��' => '鎭',
  '��' => '陣',
  '��' => '陳',
  '��' => '震',
  '��' => '侄',
  '��' => '叱',
  '��' => '姪',
  '��' => '嫉',
  '��' => '帙',
  '��' => '桎',
  '��' => '瓆',
  '��' => '疾',
  '��' => '秩',
  '��' => '窒',
  '��' => '膣',
  '��' => '蛭',
  '��' => '質',
  '��' => '跌',
  '��' => '迭',
  '��' => '斟',
  '��' => '朕',
  '��' => '什',
  '��' => '執',
  '��' => '潗',
  '��' => '緝',
  '��' => '輯',
  '�' => '鏶',
  '�' => '集',
  '�' => '徵',
  '�' => '懲',
  '�' => '澄',
  '�' => '且',
  '�' => '侘',
  '�' => '借',
  '�' => '叉',
  '�' => '嗟',
  '�' => '嵯',
  '�' => '差',
  '�' => '次',
  '�' => '此',
  '�' => '磋',
  '�' => '箚',
  '�' => '茶',
  '�' => '蹉',
  '�' => '車',
  '�' => '遮',
  '�' => '捉',
  '�' => '搾',
  '�' => '着',
  '�' => '窄',
  '�' => '錯',
  '�' => '鑿',
  '�' => '齪',
  '�' => '撰',
  '�' => '澯',
  '�' => '燦',
  '�' => '璨',
  '��' => '瓚',
  '��' => '竄',
  '��' => '簒',
  '��' => '纂',
  '��' => '粲',
  '��' => '纘',
  '��' => '讚',
  '��' => '贊',
  '��' => '鑽',
  '��' => '餐',
  '��' => '饌',
  '��' => '刹',
  '��' => '察',
  '��' => '擦',
  '��' => '札',
  '��' => '紮',
  '��' => '僭',
  '��' => '參',
  '��' => '塹',
  '��' => '慘',
  '��' => '慙',
  '��' => '懺',
  '��' => '斬',
  '��' => '站',
  '��' => '讒',
  '��' => '讖',
  '��' => '倉',
  '��' => '倡',
  '��' => '創',
  '��' => '唱',
  '��' => '娼',
  '��' => '廠',
  '��' => '彰',
  '��' => '愴',
  '��' => '敞',
  '��' => '昌',
  '��' => '昶',
  '��' => '暢',
  '��' => '槍',
  '��' => '滄',
  '��' => '漲',
  '��' => '猖',
  '��' => '瘡',
  '��' => '窓',
  '��' => '脹',
  '��' => '艙',
  '��' => '菖',
  '��' => '蒼',
  '��' => '債',
  '��' => '埰',
  '��' => '寀',
  '��' => '寨',
  '��' => '彩',
  '��' => '採',
  '��' => '砦',
  '��' => '綵',
  '��' => '菜',
  '��' => '蔡',
  '��' => '采',
  '��' => '釵',
  '��' => '冊',
  '��' => '柵',
  '��' => '策',
  '��' => '責',
  '��' => '凄',
  '��' => '妻',
  '��' => '悽',
  '��' => '處',
  '��' => '倜',
  '��' => '刺',
  '��' => '剔',
  '��' => '尺',
  '��' => '慽',
  '��' => '戚',
  '��' => '拓',
  '��' => '擲',
  '��' => '斥',
  '��' => '滌',
  '��' => '瘠',
  '��' => '脊',
  '��' => '蹠',
  '��' => '陟',
  '��' => '隻',
  '��' => '仟',
  '��' => '千',
  '��' => '喘',
  '��' => '天',
  '��' => '川',
  '��' => '擅',
  '��' => '泉',
  '��' => '淺',
  '��' => '玔',
  '��' => '穿',
  '��' => '舛',
  '��' => '薦',
  '��' => '賤',
  '��' => '踐',
  '��' => '遷',
  '��' => '釧',
  '��' => '闡',
  '��' => '阡',
  '��' => '韆',
  '��' => '凸',
  '��' => '哲',
  '��' => '喆',
  '��' => '徹',
  '��' => '撤',
  '��' => '澈',
  '��' => '綴',
  '��' => '輟',
  '��' => '轍',
  '��' => '鐵',
  '��' => '僉',
  '��' => '尖',
  '��' => '沾',
  '��' => '添',
  '��' => '甛',
  '��' => '瞻',
  '��' => '簽',
  '��' => '籤',
  '��' => '詹',
  '��' => '諂',
  '��' => '堞',
  '��' => '妾',
  '��' => '帖',
  '��' => '捷',
  '��' => '牒',
  '��' => '疊',
  '��' => '睫',
  '��' => '諜',
  '��' => '貼',
  '��' => '輒',
  '��' => '廳',
  '��' => '晴',
  '��' => '淸',
  '��' => '聽',
  '��' => '菁',
  '��' => '請',
  '��' => '靑',
  '��' => '鯖',
  '��' => '切',
  '��' => '剃',
  '��' => '替',
  '��' => '涕',
  '��' => '滯',
  '��' => '締',
  '��' => '諦',
  '��' => '逮',
  '��' => '遞',
  '��' => '體',
  '��' => '初',
  '��' => '剿',
  '��' => '哨',
  '��' => '憔',
  '��' => '抄',
  '��' => '招',
  '��' => '梢',
  '��' => '椒',
  '��' => '楚',
  '��' => '樵',
  '��' => '炒',
  '��' => '焦',
  '��' => '硝',
  '��' => '礁',
  '��' => '礎',
  '��' => '秒',
  '��' => '稍',
  '��' => '肖',
  '��' => '艸',
  '��' => '苕',
  '��' => '草',
  '��' => '蕉',
  '��' => '貂',
  '��' => '超',
  '��' => '酢',
  '��' => '醋',
  '��' => '醮',
  '��' => '促',
  '��' => '囑',
  '��' => '燭',
  '��' => '矗',
  '��' => '蜀',
  '��' => '觸',
  '��' => '寸',
  '��' => '忖',
  '��' => '村',
  '��' => '邨',
  '��' => '叢',
  '��' => '塚',
  '��' => '寵',
  '��' => '悤',
  '��' => '憁',
  '��' => '摠',
  '��' => '總',
  '��' => '聰',
  '��' => '蔥',
  '��' => '銃',
  '��' => '撮',
  '��' => '催',
  '��' => '崔',
  '��' => '最',
  '��' => '墜',
  '��' => '抽',
  '��' => '推',
  '��' => '椎',
  '��' => '楸',
  '��' => '樞',
  '��' => '湫',
  '��' => '皺',
  '��' => '秋',
  '��' => '芻',
  '��' => '萩',
  '��' => '諏',
  '��' => '趨',
  '��' => '追',
  '��' => '鄒',
  '��' => '酋',
  '��' => '醜',
  '��' => '錐',
  '��' => '錘',
  '��' => '鎚',
  '��' => '雛',
  '��' => '騶',
  '��' => '鰍',
  '��' => '丑',
  '��' => '畜',
  '��' => '祝',
  '��' => '竺',
  '��' => '筑',
  '��' => '築',
  '��' => '縮',
  '��' => '蓄',
  '��' => '蹙',
  '��' => '蹴',
  '��' => '軸',
  '��' => '逐',
  '��' => '春',
  '��' => '椿',
  '��' => '瑃',
  '��' => '出',
  '��' => '朮',
  '��' => '黜',
  '��' => '充',
  '��' => '忠',
  '��' => '沖',
  '��' => '蟲',
  '��' => '衝',
  '��' => '衷',
  '��' => '悴',
  '��' => '膵',
  '��' => '萃',
  '��' => '贅',
  '��' => '取',
  '��' => '吹',
  '��' => '嘴',
  '��' => '娶',
  '��' => '就',
  '��' => '炊',
  '��' => '翠',
  '��' => '聚',
  '��' => '脆',
  '��' => '臭',
  '��' => '趣',
  '��' => '醉',
  '��' => '驟',
  '��' => '鷲',
  '��' => '側',
  '��' => '仄',
  '��' => '厠',
  '��' => '惻',
  '��' => '測',
  '��' => '層',
  '��' => '侈',
  '��' => '値',
  '��' => '嗤',
  '��' => '峙',
  '��' => '幟',
  '��' => '恥',
  '��' => '梔',
  '��' => '治',
  '��' => '淄',
  '��' => '熾',
  '��' => '痔',
  '��' => '痴',
  '��' => '癡',
  '��' => '稚',
  '��' => '穉',
  '��' => '緇',
  '��' => '緻',
  '��' => '置',
  '��' => '致',
  '��' => '蚩',
  '��' => '輜',
  '��' => '雉',
  '��' => '馳',
  '��' => '齒',
  '��' => '則',
  '��' => '勅',
  '��' => '飭',
  '��' => '親',
  '��' => '七',
  '��' => '柒',
  '��' => '漆',
  '��' => '侵',
  '��' => '寢',
  '��' => '枕',
  '��' => '沈',
  '��' => '浸',
  '��' => '琛',
  '��' => '砧',
  '��' => '針',
  '��' => '鍼',
  '��' => '蟄',
  '��' => '秤',
  '��' => '稱',
  '��' => '快',
  '��' => '他',
  '��' => '咤',
  '��' => '唾',
  '��' => '墮',
  '��' => '妥',
  '��' => '惰',
  '��' => '打',
  '��' => '拖',
  '��' => '朶',
  '��' => '楕',
  '��' => '舵',
  '��' => '陀',
  '��' => '馱',
  '��' => '駝',
  '��' => '倬',
  '��' => '卓',
  '��' => '啄',
  '��' => '坼',
  '��' => '度',
  '��' => '托',
  '��' => '拓',
  '��' => '擢',
  '��' => '晫',
  '��' => '柝',
  '��' => '濁',
  '��' => '濯',
  '��' => '琢',
  '��' => '琸',
  '��' => '託',
  '��' => '鐸',
  '��' => '呑',
  '��' => '嘆',
  '��' => '坦',
  '��' => '彈',
  '��' => '憚',
  '��' => '歎',
  '��' => '灘',
  '��' => '炭',
  '��' => '綻',
  '��' => '誕',
  '��' => '奪',
  '��' => '脫',
  '��' => '探',
  '��' => '眈',
  '��' => '耽',
  '��' => '貪',
  '��' => '塔',
  '��' => '搭',
  '��' => '榻',
  '��' => '宕',
  '��' => '帑',
  '��' => '湯',
  '��' => '糖',
  '��' => '蕩',
  '��' => '兌',
  '��' => '台',
  '��' => '太',
  '��' => '怠',
  '��' => '態',
  '��' => '殆',
  '��' => '汰',
  '��' => '泰',
  '��' => '笞',
  '��' => '胎',
  '��' => '苔',
  '��' => '跆',
  '��' => '邰',
  '��' => '颱',
  '��' => '宅',
  '��' => '擇',
  '��' => '澤',
  '��' => '撑',
  '��' => '攄',
  '��' => '兎',
  '��' => '吐',
  '��' => '土',
  '��' => '討',
  '��' => '慟',
  '��' => '桶',
  '��' => '洞',
  '��' => '痛',
  '��' => '筒',
  '��' => '統',
  '��' => '通',
  '��' => '堆',
  '��' => '槌',
  '��' => '腿',
  '��' => '褪',
  '��' => '退',
  '��' => '頹',
  '��' => '偸',
  '��' => '套',
  '��' => '妬',
  '��' => '投',
  '��' => '透',
  '��' => '鬪',
  '��' => '慝',
  '��' => '特',
  '��' => '闖',
  '��' => '坡',
  '��' => '婆',
  '��' => '巴',
  '��' => '把',
  '��' => '播',
  '��' => '擺',
  '��' => '杷',
  '��' => '波',
  '��' => '派',
  '��' => '爬',
  '��' => '琶',
  '��' => '破',
  '��' => '罷',
  '��' => '芭',
  '��' => '跛',
  '��' => '頗',
  '��' => '判',
  '��' => '坂',
  '��' => '板',
  '��' => '版',
  '��' => '瓣',
  '��' => '販',
  '��' => '辦',
  '��' => '鈑',
  '��' => '阪',
  '��' => '八',
  '��' => '叭',
  '��' => '捌',
  '��' => '佩',
  '��' => '唄',
  '��' => '悖',
  '��' => '敗',
  '��' => '沛',
  '��' => '浿',
  '��' => '牌',
  '��' => '狽',
  '��' => '稗',
  '��' => '覇',
  '��' => '貝',
  '��' => '彭',
  '��' => '澎',
  '��' => '烹',
  '��' => '膨',
  '��' => '愎',
  '��' => '便',
  '��' => '偏',
  '��' => '扁',
  '��' => '片',
  '��' => '篇',
  '��' => '編',
  '��' => '翩',
  '��' => '遍',
  '��' => '鞭',
  '��' => '騙',
  '��' => '貶',
  '��' => '坪',
  '��' => '平',
  '��' => '枰',
  '��' => '萍',
  '��' => '評',
  '��' => '吠',
  '��' => '嬖',
  '��' => '幣',
  '��' => '廢',
  '��' => '弊',
  '��' => '斃',
  '��' => '肺',
  '��' => '蔽',
  '��' => '閉',
  '��' => '陛',
  '��' => '佈',
  '��' => '包',
  '��' => '匍',
  '��' => '匏',
  '��' => '咆',
  '��' => '哺',
  '��' => '圃',
  '��' => '布',
  '��' => '怖',
  '��' => '抛',
  '��' => '抱',
  '��' => '捕',
  '��' => '暴',
  '��' => '泡',
  '��' => '浦',
  '��' => '疱',
  '��' => '砲',
  '��' => '胞',
  '��' => '脯',
  '��' => '苞',
  '��' => '葡',
  '��' => '蒲',
  '��' => '袍',
  '��' => '褒',
  '��' => '逋',
  '��' => '鋪',
  '��' => '飽',
  '��' => '鮑',
  '��' => '幅',
  '��' => '暴',
  '��' => '曝',
  '��' => '瀑',
  '��' => '爆',
  '��' => '輻',
  '��' => '俵',
  '��' => '剽',
  '��' => '彪',
  '��' => '慓',
  '��' => '杓',
  '��' => '標',
  '��' => '漂',
  '��' => '瓢',
  '��' => '票',
  '��' => '表',
  '��' => '豹',
  '��' => '飇',
  '��' => '飄',
  '��' => '驃',
  '��' => '品',
  '��' => '稟',
  '��' => '楓',
  '��' => '諷',
  '��' => '豊',
  '��' => '風',
  '��' => '馮',
  '��' => '彼',
  '��' => '披',
  '��' => '疲',
  '��' => '皮',
  '��' => '被',
  '��' => '避',
  '��' => '陂',
  '��' => '匹',
  '��' => '弼',
  '��' => '必',
  '��' => '泌',
  '��' => '珌',
  '��' => '畢',
  '��' => '疋',
  '��' => '筆',
  '��' => '苾',
  '��' => '馝',
  '��' => '乏',
  '��' => '逼',
  '��' => '下',
  '��' => '何',
  '��' => '厦',
  '��' => '夏',
  '��' => '廈',
  '��' => '昰',
  '��' => '河',
  '��' => '瑕',
  '��' => '荷',
  '��' => '蝦',
  '��' => '賀',
  '��' => '遐',
  '��' => '霞',
  '��' => '鰕',
  '��' => '壑',
  '��' => '學',
  '��' => '虐',
  '��' => '謔',
  '��' => '鶴',
  '��' => '寒',
  '��' => '恨',
  '��' => '悍',
  '��' => '旱',
  '��' => '汗',
  '��' => '漢',
  '��' => '澣',
  '��' => '瀚',
  '��' => '罕',
  '��' => '翰',
  '��' => '閑',
  '��' => '閒',
  '��' => '限',
  '��' => '韓',
  '��' => '割',
  '��' => '轄',
  '��' => '函',
  '��' => '含',
  '��' => '咸',
  '��' => '啣',
  '��' => '喊',
  '��' => '檻',
  '��' => '涵',
  '��' => '緘',
  '��' => '艦',
  '��' => '銜',
  '��' => '陷',
  '��' => '鹹',
  '��' => '合',
  '��' => '哈',
  '��' => '盒',
  '��' => '蛤',
  '��' => '閤',
  '��' => '闔',
  '��' => '陜',
  '��' => '亢',
  '��' => '伉',
  '��' => '姮',
  '��' => '嫦',
  '��' => '巷',
  '��' => '恒',
  '��' => '抗',
  '��' => '杭',
  '��' => '桁',
  '��' => '沆',
  '��' => '港',
  '��' => '缸',
  '��' => '肛',
  '��' => '航',
  '��' => '行',
  '��' => '降',
  '��' => '項',
  '��' => '亥',
  '��' => '偕',
  '��' => '咳',
  '��' => '垓',
  '��' => '奚',
  '��' => '孩',
  '��' => '害',
  '��' => '懈',
  '��' => '楷',
  '��' => '海',
  '��' => '瀣',
  '��' => '蟹',
  '��' => '解',
  '��' => '該',
  '��' => '諧',
  '��' => '邂',
  '��' => '駭',
  '��' => '骸',
  '��' => '劾',
  '��' => '核',
  '��' => '倖',
  '��' => '幸',
  '��' => '杏',
  '��' => '荇',
  '��' => '行',
  '��' => '享',
  '��' => '向',
  '��' => '嚮',
  '��' => '珦',
  '��' => '鄕',
  '��' => '響',
  '��' => '餉',
  '��' => '饗',
  '��' => '香',
  '��' => '噓',
  '��' => '墟',
  '��' => '虛',
  '��' => '許',
  '��' => '憲',
  '��' => '櫶',
  '��' => '獻',
  '��' => '軒',
  '��' => '歇',
  '��' => '險',
  '��' => '驗',
  '��' => '奕',
  '��' => '爀',
  '��' => '赫',
  '��' => '革',
  '��' => '俔',
  '��' => '峴',
  '��' => '弦',
  '��' => '懸',
  '��' => '晛',
  '��' => '泫',
  '��' => '炫',
  '��' => '玄',
  '��' => '玹',
  '��' => '現',
  '��' => '眩',
  '��' => '睍',
  '��' => '絃',
  '��' => '絢',
  '��' => '縣',
  '��' => '舷',
  '��' => '衒',
  '��' => '見',
  '��' => '賢',
  '��' => '鉉',
  '��' => '顯',
  '��' => '孑',
  '��' => '穴',
  '��' => '血',
  '��' => '頁',
  '��' => '嫌',
  '��' => '俠',
  '��' => '協',
  '��' => '夾',
  '��' => '峽',
  '��' => '挾',
  '��' => '浹',
  '��' => '狹',
  '��' => '脅',
  '��' => '脇',
  '��' => '莢',
  '��' => '鋏',
  '��' => '頰',
  '��' => '亨',
  '��' => '兄',
  '��' => '刑',
  '��' => '型',
  '��' => '形',
  '��' => '泂',
  '��' => '滎',
  '��' => '瀅',
  '��' => '灐',
  '��' => '炯',
  '��' => '熒',
  '��' => '珩',
  '��' => '瑩',
  '��' => '荊',
  '��' => '螢',
  '��' => '衡',
  '��' => '逈',
  '��' => '邢',
  '��' => '鎣',
  '��' => '馨',
  '��' => '兮',
  '��' => '彗',
  '��' => '惠',
  '��' => '慧',
  '��' => '暳',
  '��' => '蕙',
  '��' => '蹊',
  '��' => '醯',
  '��' => '鞋',
  '��' => '乎',
  '��' => '互',
  '��' => '呼',
  '��' => '壕',
  '��' => '壺',
  '��' => '好',
  '��' => '岵',
  '��' => '弧',
  '��' => '戶',
  '��' => '扈',
  '��' => '昊',
  '��' => '晧',
  '��' => '毫',
  '��' => '浩',
  '��' => '淏',
  '��' => '湖',
  '��' => '滸',
  '��' => '澔',
  '��' => '濠',
  '��' => '濩',
  '��' => '灝',
  '��' => '狐',
  '��' => '琥',
  '��' => '瑚',
  '��' => '瓠',
  '��' => '皓',
  '��' => '祜',
  '��' => '糊',
  '��' => '縞',
  '��' => '胡',
  '��' => '芦',
  '��' => '葫',
  '��' => '蒿',
  '��' => '虎',
  '��' => '號',
  '��' => '蝴',
  '��' => '護',
  '��' => '豪',
  '��' => '鎬',
  '��' => '頀',
  '��' => '顥',
  '��' => '惑',
  '��' => '或',
  '��' => '酷',
  '��' => '婚',
  '��' => '昏',
  '��' => '混',
  '��' => '渾',
  '��' => '琿',
  '��' => '魂',
  '��' => '忽',
  '��' => '惚',
  '��' => '笏',
  '��' => '哄',
  '��' => '弘',
  '��' => '汞',
  '��' => '泓',
  '��' => '洪',
  '��' => '烘',
  '��' => '紅',
  '��' => '虹',
  '��' => '訌',
  '��' => '鴻',
  '��' => '化',
  '��' => '和',
  '��' => '嬅',
  '��' => '樺',
  '��' => '火',
  '��' => '畵',
  '��' => '禍',
  '��' => '禾',
  '��' => '花',
  '��' => '華',
  '��' => '話',
  '��' => '譁',
  '��' => '貨',
  '��' => '靴',
  '��' => '廓',
  '��' => '擴',
  '��' => '攫',
  '��' => '確',
  '��' => '碻',
  '��' => '穫',
  '��' => '丸',
  '��' => '喚',
  '��' => '奐',
  '��' => '宦',
  '��' => '幻',
  '��' => '患',
  '��' => '換',
  '��' => '歡',
  '��' => '晥',
  '��' => '桓',
  '��' => '渙',
  '��' => '煥',
  '��' => '環',
  '��' => '紈',
  '��' => '還',
  '��' => '驩',
  '��' => '鰥',
  '��' => '活',
  '��' => '滑',
  '��' => '猾',
  '��' => '豁',
  '��' => '闊',
  '��' => '凰',
  '��' => '幌',
  '��' => '徨',
  '��' => '恍',
  '��' => '惶',
  '��' => '愰',
  '��' => '慌',
  '��' => '晃',
  '��' => '晄',
  '��' => '榥',
  '��' => '況',
  '��' => '湟',
  '��' => '滉',
  '��' => '潢',
  '��' => '煌',
  '��' => '璜',
  '��' => '皇',
  '��' => '篁',
  '��' => '簧',
  '��' => '荒',
  '��' => '蝗',
  '��' => '遑',
  '��' => '隍',
  '��' => '黃',
  '��' => '匯',
  '��' => '回',
  '��' => '廻',
  '��' => '徊',
  '��' => '恢',
  '��' => '悔',
  '��' => '懷',
  '��' => '晦',
  '��' => '會',
  '��' => '檜',
  '��' => '淮',
  '��' => '澮',
  '��' => '灰',
  '��' => '獪',
  '��' => '繪',
  '��' => '膾',
  '��' => '茴',
  '��' => '蛔',
  '��' => '誨',
  '��' => '賄',
  '��' => '劃',
  '��' => '獲',
  '��' => '宖',
  '��' => '橫',
  '��' => '鐄',
  '��' => '哮',
  '��' => '嚆',
  '��' => '孝',
  '��' => '效',
  '��' => '斅',
  '��' => '曉',
  '��' => '梟',
  '��' => '涍',
  '��' => '淆',
  '��' => '爻',
  '��' => '肴',
  '��' => '酵',
  '��' => '驍',
  '��' => '侯',
  '��' => '候',
  '��' => '厚',
  '��' => '后',
  '��' => '吼',
  '��' => '喉',
  '��' => '嗅',
  '��' => '帿',
  '��' => '後',
  '��' => '朽',
  '��' => '煦',
  '��' => '珝',
  '��' => '逅',
  '��' => '勛',
  '��' => '勳',
  '��' => '塤',
  '��' => '壎',
  '��' => '焄',
  '��' => '熏',
  '��' => '燻',
  '��' => '薰',
  '��' => '訓',
  '��' => '暈',
  '��' => '薨',
  '��' => '喧',
  '��' => '暄',
  '��' => '煊',
  '��' => '萱',
  '��' => '卉',
  '��' => '喙',
  '��' => '毁',
  '��' => '彙',
  '��' => '徽',
  '��' => '揮',
  '��' => '暉',
  '��' => '煇',
  '��' => '諱',
  '��' => '輝',
  '��' => '麾',
  '��' => '休',
  '��' => '携',
  '��' => '烋',
  '��' => '畦',
  '��' => '虧',
  '��' => '恤',
  '��' => '譎',
  '��' => '鷸',
  '��' => '兇',
  '��' => '凶',
  '��' => '匈',
  '��' => '洶',
  '��' => '胸',
  '��' => '黑',
  '��' => '昕',
  '��' => '欣',
  '��' => '炘',
  '��' => '痕',
  '��' => '吃',
  '��' => '屹',
  '��' => '紇',
  '��' => '訖',
  '��' => '欠',
  '��' => '欽',
  '��' => '歆',
  '��' => '吸',
  '��' => '恰',
  '��' => '洽',
  '��' => '翕',
  '��' => '興',
  '��' => '僖',
  '��' => '凞',
  '��' => '喜',
  '��' => '噫',
  '��' => '囍',
  '��' => '姬',
  '��' => '嬉',
  '��' => '希',
  '��' => '憙',
  '��' => '憘',
  '��' => '戱',
  '��' => '晞',
  '��' => '曦',
  '��' => '熙',
  '��' => '熹',
  '��' => '熺',
  '��' => '犧',
  '��' => '禧',
  '��' => '稀',
  '��' => '羲',
  '��' => '詰',
);

$result =& $data;
unset($data);

return $result;
