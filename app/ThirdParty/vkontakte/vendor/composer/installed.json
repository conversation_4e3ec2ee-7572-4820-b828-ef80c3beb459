[{"name": "guzzlehttp/guzzle", "version": "6.3.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/407b0cb880ace85c9b63c5f9551db498cb2d50ba", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba", "shasum": ""}, "require": {"guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.4", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.0"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "time": "2018-04-22T15:46:56+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "6.3-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"]}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "time": "2016-12-20T10:07:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"]}, {"name": "guzzlehttp/psr7", "version": "1.5.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "9f83dded91781a01c63574e387eaa769be769115"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/9f83dded91781a01c63574e387eaa769be769115", "reference": "9f83dded91781a01c63574e387eaa769be769115", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.8"}, "time": "2018-12-04T20:46:45+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"]}, {"name": "ircmaxell/random-lib", "version": "v1.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ircmaxell/RandomLib.git", "reference": "e9e0204f40e49fa4419946c677eccd3fa25b8cf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ircmaxell/RandomLib/zipball/e9e0204f40e49fa4419946c677eccd3fa25b8cf4", "reference": "e9e0204f40e49fa4419946c677eccd3fa25b8cf4", "shasum": ""}, "require": {"ircmaxell/security-lib": "^1.1", "php": ">=5.3.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "^1.11", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^4.8|^5.0"}, "time": "2016-09-07T15:52:06+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-0": {"RandomLib": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://blog.ircmaxell.com"}], "description": "A Library For Generating Secure Random Numbers", "homepage": "https://github.com/ircmaxell/RandomLib", "keywords": ["cryptography", "random", "random-numbers", "random-strings"]}, {"name": "ircmaxell/security-lib", "version": "v1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ircmaxell/SecurityLib.git", "reference": "f3db6de12c20c9bcd1aa3db4353a1bbe0e44e1b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ircmaxell/SecurityLib/zipball/f3db6de12c20c9bcd1aa3db4353a1bbe0e44e1b5", "reference": "f3db6de12c20c9bcd1aa3db4353a1bbe0e44e1b5", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"mikey179/vfsstream": "1.1.*"}, "time": "2015-03-20T14:31:23+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-0": {"SecurityLib": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://blog.ircmaxell.com"}], "description": "A Base Security Library", "homepage": "https://github.com/ircmaxell/SecurityLib"}, {"name": "j4k/oauth2-vkontakte", "version": "1.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/j4k/oauth2-vkontakte.git", "reference": "9bbcf572e0af11ffe1898a490a0c1de4611c9056"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/j4k/oauth2-vkontakte/zipball/9bbcf572e0af11ffe1898a490a0c1de4611c9056", "reference": "9bbcf572e0af11ffe1898a490a0c1de4611c9056", "shasum": ""}, "require": {"league/oauth2-client": "^1.4", "php": ">=5.5"}, "require-dev": {"mockery/mockery": "~0.9", "phpunit/phpunit": "~4.6"}, "time": "2016-09-13T06:38:26+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"J4k\\OAuth2\\Client\\Provider\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Vkontakte provider for league/oauth2-client", "keywords": ["league", "package"]}, {"name": "league/oauth2-client", "version": "1.4.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-client.git", "reference": "01f955b85040b41cf48885b078f7fd39a8be5411"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-client/zipball/01f955b85040b41cf48885b078f7fd39a8be5411", "reference": "01f955b85040b41cf48885b078f7fd39a8be5411", "shasum": ""}, "require": {"ext-curl": "*", "guzzlehttp/guzzle": "~6.0", "ircmaxell/random-lib": "~1.1", "php": ">=5.5.0"}, "require-dev": {"jakub-onderka/php-parallel-lint": "0.8.*", "mockery/mockery": "~0.9", "phpunit/phpunit": "~4.0", "satooshi/php-coveralls": "0.6.*", "squizlabs/php_codesniffer": "~2.0"}, "time": "2016-07-28T13:20:43+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}], "description": "OAuth 2.0 Client Library", "keywords": ["Authentication", "SSO", "authorization", "identity", "idp", "o<PERSON>h", "oauth2", "single sign on"]}, {"name": "psr/http-message", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T14:39:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"]}, {"name": "ralouphie/getallheaders", "version": "2.0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "5601c8a83fbba7ef674a7369456d12f1e0d0eafa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/5601c8a83fbba7ef674a7369456d12f1e0d0eafa", "reference": "5601c8a83fbba7ef674a7369456d12f1e0d0eafa", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "~3.7.0", "satooshi/php-coveralls": ">=1.0"}, "time": "2016-02-11T07:05:27+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders."}]