<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddAutoTitleColumnsToCategories extends Migration
{
    public function up()
    {
        // Add auto title configuration columns to categories table
        $fields = [
            'auto_title_enabled' => [
                'type'       => 'TINYINT',
                'constraint' => 1,
                'default'    => 1,
                'null'       => false,
                'comment'    => '1=enabled, 0=disabled for auto title generation',
                'after'      => 'show_image_on_slider',
            ],
            'title_template' => [
                'type'    => 'TEXT',
                'null'    => true,
                'comment' => 'Custom title template for this category (optional)',
                'after'   => 'auto_title_enabled',
            ],
            'title_max_fields' => [
                'type'       => 'TINYINT',
                'constraint' => 3,
                'unsigned'   => true,
                'default'    => 4,
                'null'       => false,
                'comment'    => 'Maximum number of custom fields to use for title generation',
                'after'      => 'title_template',
            ],
        ];

        $this->forge->addColumn('categories', $fields);

        // Add indexes for better performance
        $this->forge->addKey(['auto_title_enabled'], false, false, 'categories');
    }

    public function down()
    {
        // Remove the added columns
        $this->forge->dropColumn('categories', ['auto_title_enabled', 'title_template', 'title_max_fields']);
    }
}
