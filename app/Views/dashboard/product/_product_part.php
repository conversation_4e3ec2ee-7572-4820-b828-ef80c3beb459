<div class="modal fade" id="fileManagerModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-file-manager" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?= trans("images"); ?></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"><i class="icon-close"></i></span>
                </button>
            </div>
            <div class="modal-body">
                <div class="file-manager">
                    <div class="file-manager-left">
                        <div class="dm-uploader-container">
                            <div id="drag-and-drop-zone-file-manager" class="dm-uploader text-center">
                                <p class="file-manager-file-types">
                                    <span>JPG</span>
                                    <span>JPEG</span>
                                    <span>WEBP</span>
                                    <span>PNG</span>
                                </p>
                                <p class="dm-upload-icon">
                                    <i class="icon-upload"></i>
                                </p>
                                <p class="dm-upload-text"><?= trans("drag_drop_images_here"); ?></p>
                                <p class="text-center">
                                    <button class="btn btn-default btn-browse-files"><?= trans('browse_files'); ?></button>
                                </p>
                                <a class='btn btn-md dm-btn-select-files'>
                                    <input type="file" name="file" size="40" multiple="multiple">
                                </a>
                                <ul class="dm-uploaded-files" id="files-file-manager"></ul>
                                <button type="button" id="btn_reset_upload_image" class="btn btn-reset-upload"><?= trans("reset"); ?></button>
                            </div>
                        </div>
                    </div>
                    <div class="file-manager-right">
                        <div class="file-manager-content">
                            <div id="ckimage_file_upload_response">
                                <?php if (!empty($fileManagerImages)):
                                    foreach ($fileManagerImages as $image): ?>
                                        <div class="col-file-manager" id="fm_img_col_id_<?= $image->id; ?>">
                                            <div class="file-box" data-file-id="<?= $image->id; ?>" data-file-path="<?= getFileManagerImageUrl($image); ?>">
                                                <div class="image-container">
                                                    <img src="<?= getFileManagerImageUrl($image); ?>" alt="" class="img-responsive">
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach;
                                endif; ?>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="selected_fm_img_file_id">
                    <input type="hidden" id="selected_fm_img_file_path">
                </div>
            </div>
            <div class="modal-footer">
                <div class="file-manager-footer">
                    <button type="button" id="btn_fm_img_delete" class="btn btn-sm btn-danger color-white pull-left btn-file-delete m-r-3"><i class="icon-trash"></i>&nbsp;&nbsp;<?= trans('delete'); ?></button>
                    <button type="button" id="btn_fm_img_select" class="btn btn-sm btn-info color-white btn-file-select"><i class="icon-check"></i>&nbsp;&nbsp;<?= trans('select_image'); ?></button>
                    <button type="button" class="btn btn-sm btn-secondary color-white" data-dismiss="modal"><?= trans('close'); ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="files-template-file-manager">
    <li class="media">
        <img class="preview-img" src="data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==" alt="">
        <div class="media-body">
            <div class="progress">
                <div class="dm-progress-waiting"><?= trans("waiting"); ?></div>
                <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
        </div>
    </li>
</script>

<script>
    function getSubCategoriesDashboard(parentId, level, langId) {
        level = parseInt(level);
        var newLevel = level + 1;
        var data = {
            'parent_id': parentId,
            'lang_id': langId
        };
        $.ajax({
            type: 'POST',
            url: MdsConfig.baseURL + '/Ajax/getSubCategories',
            data: setAjaxData(data),
            success: function (response) {
                $('.subcategory-select-container').each(function () {
                    if (parseInt($(this).attr('data-level')) > level) {
                        $(this).remove();
                    }
                });
                var obj = JSON.parse(response);
                if (obj.result == 1 && obj.htmlContent != '') {
                    var selectTag = '<div class="subcategory-select-container m-t-5" data-level="' + newLevel + '"><select class="select2 form-control subcategory-select" data-level="' + newLevel + '" name="category_id[]" onchange="getSubCategoriesDashboard(this.value,' + newLevel + ',' + langId + ');">' +
                        '<option value=""><?= trans('select_category'); ?></option>' + obj.htmlContent + '</select></div>';
                    $('#category_select_container').append(selectTag);
                }
            }
        });
    }

    // Combined function that loads both subcategories and custom fields
    function getSubCategoriesAndCustomFieldsDashboard(parentId, level, langId) {
        level = parseInt(level);
        var newLevel = level + 1;
        var data = {
            'parent_id': parentId,
            'lang_id': langId
        };
        $.ajax({
            type: 'POST',
            url: MdsConfig.baseURL + '/Ajax/getSubCategories',
            data: setAjaxData(data),
            success: function (response) {
                $('.subcategory-select-container').each(function () {
                    if (parseInt($(this).attr('data-level')) > level) {
                        $(this).remove();
                    }
                });
                var obj = JSON.parse(response);
                if (obj.result == 1 && obj.htmlContent != '') {
                    var selectTag = '<div class="subcategory-select-container m-t-5" data-level="' + newLevel + '"><select class="select2 form-control subcategory-select" data-level="' + newLevel + '" name="category_id[]" onchange="getSubCategoriesAndCustomFieldsDashboard(this.value,' + newLevel + ',' + langId + ');">' +
                        '<option value=""><?= trans('select_category'); ?></option>' + obj.htmlContent + '</select></div>';
                    $('#category_select_container').append(selectTag);
                }

                // Load custom fields for the selected category
                if (parentId && parentId !== '') {
                    // Determine product ID for edit mode
                    var productId = 0;
                    if (typeof window.currentProductId !== 'undefined') {
                        productId = window.currentProductId;
                    }
                    getCustomFieldsForCategory(parentId, langId, productId);
                }
            }
        });
    }

    // Get Custom Fields for category (similar to getSubCategories pattern)
    function getCustomFieldsForCategory(categoryId, langId, productId) {
        productId = productId || 0;
        var data = {
            'category_id': categoryId,
            'lang_id': langId,
            'product_id': productId
        };

        // Show loading state
        $('#custom_fields_content').html('<p class="text-muted"><i class="fa fa-spinner fa-spin"></i> <?= trans("loading_custom_fields"); ?>...</p>');

        $.ajax({
            type: 'POST',
            url: MdsConfig.baseURL + '/Ajax/getCustomFields',
            data: setAjaxData(data),
            success: function (response) {
                var obj = JSON.parse(response);
                if (obj.result == 1 && obj.htmlContent != '') {
                    $('#custom_fields_content').html(obj.htmlContent);

                    // Bind real-time title generation to new fields
                    bindRealTimeTitleGeneration();

                    // Generate initial title if fields have values
                    generateTitleRealTime();
                } else {
                    $('#custom_fields_content').html('<p class="text-muted"><i class="fa fa-info-circle"></i> <?= trans("no_custom_fields_for_category"); ?></p>');
                }
            },
            error: function() {
                $('#custom_fields_content').html('<p class="text-danger"><i class="fa fa-exclamation-triangle"></i> <?= trans("error_loading_custom_fields"); ?></p>');
            }
        });
    }

    // Bind real-time title generation to Custom Field inputs
    function bindRealTimeTitleGeneration() {
        // Unbind previous events to avoid duplicates
        $(document).off('input change', 'input[name^="field_"], select[name^="field_"], textarea[name^="field_"]');

        // Bind to all custom field inputs
        $(document).on('input change', 'input[name^="field_"], select[name^="field_"], textarea[name^="field_"]', function() {
            // Debounce the title generation to avoid too many calls
            clearTimeout(window.titleGenerationTimeout);
            window.titleGenerationTimeout = setTimeout(function() {
                generateTitleRealTime();
            }, 300); // 300ms delay
        });
    }

    // Generate title in real-time from current Custom Field values
    function generateTitleRealTime() {
        var categoryId = getCurrentCategoryId();
        if (!categoryId) return;

        // Collect current custom field values
        var customFieldValues = {};
        var hasValues = false;

        $('input[name^="field_"], select[name^="field_"], textarea[name^="field_"]').each(function() {
            var fieldName = $(this).attr('name');
            var fieldId = fieldName.replace('field_', '');
            var fieldValue = '';

            if ($(this).is(':checkbox')) {
                if ($(this).is(':checked')) {
                    if (!customFieldValues[fieldId]) {
                        customFieldValues[fieldId] = [];
                    }
                    customFieldValues[fieldId].push($(this).val());
                    hasValues = true;
                }
            } else {
                fieldValue = $(this).val();
                if (fieldValue) {
                    customFieldValues[fieldId] = fieldValue;
                    hasValues = true;
                }
            }
        });

        // Only generate if we have some values
        if (!hasValues) {
            return;
        }

        // AJAX call to generate title
        var data = {
            'category_id': categoryId,
            'custom_field_values': customFieldValues
        };

        $.ajax({
            type: 'POST',
            url: MdsConfig.baseURL + '/preview-title-ajax',
            data: setAjaxData(data),
            success: function(response) {
                var obj = JSON.parse(response);
                if (obj.success && obj.title) {
                    // Update title field for main language
                    var mainLangId = <?= selectedLangId(); ?>;
                    $('#title_' + mainLangId).val(obj.title);

                    // Update title fields for other languages (same title)
                    <?php foreach ($activeLanguages as $lang): ?>
                    $('#title_<?= $lang->id; ?>').val(obj.title);
                    <?php endforeach; ?>

                    // Add visual feedback
                    $('#title_' + mainLangId).addClass('field-updated');
                    setTimeout(function() {
                        $('#title_' + mainLangId).removeClass('field-updated');
                    }, 1000);
                }
            },
            error: function() {
                // Silently fail for real-time generation
                console.log('Error generating title in real-time');
            }
        });
    }

    // Get current selected category ID
    function getCurrentCategoryId() {
        // Try different selectors for category
        var categoryId = $('select[name="category_id[]"]').val() ||
                        $('select[name="category_id"]').val() ||
                        $('.subcategory-select').last().val();
        return categoryId;
    }

    window.addEventListener('keydown', function (e) {
        if (e.keyIdentifier === 'U+000A' || e.keyIdentifier === 'Enter' || e.keyCode === 13) {
            if (e.target.nodeName === 'INPUT' && e.target.type === 'text' && !e.target.closest('.bootstrap-tagsinput')) {
                e.preventDefault();
                return false;
            }
        }
    }, true);

    $(function () {
        $('#drag-and-drop-zone-file-manager').dmUploader({
            url: '<?= base_url('upload-file-manager-images-post'); ?>',
            queue: true,
            allowedTypes: 'image/*',
            extFilter: ["jpg", "jpeg", "webp", "png"],
            extraData: function (id) {
                return {
                    'file_id': id,
                    '<?= csrf_token() ?>': '<?= csrf_hash(); ?>'
                };
            },
            onNewFile: function (id, file) {
                ui_multi_add_file(id, file, 'file-manager');
                if (typeof FileReader !== "undefined") {
                    var reader = new FileReader();
                    var img = $('#uploaderFile' + id).find('img');
                    reader.onload = function (e) {
                        img.attr('src', e.target.result);
                    }
                    reader.readAsDataURL(file);
                }
            },
            onBeforeUpload: function (id) {
                $('#uploaderFile' + id + ' .dm-progress-waiting').hide();
                ui_multi_update_file_progress(id, 0, '', true);
                ui_multi_update_file_status(id, 'uploading', 'Uploading...');
                $("#btn_reset_upload_image").show();
            },
            onUploadProgress: function (id, percent) {
                ui_multi_update_file_progress(id, percent);
            },
            onUploadSuccess: function (id, data) {
                document.getElementById("uploaderFile" + id).remove();
                refreshFileManagerImages();
                ui_multi_update_file_status(id, 'success', 'Upload Complete');
                ui_multi_update_file_progress(id, 100, 'success', false);
                $("#btn_reset_upload_image").hide();
            }
        });
    });
    $(document).on('click', '#btn_reset_upload_image', function () {
        $("#drag-and-drop-zone-file-manager").dmUploader("reset");
        $("#files-file-manager").empty();
        $(this).hide();
    });
</script>