<div class="row">
    <div class="col-sm-12">
        <div class="wizard-product">
            <h1 class="product-form-title"><?= esc($title); ?></h1>
            <div class="row">
                <div class="col-md-12 wizard-add-product">
                    <ul class="wizard-progress">
                        <li class="active" id="step_general"><strong><?= trans("general_information"); ?></strong></li>
                        <li id="step_dedails"><strong><?= trans("details"); ?></strong></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-12">
        <div class="box box-add-product">
            <div class="box-body">
                <div class="alert-message-lg">
                    <?= view('dashboard/includes/_messages'); ?>
                </div>
                <div class="row">
                    <div class="col-sm-12 clearfix m-b-30">
                        <label class="control-label"><?= trans("images"); ?></label>
                        <?= view('dashboard/product/_image_upload'); ?>
                    </div>
                </div>
                <form action="<?= base_url('add-product-post'); ?>" method="post" id="form_validate">
                    <?= csrf_field(); ?>
                    <input type="hidden" name="back_url" value="<?= getCurrentUrl(); ?>">
                    <div class="form-group">
                        <label class="control-label"><?= trans('product_type'); ?></label>
                        <div class="row">
                            <?php if ($generalSettings->physical_products_system == 1): ?>
                                <div class="col-12 col-sm-6 col-custom-field">
                                    <div class="custom-control custom-radio">
                                        <input type="radio" name="product_type" value="physical" id="product_type_1"
                                               class="custom-control-input"
                                               required <?= $generalSettings->digital_products_system != 1 ? 'checked' : ''; ?>>
                                        <label for="product_type_1"
                                               class="custom-control-label"><?= trans('physical'); ?></label>
                                        <p class="form-element-exp"><?= trans('physical_exp'); ?></p>
                                    </div>
                                </div>
                            <?php endif;
                            if ($generalSettings->digital_products_system == 1): ?>
                                <div class="col-12 col-sm-6 col-custom-field">
                                    <div class="custom-control custom-radio">
                                        <input type="radio" name="product_type" value="digital" id="product_type_2"
                                               class="custom-control-input"
                                               required <?= $generalSettings->physical_products_system != 1 ? 'checked' : ''; ?>>
                                        <label for="product_type_2"
                                               class="custom-control-label"><?= trans('digital'); ?></label>
                                        <p class="form-element-exp"><?= trans('digital_exp'); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label"><?= trans('listing_type'); ?></label>
                        <div class="row">
                            <?php if ($generalSettings->marketplace_system == 1): ?>
                                <div class="col-12 col-sm-6 col-custom-field listing_sell_on_site">
                                    <div class="custom-control custom-radio">
                                        <input type="radio" name="listing_type" value="sell_on_site" id="listing_type_1"
                                               class="custom-control-input" required>
                                        <label for="listing_type_1"
                                               class="custom-control-label"><?= trans('add_product_for_sale'); ?></label><br>
                                        <p class="form-element-exp"><?= trans('add_product_for_sale_exp'); ?></p>
                                    </div>
                                </div>
                            <?php endif;
                            if ($generalSettings->classified_ads_system == 1 && $generalSettings->physical_products_system == 1): ?>
                                <div class="col-12 col-sm-6 col-custom-field listing_ordinary_listing">
                                    <div class="custom-control custom-radio">
                                        <input type="radio" name="listing_type" value="ordinary_listing"
                                               id="listing_type_2" class="custom-control-input" required>
                                        <label for="listing_type_2"
                                               class="custom-control-label"><?= trans('add_product_services_listing'); ?></label>
                                        <p class="form-element-exp"><?= trans('add_product_services_listing_exp'); ?></p>
                                    </div>
                                </div>
                            <?php endif;
                            if ($generalSettings->bidding_system == 1 && $generalSettings->physical_products_system == 1): ?>
                                <div class="col-12 col-sm-6 col-custom-field listing_bidding">
                                    <div class="custom-control custom-radio">
                                        <input type="radio" name="listing_type" value="bidding" id="listing_type_3"
                                               class="custom-control-input" required>
                                        <label for="listing_type_3"
                                               class="custom-control-label"><?= trans('add_product_get_price_requests'); ?></label>
                                        <p class="form-element-exp"><?= trans('add_product_get_price_requests_exp'); ?></p>
                                    </div>
                                </div>
                            <?php endif;
                            if ($generalSettings->digital_products_system == 1 && $generalSettings->selling_license_keys_system == 1): ?>
                                <div class="col-12 col-sm-6 col-custom-field listing_license_keys">
                                    <div class="custom-control custom-radio">
                                        <input type="radio" name="listing_type" value="license_key" id="listing_type_4"
                                               class="custom-control-input" required>
                                        <label for="listing_type_4"
                                               class="custom-control-label"><?= trans('add_product_sell_license_keys'); ?></label>
                                        <p class="form-element-exp"><?= trans('add_product_sell_license_keys_exp'); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="form-group form-group-category">
                        <label class="control-label"><?= trans("category"); ?></label>
                        <select id="categories" name="category_id[]" class="select2 form-control subcategory-select m-0"
                                onchange="getSubCategoriesAndCustomFieldsDashboard(this.value, 1, <?= selectedLangId(); ?>);"
                                required>
                            <option value=""><?= trans('select_category'); ?></option>
                            <?php if (!empty($parentCategories)):
                                foreach ($parentCategories as $item): ?>
                                    <option value="<?= esc($item->id); ?>"><?= getCategoryName($item, $activeLang->id); ?></option>
                                <?php endforeach;
                            endif; ?>
                        </select>
                        <div id="category_select_container"></div>
                    </div>

                    <!-- Custom Fields Section for Real-time Title Generation -->
                    <div id="custom_fields_section" class="custom-fields-container"
                         style="margin: 20px 0; padding: 15px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #f9f9f9;">
                        <h5 style="margin-bottom: 15px; color: #333;">
                            <i class="fa fa-list-alt"></i> <?= trans('custom_fields'); ?>
                            <small class="text-muted"
                                   style="font-weight: normal; margin-left: 10px;"><?= trans('fill_fields_auto_title'); ?></small>
                        </h5>
                        <div id="custom_fields_content">
                            <!-- Custom fields will be loaded here dynamically -->
                            <p class="text-muted" id="no_custom_fields_message">
                                <i class="fa fa-info-circle"></i> <?= trans('select_category_to_load_fields'); ?>
                            </p>
                        </div>
                    </div>

                    <div class="panel-group panel-group-product">
                        <?php $languages = array();
                        array_push($languages, $activeLang);
                        if (!empty($activeLanguages)):
                            foreach ($activeLanguages as $language):
                                if (!empty($language->id != selectedLangId())) {
                                    array_push($languages, $language);
                                }
                            endforeach;
                        endif;
                        if (!empty($languages)):
                            foreach ($languages as $language):?>
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse"
                                               href="#collapse_<?= $language->id; ?>"><?= trans("details"); ?><?= $activeLanguages > 1 ? ':&nbsp;' . esc($language->name) : ''; ?>
                                                &nbsp;<?= selectedLangId() != $language->id ? '(' . trans("optional") . ')' : ''; ?>
                                                <i class="fa fa-caret-down pull-right"></i></a>
                                        </h4>
                                    </div>
                                    <div id="collapse_<?= $language->id; ?>"
                                         class="panel-collapse collapse <?= selectedLangId() == $language->id ? 'in' : ''; ?>">
                                        <div class="panel-body">
                                            <div class="form-group m-b-15">
                                                <label class="control-label">
                                                    <?= trans("title"); ?>
                                                    <span class="badge badge-info"><?= trans("auto_generated"); ?></span>
                                                </label>
                                                <div class="input-group">
                                                    <input type="text" name="title_<?= $language->id; ?>"
                                                           id="title_<?= $language->id; ?>"
                                                           class="form-control form-input auto-generated-field"
                                                           placeholder="<?= trans("title_will_be_generated_from_custom_fields"); ?>"
                                                           readonly
                                                           maxlength="499">
                                                    <div class="input-group-addon">
                                                        <i class="fa fa-magic text-info" title="<?= trans('automatically_generated_from_custom_fields'); ?>"></i>
                                                    </div>
                                                </div>
                                                <?php if (selectedLangId() == $language->id): ?>
                                                    <small class="text-info">
                                                        <i class="fa fa-info-circle"></i>
                                                        <?= trans('title_automatically_generated_from_custom_fields'); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                            <div class="form-group m-b-15">
                                                <label class="control-label"><?= trans("short_description"); ?></label>
                                                <input type="text" name="short_description_<?= $language->id; ?>"
                                                       class="form-control form-input"
                                                       placeholder="<?= trans("short_description"); ?>" maxlength="499">
                                            </div>
                                            <div class="form-group m-b-15">
                                                <label class="control-label"><?= trans("tags"); ?>
                                                    &nbsp;<small>(<?= trans("tags_product_exp"); ?>)</small></label>
                                                <input type="text" name="tags_<?= $language->id; ?>" value=""
                                                       class="tags-input form-control"
                                                       placeholder="<?= trans("type_tag"); ?>">
                                            </div>
                                            <div class="form-group m-b-15">
                                                <label class="control-label"><?= trans("description"); ?></label>
                                                <div class="row">
                                                    <div class="col-sm-12 m-b-5">
                                                        <button type="button" id="btn_add_image_editor"
                                                                class="btn btn-sm btn-info"
                                                                data-editor-id="editor_<?= $language->id; ?>"
                                                                data-toggle="modal" data-target="#fileManagerModal"><i
                                                                    class="icon-image"></i>&nbsp;&nbsp;<?= trans("add_image"); ?>
                                                        </button>
                                                    </div>
                                                </div>
                                                <textarea name="description_<?= $language->id; ?>"
                                                          id="editor_<?= $language->id; ?>"
                                                          class="tinyMCE text-editor"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach;
                        endif; ?>
                    </div>

                    <div class="row">
                        <div class="col-sm-12 m-t-30 buttons-product-form">
                            <button type="submit" class="btn btn-lg btn-success pull-right"><i class="fa fa-check"></i>&nbsp;&nbsp;<?= trans("save_and_continue"); ?>
                            </button>
                        </div>
                    </div>

                </form>
            </div>
        </div>
    </div>

</div>

<div class="modal fade" id="fileManagerModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
     aria-hidden="true">
    <div class="modal-dialog modal-lg modal-file-manager" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?= trans("images"); ?></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"><i class="icon-close"></i></span>
                </button>
            </div>
            <div class="modal-body">
                <div class="file-manager">
                    <div class="file-manager-left">
                        <div class="dm-uploader-container">
                            <div id="drag-and-drop-zone-file-manager" class="dm-uploader text-center">
                                <p class="file-manager-file-types">
                                    <span>JPG</span>
                                    <span>JPEG</span>
                                    <span>PNG</span>
                                </p>
                                <p class="dm-upload-icon">
                                    <i class="icon-upload"></i>
                                </p>
                                <p class="dm-upload-text"><?= trans("drag_drop_images_here"); ?></p>
                                <p class="text-center">
                                    <button class="btn btn-default btn-browse-files"><?= trans('browse_files'); ?></button>
                                </p>
                                <a class='btn btn-md dm-btn-select-files'>
                                    <input type="file" name="file" size="40" multiple="multiple">
                                </a>
                                <ul class="dm-uploaded-files" id="files-file-manager"></ul>
                                <button type="button" id="btn_reset_upload_image"
                                        class="btn btn-reset-upload"><?= trans("reset"); ?></button>
                            </div>
                        </div>
                    </div>
                    <div class="file-manager-right">
                        <div class="file-manager-content">
                            <div id="ckimage_file_upload_response">
                                <?php if (!empty($fileManagerImages)):
                                    foreach ($fileManagerImages as $image): ?>
                                        <div class="col-file-manager" id="fm_img_col_id_<?= $image->id; ?>">
                                            <div class="file-box" data-file-id="<?= $image->id; ?>"
                                                 data-file-path="<?= getFileManagerImageUrl($image); ?>">
                                                <div class="image-container">
                                                    <img src="<?= getFileManagerImageUrl($image); ?>" alt=""
                                                         class="img-responsive">
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach;
                                endif; ?>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="selected_fm_img_file_id">
                    <input type="hidden" id="selected_fm_img_file_path">
                </div>
            </div>
            <div class="modal-footer">
                <div class="file-manager-footer">
                    <button type="button" id="btn_fm_img_delete"
                            class="btn btn-sm btn-danger color-white pull-left btn-file-delete m-r-3"><i
                                class="icon-trash"></i>&nbsp;&nbsp;<?= trans('delete'); ?></button>
                    <button type="button" id="btn_fm_img_select"
                            class="btn btn-sm btn-info color-white btn-file-select"><i class="icon-check"></i>&nbsp;&nbsp;<?= trans('select_image'); ?>
                    </button>
                    <button type="button" class="btn btn-sm btn-secondary color-white"
                            data-dismiss="modal"><?= trans('close'); ?></button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {
        // Load Custom Fields when category changes
        $('select[name="category_id[]"]').change(function () {
            var categoryId = $(this).val();
            if (categoryId) {
                getCustomFieldsForCategory(categoryId, <?= selectedLangId(); ?>, 0);
            } else {
                $('#custom_fields_content').html('<p class="text-muted" id="no_custom_fields_message"><i class="fa fa-info-circle"></i> <?= trans("select_category_to_load_fields"); ?></p>');
            }
        });

        // Also listen to subcategory changes
        $(document).on('change', '.subcategory-select', function () {
            var categoryId = $(this).val();
            if (categoryId) {
                getCustomFieldsForCategory(categoryId, <?= selectedLangId(); ?>, 0);
            }
        });

        // Auto Generate Title functionality removed - titles are now automatically generated
        // Keeping this code commented for reference
        /*
        $('#btn_auto_generate_title_add').click(function () {
            var btn = $(this);
            var originalText = btn.html();

            // Check if category is selected
            var categoryId = $('select[name="category_id"]').val();
            if (!categoryId) {
                alert('<?= trans("please_select_category_first"); ?>');
                return;
            }

            // Show loading state
            btn.html('<i class="fa fa-spinner fa-spin"></i> <?= trans("generating"); ?>...');
            btn.prop('disabled', true);

            // Collect custom field values from the form
            var customFieldValues = {};
            $('input[name^="field_"], select[name^="field_"], textarea[name^="field_"]').each(function () {
                var fieldName = $(this).attr('name');
                var fieldId = fieldName.replace('field_', '');
                var fieldValue = '';

                if ($(this).is(':checkbox')) {
                    // Handle checkbox fields
                    if ($(this).is(':checked')) {
                        if (!customFieldValues[fieldId]) {
                            customFieldValues[fieldId] = [];
                        }
                        customFieldValues[fieldId].push($(this).val());
                    }
                } else {
                    fieldValue = $(this).val();
                    if (fieldValue) {
                        customFieldValues[fieldId] = fieldValue;
                    }
                }
            });

            // AJAX call to preview title
            $.ajax({
                url: '<?= base_url("preview-title-ajax"); ?>',
                type: 'POST',
                data: {
                    category_id: categoryId,
                    custom_field_values: customFieldValues,
                    <?= csrf_token(); ?>: '<?= csrf_hash(); ?>'
                },
                dataType: 'json',
                success: function (response) {
                    if (response.success && response.title) {
                        // Update title field for main language
                        $('#title_<?= selectedLangId(); ?>').val(response.title);

                        // Update title fields for other languages (same title)
                        <?php foreach ($activeLanguages as $lang): ?>
                        $('#title_<?= $lang->id; ?>').val(response.title);
                        <?php endforeach; ?>

                        // Show success message
                        showNotificationAdd('success', response.message || '<?= trans("title_generated_successfully"); ?>');
                    } else {
                        showNotificationAdd('error', response.message || '<?= trans("error_generating_title"); ?>');
                    }
                },
                error: function () {
                    showNotificationAdd('error', '<?= trans("error_generating_title"); ?>');
                },
                complete: function () {
                    // Restore button state
                    btn.html(originalText);
                    btn.prop('disabled', false);
                }
            });
        });
        */
    });

    // Custom Fields functionality is now handled by shared functions in _product_part.php

    // Simple notification function for add product
    function showNotificationAdd(type, message) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var notification = '<div class="alert ' + alertClass + ' alert-dismissible" role="alert">' +
            '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
            '<span aria-hidden="true">&times;</span></button>' + message + '</div>';

        $('.alert-message-lg').html(notification);

        // Auto hide after 5 seconds
        setTimeout(function () {
            $('.alert').fadeOut();
        }, 5000);
    }
</script>

<style>
    /* Custom Fields Styling for Add Product */
    .custom-fields-container {
        transition: all 0.3s ease;
    }

    .custom-fields-container .form-group {
        margin-bottom: 15px;
    }

    .custom-fields-container .col-custom-field {
        padding: 0 10px;
    }

    /* Visual feedback for auto-updated title */
    .field-updated {
        background-color: #d4edda !important;
        border-color: #28a745 !important;
        transition: all 0.3s ease;
    }

    /* Auto-generated field styling */
    .auto-generated-field {
        background-color: #f8f9fa !important;
        border-color: #17a2b8 !important;
        color: #495057 !important;
        cursor: not-allowed;
    }

    .auto-generated-field:focus {
        background-color: #f8f9fa !important;
        border-color: #17a2b8 !important;
        box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25) !important;
    }

    .badge-info {
        background-color: #17a2b8;
        font-size: 0.75em;
        margin-left: 5px;
    }

    /* Custom field input styling */
    .custom-fields-container input,
    .custom-fields-container select,
    .custom-fields-container textarea {
        border: 1px solid #ddd;
        transition: border-color 0.2s ease;
    }

    .custom-fields-container input:focus,
    .custom-fields-container select:focus,
    .custom-fields-container textarea:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* Loading state */
    .custom-fields-loading {
        text-align: center;
        padding: 20px;
        color: #6c757d;
    }
</style>

<?= view('dashboard/product/_product_part'); ?>