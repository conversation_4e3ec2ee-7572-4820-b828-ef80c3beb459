<?php

/**
 * Demo: Auto Title Generation (Phương án 1: Auto-generate hoàn toàn)
 * 
 * This demo shows how the auto title generation works with sample data
 */

// Include the helper file
require_once 'app/Helpers/custom_field_helper.php';

echo "=== DEMO: Auto Title Generation (Phương án 1) ===\n\n";

// Demo 1: Basic title cleaning
echo "1. Title Cleaning Demo:\n";
echo "-------------------\n";

$sampleTitles = [
    "  Toyota   Camry   2020  Black  ",
    "Honda Civic Sedan Blue Manual 2019",
    "iPhone 13 Pro Max 256GB Space Gray Unlocked",
    str_repeat("Very Long Product Name ", 10)
];

foreach ($sampleTitles as $title) {
    $cleaned = cleanTitleString($title);
    echo "Original: '$title'\n";
    echo "Cleaned:  '$cleaned'\n";
    echo "Length:   " . strlen($cleaned) . " characters\n\n";
}

// Demo 2: Simulated Custom Field Values
echo "2. Simulated Custom Field Processing:\n";
echo "-----------------------------------\n";

// Simulate custom fields for a car category
$carCustomFields = [
    (object)[
        'id' => 1,
        'field_order' => 1,
        'field_type' => 'number',
        'name_array' => 'a:1:{i:0;a:2:{s:7:"lang_id";s:1:"1";s:4:"name";s:4:"Year";}}',
    ],
    (object)[
        'id' => 2,
        'field_order' => 2,
        'field_type' => 'text',
        'name_array' => 'a:1:{i:0;a:2:{s:7:"lang_id";s:1:"1";s:4:"name";s:4:"Make";}}',
    ],
    (object)[
        'id' => 3,
        'field_order' => 3,
        'field_type' => 'text',
        'name_array' => 'a:1:{i:0;a:2:{s:7:"lang_id";s:1:"1";s:4:"name";s:5:"Model";}}',
    ],
    (object)[
        'id' => 4,
        'field_order' => 4,
        'field_type' => 'text',
        'name_array' => 'a:1:{i:0;a:2:{s:7:"lang_id";s:1:"1";s:4:"name";s:5:"Color";}}',
    ],
    (object)[
        'id' => 5,
        'field_order' => 5,
        'field_type' => 'dropdown',
        'name_array' => 'a:1:{i:0;a:2:{s:7:"lang_id";s:1:"1";s:4:"name";s:9:"Condition";}}',
    ]
];

// Simulate custom field values for a product
$productCustomFieldValues = [
    1 => '2020',           // Year
    2 => 'Toyota',         // Make
    3 => 'Camry',          // Model
    4 => 'Black',          // Color
    5 => 'Used'            // Condition (from dropdown)
];

echo "Custom Fields (ordered by field_order):\n";
foreach ($carCustomFields as $field) {
    $fieldName = unserialize($field->name_array)[0]['name'];
    $value = isset($productCustomFieldValues[$field->id]) ? $productCustomFieldValues[$field->id] : '(empty)';
    echo "  {$field->field_order}. {$fieldName} ({$field->field_type}): {$value}\n";
}

// Generate title from these values
$titleParts = [];
foreach ($carCustomFields as $field) {
    if (isset($productCustomFieldValues[$field->id]) && !empty($productCustomFieldValues[$field->id])) {
        $titleParts[] = $productCustomFieldValues[$field->id];
    }
}

$generatedTitle = implode(' ', $titleParts);
$cleanedTitle = cleanTitleString($generatedTitle);

echo "\nGenerated Title: '$cleanedTitle'\n\n";

// Demo 3: Different scenarios
echo "3. Different Scenarios:\n";
echo "---------------------\n";

$scenarios = [
    'Complete data' => [
        'Year' => '2021',
        'Make' => 'Honda',
        'Model' => 'Civic',
        'Color' => 'Blue',
        'Transmission' => 'Manual'
    ],
    'Missing some fields' => [
        'Year' => '2019',
        'Make' => 'Ford',
        'Model' => 'Focus',
        'Color' => '',  // Empty
        'Transmission' => 'Automatic'
    ],
    'Only basic info' => [
        'Year' => '',   // Empty
        'Make' => 'BMW',
        'Model' => 'X5',
        'Color' => '',  // Empty
        'Transmission' => ''  // Empty
    ]
];

foreach ($scenarios as $scenarioName => $values) {
    echo "Scenario: $scenarioName\n";
    
    $parts = [];
    foreach ($values as $fieldName => $value) {
        echo "  $fieldName: " . ($value ?: '(empty)') . "\n";
        if (!empty($value)) {
            $parts[] = $value;
        }
    }
    
    $title = implode(' ', $parts);
    $cleanedTitle = cleanTitleString($title);
    
    echo "  Generated Title: '$cleanedTitle'\n\n";
}

// Demo 4: Real-world examples
echo "4. Real-world Examples:\n";
echo "---------------------\n";

$realWorldExamples = [
    'Car Listing' => ['2020', 'Toyota', 'Camry', 'LE', 'Black', 'Automatic', 'Used'],
    'Phone Listing' => ['iPhone', '13', 'Pro', '256GB', 'Space Gray', 'Unlocked'],
    'House Listing' => ['3', 'Bedroom', 'House', 'Lagos', 'Furnished', '2021'],
    'Electronics' => ['Samsung', 'Galaxy', 'S21', '128GB', 'Phantom Gray', 'New']
];

foreach ($realWorldExamples as $category => $values) {
    $title = implode(' ', array_filter($values)); // Remove empty values
    $cleanedTitle = cleanTitleString($title);
    
    echo "$category: '$cleanedTitle'\n";
}

echo "\n=== Demo Complete ===\n\n";

echo "Key Features of Phương án 1 (Auto-generate hoàn toàn):\n";
echo "✓ Uses ALL custom fields with values\n";
echo "✓ Maintains field_order sequence\n";
echo "✓ Automatically cleans and formats title\n";
echo "✓ Handles empty fields gracefully\n";
echo "✓ Limits title length to reasonable size\n";
echo "✓ Works with all field types (text, number, dropdown, etc.)\n";
echo "✓ No manual intervention required\n";
echo "✓ Consistent title format across all products\n\n";

echo "Next Steps:\n";
echo "1. Test with real data in the admin panel\n";
echo "2. Add/edit products with custom fields\n";
echo "3. Verify titles are auto-generated correctly\n";
echo "4. Check that titles update when custom fields change\n";
