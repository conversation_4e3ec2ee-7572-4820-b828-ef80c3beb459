<?php

/**
 * Test Helper Loading in CodeIgniter 4 Context
 */

echo "=== Testing Helper Loading ===\n\n";

// Test 1: Direct include
echo "1. Testing direct include...\n";
try {
    require_once 'app/Helpers/custom_field_helper.php';
    echo "✓ Helper file included successfully\n";
    
    if (function_exists('generateTitleFromCustomFields')) {
        echo "✓ generateTitleFromCustomFields function available\n";
    } else {
        echo "✗ generateTitleFromCustomFields function not available\n";
    }
    
    if (function_exists('cleanTitleString')) {
        echo "✓ cleanTitleString function available\n";
    } else {
        echo "✗ cleanTitleString function not available\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error including helper: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Test cleanTitleString function
echo "2. Testing cleanTitleString function...\n";
if (function_exists('cleanTitleString')) {
    $testTitle = "  Toyota   Camry   2020  ";
    $cleanedTitle = cleanTitleString($testTitle);
    echo "Input: '$testTitle'\n";
    echo "Output: '$cleanedTitle'\n";
    echo "✓ Function works correctly\n";
} else {
    echo "✗ cleanTitleString function not available\n";
}

echo "\n";

// Test 3: Check file permissions
echo "3. Checking file permissions...\n";
$helperFile = 'app/Helpers/custom_field_helper.php';
if (file_exists($helperFile)) {
    echo "✓ Helper file exists\n";
    if (is_readable($helperFile)) {
        echo "✓ Helper file is readable\n";
    } else {
        echo "✗ Helper file is not readable\n";
    }
} else {
    echo "✗ Helper file does not exist\n";
}

echo "\n=== Test Complete ===\n";

echo "\nCodeIgniter 4 Helper Loading Instructions:\n";
echo "1. In Controllers: helper('custom_field');\n";
echo "2. In Models: helper('custom_field');\n";
echo "3. Helper file should be in app/Helpers/custom_field_helper.php\n";
echo "4. Functions should be wrapped in if (!function_exists()) checks\n";
